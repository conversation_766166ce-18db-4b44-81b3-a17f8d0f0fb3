<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction as BaseRequiredAction;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\Routing\RouterInterface as Router;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelEntity;

class OpenAIRequiredActionAdapter
{
    private array $storeApiError = [];

    public function __construct(
        private SystemConfigService $configService,
        private HttpClientInterface $httpClient,
        private Router $router,
        private LoggerInterface $logger,
        private EntityRepository $salesChannelRepository,
        private ?BaseRequiredAction $baseRequiredAction = null
    ) {
    }

    /**
     * List of restricted methods that should not be forwarded to the base plugin
     * @var array<string>
     */
    private array $restrictedMethods = [
        'get_order_status',
        'get_date_time',
        'go_to_url',
        'get_chatbot_name',
        'get_meta_information',
        'fetch_initial_chat_guidelines',
        'get_faqs'
    ];

    /**
     * Handle required actions from OpenAI
     *
     * @param array<int, mixed> $toolCalls
     * @return array<string, mixed>
     */
    public function requiredAction(array $toolCalls): array
    {
        $toolResponse = fn($id, $out) => ['tool_call_id' => $id, 'output' => $out];
        $executed = [];

        foreach($toolCalls as $fnCall) {
            if ($method = ($fnCall['function']['name'] ?? null)) {
                // Check if this is a restricted method
                if (in_array($method, $this->restrictedMethods) && $this->baseRequiredAction !== null) {
                    $this->logger->debug('OpenAIRequiredActionAdapter: Blocking restricted method', [
                        'method' => $method
                    ]);

                    // Call our local implementation directly without trying the base plugin
                    $args = [];
                    if ($arguments = ($fnCall['function']['arguments'] ?? null)) {
                        $args = json_decode($arguments, true) ?: [];
                    }

                    try {
                        $output = $this->{$method}($args);
                        $executed[] = $toolResponse($fnCall['id'], $output);
                    } catch (\Exception $e) {
                        $this->logger->error('OpenAIRequiredActionAdapter: Error executing restricted function', [
                            'method' => $method,
                            'error' => $e->getMessage()
                        ]);
                        $executed[] = $toolResponse($fnCall['id'], ['error' => $e->getMessage()]);
                    }
                    continue;
                }

                if (method_exists($this, $method)) {
                    $args = [];
                    if ($arguments = ($fnCall['function']['arguments'] ?? null)) {
                        $args = json_decode($arguments, true) ?: [];
                    }

                    $this->logger->debug('OpenAIRequiredActionAdapter: Executing function', [
                        'method' => $method,
                        'args' => $args
                    ]);

                    try {
                        $output = $this->{$method}($args);
                        $executed[] = $toolResponse($fnCall['id'], $output);
                    } catch (\Exception $e) {
                        $this->logger->error('OpenAIRequiredActionAdapter: Error executing function', [
                            'method' => $method,
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        $executed[] = $toolResponse($fnCall['id'], ['error' => $e->getMessage()]);
                    }
                } else {
                    $this->logger->warning('OpenAIRequiredActionAdapter: Method not found', [
                        'method' => $method
                    ]);
                    $executed[] = $toolResponse($fnCall['id'], ['error' => "Method {$method} not found"]);
                }
            }
        }

        return $executed;
    }

    /**
     * Get store API errors
     *
     * @return array<string, mixed>
     */
    public function getStoreApiError(): array
    {
        return $this->storeApiError;
    }



    /**
     * Get the Shopware Store API access key
     *
     * @return string The access key
     */
    private function getAccessKey(): string
    {
        // Try to get the access key from the sales channel
        try {
            $criteria = new \Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria();
            $criteria->addFilter(new \Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter('active', true));
            $criteria->setLimit(1);

            $salesChannels = $this->salesChannelRepository->search($criteria, Context::createDefaultContext());

            if ($salesChannels->count() > 0) {
                $salesChannel = $salesChannels->first();
                if ($salesChannel instanceof SalesChannelEntity) {
                    $accessKey = $salesChannel->getAccessKey();
                    if ($accessKey) {
                        return $accessKey;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning('Failed to get access key from sales channel: ' . $e->getMessage());
        }

        // Try to get the access key from the system config
        $accessKey = $this->configService->get('core.api.access_key');
        if ($accessKey) {
            return $accessKey;
        }

        // Fall back to the default access key if not found
        return 'SWSCMVWFJZET2NQRXF5UYWWJWQ'; // Default access key for Shopware 6
    }

    /**
     * Call a method on the base plugin
     *
     * @param string $methodName The name of the method to call
     * @param array<string, mixed> $args The arguments to pass to the method
     * @return array<string, mixed>|null The result of the method call, or null if it failed
     */
    private function callBasePluginMethod(string $methodName, array $args = []): ?array
    {
        if (!$this->baseRequiredAction) {
            return null;
        }

        try {
            // Create a tool call that the base plugin can handle
            $toolCall = [
                'id' => 'call_' . uniqid(),
                'function' => [
                    'name' => $methodName,
                    'arguments' => json_encode($args)
                ]
            ];

            // Call the base plugin's requiredAction method with our tool call
            $result = $this->baseRequiredAction->requiredAction([$toolCall]);

            // Check if there are any store API errors
            $storeApiErrors = $this->baseRequiredAction->getStoreApiError();
            if ($storeApiErrors) {
                $this->logger->error("OpenAIRequiredActionAdapter: Base plugin store API errors for {$methodName}", [
                    'storeApiErrors' => $storeApiErrors
                ]);
                $this->storeApiError = array_merge($this->storeApiError, $storeApiErrors);

                // Even with errors, we might have a partial result
                if (isset($result[$toolCall['id']]['output'])) {
                    return $result[$toolCall['id']]['output'];
                }

                // If we have errors but no result, return null to fall back to our implementation
                return null;
            }

            // Extract the output from the result
            if (isset($result[$toolCall['id']]['output'])) {
                return $result[$toolCall['id']]['output'];
            }

            // If we have no errors but also no result, log a warning
            $this->logger->warning("OpenAIRequiredActionAdapter: Base plugin returned no result for {$methodName}");
            return null;
        } catch (\Exception $e) {
            $this->logger->error("OpenAIRequiredActionAdapter: Failed to use base plugin for {$methodName}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get categories
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_categories(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_categories', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to our implementation
        $getCategories = $this->get_product_properties($args)['categories'] ?? null;

        return ['output' => ['categories' => $getCategories ?: 'No categories found']];
    }

    /**
     * Get manufacturers
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_manufacturer(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_manufacturer', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to our implementation
        $getManufacturer = $this->get_product_properties($args)['properties']['Manufacturer'] ?? null;

        return ['output' => ['manufacturer' => $getManufacturer ?: 'No manufacturer found']];
    }

    /**
     * Get payment methods
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_payment_methods(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_payment_methods', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple error message
        return ['output' => ['payment_methods' => 'Payment methods could not be retrieved. Base plugin functionality is required.']];
    }

    /**
     * Get delivery times
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_delivery_times(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_delivery_times', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple error message
        return ['output' => ['delivery_times' => 'Delivery times could not be retrieved. Base plugin functionality is required.']];
    }

    /**
     * Get countries
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_countries(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_countries', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple error message
        return ['output' => ['countries' => 'Countries could not be retrieved. Base plugin functionality is required.']];
    }

    /**
     * Get order status
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_order_status(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Die Bestellstatus-Funktion ist für den Wein-Berater nicht verfügbar. Dieser Berater ist nur für Produktinformationen zuständig. Für Bestellstatus-Informationen wenden Sie sich bitte an den Kundenservice.',
            'available' => false,
            'alternative_contact' => 'Kundenservice'
        ]];
    }

    /**
     * Get date and time
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_date_time(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Die Datums- und Zeitfunktion ist für den Wein-Berater nicht verfügbar. Dieser Berater konzentriert sich auf Produktinformationen und Beratung zu Weinen.',
            'available' => false
        ]];
    }

    /**
     * Go to URL
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function go_to_url(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Die URL-Weiterleitungsfunktion ist für den Wein-Berater nicht verfügbar. Dieser Berater kann Ihnen Informationen zu Produkten geben, aber keine Weiterleitungen durchführen.',
            'available' => false
        ]];
    }

    /**
     * Get chatbot name
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_chatbot_name(array $args = []): array
    {
        // Return the chatbot name
        return ['output' => [
            'name' => 'Wein-Berater',
            'description' => 'Ein Berater für Weine und andere Produkte in unserem Shop.'
        ]];
    }

    /**
     * Get meta information
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_meta_information(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Diese Funktion ist für den Wein-Berater nicht verfügbar. Bitte nutzen Sie stattdessen die Funktionen get_categories, get_manufacturer oder get_product_properties, um Informationen über Produkte zu erhalten.',
            'available' => false,
            'alternative_functions' => [
                'get_categories',
                'get_manufacturer',
                'get_product_properties'
            ]
        ]];
    }

    /**
     * Fetch initial chat guidelines
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function fetch_initial_chat_guidelines(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Willkommen beim Wein-Berater! Ich kann Ihnen helfen, den perfekten Wein zu finden. Fragen Sie mich nach Kategorien, Herstellern oder Produkteigenschaften.',
            'guidelines' => [
                'Ich kann Ihnen Informationen zu Weinen und anderen Produkten geben.',
                'Ich kann nach Kategorien, Herstellern und Eigenschaften suchen.',
                'Ich kann Ihnen bei der Auswahl des perfekten Weins helfen.'
            ]
        ]];
    }

    /**
     * Get FAQs
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_faqs(array $args = []): array
    {
        // This function is not available for the product advisor chatbot
        return ['output' => [
            'message' => 'Häufig gestellte Fragen sind für den Wein-Berater nicht verfügbar. Sie können mir aber direkt Fragen zu Weinen und anderen Produkten stellen.',
            'available' => false,
            'suggestion' => 'Fragen Sie mich direkt nach Weinen, Kategorien oder Produkteigenschaften.'
        ]];
    }

    /**
     * Product search
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function product_search(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('product_search', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to our implementation
        try {
            // Get the access key from the system config
            $accessKey = 'SWSCMVWFJZET2NQRXF5UYWWJWQ'; // Hard-coded access key for Shopware 6

            $this->logger->debug('OpenAIRequiredActionAdapter: Using access key', [
                'accessKey' => $accessKey ? 'set' : 'not set'
            ]);

            // Prepare search parameters
            $searchParams = [
                'includes' => [
                    'product' => ['id', 'name', 'description', 'translated', 'available', 'markAsTopseller'],
                    'product_manufacturer' => ['id', 'name', 'translated']
                ],
                'limit' => $args['limit'] ?? 10,
                'page' => $args['page'] ?? 1
            ];

            // Add category filter if provided
            if (!empty($args['categories'])) {
                $searchParams['filter'][] = [
                    'type' => 'equalsAny',
                    'field' => 'product.categoryTree',
                    'value' => $args['categories']
                ];
            }

            // Add manufacturer filter if provided
            if (!empty($args['manufacturer'])) {
                $searchParams['filter'][] = [
                    'type' => 'equalsAny',
                    'field' => 'product.manufacturerId',
                    'value' => $args['manufacturer']
                ];
            }

            // Add search term if provided
            if (!empty($args['query'])) {
                $searchParams['search'] = $args['query'];
            }

            // Add price range if provided
            if (!empty($args['price_min']) || !empty($args['price_max'])) {
                $priceFilter = [
                    'type' => 'range',
                    'field' => 'product.price',
                    'parameters' => []
                ];

                if (!empty($args['price_min'])) {
                    $priceFilter['parameters']['gte'] = (float)$args['price_min'];
                }

                if (!empty($args['price_max'])) {
                    $priceFilter['parameters']['lte'] = (float)$args['price_max'];
                }

                $searchParams['filter'][] = $priceFilter;
            }

            // Execute search
            $result = $this->storeApiClient('POST', '/product', $searchParams);

            if (empty($result['elements'])) {
                return ['output' => ['products' => 'No products found matching your criteria']];
            }

            // Process results - create a more complete product structure for the template
            $products = [];
            foreach ($result['elements'] as $product) {
                // Create a product structure that matches what the template expects
                $processedProduct = [
                    'id' => $product['id'],
                    'parentId' => $product['parentId'] ?? null,
                    'name' => $product['name'],
                    'description' => $product['description'] ?? '',
                    'translated' => [
                        'name' => $product['translated']['name'] ?? $product['name'],
                        'description' => $product['translated']['description'] ?? $product['description'] ?? ''
                    ],
                    'available' => $product['available'] ?? true,
                    'markAsTopseller' => $product['markAsTopseller'] ?? false,
                    'calculatedPrice' => [
                        'totalPrice' => $product['calculatedPrice']['totalPrice'] ?? null
                    ],
                    'manufacturer' => [
                        'id' => $product['manufacturer']['id'] ?? null,
                        'name' => $product['manufacturer']['name'] ?? 'Unknown',
                        'translated' => [
                            'name' => $product['manufacturer']['translated']['name'] ?? $product['manufacturer']['name'] ?? 'Unknown'
                        ]
                    ],
                    'cover' => [
                        'media' => [
                            'thumbnails' => [
                                [
                                    'url' => $product['cover']['media']['thumbnails'][0]['url'] ?? null
                                ]
                            ]
                        ]
                    ],
                    'options' => $product['options'] ?? [],
                    'properties' => $product['properties'] ?? []
                ];

                $products[] = $processedProduct;
            }

            // Create a response structure that matches what the template expects
            return [
                'output' => [
                    'exec' => 'product_search',
                    'total' => $result['total'] ?? count($products),
                    'totalAll' => $result['total'] ?? count($products),
                    'args' => $args,
                    'propertyData' => [
                        'setParameter' => [
                            'properties' => []
                        ]
                    ],
                    'categoryMap' => [],
                    'items' => [
                        '_meta_data' => [
                            'products_total' => $result['total'] ?? count($products),
                            'products_children' => $result['total'] ?? count($products),
                            'limit' => $args['limit'] ?? 10,
                            'notification' => []
                        ],
                        'products' => $products
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Error searching products', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ['output' => ['error' => 'Failed to search products: ' . $e->getMessage()]];
        }
    }

    /**
     * Get product details
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_product_details(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_product_details', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to our implementation - try to get product details using product_search
        try {
            if (!empty($args['id'])) {
                // Use product_search to get the product details
                $searchArgs = ['id' => $args['id']];
                $searchResult = $this->product_search($searchArgs);

                if (isset($searchResult['output']) && isset($searchResult['output']['items']['products'][0])) {
                    $product = $searchResult['output']['items']['products'][0];

                    // Format the response to match what the template expects
                    return [
                        'output' => [
                            'exec' => 'get_product_details',
                            'product' => $product,
                            'total' => 1,
                            'items' => [
                                'products' => [$product]
                            ]
                        ]
                    ];
                }
            }

            // If we couldn't get the product details
            return ['output' => [
                'message' => 'Produktdetails konnten nicht abgerufen werden.',
                'available' => false
            ]];
        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Error getting product details', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ['output' => [
                'message' => 'Fehler beim Abrufen der Produktdetails: ' . $e->getMessage(),
                'available' => false
            ]];
        }
    }

    /**
     * Tags for logs
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function tags_for_logs(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('tags_for_logs', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple error message
        return ['output' => ['tags' => 'Tags could not be retrieved. Base plugin functionality is required.']];
    }

    /**
     * Search logs
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function search_logs(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('search_logs', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple error message
        return ['output' => 'Logs could not be searched. Base plugin functionality is required.'];
    }

    /**
     * Log a message
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function log(array $args = []): array
    {
        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('log', $args);
        if ($result !== null) {
            return $result;
        }

        // Fall back to a simple implementation
        $tag = $args['tag'] ?? null;
        $message = $args['message'] ?? null;

        if (is_string($message) && !empty(trim($message)) && $tag) {
            $this->logger->info('OpenAIRequiredActionAdapter: Log message', [
                'tag' => $tag,
                'message' => $message,
                'priority' => $args['priority'] ?? 'medium'
            ]);

            return ['output' => ['log_response' => [
                'logged' => true,
                'message' => $message,
                'tag' => $tag
            ]]];
        }

        return ['output' => ['log_response' => [
            'logged' => false,
            'error' => 'No message or tag submitted',
            'loggedData' => null,
        ]]];
    }

    /**
     * Get product properties
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_product_properties(array $args = []): array
    {
        // Getting product properties

        // Use the base plugin's implementation
        $result = $this->callBasePluginMethod('get_product_properties', $args);
        if ($result !== null) {
            return $result;
        }

        $this->logger->warning('OpenAIRequiredActionAdapter: Base plugin required action handler not available or failed');

        $startTime = microtime(true);

        try {
            // Get the access key
            $accessKey = $this->getAccessKey();
            $baseUrl = $this->configService->get('core.basicInformation.shopUrl');
            if (!$baseUrl) {
                $baseUrl = 'http://localhost';
            }

            $this->logger->debug('OpenAIRequiredActionAdapter: Making request to Store API', [
                'url' => $baseUrl . '/store-api/category',
                'accessKey' => $accessKey
            ]);

            // Fetch categories
            $categoryFilter = [
                ['type' => 'equals', 'field' => 'active', 'value' => true],
                ['type' => 'equals', 'field' => 'visible', 'value' => true]
            ];

            $categoryResponse = $this->httpClient->request('POST', $baseUrl . '/store-api/category', [
                'headers' => [
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                    'sw-access-key' => $accessKey
                ],
                'json' => [
                    'filter' => $categoryFilter,
                    'sort' => [['field' => 'level', 'order' => 'ASC']],
                    'includes' => ['category' => ['id', 'name', 'breadcrumb', 'translated', 'level']]
                ]
            ]);

            $categoryResult = $categoryResponse->toArray();
            $categories = [];

            if (isset($categoryResult['elements']) && is_array($categoryResult['elements'])) {
                foreach ($categoryResult['elements'] as $i => $category) {
                    $breadcrumb = $category['translated']['breadcrumb'] ?? $category['breadcrumb'] ?? null;
                    $name = $category['translated']['name'] ?? $category['name'] ?? null;

                    if ($breadcrumb && $name) {
                        $categories[$i] = [
                            'name' => $name,
                            'parent' => array_slice($breadcrumb, 0, -1),
                            'url' => '/navigation/' . $category['id'],
                            'id' => $category['id'],
                        ];
                    }
                }
            }

            // Fetch manufacturers
            $manufacturerResponse = $this->httpClient->request('POST', $baseUrl . '/store-api/product-manufacturer', [
                'headers' => [
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                    'sw-access-key' => $accessKey
                ],
                'json' => [
                    'includes' => ['product_manufacturer' => ['id', 'name', 'translated']]
                ]
            ]);

            $manufacturerResult = $manufacturerResponse->toArray();
            $properties = [];

            if (isset($manufacturerResult['elements']) && is_array($manufacturerResult['elements'])) {
                $properties['Manufacturer'] = [];

                foreach ($manufacturerResult['elements'] as $idx => $manufacturer) {
                    $name = $manufacturer['translated']['name'] ?? $manufacturer['name'] ?? null;
                    if ($name) {
                        $properties['Manufacturer'][$idx] = $name;
                    }
                }
            }

            // Fetch property groups
            $propertyResponse = $this->httpClient->request('POST', $baseUrl . '/store-api/property-group', [
                'headers' => [
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                    'sw-access-key' => $accessKey
                ],
                'json' => [
                    'includes' => [
                        'property_group' => ['id', 'name', 'translated'],
                        'property_group_option' => ['id', 'name', 'translated', 'groupId']
                    ]
                ]
            ]);

            $propertyResult = $propertyResponse->toArray();

            if (isset($propertyResult['elements']) && is_array($propertyResult['elements'])) {
                foreach ($propertyResult['elements'] as $group) {
                    $groupName = $group['translated']['name'] ?? $group['name'] ?? null;

                    if ($groupName && isset($group['options']) && is_array($group['options']) && count($group['options']) > 0) {
                        $properties[$groupName] = [];

                        foreach ($group['options'] as $optionIdx => $option) {
                            $optionName = $option['translated']['name'] ?? $option['name'] ?? null;
                            if ($optionName) {
                                $properties[$groupName][$optionIdx] = $optionName;
                            }
                        }
                    }
                }
            }

            $result = [
                '_meta_data' => [
                    'count' => [
                        'properties' => count($properties, COUNT_RECURSIVE) - count($properties),
                        'categories' => count($categories)
                    ],
                    'notification' => [],
                    'request_took' => number_format(microtime(true) - $startTime, 4, '.') . ' sec'
                ],
                'properties' => $properties,
                'categories' => array_values($categories)
            ];

            $this->logger->debug('OpenAIRequiredActionAdapter: Product properties response', [
                'result' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Error fetching product properties', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        // Fallback to dummy data if the API call fails
        $categories = [
            [
                'name' => 'Reptilien',
                'parent' => [],
                'url' => '#',
                'id' => 'dummy-1',
            ],
            [
                'name' => 'Futter',
                'parent' => ['Reptilien'],
                'url' => '#',
                'id' => 'dummy-2',
            ],
            [
                'name' => 'Zubehör',
                'parent' => ['Reptilien'],
                'url' => '#',
                'id' => 'dummy-3',
            ]
        ];

        $properties = [
            'Manufacturer' => ['Exo Terra', 'Zoo Med', 'Lucky Reptile'],
            'Tierart' => ['Gecko', 'Schlange', 'Schildkröte', 'Leguan'],
            'Futtertyp' => ['Lebendfutter', 'Trockenfutter', 'Frostfutter']
        ];

        $endTime = microtime(true);
        $executionTime = number_format($endTime - $startTime, 4, '.') . ' sec';

        $result = [
            '_meta_data' => [
                'count' => [
                    'properties' => count($properties, COUNT_RECURSIVE) - count($properties),
                    'categories' => count($categories)
                ],
                'notification' => [],
                'request_took' => $executionTime
            ],
            'properties' => $properties,
            'categories' => array_values($categories)
        ];

        $this->logger->debug('OpenAIRequiredActionAdapter: Returning dummy product properties', [
            'result' => $result
        ]);

        return $result;
    }

    /**
     * Make a request to the store API
     *
     * @param string $method HTTP method
     * @param string $path API path
     * @param array<string, mixed> $content Request body
     * @param array<string, mixed>|null $header Additional headers
     * @return array<string, mixed>
     */
    private function storeApiClient(string $method, string $path, array $content = [], ?array $header = []): array
    {
        // Try to use the base plugin's implementation if available
        if ($this->baseRequiredAction) {
            try {
                $this->logger->debug('OpenAIRequiredActionAdapter: Using base plugin for Store API request', [
                    'method' => $method,
                    'path' => $path
                ]);

                return $this->baseRequiredAction->storeApiClient($method, $path, $content, $header);
            } catch (\Exception $e) {
                $this->logger->error('OpenAIRequiredActionAdapter: Failed to use base plugin for Store API request', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // Fall back to our implementation
            }
        }

        // Fall back to our implementation
        try {
            // Get the access key
            $accessKey = $this->getAccessKey();

            $this->logger->debug('OpenAIRequiredActionAdapter: Store API access key', [
                'accessKey' => $accessKey ? 'set' : 'not set'
            ]);

            $headers = [
                'accept' => 'application/json',
                'content-type' => 'application/json',
                'sw-access-key' => $accessKey
            ];

            if ($header) {
                $headers = array_merge($headers, $header);
            }

            $baseUrl = $this->configService->get('core.basicInformation.shopUrl');
            if (!$baseUrl) {
                $baseUrl = 'http://localhost';
            }

            $this->logger->debug('OpenAIRequiredActionAdapter: Making Store API request', [
                'method' => $method,
                'url' => $baseUrl . '/store-api/' . ltrim($path, '/'),
                'headers' => array_keys($headers),
                'content' => $content
            ]);

            $response = $this->httpClient->request($method, $baseUrl . '/store-api/' . ltrim($path, '/'), [
                'headers' => $headers,
                'json' => $content
            ]);

            $result = $response->toArray();

            $this->logger->debug('OpenAIRequiredActionAdapter: Store API response', [
                'status' => $response->getStatusCode(),
                'result' => $result
            ]);

            return $result;
        } catch(\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Store-API request failed', [
                'exception' => $e->getMessage(),
                'path' => $path,
                'trace' => $e->getTraceAsString()
            ]);

            $this->storeApiError['apiException'][] = $e->getMessage();
        }

        return [];
    }
}
