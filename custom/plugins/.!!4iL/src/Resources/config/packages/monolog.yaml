monolog:
  channels: ['fel_custom_gpt_agent']

  handlers:
    felAIProductAdvisorLogHandler:
        type: rotating_file
        path: "%kernel.logs_dir%/fel_custom_gpt_agent_%kernel.environment%.log"
        # In production, only log errors and above
        level: error
        channels: [ "fel_custom_gpt_agent"]

    # Filter out noisy message queue and notification logs
    messageQueueFilter:
        type: fingers_crossed
        handler: main
        action_level: error  # Only log errors and above for these routes
        channels: ["!event"]
        excluded_404s:
            - ^/api/_action/message-queue/consume
            - ^/api/notification/message
