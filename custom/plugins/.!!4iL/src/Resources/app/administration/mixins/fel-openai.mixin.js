const { Component, Mixin } = Shopware;

Mixin.register('fel-openai-mixin', {

    data() {
        return {
            assistant: {},
            assistantId: null,
            assistantList: [],
            assistantListMap: [],
            aiModels: [],
            filesList: {},
            vectorStoreList: [],
            activeFilesList: {},
            modelError: false,
            assistantTemperatureDefault: 0.3,
            allowedFileTypes: [
                '.txt', '.md', '.json', '.doc', '.docx', '.html', '.pptx', '.pdf'
            ],
            availableModels: [
                'gpt-3.5-turbo',
                'gpt-4o',
                'gpt-4-turbo',
            ],
            availableFunctionsConfig: {
                sorting: [
                    'name-asc',
                    'name-desc',
                    'price-asc',
                    'price-desc',
                    'topseller',
                ],
            },
        };
    },

    methods: {

        deleteFile(fileId) {
            if (!confirm(`${this.$t('fel-gpt-assistant.delete.delete')}?`) == true) {
                return false;
            }
            this.httpClientDelete(`${this.pluginConfig.apiUrl}/files/${fileId}`, (response) => {
                if ('undefined' !== typeof response.data && response.data.deleted) {
                    this.isDeleted = true;
                    this.fetchFiles();
                } else {
                    this.createNotificationError({message: response.message || 'error:deleteFile'});
                }
            });
        },

        uploadFile(event, reloadPage=false) {
            if ('undefined' !== typeof event.target.files && 'undefined' !== typeof event.target.files[0]) {
                var file = event.target.files[0];
                if (0 === file.size) {
                    this.createNotificationError({message: this.$t('fel-gpt-assistant.files.vectorStore.fileIsEmpty')});
                }
                else if (/^(\/?[a-z0-9A-Z.\-_]+)+$/.test(file.name)) {
                    this.isLoading = true;
                    this.httpClient.post(`${this.pluginConfig.apiUrl}/files`, {
                        purpose: 'assistants',
                        file: event.target.files[0]
                    }, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authorization': this.pluginConfig.apiHeader.Authorization,
                            'OpenAI-Beta': this.pluginConfig.openAiBetaVersion,
                        }
                    })
                    .then((response) => {
                        if ('undefined' !== typeof response.data) {
                            if (!reloadPage) {
                                this.createNotificationSuccess({message: response.data.filename});
                            }
                            this.fetchFiles();
                        }
                    })
                    .catch((error) => {this.createNotificationError({message: error.message})})
                    .finally(() => {
                        this.isLoading = false;
                        if (reloadPage) {
                            window.location.reload();
                        }
                    });
                }
                else {
                    this.createNotificationError({message: this.$t('fel-gpt-assistant.files.vectorStore.filenameInvalid')});
                }
            }
        },

        createVectorStore(event) {
            if ('undefined' !== typeof event.target) {
                var setName = event.target.querySelector('[name="fel-sw-create-vector-store"]');
                if (setName && 'undefined' !== typeof setName.value) {
                    if (setName.value) {
                        this.httpClient.post(`${this.pluginConfig.apiUrl}/vector_stores`, {
                            name: setName.value
                        }, {
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': this.pluginConfig.apiHeader.Authorization,
                                'OpenAI-Beta': this.pluginConfig.openAiBetaVersion,
                            }
                        })
                        .then((response) => {
                            if ('undefined' !== typeof response.data && 'undefined' !== typeof response.data.status) {
                                if ('completed' === response.data.status) {
                                    this.fetchVectorStores();
                                }
                            }
                        })
                        .catch((error) => {this.createNotificationError({message: error.message})})
                        .finally(() => {setName.value = '';});
                    } else {
                        this.createNotificationError({message: this.$t('fel-gpt-assistant.files.vectorStore.setName')});
                    }
                }
            }
        },

        async fetchVectorStores() {
            await this.httpClientGet(`${this.pluginConfig.apiUrl}/vector_stores`, (response) => {
                if ('undefined' !== typeof response.data.data && 'undefined' !== typeof response.data.data.length) {
                    var vectors = response.data.data;
                    var setVectors = [];
                    for (var i = 0; i < vectors.length; i++) {
                        if (vectors[i].name) {
                            setVectors.push(vectors[i]);
                        }
                    }
                    this.vectorStoreList = setVectors;
                }
            });
        },

        async fetchFiles() {
            await this.httpClientGet(`${this.pluginConfig.apiUrl}/files`, (response) => {
                if (response.data && 'undefined' !== typeof response.data.data.length) {
                    for (var i = 0; i < response.data.data.length; i++) {
                        var file = response.data.data[i];
                        this.filesList[file.id] = file;
                    }
                    this.filesList = {data: this.filesList}
                }
            });
        },

        async fetchModels() {
            await this.httpClientGet(`${this.pluginConfig.apiUrl}/models`, (response) => {
                if ('undefined' !== typeof response.data && 'undefined' !== typeof response.data.data) {
                    this.aiModels = response.data.data;

                    var setAvailableModels = [];
                    var allowedModels = [];
                    for (var i = 0; i < this.aiModels.length; i++) {
                        if ('undefined' !== typeof this.aiModels[i].id) {
                            allowedModels.push(this.aiModels[i].id);
                        }
                    }

                    for (var idx = 0; idx < this.availableModels.length; idx++) {
                        if (allowedModels.includes(this.availableModels[idx])) {
                            setAvailableModels.push(this.availableModels[idx]);
                        } else {
                            delete this.availableModels[idx];
                        }
                    }
                }
            });
        },

        async fetchAssistants() {
            await this.httpClientGet(`${this.pluginConfig.apiUrl}/assistants`, (response) => {
                this.assistantList = response.data;
                var assistantVectoreStores = {};

                if ('undefined' !== typeof this.assistantList.data) {
                    var assistantsData = this.assistantList.data;
                    var setIdNameList = {};
                    for (var ali = 0; ali < assistantsData.length; ali++) {
                        var oaiAssistant = assistantsData[ali];
                        setIdNameList[oaiAssistant.id] = {
                            name: oaiAssistant.name,
                            model: oaiAssistant.model,
                            temperature: oaiAssistant.temperature,
                        };

                        if ('undefined' !== typeof oaiAssistant.tool_resources.file_search &&
                            'undefined' !== typeof oaiAssistant.tool_resources.file_search.vector_store_ids) {
                            assistantVectoreStores[oaiAssistant.id] = [];
                            for (var vsi = 0; vsi < oaiAssistant.tool_resources.file_search.vector_store_ids.length; vsi++) {
                                var vectorStore = oaiAssistant.tool_resources.file_search.vector_store_ids[vsi];
                                if (!assistantVectoreStores[oaiAssistant.id].includes(vectorStore)) {
                                    assistantVectoreStores[oaiAssistant.id].push(vectorStore);
                                }
                            }
                        }
                    }
                    this.assistantListMap = setIdNameList;
                }

                this.activeFilesList = {vectorStores: assistantVectoreStores};

                this.fetchModels();

                return response.data;
            });
        },

        availableFunctions() {
            return {
                defaults: [
                    'get_meta_information',
                    'get_product_properties',
                    'product_search',
                ],
                list: {
                    get_meta_information: {
                        name: 'get_meta_information',
                        description: 'Retrieve metadata about the shop, including contact details, opening times, FAQs, and other customizable information defined by the shop owner.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_product_properties: {
                        name: 'get_product_properties',
                        description: 'Retrieve available product properties, manufacturer names, and categories used by the shop.',
                        parameters: {
                            type: 'object',
                            properties: {
                                categories: {
                                    type: 'array',
                                    description: 'An array of category names to filter and retrieve properties specific to these categories. Never use this option for the first call to this function; the first call should be made without categories to retrieve all necessary information. Subsequent calls can use this parameter as needed.',
                                    items: {type: "string"}
                                }
                            },
                            required: []
                        }
                    },
                    product_search: {
                        name: "product_search",
                        description: "Perform a product search using the user-provided text query, filtering by categories, properties, price range, and sort order.",
                        parameters: {
                            type: "object",
                            properties: {
                                query: {
                                    type: "string",
                                    description: "The text query to search for. Ensure the query is singularized."
                                },
                                categories: {
                                    type: "array",
                                    description: "An array of category names to filter the search results by specific categories.",
                                    items: {type: "string"}
                                },
                                properties: {
                                    type: "array",
                                    description: "An array of property and manufacturer names to filter the search results based on the customer's query.",
                                    items: {type: "string"}
                                },
                                price_min: {
                                    type: "integer",
                                    description: "The minimum price."
                                },
                                price_max: {
                                    type: "integer",
                                    description: "The maximum price."
                                },
                                order: {
                                    type: "string",
                                    description: "The sort order of the search results.",
                                    enum: this.availableFunctionsConfig.sorting,
                                }
                            },
                            required: []
                        }
                    },
                    get_product_details: {
                        name: 'get_product_details',
                        description: 'Get product details',
                        parameters: {
                            type: 'object',
                            properties: {
                                id: {
                                    type: 'string',
                                    description: 'The product ID.'
                                }
                            },
                            required: ['id']
                        }
                    },
                    get_categories: {
                        name: 'get_categories',
                        description: 'Retrieve available categories with URLs, names and breadcrumbs as used by the shop.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    go_to_url: {
                    name: 'go_to_url',
                    description: 'Redirect to a URL. Redirect only if user explicitly asks for a redirection.',
                    parameters: {
                        type: 'object',
                        properties: {
                            url: {
                                type: 'string',
                                description: 'The URL to redirect to.'
                            }
                        },
                        required: ['url']
                    }
                    },
                    get_date_time: {
                        name: 'get_date_time',
                        description: 'Get date, time and timezone.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_manufacturer: {
                        name: 'get_manufacturer',
                        description: 'Get manufacturer.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_countries: {
                        name: 'get_countries',
                        description: 'Get countries.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_delivery_times: {
                        name: 'get_delivery_times',
                        description: 'Get delivery times.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_payment_methods: {
                        name: 'get_payment_methods',
                        description: 'Get payment methods.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                    get_chatbot_name: {
                        name: 'get_chatbot_name',
                        description: 'Get chatbot name.',
                        parameters: {type: 'object', properties: {}, required: []}
                    },
                }
            };
        },

        defaultInstructions() {
            // To ensure functionality, the instructions must be in English
            return this.flattenString(`
                You are a customer support chatbot in a Shopware web shop environment. Follow these guidelines to answer customer questions effectively.

                Rules
                1. Language: Respond only in the same language as the user query.
                2. HTML Responses: Format responses always using HTML elements like <h2>, <p>, <a>, <img>, <b>, and <ul>. Use a single <ul> for product listings and always include the 'url' of products to link them in the response.
                3. Clickable Content: Use always <a> elements for URLs, paths, emails, and phone numbers to ensure they are easy to read and clickable. Use <b> elements to highlight key names.
                4. Initial Data Retrieval: Call get_product_properties() once and as soon as possible to retrieve categories, properties, and manufacturer names and use its content for subsequent requests.
                5. Product Recommendations: For general advice or recommendations without specific product details, use retrieved data from get_product_properties() and tailor your advice.
                6. Property Names: Use always property and category names from get_product_properties() as arguments for product_search().
                7. No Invented Information: Never invent URLs or any information that might or could fit. Execute functions like get_product_properties() or get_meta_information().
                    - Only deliver URLs and individual information retrieved from function calls.
                    - For general questions such as contact details, opening times, terms and conditions, etc., call the function get_meta_information(), it contains contact details, opening times, FAQs, and other customizable information defined by the shop owner.
                    - Call get_meta_information() only once and use its content for subsequent requests.

                Search
                1. Initial Data Retrieval: If not requested yet, call get_product_properties() to get categories, properties, and manufacturer names as used by the shop.
                    - Call get_product_properties() only once and use its content for subsequent requests.
                2. User Query Analysis: Detect and differentiate between categories and properties in the user query.
                    - If the user query matches or contains a category, set it as the category for the search.
                    - Remove categories and properties from the query before processing.
                3. Search Term Refinement: Use natural language processing to singularize and refine search terms.
                4. Handling Large Results: If product_search() returns too many results, ask the user for more specific categories or properties to refine the search. Suggest existing options retrieved from get_product_properties().

                Functions
                - Adhere strictly to the descriptions of each function.
                - Expect JSON objects as return types from functions.

                Example Workflow
                1. Call get_product_properties() only once and use its content for subsequent requests.
                2. User Query: Analyze the query to identify categories, properties, and manufacturers.
                3. Product Search: Use the refined query and data from get_product_properties() for product_search().
                4. Categories: If the refined query matches or contains a category, set it also as categories argument for product_search().
                5. Response: Format the response using HTML and ensure all clickable content is properly linked with <a> elements.
                6. Language: Make sure your response is in the same language as the user query.

                Instructions end.

                Generic HTML Example for Chat Messages

                <div class="fel-oai-response">
                    <a href="[tel|mailto|null]:URL">LABEL</a>
                    <p>MESSAGE</p>
                    <ul>
                        <li>MESSAGES</li>
                    </ul>
                </div>
            `);
        },

        genericExamplesForSearchQueries() {
            return this.flattenString(`
                Function Call Example: get_product_properties()

                {
                    "properties": {
                        "GroupOfColors": [
                            "GoldPurple",
                            "RiverDark"
                        ],
                        "GroupOfSizes": [
                            "BigSize"
                        ],
                        "Manufacturer": [
                            "ManufacturerFive"
                        ]
                    },
                    "categories": [
                        {
                            "name": "ManBear",
                            "parent": [
                                "Sub Category"
                            ],
                            "url": "/navigation/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                        }
                    ]
                }

                User Interaction Examples

                User: "I search for Paddles."
                Assistant: product_search(query: "Paddle")

                User: "I am looking for ManBear."
                Assistant: product_search(query: "ManBear", categories: ["ManBear"])

                User: "I search for a Pullover in GoldPurple."
                Assistant: product_search(query: "Pullover", properties: ["GoldPurple"])

                User: "I am looking for Pants in the category ManBear."
                Assistant: product_search(query: "Pant", categories: ["ManBear"])

                User: "I am searching for Hats in GoldPurple with a budget starting from 20 €."
                Assistant: product_search(query: "Hat", properties: ["GoldPurple"], price_min: 20)

                User: "I am searching for Hats in GoldPurple or RiverDark with a budget starting from 20 €."
                Assistant: product_search(query: "Hat", properties: ["GoldPurple", "RiverDark"], price_min: 20)

                User: "I am looking for Pants from ManufacturerFive with a budget between 5 and 100 €."
                Assistant: product_search(query: "Pant", properties: ["ManufacturerFive"], price_min: 5, price_max: 100)

                User: "I am searching for anything in GoldPurple or RiverDark in BigSize in the category ManBear."
                Assistant: product_search(categories: ['ManBear'], properties: ["GoldPurple", "RiverDark", "BigSize"])

                User: "I am searching Pants in GoldPurple and RiverDark in BigSize from ManufacturerFive."
                Assistant: product_search(query: "Pant", properties: ["GoldPurple", "RiverDark", "BigSize", "ManufacturerFive"])

                User: "I am searching for a present in GoldPurple from ManufacturerFive in ManBear with a budget from 30 to 220 €, cheapest first."
                Assistant: product_search(categories: ["ManBear"], properties: ["GoldPurple", "ManufacturerFive"], price_min: 30, price_max: 220, order: "price-asc")
            `);
        },

        flattenString(str) {
            var arr = str.trim().split("\n");
            var clean = [];
            for (var i = 0; i < arr.length; i++) {
                clean.push(arr[i].replace(' '.repeat(16), ''));
            }
            return clean.join("\n");
        }
    },

});
