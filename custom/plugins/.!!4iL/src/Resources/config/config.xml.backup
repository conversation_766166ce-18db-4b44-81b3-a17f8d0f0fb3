<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/trunk/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>OpenAI Authentication Configuration</title>
        <title lang="de-DE">OpenAI Authentifizierungs Konfiguration</title>


        <input-field type="password">
            <name>felOpenAiApiKey</name>
            <label>API Key (https://platform.openai.com/account/api-keys)</label>
            <label lang="de-DE">Geheimschlüssel (https://platform.openai.com/account/api-keys)</label>
            <defaultValue></defaultValue>
            <helpText>Your secret token from OpenAI. If left empty, the API key from the base plugin (FelOAIAssistantsManager) will be used.</helpText>
            <helpText lang="de-DE">Ihr geheimer API-Schlü<PERSON> von OpenAI. Wen<PERSON> leer gel<PERSON>, wird der API-Schlüssel vom Basis-Plugin (FelOAIAssistantsManager) verwendet.</helpText>
        </input-field>

        <component name="fel-test-openai-api-key">
            <name>apiTest</name>
        </component>
    </card>

    <card>
        <title>OpenAI Assistant Configuration</title>
        <title lang="de-DE">OpenAI Assistent Konfiguration</title>

        <input-field type="text">
            <name>felOpenAiAssistantId</name>
            <defaultValue></defaultValue>
            <label>Default Assistant ID (Fallback)</label>
            <label lang="de-DE">Standard Assistenten-ID (Fallback)</label>
            <helpText>This is only used as a fallback if no Assistant ID is specified in the CMS element. Normally, you should set the Assistant ID in each CMS element.</helpText>
            <helpText lang="de-DE">Dies wird nur als Fallback verwendet, wenn keine Assistenten-ID im CMS-Element angegeben ist. Normalerweise sollten Sie die Assistenten-ID in jedem CMS-Element festlegen.</helpText>
        </input-field>
    </card>

    <card>
        <title>OpenAI Chat Log</title>
        <title lang="de-DE">OpenAI Chat-Log</title>

        <input-field type="checkbox">
            <name>enableLogging</name>
            <label>Enable Logging</label>
            <label lang="de-DE">Log aktivieren</label>
            <defaultValue>false</defaultValue>
            <helpText>Since the Assistants API requires many independent requests for each chat message, each containing only parts of the query, it is quite difficult to log everything comprehensively. Therefore, we only log the optimized course of the AI API that is included in the final response. The AI's course may deviate from what is displayed in the storefront. To see what actually happened in the chat, you can simulate the logged chat in your chatbot.</helpText>
            <helpText lang="de-DE">Da die Assistenten-API für jede Chat-Nachricht viele unabhängige Anfragen benötigt, die jeweils nur Teile der Anfrage enthalten, ist es ziemlich schwierig, alles umfassend zu protokollieren. Daher protokollieren wir nur den optimierten Verlauf der KI-API, der Teil der abschließenden Antwort ist. Der KI-Verlauf kann von dem abweichen, was im Storefront angezeigt wird. Um zu sehen, was im Chat wirklich passiert ist, können Sie den geloggten Chat in ihrem Chatbot simulieren.</helpText>
        </input-field>
    </card>

    <card>
        <title>Customer Authentication</title>
        <title lang="de-DE">Benutzer Authentifizierung</title>

        <input-field type="checkbox">
            <name>authenticatedUserOnly</name>
            <label>Only for authenticated users</label>
            <label lang="de-DE">Nur für authentifizierte Nutzer zugänglich</label>
        </input-field>

        <component name="sw-entity-multi-id-select">
            <name>authenticatedUserOnlyGroup</name>
            <entity>customer_group</entity>
            <label>If "Only for authenticated users", restrict access to customer groups.</label>
            <label lang="de-DE">Wenn nur für authentifizierte Benutzer, beschränken Sie den Zugriff auf Kundengruppen.</label>
        </component>
    </card>

    <card>
        <title>OpenAI misc Configuration</title>
        <title lang="de-DE">OpenAI sonstige Konfiguration</title>

        <input-field type="int">
            <name>openAiUserInputMaxLength</name>
            <step>1</step>
            <min>2</min>
            <defaultValue>400</defaultValue>
            <label>Specify the maximum number of characters a user can enter into the chat interface.</label>
            <label lang="de-DE">Geben Sie die maximale Anzahl an Zeichen an, die ein Benutzer in die Chat-Oberfläche eingeben kann.</label>
        </input-field>

        <input-field type="int">
            <name>openAiSearchLimit</name>
            <step>1</step>
            <min>1</min>
            <max>50</max>
            <defaultValue>8</defaultValue>
            <label>Set Limit for internal search results the chatbot receives.</label>
            <label lang="de-DE">Legen Sie ein Limit für Suchergebnisse fest, die intern der KI zurück gegeben werden.</label>
        </input-field>

        <input-field type="int">
            <name>openAiFilterMaxDescriptionLength</name>
            <step>1</step>
            <min>50</min>
            <defaultValue>500</defaultValue>
            <label>When the chatbot calls up product details, limit the product description to a maximum of x characters.</label>
            <label lang="de-DE">Wenn der Chatbot Produktdetails aufruft, kürze die Produktbeschreibung auf maximal x Zeichen ein.</label>
        </input-field>

        <input-field type="int">
            <name>openAiLoopUntilCompleted</name>
            <step>1</step>
            <min>10</min>
            <max>100</max>
            <defaultValue>50</defaultValue>
            <label>Max. attempts before abort the loop.</label>
            <label lang="de-DE">Max. Versuche vor Abbruch der Schleife.</label>
            <helpText>A recursive loop that checks whether the chatbot's response is completed.</helpText>
            <helpText lang="de-DE">Eine rekursive Schleife, die prüft, ob die Antwort des Chatbots fertig ist.</helpText>
        </input-field>

        <input-field type="int">
            <name>openAiLoopWaitSeconds</name>
            <step>1</step>
            <min>1</min>
            <max>10</max>
            <defaultValue>2</defaultValue>
            <label>Wait x seconds within the loop before checking whether the chatbot's answer is completed.</label>
            <label lang="de-DE">Warte x Sekunden innerhalb der Schleife, bevor geprüft wird, ob die Antwort des Chatbots abgeschlossen ist.</label>
            <helpText>When the AI (GPT-3.5) calls functions, it usually takes between 5 to 15 seconds to respond. Simple questions that do not require function calls are answered by the AI on good days in around a second. However, since most functions in this plugin involve function calls, the longer response time occurs more frequently.</helpText>
            <helpText lang="de-DE">Wenn die KI (GPT-3.5) Funktionen aufruft, benötigt sie in der Regel 5 bis 15 Sekunden für eine Antwort. Einfache Fragen, die keine Funktionsaufrufe erfordern, beantwortet die KI an besonders guten Tagen innerhalb von 1 - 2 Sekunden. Da jedoch die meisten Funktionen in diesem Plugin Funktionsaufrufe beinhalten, tritt die längere Antwortzeit häufiger zu.</helpText>
        </input-field>
    </card>

    <card>
        <title>Reduce and Optimize Content Generated for the AI</title>
        <title lang="de-DE">Generierte Inhalte für die KI reduzieren und optimieren</title>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistCategory</name>
            <entity>category</entity>
            <label>Specify categories that should be excluded from generated responses of functions executed by the AI.</label>
            <label lang="de-DE">Bestimmen Sie Kategorien, die von generierten Antworten der von der KI ausgeführten Funktionen ausgeschlossen werden sollen.</label>
            <helpText lang="en">By default, the AI retrieves categories through functions only where products are available for purchase. However, many shops have extensive category lists that the AI can find challenging to process. By selectively excluding categories deemed irrelevant, you can significantly enhance the efficiency and performance of the chatbot.</helpText>
            <helpText lang="de-DE">Standardmäßig bekommt die KI über Funktionen nur Kategorien zurück, in denen Produkte zum Kauf verfügbar sind. Viele Shops verfügen jedoch über umfangreiche Kategorienlisten, die von der KI schwer bis gar nicht verarbeitet werden können. Durch das gezielte Ausschließen als irrelevant erachteter Kategorien können Sie die Effizienz und Leistungsfähigkeit des Chatbots deutlich verbessern.</helpText>
        </component>

        <input-field type="int">
            <name>openAiFilterCategoryLevel</name>
            <step>1</step>
            <min>0</min>
            <max>50</max>
            <defaultValue>0</defaultValue>
            <label>Define the starting level for category breadcrumbs (e.g., 1 for a structure like 'Catalogue #1').</label>
            <label lang="de-DE">Legen Sie die Startebene für die Brotkrumen-Navigation fest (z.B. 1 für eine Struktur wie 'Katalog Nr. 1').</label>
            <helpText>The breadcrumb navigation for categories beyond level 5 will be approximately halved to simplify the structure.</helpText>
            <helpText lang="de-DE">Die Brotkrumen-Navigation für Kategorien über Ebene 5 wird zur Vereinfachung der Struktur etwa halbiert.</helpText>
        </input-field>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistManufacturer</name>
            <entity>product_manufacturer</entity>
            <label>Specify manufacturers that should be ignored in the functions executed by the AI, to ensure that these manufacturers are not considered in the generated content.</label>
            <label lang="de-DE">Bestimmen Sie Hersteller, die in den von der KI ausgeführten Funktionen ignoriert werden sollen, um sicherzustellen, dass diese Hersteller nicht in den generierten Inhalten aufgelistet werden.</label>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>openAiFilterAllowedProperties</name>
            <entity>property_group</entity>
            <label>Set property groups that the chatbot should be allowed to access and process. If none is selected, the chatbot will access all property groups.</label>
            <label lang="de-DE">Bestimmen Sie Eigenschaftsgruppen, auf die der Chatbot zugreifen und die er verarbeiten darf. Wenn keine ausgewählt sind, hat der Chatbot Zugriff auf alle Eigenschaftsgruppen.</label>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistPropertyOptions</name>
            <entity>property_group_option</entity>
            <label>Specify properties that should be ignored in the functions executed by the AI.</label>
            <label lang="de-DE">Bestimmen Sie Eigenschaften, die in den von der KI ausgeführten Funktionen ignoriert werden sollen.</label>
        </component>

        <input-field type="int">
            <name>openAiFilterMaxOptions</name>
            <step>1</step>
            <min>1</min>
            <max>250</max>
            <defaultValue>20</defaultValue>
            <label>Set the maximum number of properties per property group that the chatbot should process. This includes manufacturers.</label>
            <label lang="de-DE">Legen Sie die maximale Anzahl an Eigenschaften pro Eigenschaftsgruppe fest, die der Chatbot verarbeiten soll. Dies schließt Hersteller ein.</label>
            <helpText>To check which properties the chatbot is aware of, simply ask: "What product properties are there?"</helpText>
            <helpText lang="de-DE">Um zu überprüfen, welche Eigenschaften der Chatbot kennt, fragen Sie einfach: „Welche Produkteigenschaften gibt es?"</helpText>
        </input-field>
    </card>
</config>
