<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatLogService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction as BaseRequiredAction;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Framework\Routing\Router;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Psr\Log\LoggerInterface;
use const DATE_RFC850;
use const PHP_EOL;
use const SORT_REGULAR;
use function array_merge;
use function array_values;
use function crc32;
use function date;
use function date_default_timezone_set;
use function implode;
use function in_array;
use function ini_get;
use function is_array;
use function is_string;
use function json_decode;
use function json_encode;
use function ksort;
use function sleep;
use function strpos;
use function time;

class OpenAIService
{
    private ?string $assistantId = null;

    private ?string $threadId = null;

    private ?array $actionRequired = null;

    private array $trackError = [];

    private array $trackActions = [];

    private array $uiActionRequired = [];

    private array $runCompletedResponse = [];

    private ?float $requestStartTime = null;

    public function __construct(
        private SystemConfigService $configService,
        private HttpClientInterface $httpClient,
        private Router $router,
        private OpenAIConfigAdapter $aiConfig,
        private OpenAIClientAdapter $aiClientService,
        private TranslatorInterface $translator,
        private LoggerInterface $logger,
        private OpenAIRequiredActionAdapter $requiredActionAdapter,
        private ?OpenAIChatService $baseChatService = null,
        private ?BaseRequiredAction $baseRequiredAction = null,
        private ?OpenAIChatLogService $baseChatLogService = null
    ) {
        if ($setTz = ini_get('date.timezone')) {
            date_default_timezone_set($setTz);
        }

        $this->assistantId = $this->aiConfig->getAssistantId();
    }

    public function setAiConfigSalesChannelContext(SalesChannelContext $salesChannelContext, ?string $cmsAssistantId = null): self
    {
        $this->aiConfig->setSalesChannelContext($salesChannelContext, $cmsAssistantId);
        $this->assistantId = $cmsAssistantId ?? $this->aiConfig->getAssistantId();
        $this->logger->debug('OpenAIService: Set sales channel context and assistantId', [
            'assistantId' => $this->assistantId,
            'salesChannelId' => $salesChannelContext->getSalesChannelId()
        ]);

        return $this;
    }

    public function getClientService(): OpenAIClientAdapter
    {
        return $this->aiClientService;
    }

    public function getClientConfig(): OpenAIConfigAdapter
    {
        return $this->aiConfig;
    }

    public function isEnabled($userInGroupId): bool
    {
        $isEnabled = $this->aiConfig->isEnabled($userInGroupId);
        $this->logger->debug('OpenAIService: Checking if plugin is enabled', [
            'isEnabled' => $isEnabled,
            'userInGroupId' => $userInGroupId
        ]);
        return $isEnabled;
    }

    public function disablePlugin(SalesChannelContext $salesContext): void
    {
        $this->aiConfig->disablePlugin($salesContext->getSalesChannelId());
    }

    public function getStandardDate(): string|false
    {
        return date(DATE_RFC850);
    }

    public function requiredAction($run): array
    {
        if ($toolCalls = ($run['required_action']['submit_tool_outputs']['tool_calls'] ?? null)) {
            $this->logger->debug('OpenAIService: Handling required action', [
                'toolCalls' => count($toolCalls)
            ]);

            // Always try to use the base plugin's required action handler first if available
            if ($this->baseRequiredAction) {
                try {
                    // Configure the base plugin's required action handler with the necessary settings
                    $this->logger->debug('OpenAIService: Configuring base plugin required action handler');

                    // Get the storefront URL and access key from the config adapter
                    $storefrontUrl = $this->aiConfig->getStorefrontUrl();
                    $accessKey = $this->aiConfig->getAccessKey();
                    $salesChannelId = $this->aiConfig->getSalesChannelId();
                    $context = null;
                    $salesChannelContext = null;

                    if (!$storefrontUrl || !$accessKey) {
                        throw new \Exception('Storefront URL or access key is not set');
                    }

                    $this->logger->debug('OpenAIService: Setting base plugin required action handler configs', [
                        'storefrontUrl' => $storefrontUrl,
                        'accessKey' => $accessKey,
                        'salesChannelId' => $salesChannelId
                    ]);

                    // Set the necessary configurations on the base plugin's required action handler
                    $this->baseRequiredAction->setSwConfigs(
                        $storefrontUrl,
                        $accessKey,
                        $this->aiConfig->getCurrency(),
                        $salesChannelId,
                        $context,
                        $salesChannelContext
                    );

                    // Log the current configuration for debugging
                    $this->logger->debug('OpenAIService: Base plugin required action handler configuration', [
                        'storefrontUrl' => $storefrontUrl,
                        'accessKey' => $accessKey ? 'set' : 'not set',
                        'salesChannelId' => $salesChannelId
                    ]);

                    $this->logger->debug('OpenAIService: Using base plugin required action handler');
                    $result = $this->baseRequiredAction->requiredAction($toolCalls);

                    // Log the result for debugging
                    $this->logger->debug('OpenAIService: Base plugin required action handler result', [
                        'result' => $result
                    ]);

                    // Check if there are any store API errors
                    $storeApiErrors = $this->baseRequiredAction->getStoreApiError();
                    if ($storeApiErrors) {
                        $this->logger->error('OpenAIService: Base plugin required action handler store API errors', [
                            'storeApiErrors' => $storeApiErrors
                        ]);
                    }

                    return $result;
                } catch (\Exception $e) {
                    $this->logger->error('OpenAIService: Failed to use base plugin required action handler', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // Fall back to our adapter
                }
            }

            // Fall back to our adapter if the base plugin handler fails or is not available
            $this->logger->debug('OpenAIService: Falling back to our adapter');
            $exec = $this->requiredActionAdapter->requiredAction($toolCalls);

            if ($this->requiredActionAdapter->getStoreApiError()) {
                $this->trackError['storeApiError'] = array_merge(
                    $this->trackError['storeApiError'] ?? [],
                    $this->requiredActionAdapter->getStoreApiError()
                );

                $this->logger->error('OpenAIService: Our adapter store API errors', [
                    'storeApiErrors' => $this->requiredActionAdapter->getStoreApiError()
                ]);
            }

            return $exec;
        }

        return [];
    }

    /** clean up response output */
    private function cleanUpThreadMessages($messages): array
    {
        $r = ['response' => [], 'log' => []];
        if ($messages['data'] ?? null) {
            $break = false;
            foreach ($messages['data'] as $i => $message) {
                $role = $message['role'] ?? null;
                if (!$role || empty($message['content'])) continue;

                // Get the message content
                $messageContent = $message['content'][0]['text']['value'] ?? null;
                $annotations = $message['content'][0]['text']['annotations'] ?? null;

                // Add to response if it's an assistant message and we haven't broken yet
                if ('assistant' === $role && !$break) {
                    $r['response'][$message['created_at'] . $i] = [
                        'value' => $messageContent,
                        'id' => $message['id'],
                        'role' => $role,
                        'datetime' => date(DATE_RFC850, $message['created_at']),
                        'annotations' => $annotations,
                    ];
                }

                // If it's a user message and we haven't broken yet, break after this message
                if ('user' === $role && !$break) {
                    $break = true;
                }

                // Always log all messages
                $r['log'][] = [
                    'value' => $messageContent,
                    'id' => $message['id'],
                    'role' => $message['role'],
                    'datetime' => date(DATE_RFC850, $message['created_at']),
                    'annotations' => $annotations,
                ];
            }

            // Sort and reindex the response array
            ksort($r['response'], SORT_REGULAR);
            $r['response'] = array_values($r['response']);
        } else {
            $r['response'][] = ['role' => 'assistant', 'value' => 'error'];
        }

        if ($this->trackError) {
            $r = [
                ['value' => $this->trackError['message'] ?? json_encode($this->trackError)],
                ['status' => 'error', 'history' => $r]
            ];
        }

        return $r;
    }

    /** wait recursively until the OpenAI run is completed */
    private function waitUntilCompleted($request, $i = 0): int
    {
        if ($request['runId'] ?? null) {
            $checkRunStatus = $this->aiClientService->checkRunStatus($request['runId']);
            $status = $checkRunStatus['status'] ?? null;
            if (in_array($status, ['failed', 'cancelled', 'expired', 'incomplete'])) {
                $this->trackError = [
                    'message' => "Request {$status}",
                    'full_status' => $checkRunStatus,
                ];
                return $i;
            } else if ('requires_action' === $status) {
                $this->actionRequired = $checkRunStatus;
                return $i;
            } else if ($checkRunStatus['last_error'] ?? null) {
                $this->trackError = $checkRunStatus['last_error'];
                $this->trackError['full_status'] = $checkRunStatus;
                return $i;
            } else if ('completed' !== $status and $this->aiConfig->getLoopUntilCompleted() > $i) {
                sleep($this->aiConfig->getLoopUntilCompletedSeconds());
                return $this->waitUntilCompleted($request, ++$i);
            } else if ('completed' === $status) {
                $this->runCompletedResponse = $checkRunStatus;
            }
        } else {
            $this->trackError = ['message' => 'missing runId'];
        }

        return $i;
    }

    /** Process tool outputs from required actions */
    private function filterToolOutputResponse(array $actionResponse): array
    {
        $r = [];
        $actions = [];

        foreach ($actionResponse as $data) {
            $output = $data['output'];

            if (is_string($output)) {
                $output = json_decode($output, true);

                if (is_array($output) and ($output['exec'] ?? null)) {
                    if ('product_search' === $output['exec'] and !isset($actions['product_search'])) {
                        $data['output'] = json_encode(array_values($output['items']), OpenAIRequiredAction::FEL_PRETTY_PRINT);
                        $actions['product_search'] = $output;
                    }
                    if ('go_to_url' === $output['exec'] and !isset($actions['redirectTo'])) {
                        $actions['redirectTo'] = $output['url'];
                    }
                    if ('get_product_details' === $output['exec']) {
                        $data['output'] = json_encode($output['product'], OpenAIRequiredAction::FEL_PRETTY_PRINT);
                        $actions['product'] = $output;
                    }
                    if ('get_meta_information' === $output['exec']) {
                        $actions['task'] = 'remove.output';
                        $data['output'] = $output['response'];
                    }
                    if ('ui_action' === $output['exec']) {
                        $actions['uiAction'] = $output;
                    }
                }
            }

            $r[] = $data;
        }

        return [
            'output' => $r,
            'actions' => $actions,
        ];
    }

    /** OpenAI can call multiple tools at once or separated by multiple requests. To handle all accurately, we have to handle them recursively */
    private function handleRequiredActions($waited, $post, $request, $salesContext): int
    {
        $tmpActReq = $this->actionRequired;

        if ($ar = $this->requiredAction($tmpActReq, $post)) {
            $filterOutput = $this->filterToolOutputResponse($ar);

            if ($filterOutput['actions'] ?? null) {
                if ($this->uiActionRequired) {
                    $this->uiActionRequired['addActions'][] = $filterOutput['actions'];
                } else {
                    $this->uiActionRequired = $filterOutput['actions'];
                }
                if ('remove.output' === ($filterOutput['actions']['task'] ?? null)) {
                    $trackActionCleanResponse = ['Response removed'];
                }
            }

            $this->trackActions[] = [
                'required_action' => $tmpActReq['required_action'] ?? null,
                'response' => $trackActionCleanResponse ?? $filterOutput['output'],
            ];

            $this->actionRequired = null;

            // Format the tool outputs as expected by the OpenAI API
            $toolOutputs = [];
            foreach ($ar as $toolOutput) {
                $toolOutputs[] = [
                    'tool_call_id' => $toolOutput['tool_call_id'],
                    'output' => is_string($toolOutput['output']) ? $toolOutput['output'] : json_encode($toolOutput['output'])
                ];
            }

            $this->logger->debug('OpenAIService: Submitting tool outputs', [
                'threadId' => $tmpActReq['thread_id'],
                'runId' => $tmpActReq['id'],
                'toolOutputs' => $toolOutputs
            ]);

            $this->aiClientService->submitToolOutputs($tmpActReq['thread_id'], $tmpActReq['id'], $toolOutputs);

            $waited += $this->waitUntilCompleted($post);

            if ($this->actionRequired) {
                return $this->handleRequiredActions($waited, $post, $request, $salesContext);
            }
        }

        return $waited;
    }

    /** search in the AI-response, which products the AI has used in its final HTML-response while also keeping the index the AI has used */
    public function getUsedProducts(array $items, string $aiResponse): array
    {
        $r = [];
        $this->logger->debug('OpenAIService: Searching for products in AI response', [
            'itemCount' => count($items),
            'responseLength' => strlen($aiResponse)
        ]);

        // If the AI response contains a product list, always return all products
        if (strpos($aiResponse, 'Produkte in der Kategorie') !== false ||
            strpos($aiResponse, 'Suchergebnisse für') !== false ||
            strpos($aiResponse, '<h2>Produkte') !== false ||
            strpos($aiResponse, '<h3>Produkte') !== false ||
            (strpos($aiResponse, '<ul>') !== false && strpos($aiResponse, '<li>') !== false)) {
            $this->logger->debug('OpenAIService: Found product list in AI response, returning all products');
            return $items;
        }

        // Otherwise, search for product IDs or names in the response
        foreach ($items as $data) {
            // Search for product ID
            if ($pos = strpos($aiResponse, $data['id'])) {
                $r[$pos] = $data;
                continue;
            }

            // Search for product name
            $name = $data['translated']['name'] ?? $data['name'] ?? '';
            if ($name && ($pos = strpos($aiResponse, $name))) {
                $r[$pos] = $data;
            }
        }

        ksort($r);
        return array_values($r);
    }

    /** create thread handler */
    public function handleCreateThread($post): array
    {
        $userInput = $post['userInput'] ?? 'Hello';

        // Handle empty messages
        if (empty(trim($userInput))) {
            $userInput = 'Hello';
        }

        $this->logger->debug('OpenAIService: Handling create thread', [
            'assistantId' => $this->assistantId,
            'hasThreadId' => isset($post['threadId']),
            'cmsCategoryId' => $post['cmsCategoryId'] ?? null
        ]);

        if ($post['threadId'] ?? null) {
            $this->threadId = $post['threadId'];

            // Set the thread ID on the client service
            $this->aiClientService->setThreadId($this->threadId);
            $this->logger->debug('OpenAIService: Set thread ID on client service', [
                'threadId' => $this->threadId
            ]);

            $run = $this->aiClientService->addMessageAndRun(
                $this->assistantId,
                $this->threadId,
                'user',
                $userInput
            );
        } else {
            // Create a new thread with the first message
            $run = $this->aiClientService->createThreadRun(
                $this->assistantId,
                'user',
                $userInput,
                $post['cmsCategoryId'] ?? null
            );

            // Get the thread ID from the response and set it
            if ($threadId = ($run['thread_id'] ?? null)) {
                $this->threadId = $threadId;
                $this->aiClientService->setThreadId($this->threadId);
                $this->logger->debug('OpenAIService: Set new thread ID on client service', [
                    'threadId' => $this->threadId
                ]);
            }
        }

        $runId = $run['id'] ?? null;
        $threadId = $run['thread_id'] ?? null;

        if ($this->trackError['storeApiError'] ?? null) {
            $run['exception'] = implode(PHP_EOL, $this->trackError['storeApiError']);
        }

        return [
            'id' => 'chat-' . crc32($threadId . $runId . time()),
            'datetime' => $this->getStandardDate(),
            'runId' => $runId,
            'threadId' => $threadId,
            'error' => isset($run['exception']) ? $run : null,
        ];
    }


    /** default request handler */
    public function handleRequest($post, Request $request, SalesChannelContext $salesContext, Context $context): array|bool|RedirectResponse
    {
        // Set the request start time for tracking request duration
        $this->requestStartTime = microtime(true);

        $this->threadId = $post['threadId'] ?? null;
        $userInput = $post['userInput'] ?? '';
        $runId = $post['runId'] ?? null;

        // Log the received post data
        $this->logger->debug('OpenAIService: Received post data', [
            'post' => $post
        ]);

        // Handle empty messages - this is a special case for the run request after create-thread
        // In this case, we don't need to send another message because the first message was already sent
        // in the create-thread request
        $isEmptyInput = empty(trim($userInput));

        $this->logger->debug('OpenAIService: Handling request', [
            'assistantId' => $this->assistantId,
            'threadId' => $this->threadId,
            'runId' => $runId,
            'isEmptyInput' => $isEmptyInput,
            'requestStartTime' => $this->requestStartTime,
            'userInput' => $userInput
        ]);

        if (!$this->threadId) {
            $this->logger->error('OpenAIService: No thread ID provided');
            return [
                'error' => ['exception' => 'No thread ID provided']
            ];
        }

        // Set the thread ID on the client service
        $this->aiClientService->setThreadId($this->threadId);
        $this->logger->debug('OpenAIService: Set thread ID on client service', [
            'threadId' => $this->threadId
        ]);

        // If we have a runId, check if the run is still in progress
        if ($runId) {
            try {
                $runStatus = $this->aiClientService->checkRunStatus($runId);
                $status = $runStatus['status'] ?? null;

                $this->logger->debug('OpenAIService: Checking run status', [
                    'runId' => $runId,
                    'status' => $status
                ]);

                // If the run is completed, failed, or cancelled, we can create a new run
                if (in_array($status, ['completed', 'failed', 'cancelled'])) {
                    $runId = null; // Reset runId to create a new run
                }
            } catch (\Exception $e) {
                $this->logger->error('OpenAIService: Failed to check run status', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // If we can't check the run status, assume it's completed
                $runId = null; // Reset runId to create a new run
            }
        }

        // If we don't have a runId and we have user input, create a new run
        if (!$runId && !$isEmptyInput) {
            try {
                // Add the user message to the thread
                $this->logger->debug('OpenAIService: Adding user message to thread', [
                    'userInput' => $userInput
                ]);

                $run = $this->aiClientService->addMessageAndRun(
                    $this->assistantId,
                    $this->threadId,
                    'user',
                    $userInput
                );

                // Update the post with the new runId
                $post['runId'] = $run['id'] ?? null;
                $runId = $run['id'] ?? null;

                $this->logger->debug('OpenAIService: Created new run', [
                    'runId' => $runId
                ]);
            } catch (\Exception $e) {
                $this->logger->error('OpenAIService: Failed to add message and create run', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        // If we have a runId, we need to check its status
        if ($runId) {
            // Wait for the run to complete
            $waited = $this->waitUntilCompleted($post);

            if ($this->actionRequired) {
                $waited = $this->handleRequiredActions($waited, $post, $request, $salesContext);
            }
        } else {
            // If we don't have a runId, we just check the thread messages
            $waited = 0;
        }

        $messages = $this->aiClientService->getThreadMessages();
        $cleanMessages = $this->cleanUpThreadMessages($messages);

        if ($this->trackError['storeApiError'] ?? null) {
            $messages['exception'] = implode(PHP_EOL, $this->trackError['storeApiError']);
            $cleanMessages['log'][] = ['role' => 'assistant', 'value' => $messages['exception']];
        }

        // Log the conversation if logging is enabled
        if ($this->threadId && $this->baseChatLogService) {
            try {
                // Fetch all thread messages to ensure we have the complete conversation
                // Use the raw getThreadMessages method from the client service
                // This will return the messages in the same format as the backend chatbot
                $allMessages = $this->aiClientService->getThreadMessages($this->threadId);

                // Make sure we have at least 2 messages
                if (!isset($allMessages['data']) || count($allMessages['data']) < 2) {
                    // Not enough messages to log, skip
                    return $cleanMessages;
                }

                // Sort messages by created_at timestamp in ascending order (oldest first)
                if (isset($allMessages['data']) && !empty($allMessages['data'])) {
                    // Create a copy of the data array to sort
                    $sortedMessages = $allMessages['data'];

                    // Sort by created_at timestamp (ascending order)
                    usort($sortedMessages, function($a, $b) {
                        return ($a['created_at'] ?? 0) <=> ($b['created_at'] ?? 0);
                    });

                    // Replace the original data with the sorted data
                    $allMessages['data'] = $sortedMessages;

                    // Get the last two messages (user and assistant) for logging
                    // This ensures we only log the latest conversation
                    $lastUserIndex = null;
                    $lastAssistantIndex = null;

                    // Find the last user and assistant messages
                    for ($i = count($allMessages['data']) - 1; $i >= 0; $i--) {
                        $message = $allMessages['data'][$i];
                        if (($message['role'] ?? '') === 'user' && $lastUserIndex === null) {
                            $lastUserIndex = $i;
                        } else if (($message['role'] ?? '') === 'assistant' && $lastAssistantIndex === null) {
                            $lastAssistantIndex = $i;
                        }

                        // If we found both, we can stop searching
                        if ($lastUserIndex !== null && $lastAssistantIndex !== null) {
                            break;
                        }
                    }

                    // Create a new array with just the last user and assistant messages
                    $lastMessages = [];

                    // Add the last user message if found
                    if ($lastUserIndex !== null) {
                        $lastMessages[] = $allMessages['data'][$lastUserIndex];
                    }

                    // Add the last assistant message if found
                    if ($lastAssistantIndex !== null) {
                        $lastMessages[] = $allMessages['data'][$lastAssistantIndex];
                    }

                    // Sort the last messages by created_at
                    usort($lastMessages, function($a, $b) {
                        return ($a['created_at'] ?? 0) <=> ($b['created_at'] ?? 0);
                    });
                }

                // Use the last messages array for last_thread
                // This ensures we only log the latest conversation
                $allMessages['last_thread'] = $lastMessages;

                // Add tracking information to the last message
                if (isset($allMessages['last_thread']) && count($allMessages['last_thread']) > 0) {
                    $lastIndex = count($allMessages['last_thread']) - 1;
                    if ($allMessages['last_thread'][$lastIndex]['role'] === 'assistant') {
                        $allMessages['last_thread'][$lastIndex]['trackRequest'] = [
                            ['request_took' => microtime(true) - ($this->requestStartTime ?? microtime(true))]
                        ];
                    }
                }

                $this->logger->debug('OpenAIService: Logging to base plugin', [
                    'threadId' => $this->threadId,
                    'assistantId' => $this->assistantId,
                    'messageCount' => count($allMessages['data']),
                    'lastThreadCount' => count($allMessages['last_thread'] ?? [])
                ]);

                // Call the base plugin's log method with the raw messages
                $this->baseChatLogService->log(
                    $salesContext->getSalesChannel()->getId(),
                    $this->assistantId,
                    $this->threadId,
                    $allMessages,
                    $context
                );
            } catch (\Exception $e) {
                $this->logger->error('OpenAIService: Failed to log conversation: ' . $e->getMessage());
            }
        }

        return [
            'id' => 'chat-' . crc32($this->threadId . time()),
            'datetime' => $this->getStandardDate(),
            'waitCycle' => $waited,
            'threadId' => $this->threadId,
            'threadMessages' => $cleanMessages['response'] ?? [],
            'error' => isset($messages['exception']) ? $messages : null,
            'trackError' => $this->trackError,
            'uiActionRequired' => $this->uiActionRequired,
            '_trackActions' => $this->trackActions,
        ];
    }

    /** delete current thread */
    public function deleteThread(Context $context, string $threadId): array
    {
        $this->logger->debug('OpenAIService: Deleting thread', [
            'threadId' => $threadId
        ]);

        $deleteThread = $this->aiClientService->deleteThread($threadId);

        try {
            if (($deleteThread['deleted'] ?? null) && $this->baseChatLogService) {
                $this->logger->debug('OpenAIService: Marking thread as deleted in base plugin', [
                    'threadId' => $threadId
                ]);
                $this->baseChatLogService->setThreadDeleted($threadId, $context);
            }
        } catch (\Exception $e) {
            $this->logger->error('OpenAIService: Failed to log thread deletion', [
                'error' => $e->getMessage(),
                'threadId' => $threadId
            ]);
        }

        return $deleteThread;
    }
}
