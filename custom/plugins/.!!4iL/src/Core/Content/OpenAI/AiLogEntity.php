<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Core\Content\OpenAI;

use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityIdTrait;

class AiLogEntity extends Entity
{
    use EntityIdTrait;

    protected string $salesChannelID;

    protected string $assistantID;

    protected string $threadID;

    protected bool $threadDeleted;

    protected string $threadMessages;

    protected string $additionalData;

    protected string $errors;

    public function getSalesChannelID(): ?string
    {
        return $this->salesChannelID;
    }

    public function setSalesChannelID($salesChannelID): static
    {
        $this->salesChannelID = $salesChannelID;

        return $this;
    }

    public function getThreadID(): string
    {
        return $this->threadID;
    }

    public function setThreadID($threadID): static
    {
        $this->threadID = $threadID;

        return $this;
    }

    public function getThreadMessages(): ?string
    {
        return $this->threadMessages;
    }

    public function setThreadMessages($threadMessages): static
    {
        $this->threadMessages = $threadMessages;

        return $this;
    }

    public function getAdditionalData(): ?string
    {
        return $this->additionalData;
    }

    public function setAdditionalData($additionalData): static
    {
        $this->additionalData = $additionalData;

        return $this;
    }

    public function getErrors(): ?string
    {
        return $this->errors;
    }

    public function setErrors($errors): static
    {
        $this->errors = $errors;

        return $this;
    }

    public function getAssistantID(): ?string
    {
        return $this->assistantID;
    }

    public function setAssistantID($assistantID): static
    {
        $this->assistantID = $assistantID;

        return $this;
    }

    public function getThreadDeleted(): ?bool
    {
        return $this->threadDeleted;
    }

    public function setThreadDeleted($threadDeleted): static
    {
        $this->threadDeleted = $threadDeleted;

        return $this;
    }

}
