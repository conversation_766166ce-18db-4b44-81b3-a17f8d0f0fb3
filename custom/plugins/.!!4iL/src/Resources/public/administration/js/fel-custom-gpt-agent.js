!function(){var e={12:function(){},993:function(){},766:function(){},352:function(){},747:function(){},249:function(){let e=Shopware.Classes.ApiService,{Application:t}=Shopware;class s extends e{constructor(e,t,s="fel-api-test"){super(e,t,s)}check(t){let s=this.getBasicHeaders({});return this.httpClient.post(`_action/${this.getApiBasePath()}/verify`,t,{headers:s}).then(t=>e.handleResponse(t))}}t.addServiceProvider("felApiTest",e=>new s(t.getContainer("init").httpClient,e.loginService))},329:function(e,t,s){var n=s(12);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),(0,s(346).Z)("2253e04e",n,!0,{})},817:function(e,t,s){var n=s(993);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),(0,s(346).Z)("c04e51b4",n,!0,{})},717:function(e,t,s){var n=s(766);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),(0,s(346).Z)("a8eaebca",n,!0,{})},410:function(e,t,s){var n=s(352);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),(0,s(346).Z)("4b71e399",n,!0,{})},0:function(e,t,s){var n=s(747);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),(0,s(346).Z)("63f0f10e",n,!0,{})},346:function(e,t,s){"use strict";function n(e,t){for(var s=[],n={},i=0;i<t.length;i++){var a=t[i],l=a[0],o={id:e+":"+i,css:a[1],media:a[2],sourceMap:a[3]};n[l]?n[l].parts.push(o):s.push(n[l]={id:l,parts:[o]})}return s}s.d(t,{Z:function(){return h}});var i,a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var l={},o=a&&(document.head||document.getElementsByTagName("head")[0]),r=null,c=0,d=!1,p=function(){},f=null,u="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e,t,s,i){d=s,f=i||{};var a=n(e,t);return g(a),function(t){for(var s=[],i=0;i<a.length;i++){var o=l[a[i].id];o.refs--,s.push(o)}t?g(a=n(e,t)):a=[];for(var i=0;i<s.length;i++){var o=s[i];if(0===o.refs){for(var r=0;r<o.parts.length;r++)o.parts[r]();delete l[o.id]}}}}function g(e){for(var t=0;t<e.length;t++){var s=e[t],n=l[s.id];if(n){n.refs++;for(var i=0;i<n.parts.length;i++)n.parts[i](s.parts[i]);for(;i<s.parts.length;i++)n.parts.push(b(s.parts[i]));n.parts.length>s.parts.length&&(n.parts.length=s.parts.length)}else{for(var a=[],i=0;i<s.parts.length;i++)a.push(b(s.parts[i]));l[s.id]={id:s.id,refs:1,parts:a}}}}function v(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function b(e){var t,s,n=document.querySelector("style["+u+'~="'+e.id+'"]');if(n){if(d)return p;n.parentNode.removeChild(n)}if(m){var i=c++;t=_.bind(null,n=r||(r=v()),i,!1),s=_.bind(null,n,i,!0)}else t=y.bind(null,n=v()),s=function(){n.parentNode.removeChild(n)};return t(e),function(n){n?(n.css!==e.css||n.media!==e.media||n.sourceMap!==e.sourceMap)&&t(e=n):s()}}var w=(i=[],function(e,t){return i[e]=t,i.filter(Boolean).join("\n")});function _(e,t,s,n){var i=s?"":n.css;if(e.styleSheet)e.styleSheet.cssText=w(t,i);else{var a=document.createTextNode(i),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(a,l[t]):e.appendChild(a)}}function y(e,t){var s=t.css,n=t.media,i=t.sourceMap;if(n&&e.setAttribute("media",n),f.ssrId&&e.setAttribute(u,t.id),i&&(s+="\n/*# sourceURL="+i.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),e.styleSheet)e.styleSheet.cssText=s;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(s))}}},38:function(){let{Mixin:e}=Shopware;e.register("fel-export-to-file",{data(){return{jsonTextIndentation:2,exportToContentType:{json:"application/json;charset=utf-8",csv:"text/csv;charset=utf-8",txt:"text/plain;charset=utf-8"}}},methods:{setJsonTextIndentation(e){this.jsonTextIndentation=e},formatToJSON(e){return JSON.stringify(e,null,this.jsonTextIndentation)},formatToCSV(e,t){"messagesTotal"!==t[5]&&t.splice(5,0,"messagesTotal");let s=[t];for(let n=0;n<e.length;n++){let i=e[n];if(void 0!==i.id){let e=[];for(let s=0;s<t.length;s++){let n=t[s],a=i[n];if("messagesTotal"===n)a=i.threadMessages.length;else if(["createdAt","updatedAt"].includes(n))a=`"${a}"`;else if("errors"===n)a=JSON.stringify(a);else if("threadMessages"===n){let e=[];for(let t=0;t<a.length;t++){let s=a[t];if(void 0!==s.value){let t="assistant"===s.role?"\n":"";e.push(`${s.role}: ${s.value}${t}`)}}a=JSON.stringify(e.join("\n"))}e.push(a)}s.push(e.join(", "))}}return s.join("\n")},exportToFile(e,t,s,n){try{let[a]=new Date().toISOString().split("."),[l]=e.split(".");if(e.endsWith(".json")){n="json";var i=this.formatToJSON(t)}else if(e.endsWith(".csv")){n="csv";var i=this.formatToCSV(t,s)}else if(e.endsWith(".txt")){n="txt";var i=t.trim()}void 0!==i&&i&&(e=(e=`${l}_${a}.${n}`).replaceAll(":","_"),this.dispatchDownloadEvent(e,this.exportToContentType[n],i))}catch(e){console.error(e)}},dispatchDownloadEvent(e,t,s){var n=document.createElement("a");if(void 0!==n.download){var i=URL.createObjectURL(new Blob([s],{type:t}));n.setAttribute("href",i),n.setAttribute("download",e),n.style.visibility="hidden",document.body.appendChild(n),n.dispatchEvent(new MouseEvent("click")),document.body.removeChild(n)}}}})},474:function(){let{Mixin:e}=Shopware;e.register("fel-helper-mixin",{methods:{felMultiBtn(e){var t=e.target,s=t.dataset;switch(qSelector=(all="true"==s.queryAll)?"querySelectorAll":"querySelector",s.action){case"click":var n=this.$el[qSelector](s.target);if(null!==n&&(n||void 0!==n.length)&&(all||(n=[n]),void 0!==n.length))for(let e=0;e<n.length;e++)n[e].dispatchEvent(new MouseEvent("click",{bubbles:!0}));break;case"toggle":var i=s.activeIf||"set",a=s.targetClass,l=s.selfClass,n=this.$el[qSelector](s.target);if(null===n&&(n=document[qSelector](s.target)),!all&&n&&(n=[n]),null!==n&&void 0!==n.length)for(let e=0;e<n.length;e++){n[e].classList.toggle(a);var o,r=n[e].classList.contains(a);l&&("not"===i?o=r?"remove":"add":"set"===i&&(o=r?"add":"remove"),o&&t.classList[o](...l.split("|")))}}}}})},893:function(){let{Component:e,Mixin:t}=Shopware;t.register("fel-input-helper-mixin",{data(){return{isCreated:!1,canCreate:!1,canUpdate:!1,isUpdated:!1,availableFunctionsObject:{},toSubmit:{"fel--sw--assistant_name":null,"fel--sw--assistant_tools":[],"fel--sw--assistant_files":[],"fel--sw--assistant_vector_store":[],"fel--sw--assistant_attached_files":[],"fel--sw--assistant_instruction":null,"fel--sw--assistant_model":null,"fel--sw--assistant_temperature":null},toSubmitRequired:["fel--sw--assistant_name","fel--sw--assistant_instruction","fel--sw--assistant_model","fel--sw--assistant_temperature"]}},methods:{checkIfRetrievalIsInTools(e){for(var t=0;t<e.length;t++)if("retrieval"===e[t].type)return!0;return!1},createOrUpdateAssistant(e){if(this.checkRequirements(),this.canCreate){e=e?`/${e}`:"";var t={name:this.toSubmit["fel--sw--assistant_name"],tools:[],instructions:this.toSubmit["fel--sw--assistant_instruction"],model:this.toSubmit["fel--sw--assistant_model"],temperature:this.toSubmit["fel--sw--assistant_temperature"],tool_resources:{file_search:{vector_store_ids:this.toSubmit["fel--sw--assistant_vector_store"]}}};if(this.toSubmit["fel--sw--assistant_vector_store"].length&&t.tools.push({type:"file_search"}),this.toSubmit["fel--sw--assistant_tools"].length)for(var s=this.availableFunctionsObject.list,n=0;n<this.toSubmit["fel--sw--assistant_tools"].length;n++)void 0!==s[this.toSubmit["fel--sw--assistant_tools"][n]]&&t.tools.push({type:"function",function:s[this.toSubmit["fel--sw--assistant_tools"][n]]});this.httpClientPost(`${this.pluginConfig.apiUrl}/assistants${e}`,t,e=>{this.canCreate=!1,this.canUpdate=!1,void 0!==e.data.id?(this.isCreated=e.data.id,this.isUpdated=!0,this.createNotificationSuccess({message:`${e.data.name}
${e.data.id}`})):void 0!==e.data&&this.createNotificationError({message:JSON.stringify(e.data)})})}},resetSelect(e){var t=document.querySelectorAll(`${e} option:checked`);if(t.length){for(let e=0;e<t.length;e++)t[e].selected=null;e=e.replace(/[\#|.]/g,""),this.toSubmit[e]=[],this.canUpdate=!0}},onAssistantInput(e){if(void 0!==e.target){var t=e.target.name,s=e.target.value;if(this.canUpdate=!0,"fel--sw--assistant_tools"===t){var n=document.body.querySelectorAll(`[name="${t}"]:checked`);s=[];for(var i=0;i<n.length;i++)s.push(n[i].value)}else if(void 0!==e.target.options&&20>=this.$el.querySelectorAll('select[name="fel--sw--assistant_files"] option:checked').length){s=[];for(var i=0;i<e.target.options.length;i++){var a=e.target.options[i];a.selected&&s.push(a.value)}}"fel--sw--assistant_temperature"===t&&(s=parseFloat(s)),this.toSubmit[t]=s,this.checkRequirements()}},checkRequirements(){this.canCreate=!0;for(var e=0;e<this.toSubmitRequired.length;e++){var t=this.toSubmitRequired[e],s=this.toSubmit[t];s&&"function"==typeof s.trim&&(s=s.trim()),""!=s&&null!=s&&s||(this.canCreate=!1)}this.modelError="gpt-3.5-turbo"===this.toSubmit["fel--sw--assistant_model"]}}})},779:function(){let{Component:e,Mixin:t}=Shopware;t.register("fel-openai-mixin",{data(){return{assistant:{},assistantId:null,assistantList:[],assistantListMap:[],aiModels:[],filesList:{},vectorStoreList:[],activeFilesList:{},modelError:!1,assistantTemperatureDefault:.3,allowedFileTypes:[".txt",".md",".json",".doc",".docx",".html",".pptx",".pdf"],availableModels:["gpt-3.5-turbo","gpt-4o","gpt-4-turbo"],availableFunctionsConfig:{sorting:["name-asc","name-desc","price-asc","price-desc","topseller"]}}},methods:{deleteFile(e){if(!0==!confirm(`${this.$t("fel-gpt-assistant.delete.delete")}?`))return!1;this.httpClientDelete(`${this.pluginConfig.apiUrl}/files/${e}`,e=>{void 0!==e.data&&e.data.deleted?(this.isDeleted=!0,this.fetchFiles()):this.createNotificationError({message:e.message||"error:deleteFile"})})},uploadFile(e,t=!1){if(void 0!==e.target.files&&void 0!==e.target.files[0]){var s=e.target.files[0];0===s.size?this.createNotificationError({message:this.$t("fel-gpt-assistant.files.vectorStore.fileIsEmpty")}):/^(\/?[a-z0-9A-Z.\-_]+)+$/.test(s.name)?(this.isLoading=!0,this.httpClient.post(`${this.pluginConfig.apiUrl}/files`,{purpose:"assistants",file:e.target.files[0]},{headers:{"Content-Type":"multipart/form-data",Authorization:this.pluginConfig.apiHeader.Authorization,"OpenAI-Beta":this.pluginConfig.openAiBetaVersion}}).then(e=>{void 0!==e.data&&(t||this.createNotificationSuccess({message:e.data.filename}),this.fetchFiles())}).catch(e=>{this.createNotificationError({message:e.message})}).finally(()=>{this.isLoading=!1,t&&window.location.reload()})):this.createNotificationError({message:this.$t("fel-gpt-assistant.files.vectorStore.filenameInvalid")})}},createVectorStore(e){if(void 0!==e.target){var t=e.target.querySelector('[name="fel-sw-create-vector-store"]');t&&void 0!==t.value&&(t.value?this.httpClient.post(`${this.pluginConfig.apiUrl}/vector_stores`,{name:t.value},{headers:{"Content-Type":"application/json",Authorization:this.pluginConfig.apiHeader.Authorization,"OpenAI-Beta":this.pluginConfig.openAiBetaVersion}}).then(e=>{void 0!==e.data&&void 0!==e.data.status&&"completed"===e.data.status&&this.fetchVectorStores()}).catch(e=>{this.createNotificationError({message:e.message})}).finally(()=>{t.value=""}):this.createNotificationError({message:this.$t("fel-gpt-assistant.files.vectorStore.setName")}))}},async fetchVectorStores(){await this.httpClientGet(`${this.pluginConfig.apiUrl}/vector_stores`,e=>{if(void 0!==e.data.data&&void 0!==e.data.data.length){for(var t=e.data.data,s=[],n=0;n<t.length;n++)t[n].name&&s.push(t[n]);this.vectorStoreList=s}})},async fetchFiles(){await this.httpClientGet(`${this.pluginConfig.apiUrl}/files`,e=>{if(e.data&&void 0!==e.data.data.length){for(var t=0;t<e.data.data.length;t++){var s=e.data.data[t];this.filesList[s.id]=s}this.filesList={data:this.filesList}}})},async fetchModels(){await this.httpClientGet(`${this.pluginConfig.apiUrl}/models`,e=>{if(void 0!==e.data&&void 0!==e.data.data){this.aiModels=e.data.data;for(var t=[],s=[],n=0;n<this.aiModels.length;n++)void 0!==this.aiModels[n].id&&s.push(this.aiModels[n].id);for(var i=0;i<this.availableModels.length;i++)s.includes(this.availableModels[i])?t.push(this.availableModels[i]):delete this.availableModels[i]}})},async fetchAssistants(){await this.httpClientGet(`${this.pluginConfig.apiUrl}/assistants`,e=>{this.assistantList=e.data;var t={};if(void 0!==this.assistantList.data){for(var s=this.assistantList.data,n={},i=0;i<s.length;i++){var a=s[i];if(n[a.id]={name:a.name,model:a.model,temperature:a.temperature},void 0!==a.tool_resources.file_search&&void 0!==a.tool_resources.file_search.vector_store_ids){t[a.id]=[];for(var l=0;l<a.tool_resources.file_search.vector_store_ids.length;l++){var o=a.tool_resources.file_search.vector_store_ids[l];t[a.id].includes(o)||t[a.id].push(o)}}}this.assistantListMap=n}return this.activeFilesList={vectorStores:t},this.fetchModels(),e.data})},availableFunctions(){return{defaults:["get_meta_information","get_product_properties","product_search"],list:{get_meta_information:{name:"get_meta_information",description:"Retrieve metadata about the shop, including contact details, opening times, FAQs, and other customizable information defined by the shop owner.",parameters:{type:"object",properties:{},required:[]}},get_product_properties:{name:"get_product_properties",description:"Retrieve available product properties, manufacturer names, and categories used by the shop.",parameters:{type:"object",properties:{categories:{type:"array",description:"An array of category names to filter and retrieve properties specific to these categories. Never use this option for the first call to this function; the first call should be made without categories to retrieve all necessary information. Subsequent calls can use this parameter as needed.",items:{type:"string"}}},required:[]}},product_search:{name:"product_search",description:"Perform a product search using the user-provided text query, filtering by categories, properties, price range, and sort order.",parameters:{type:"object",properties:{query:{type:"string",description:"The text query to search for. Ensure the query is singularized."},categories:{type:"array",description:"An array of category names to filter the search results by specific categories.",items:{type:"string"}},properties:{type:"array",description:"An array of property and manufacturer names to filter the search results based on the customer's query.",items:{type:"string"}},price_min:{type:"integer",description:"The minimum price."},price_max:{type:"integer",description:"The maximum price."},order:{type:"string",description:"The sort order of the search results.",enum:this.availableFunctionsConfig.sorting}},required:[]}},get_product_details:{name:"get_product_details",description:"Get product details",parameters:{type:"object",properties:{id:{type:"string",description:"The product ID."}},required:["id"]}},get_categories:{name:"get_categories",description:"Retrieve available categories with URLs, names and breadcrumbs as used by the shop.",parameters:{type:"object",properties:{},required:[]}},go_to_url:{name:"go_to_url",description:"Redirect to a URL. Redirect only if user explicitly asks for a redirection.",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to redirect to."}},required:["url"]}},get_date_time:{name:"get_date_time",description:"Get date, time and timezone.",parameters:{type:"object",properties:{},required:[]}},get_manufacturer:{name:"get_manufacturer",description:"Get manufacturer.",parameters:{type:"object",properties:{},required:[]}},get_countries:{name:"get_countries",description:"Get countries.",parameters:{type:"object",properties:{},required:[]}},get_delivery_times:{name:"get_delivery_times",description:"Get delivery times.",parameters:{type:"object",properties:{},required:[]}},get_payment_methods:{name:"get_payment_methods",description:"Get payment methods.",parameters:{type:"object",properties:{},required:[]}},get_chatbot_name:{name:"get_chatbot_name",description:"Get chatbot name.",parameters:{type:"object",properties:{},required:[]}}}}},defaultInstructions(){return this.flattenString(`
                You are a customer support chatbot in a Shopware web shop environment. Follow these guidelines to answer customer questions effectively.

                Rules
                1. Language: Respond only in the same language as the user query.
                2. HTML Responses: Format responses always using HTML elements like <h2>, <p>, <a>, <img>, <b>, and <ul>. Use a single <ul> for product listings and always include the 'url' of products to link them in the response.
                3. Clickable Content: Use always <a> elements for URLs, paths, emails, and phone numbers to ensure they are easy to read and clickable. Use <b> elements to highlight key names.
                4. Initial Data Retrieval: Call get_product_properties() once and as soon as possible to retrieve categories, properties, and manufacturer names and use its content for subsequent requests.
                5. Product Recommendations: For general advice or recommendations without specific product details, use retrieved data from get_product_properties() and tailor your advice.
                6. Property Names: Use always property and category names from get_product_properties() as arguments for product_search().
                7. No Invented Information: Never invent URLs or any information that might or could fit. Execute functions like get_product_properties() or get_meta_information().
                    - Only deliver URLs and individual information retrieved from function calls.
                    - For general questions such as contact details, opening times, terms and conditions, etc., call the function get_meta_information(), it contains contact details, opening times, FAQs, and other customizable information defined by the shop owner.
                    - Call get_meta_information() only once and use its content for subsequent requests.

                Search
                1. Initial Data Retrieval: If not requested yet, call get_product_properties() to get categories, properties, and manufacturer names as used by the shop.
                    - Call get_product_properties() only once and use its content for subsequent requests.
                2. User Query Analysis: Detect and differentiate between categories and properties in the user query.
                    - If the user query matches or contains a category, set it as the category for the search.
                    - Remove categories and properties from the query before processing.
                3. Search Term Refinement: Use natural language processing to singularize and refine search terms.
                4. Handling Large Results: If product_search() returns too many results, ask the user for more specific categories or properties to refine the search. Suggest existing options retrieved from get_product_properties().

                Functions
                - Adhere strictly to the descriptions of each function.
                - Expect JSON objects as return types from functions.

                Example Workflow
                1. Call get_product_properties() only once and use its content for subsequent requests.
                2. User Query: Analyze the query to identify categories, properties, and manufacturers.
                3. Product Search: Use the refined query and data from get_product_properties() for product_search().
                4. Categories: If the refined query matches or contains a category, set it also as categories argument for product_search().
                5. Response: Format the response using HTML and ensure all clickable content is properly linked with <a> elements.
                6. Language: Make sure your response is in the same language as the user query.

                Instructions end.

                Generic HTML Example for Chat Messages

                <div class="fel-oai-response">
                    <a href="[tel|mailto|null]:URL">LABEL</a>
                    <p>MESSAGE</p>
                    <ul>
                        <li>MESSAGES</li>
                    </ul>
                </div>
            `)},genericExamplesForSearchQueries(){return this.flattenString(`
                Function Call Example: get_product_properties()

                {
                    "properties": {
                        "GroupOfColors": [
                            "GoldPurple",
                            "RiverDark"
                        ],
                        "GroupOfSizes": [
                            "BigSize"
                        ],
                        "Manufacturer": [
                            "ManufacturerFive"
                        ]
                    },
                    "categories": [
                        {
                            "name": "ManBear",
                            "parent": [
                                "Sub Category"
                            ],
                            "url": "/navigation/aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                        }
                    ]
                }

                User Interaction Examples

                User: "I search for Paddles."
                Assistant: product_search(query: "Paddle")

                User: "I am looking for ManBear."
                Assistant: product_search(query: "ManBear", categories: ["ManBear"])

                User: "I search for a Pullover in GoldPurple."
                Assistant: product_search(query: "Pullover", properties: ["GoldPurple"])

                User: "I am looking for Pants in the category ManBear."
                Assistant: product_search(query: "Pant", categories: ["ManBear"])

                User: "I am searching for Hats in GoldPurple with a budget starting from 20 €."
                Assistant: product_search(query: "Hat", properties: ["GoldPurple"], price_min: 20)

                User: "I am searching for Hats in GoldPurple or RiverDark with a budget starting from 20 €."
                Assistant: product_search(query: "Hat", properties: ["GoldPurple", "RiverDark"], price_min: 20)

                User: "I am looking for Pants from ManufacturerFive with a budget between 5 and 100 €."
                Assistant: product_search(query: "Pant", properties: ["ManufacturerFive"], price_min: 5, price_max: 100)

                User: "I am searching for anything in GoldPurple or RiverDark in BigSize in the category ManBear."
                Assistant: product_search(categories: ['ManBear'], properties: ["GoldPurple", "RiverDark", "BigSize"])

                User: "I am searching Pants in GoldPurple and RiverDark in BigSize from ManufacturerFive."
                Assistant: product_search(query: "Pant", properties: ["GoldPurple", "RiverDark", "BigSize", "ManufacturerFive"])

                User: "I am searching for a present in GoldPurple from ManufacturerFive in ManBear with a budget from 30 to 220 €, cheapest first."
                Assistant: product_search(categories: ["ManBear"], properties: ["GoldPurple", "ManufacturerFive"], price_min: 30, price_max: 220, order: "price-asc")
            `)},flattenString(e){for(var t=e.trim().split("\n"),s=[],n=0;n<t.length;n++)s.push(t[n].replace(" ".repeat(16),""));return s.join("\n")}}})},131:function(){let{Component:e,Mixin:t}=Shopware;t.register("fel-paginator-mixin",{data(){return{optQueries:{page:+(this.$route.query.page||1),limit:+(this.$route.query.limit||5),sort:this.$route.query.sort,pages:0,filterable:null}}},methods:{refreshOptQueries(){if(this.optQueries.page=+(this.$route.query.page||this.optQueries.page),this.optQueries.limit=+(this.$route.query.limit||this.optQueries.limit),this.optQueries.sort=this.$route.query.sort||this.optQueries.sort,this.optQueries.filterable=this.$route.query.filterable,this.optQueries.pages=+this.optQueries.pages,this.optQueries.setFilterCriteria=[],this.optQueries.filterable)for(var e=this.optQueries.filterable.split(","),t=0;t<e.length;t++){let i=e[t];if(i){var[s,n]=i.split(":");s&&n&&(n=n.split("|"),this.optQueries.setFilterCriteria.push({filterName:s,filterIds:n}))}}},felRouteLink(e,t,s){return this.$router.resolve({name:e,params:t||{},query:s||{}}).href},createLogLink(e,t){var s={page:this.optQueries.page,limit:this.optQueries.limit,sort:this.optQueries.sort};return this.optQueries.filterable&&(s.filterable=this.optQueries.filterable),t={...s,...t},new URL(this.$router.resolve({name:"fel.gpt.assistant.logs",params:e||{},query:t||{}}).href,window.location.origin+window.location.pathname).href},getLogPagination(){return this.fetchLogPagination()},fetchLogPagination(){let e=+this.optQueries.page,t=+this.optQueries.pages,s=+this.optQueries.limit,n=this.optQueries.currentLength,i=this.createNavNumbers(e,t),a=this.createNavSteps(e,t),l=e*s-s,o={from:l?l+1:1,to:l+n,total:this.optQueries.currentTotal};return l>this.optQueries.currentTotal&&(o={from:0,to:0,total:0}),{meta:{index:o},numbers:i,steps:a,numbersMap:500>=t?this.createNavNumbersMap(t):null,pageError:{dontExists:e>t},queries:this.optQueries}},createNavSteps(e,t){return{first:1===e?null:1,prev:e>1&&e<=t?e-1:null,next:e>=1&&e<t?e+1:null,last:e===t?null:t}},createNavNumbersMap(e){return Array(e).fill(1).map((e,t)=>e+t)},createNavNumbers(e,t){let s,n;return t<=5?Array.from({length:t},(e,t)=>t+1):((e=Math.max(e,1))-2>0?(s=e-2,(n=e+2)>t&&(s=t-4,n=t)):e-1>0?(s=e-1,n=e+3):(s=e,n=e+4),s<1&&(s=1),Array.from({length:n-s+1},(e,t)=>t+s))}}})},739:function(){let{Component:e,Mixin:t}=Shopware;t.register("fel-plugin-config-mixin",{inject:["systemConfigApiService"],data(){return{configName:"FelAIProductAdvisor.config",pluginConfig:{},canRun:!0,detailPage:!1}},created(){this.httpClient=this.systemConfigApiService.client},methods:{setConfigValue(e,t){"assistantId"===e?e=`${this.configName}.openAiAssistantId`:"chatbotName"===e&&(e=`${this.configName}.openAiChatMetaChatbotName`),this.systemConfigApiService.saveValues({[e]:t})},httpClientGet(){return this.httpClientExec("get",...arguments)},httpClientDelete(){return this.httpClientExec("delete",...arguments)},httpClientPost(e,t,s){return this.httpClientExec("post",e,s,t)},async httpClientExec(e,t,s,n){if(!this.canRun)return!1;this.isLoading=!0;var i=[t];return n&&i.push(n),i.push({headers:this.pluginConfig.apiHeader}),await this.httpClient[e](...i).then(e=>s(e)).catch(e=>(console.error(e),s(e))).finally(()=>{setTimeout(()=>{this.isLoading=!1},300)})},async fetchPluginConfig(){return await this.systemConfigApiService.getValues(this.configName)},async getPluginConfig(){return await this.fetchPluginConfig().then(e=>{var t={setLoadSkeleton:Math.round(this.$el.querySelector(".sw-page__content").offsetHeight/64),apiKey:e[`${this.configName}.openAiApiKey`],chatbotName:e[`${this.configName}.openAiChatMetaChatbotName`],assistantId:e[`${this.configName}.openAiAssistantId`],openAiBaseUrl:e[`${this.configName}.openAiBaseUrl`],openAiVersion:e[`${this.configName}.openAiVersion`],openAiBetaVersion:e[`${this.configName}.openAiBetaVersion`],pluginConfig:e};return t.apiUrl=`${t.openAiBaseUrl}/${t.openAiVersion}`,t.apiHeader={"Content-Type":"application/json",Authorization:`Bearer ${t.apiKey}`,"OpenAI-Beta":t.openAiBetaVersion},(!1===t.apiKey||""===t.apiKey)&&(this.canRun=!1),this.pluginConfig=t,t})}}})}},t={};function s(n){var i=t[n];if(void 0!==i)return i.exports;var a=t[n]={id:n,exports:{}};return e[n](a,a.exports,s),a.exports}s.d=function(e,t){for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="bundles/felaiproductadvisor/",window?.__sw__?.assetPath&&(s.p=window.__sw__.assetPath+"/bundles/felaiproductadvisor/"),function(){"use strict";s(38),s(474),s(893),s(779),s(131),s(739);let{Component:e,Mixin:t}=Shopware;e.register("fel-api-test-button",{template:'<div class="fel-test-api-button-container">\n    <sw-button-process\n        :isLoading="isLoading"\n        :processSuccess="isSaveSuccessful"\n        @process-finish="saveFinish"\n        @click="check"\n    >{{ $tc(\'fel-gpt-assistant.api-test-button.button\') }}</sw-button-process>\n    <template v-if="generalNotification">\n        <div v-if="generalNotification.id && \'assistant\' === generalNotification.object">\n            <p class="alert alert-info fel-alert">\n                {{ $t(\'fel-gpt-assistant.api-test-button.assistantIdCheckOk\') }}: <b>{{ generalNotification.name }} ({{ generalNotification.id }})</b>\n            </p>\n        </div>\n    </template>\n    <template v-if="generalError">\n        <p class="alert alert-danger fel-alert">\n            {{ generalError }}\n        </p>\n    </template>\n    <template v-if="Object.keys(tempAssistantList).length">\n        <div v-if="tempAssistantList.data.length">\n            <h2>{{ $t(\'fel-gpt-assistant.general.openAIAssistants\') }}</h2>\n            <ul class="fel-temp-assistant-list">\n                <li v-for="obj in tempAssistantList.data">\n                    <span>\n                        <b>{{ obj.name }}</b>\n                        <small>{{ obj.model }}</small>\n                    </span>\n                    <span :title="$t(\'fel-gpt-assistant.general.assistantId\')">{{ obj.id }}</span>\n                </li>\n            </ul>\n        </div>\n        <div v-else>\n            <p class="alert fel-alert">\n                {{ $t(\'fel-gpt-assistant.general.noAssistantsFound\') }}\n            </p>\n        </div>\n    </template>\n</div>\n',props:["label"],inject:["felApiTest"],mixins:[t.getByName("notification")],data(){return{generalError:null,generalNotification:null,isLoading:!1,isSaveSuccessful:!1,tempAssistantList:{}}},computed:{pluginConfig(){let e=this.$parent;for(;void 0===e.actualConfigData;)e=e.$parent;return e.actualConfigData.null}},methods:{saveFinish(){this.isSaveSuccessful=!1},check(){this.isLoading=!0;var e="FelAIProductAdvisor.config.openAiApiKey",t="FelAIProductAdvisor.config.openAiAssistantId",s=document.getElementById(e),n=document.getElementById(t);void 0!==s.value&&s.value&&(this.pluginConfig[e]=s.value),void 0!==n.value&&(this.pluginConfig[t]=n.value),this.felApiTest.check(this.pluginConfig).then(e=>{if(this.isLoading=!1,void 0!==e.assistantList?this.tempAssistantList=e.assistantList:this.tempAssistantList={},void 0!==e.assistantOk?this.generalNotification=e.assistantOk:this.generalNotification=null,void 0!==e.error){var t=this.$tc(`fel-gpt-assistant.api-test-button.${e.error}`);this.createNotificationError({title:this.$tc("fel-gpt-assistant.api-test-button.title"),message:t.replace("fel-gpt-assistant.api-test-button.","")})}else void 0!==e.success&&e.success?(this.isSaveSuccessful=!0,this.createNotificationSuccess({title:this.$tc("fel-gpt-assistant.api-test-button.title"),message:this.$tc("fel-gpt-assistant.api-test-button.success")})):this.createNotificationError({title:this.$tc("fel-gpt-assistant.api-test-button.title"),message:this.$tc("fel-gpt-assistant.api-test-button.error")});void 0!==e.assistantFailed&&!0===e.assistantFailed?this.generalError=this.$tc("fel-gpt-assistant.api-test-button.assistantIdCheckFailed"):this.generalError=null}).catch(e=>{this.isLoading=!1,this.tempAssistantList={},this.generalNotification=null,void 0!==e.response&&void 0!==e.response.data?void 0!==e.response.data.errors&&void 0!==e.response.data.errors[0]&&(this.generalError=e.response.data.errors[0].detail):this.generalError=null,this.generalError&&this.createNotificationError({title:this.$tc("fel-gpt-assistant.api-test-button.title"),message:this.generalError})}).finally(()=>{this.isLoading=!1})}}}),s(329);var n='{% block fel_gpt_assistant_create %}\n	<sw-page class="fel-gpt-assistant-create fel-assistant">\n\n		<template #smart-bar-actions>\n			{# create assistant link #}\n			<template v-if="!detailPage">\n				<mt-button\n					variant="primary"\n					:disabled="!canCreate"\n					v-html="$t(\'fel-gpt-assistant.general.createAssistant\')"\n					@click="createOrUpdateAssistant(null)" />\n			</template>\n			{# actions for existing assistants #}\n			<template v-if="detailPage && !isLoading">\n				<mt-button\n					class="fel-mr-1"\n					variant="primary"\n					v-if="pluginConfig.assistantId != assistant.id"\n					v-html="$t(\'fel-gpt-assistant.create.setAsDefaultAssistant\')"\n					@click="setAssistantAsDefault($event, assistant.id)" />\n				<mt-button\n					class="fel-mr-1"\n					ghost="ghost"\n					:disabled="!canUpdate"\n					v-html="$t(\'fel-gpt-assistant.general.updateAssistant\')"\n					@click="createOrUpdateAssistant(assistant.id)" />\n				<mt-button\n					variant="critical"\n					v-if="pluginConfig.assistantId != assistant.id"\n					v-html="$t(\'fel-gpt-assistant.delete.delete\')"\n					@click="this.$router.push({name: \'fel.gpt.assistant.delete\'})" />\n			</template>\n		</template>\n\n		<template #content>\n\n			{# API key is missing #}\n			<template v-if="!pluginConfig.apiKey && !isLoading">\n				<div class="fel-sw-card-view">\n					<mt-card positionIdentifier="index">\n						<div class="fel-content-box">\n							<h2 class="fel-alert">{{ $t(\'fel-gpt-assistant.general.missingOpenAiKey\') }}</h2>\n							<hr >\n							<p class="fel-size-xxs">{{ $t(\'fel-gpt-assistant.general.requiredApiKeyInfo\') }}</p>\n							<hr >\n							<p class="fel-size-xxs">\n								<mt-button\n									variant="primary"\n									ghost="true"\n									v-html="$t(\'fel-gpt-assistant.general.goToConfiguration\')"\n									@click="this.$router.push({name: \'sw.extension.config\', params: {namespace: \'FelAIProductAdvisor\'}})" />\n							</p>\n						</div>\n					</mt-card>\n				</div>\n			</template>\n\n			{# go to created assistant button #}\n			<template v-if="!detailPage && isCreated">\n				<div class="fel-sw-card-view">\n					<mt-card positionIdentifier="index">\n						<mt-button\n							variant="primary"\n							ghost="true"\n							v-html="isCreated"\n							@click="this.$router.push({name: \'fel.gpt.assistant.detail\', params: {id: isCreated}})" />\n					</mt-card>\n				</div>\n			</template>\n\n			<div v-if="(!isCreated || (detailPage && isUpdated)) && pluginConfig.apiKey" class="fel-sw-card-view">\n\n				<mt-card positionIdentifier="index">\n					{# Assistant name #}\n					<div maxlength="100" class="sw-field sw-block-field sw-field--default sw-contextual-field sw-field--text">\n						<div class="sw-field__label">\n							<label for="fel--sw--assistant_name" class="is--required">\n								{{ $t(\'fel-gpt-assistant.create.form.setAssistantName\') }}\n							</label>\n						</div>\n						<div class="sw-block-field__block">\n							<input\n								v-model="assistant.name"\n								type="text"\n								id="fel--sw--assistant_name"\n								name="fel--sw--assistant_name"\n								maxlength="100"\n								required="true"\n								:placeholder="$t(\'fel-gpt-assistant.create.form.setAssistantName\')"\n								@input="onAssistantInput($event)">\n							<div class="sw-field__addition">\n								{{ $t(\'fel-gpt-assistant.create.form.setAssistantNameHelp\') }}\n							</div>\n						</div>\n					</div>\n\n					{# model #}\n					<div class="fel-select-model-container fel-flex-space-between">\n						<div class="sw-field sw-field--radio sw-boolean-radio-group fel-radio-group">\n							<div class="fel-field__label">\n								<div class="fel-content-box">\n									<h2>{{ $t(\'fel-gpt-assistant.create.selectModelToUse\') }}</h2>\n								</div>\n							</div>\n							<div class="sw-field__radio-group">\n								<div v-for="data,idx in availableModels" class="sw-field__radio-option sw-field__radio-option-checked">\n									<div class="sw-field__radio-input">\n										<input\n											:id="\'fel--sw--assistant_model\' + data"\n											name="fel--sw--assistant_model"\n											type="radio"\n											:value="data"\n											:checked="data == assistant.model"\n											@change="onAssistantInput($event)"\n											required="true">\n										<div class="sw-field__radio-state"></div>\n									</div>\n									<label :for="\'fel--sw--assistant_model\' + data" class="sw-field__radio-option-label">\n										{{ data }}\n									</label>\n								</div>\n							</div>\n						</div>\n						<div class="fel-assistant-files-notification fel-flex-align-center fel-flex-end fel-size-xxs"\n							v-if="!isLoading && toSubmit[\'fel--sw--assistant_vector_store\'].length">\n							<span class="fel-mr-1">{{ $t(\'fel-gpt-assistant.files.vectorStore.name\') }}</span>\n							<b>{{ toSubmit[\'fel--sw--assistant_vector_store\'].length }}</b>\n						</div>\n					</div>\n					<template v-if="!isLoading && toSubmit[\'fel--sw--assistant_vector_store\'].length && \'gpt-3.5-turbo\' === toSubmit[\'fel--sw--assistant_model\']">\n						<div class="alert fel-alert">\n							{{ $t(\'fel-gpt-assistant.create.gptThreeFileReadingInfo\') }}\n						</div>\n					</template>\n				</mt-card>\n\n				<mt-card positionIdentifier="index">\n\n					{# instructions #}\n					<div class="fel-set-instructions-wrapper mt-field mt-field--default mt-field--textarea">\n						<div class="mt-field__label">\n							<label for="fel--sw--assistant_instruction" class="mt-field__label fel-label">\n								<div class="fel-content-box fel-flex-space-between">\n									<h2>{{ $t(\'fel-gpt-assistant.create.setInstructions\') }}</h2>\n									<span class="fel-instructions-head-options">\n										<sw-help-text\n											:text="$t(\'fel-gpt-assistant.create.setInstructionsInfo\')"\n											:width="240"\n											:showDelay="100"\n											:hideDelay="100"\n											tooltipPosition="left" />\n									</span>\n								</div>\n							</label>\n						</div>\n						<div class="mt-block-field__block">\n							<textarea id="fel--sw--assistant_instruction"\n								name="fel--sw--assistant_instruction"\n								@input="onAssistantInput($event)"\n							>{{ assistant.instructions ? assistant.instructions : toSubmit[\'fel--sw--assistant_instruction\'] }}</textarea>\n						</div>\n					</div>\n\n					{# temperature #}\n					<div class="fel-set-temperature-wrapper mt-field mt-block-field mt-contextual-field mt-field--number mt-field--default fel-mb-1">\n						<div class="mt-block-field__block">\n							<div class="mt-field__addition is--prefix">\n								{{ $t(\'fel-gpt-assistant.general.temperature\') }}\n							</div>\n							<input\n								v-model="assistant.temperature"\n								name="fel--sw--assistant_temperature"\n								id="fel--sw--assistant_temperature"\n								type="number"\n								autocomplete="off"\n								:step="0.1"\n								:min="0.1"\n								:max="2"\n								:digits="1"\n								:required="true"\n								@input="onAssistantInput($event)">\n							<div class="mt-field__addition is--suffix">\n								<sw-help-text\n									:text="$t(\'fel-gpt-assistant.general.temperatureDescription\')"\n									:width="240"\n									tooltipPosition="left"\n									:showDelay="100"\n									:hideDelay="100">\n								</sw-help-text>\n							</div>\n						</div>\n					</div>\n				</mt-card>\n\n				{# functions (tools) #}\n				<mt-card positionIdentifier="index">\n					<div class="fel-select-tools-wrapper mt-field mt-block-field mt-select mt-multi-select mt-form-field-renderer mt-field--default">\n						<div class="fel-field__label mt-field__label">\n							<div class="fel-content-box fel-mb-1">\n								<div class="fel-flex-space-between">\n									<h2>{{ $t(\'fel-gpt-assistant.create.selectAllowedFunctions\') }}</h2>\n									<sw-help-text\n										:text="$t(\'fel-gpt-assistant.create.selectAllowedFunctionsInfo\')"\n										:width="240"\n										:showDelay="100"\n										:hideDelay="100"\n										tooltipPosition="left" />\n								</div>\n								<div v-if="pluginConfig.pluginConfig && pluginConfig.pluginConfig[\'FelAIProductAdvisor.config.openAiStaticInformationField\']">\n									<div class="fel-alert-container"\n										v-if="toSubmit[\'fel--sw--assistant_tools\'] && !toSubmit[\'fel--sw--assistant_tools\'].includes(\'get_meta_information\')">\n										<div class="fel-alert">\n											{{ $t(\'fel-gpt-assistant.create.dynamicFileSettedButFunctionDisabled\') }}\n											<br>\n											<mt-button\n												class="fel-mt-1"\n												variant="critical"\n												size="x-small"\n												data-action="click"\n												data-target="[name=\'fel--sw--assistant_tools\']"\n												data-query-all="false"\n												@click="felMultiBtn($event)"\n												v-html="$t(\'fel-gpt-assistant.general.fixIt\')" />\n										</div>\n									</div>\n								</div>\n							</div>\n						</div>\n						<div class="fel-multi-checkbox">\n							<template v-for="data,name in availableFunctionsObject.list">\n								<div class="mt-field--checkbox__container" :value="name">\n									<div class="mt-field--checkbox">\n										<div class="mt-field--checkbox__content">\n											<div class="mt-field__checkbox">\n												<input type="checkbox"\n													:id="\'fel--sw--assistant_tools_\' + name"\n													name="fel--sw--assistant_tools"\n													:value="name"\n													:checked="availableFunctionsObject.defaults.includes(name)"\n													@change="onAssistantInput($event)">\n												<div class="mt-field__checkbox-state">\n													<mt-icon name="regular-checkmark-xxs"></mt-icon>\n												</div>\n											</div>\n											<div class="mt-field mt-field--default">\n												<div class="mt-field__label">\n													<label :for="\'fel--sw--assistant_tools_\' + name">\n														{{ $t(`fel-gpt-assistant.general.aiToolsTranslation[\'${name}\']`) }}\n													</label>\n												</div>\n											</div>\n										</div>\n									</div>\n								</div>\n							</template>\n						</div>\n						<div class="fel-view-object-container fel-available-functions-wrapper">\n							<div class="fel-view-object-buttons fel-flex-space-between">\n								<div class="fel-available-functions-button">\n									<mt-button\n										data-action="toggle"\n										data-target=".fel-available-functions"\n										data-target-class="fel-is-hidden"\n										data-self-class="mt-button--primary"\n										data-active-if="not"\n										@click="felMultiBtn($event)"\n										v-html="$t(\'fel-gpt-assistant.general.showFunctions\')">\n									</mt-button>\n								</div>\n								<div class="fel-toggle-assistant-object-container" v-if="assistant && \'undefined\' !== typeof assistant.id">\n									<mt-button\n										data-action="toggle"\n										data-target=".fel-custom-action"\n										data-target-class="fel-is-hidden"\n										data-self-class="mt-button--primary"\n										data-active-if="not"\n										@click="felMultiBtn($event)"\n										v-html="$t(\'fel-gpt-assistant.general.assistant\')">\n									</mt-button>\n								</div>\n							</div>\n							<pre class="fel-available-functions fel-is-hidden">{{ Object.values(availableFunctions().list) }}</pre>\n							<pre class="fel-custom-action fel-is-hidden">{{ assistant }}</pre>\n						</div>\n					</div>\n				</mt-card>\n\n				{# vector store List #}\n				<mt-card positionIdentifier="index">\n					<div class="mt-field mt-block-field mt-select mt-multi-select mt-form-field-renderer mt-field--default">\n						<div class="fel-field__label">\n							<label for="fel--sw--assistant_vector_store">\n								<div class="fel-content-box">\n									<h2 class="fel-flex-space-between">\n										{{ $t(\'fel-gpt-assistant.files.vectorStore.attachVectorStoreToAssistant\') }}\n									</h2>\n									<template v-if="\'gpt-3.5-turbo\' === toSubmit[\'fel--sw--assistant_model\']">\n										<div class="alert fel-alert">\n											{{ $t(\'fel-gpt-assistant.create.gptThreeFileReadingInfo\') }}\n										</div>\n									</template>\n								</div>\n							</label>\n						</div>\n						<div class="mt-block-field__block">\n							<select\n								id="fel--sw--assistant_vector_store"\n								class="fel-multi-select"\n								name="fel--sw--assistant_vector_store"\n								@change="onAssistantInput($event)"\n								:size="(vectorStoreList && vectorStoreList.length <= 5) ? vectorStoreList.length : 5">\n								<template v-for="data in vectorStoreList">\n									<option :value="data.id" :selected="toSubmit[\'fel--sw--assistant_vector_store\'].includes(data.id)">\n										{{ data.name }} ~ {{ data.status }} ~ {{ data.file_counts.total }} files ~ {{ data.id }}\n									</option>\n								</template>\n							</select>\n						</div>\n						<div class="fel-oai-files-wrapper">\n							<mt-button\n								class="fel-reset-vector-store-select"\n								:block="true"\n								@click="resetSelect(\'#fel--sw--assistant_vector_store\')">\n								{{ $t(\'fel-gpt-assistant.general.reset\') }}\n							</mt-button>\n						</div>\n					</div>\n				</mt-card>\n\n			</div>\n\n		</template>\n	</sw-page>\n{% endblock %}\n';let{Component:i,Mixin:a}=Shopware;i.register("fel-gpt-assistant-create",{template:n,mixins:[a.getByName("notification"),a.getByName("fel-input-helper-mixin"),a.getByName("fel-openai-mixin"),a.getByName("fel-plugin-config-mixin"),a.getByName("fel-helper-mixin")],data(){return{assistant:{name:null}}},created(){this.isLoading=!0,this.getPluginConfig().then(e=>{this.availableFunctionsObject=this.availableFunctions(),this.assistant.temperature=this.assistantTemperatureDefault,this.assistant.model=this.availableModels[0],this.toSubmit["fel--sw--assistant_tools"]=this.availableFunctionsObject.defaults,this.toSubmit["fel--sw--assistant_model"]=this.assistant.model,this.toSubmit["fel--sw--assistant_instruction"]=`${this.defaultInstructions()}

${this.genericExamplesForSearchQueries()}`,this.toSubmit["fel--sw--assistant_temperature"]=this.assistant.temperature;var t=this.$el.querySelector('[name="fel--sw--assistant_instruction"]');t&&"string"==typeof this.toSubmit["fel--sw--assistant_instruction"]&&(t.rows=this.toSubmit["fel--sw--assistant_instruction"].split("\n").length+3),this.fetchFiles(),this.fetchVectorStores(),this.fetchAssistants()})},metaInfo(){return{title:this.$createTitle()}}});let{Component:l,Mixin:o}=Shopware;l.register("fel-gpt-assistant-delete",{template:'{% block fel_gpt_assistant_delete %}\n	<sw-page class="fel-gpt-assistant-delete fel-assistant">\n		<template #content>\n			<div class="fel-mt-3" v-if="canRun">\n				<mt-card :title="$t(\'fel-gpt-assistant.delete.deleteAssistant\')" positionIdentifier="index">\n\n					<mt-button-group v-if="!isDeleted">\n						<mt-button\n							variant="critical"\n							:aria-label="$t(\'fel-gpt-assistant.delete.delete\')"\n							@click="deleteAssistant(deleteAssistantId)">\n							{{ $t(\'fel-gpt-assistant.delete.delete\') }}\n						</mt-button>\n						<mt-button\n							variant="primary"\n							ghost="true"\n							@click="this.$router.push({name: \'fel.gpt.assistant.detail\', params: {id: deleteAssistantId}})"\n							:aria-label="deleteAssistantId">\n							{{ deleteAssistantId }}\n						</mt-button>\n					</mt-button-group>\n\n					<mt-button\n						v-if="isDeleted"\n						variant="primary"\n						ghost="true"\n						@click="this.$router.push({name: \'fel.gpt.assistant.index\'})"\n						:aria-label="$t(\'fel-gpt-assistant.delete.backToIndex\')">\n						{{ $t(\'fel-gpt-assistant.delete.backToIndex\') }}\n					</mt-button>\n\n				</mt-card>\n			</div>\n		</template>\n	</sw-page>\n{% endblock %}\n',mixins:[o.getByName("notification"),o.getByName("fel-plugin-config-mixin")],data(){return{deleteAssistantId:this.$route.params.id,isDeleted:!1}},created(){this.getPluginConfig().then(e=>{})},methods:{deleteAssistant(e){this.httpClientDelete(`${this.pluginConfig.apiUrl}/assistants/${e}`,t=>{!0===(t=t.data).deleted?(this.isDeleted=!0,this.createNotificationInfo({message:`${this.$t("fel-gpt-assistant.general.deleted")} ${e}`})):this.createNotificationError({message:"error"})})}},metaInfo(){return{title:this.$createTitle()}}});let{Component:r,Mixin:c}=Shopware;r.register("fel-gpt-assistant-detail",{template:n,mixins:[c.getByName("notification"),c.getByName("fel-input-helper-mixin"),c.getByName("fel-openai-mixin"),c.getByName("fel-plugin-config-mixin"),c.getByName("fel-helper-mixin")],data(){return{assistant:{},isLoading:!0,assistantId:this.$route.params.id,tempExamplesTotal:void 0}},created(){this.detailPage=!0,this.isCreated=!1,this.getPluginConfig().then(e=>{this.fetchVectorStores(),this.fetchAssistants(),this.fetchAssistant(e)})},methods:{async fetchAssistant(e){await this.httpClientGet(`${this.pluginConfig.apiUrl}/assistants/${this.assistantId}`,e=>{if(this.assistant=e.data,this.assistant&&void 0!==this.assistant.id){if(this.availableFunctionsObject=this.availableFunctions(),this.availableFunctionsObject.defaults=this.assistant.tools,this.assistant.tools.length){for(var t=[],s=0;s<this.assistant.tools.length;s++){var n=this.assistant.tools[s];"function"===n.type&&t.push(n.function.name)}t.length&&(this.availableFunctionsObject.defaults=t,this.toSubmit["fel--sw--assistant_tools"]=t)}this.assistant.tool_resources&&this.assistant.tool_resources.file_search&&this.assistant.tool_resources.file_search.vector_store_ids?this.toSubmit["fel--sw--assistant_vector_store"]=this.assistant.tool_resources.file_search.vector_store_ids:this.toSubmit["fel--sw--assistant_vector_store"]=[],this.$el.querySelector('input[name="fel--sw--assistant_name"]')&&(this.$el.querySelector('input[name="fel--sw--assistant_name"]').value=this.assistant.name),this.toSubmit["fel--sw--assistant_name"]=this.assistant.name,this.toSubmit["fel--sw--assistant_model"]=this.assistant.model,this.toSubmit["fel--sw--assistant_instruction"]=this.assistant.instructions,this.toSubmit["fel--sw--assistant_temperature"]=parseFloat(this.assistant.temperature);var i=this.$el.querySelector('[name="fel--sw--assistant_instruction"]');i&&"string"==typeof this.toSubmit["fel--sw--assistant_instruction"]&&(i.rows=this.toSubmit["fel--sw--assistant_instruction"].split("\n").length+3),this.modelError=["gpt-3.5-turbo","gpt-3.5-turbo-1106"].includes(this.toSubmit["fel--sw--assistant_model"])}})},setAssistantAsDefault(e,t){this.setConfigValue("assistantId",t),this.createNotificationSuccess({message:t}),e.target.parentNode.remove()}},metaInfo(){return{title:this.$createTitle()}}});let{Component:d,Mixin:p}=Shopware;d.register("fel-gpt-assistant-file",{template:'{% block fel_gpt_assistant_file %}\n	<sw-page class="fel-gpt-assistant-file fel-assistant">\n\n		<template #smart-bar-actions>\n			<template v-if="detailPage">\n				<mt-button\n					variant="critical"\n					:aria-label="$t(\'fel-gpt-assistant.delete.delete\')"\n					:disabled="isDeleted || \'undefined\' === typeof file"\n                    @click="deleteFile(fileId)">\n					{{ $t(\'fel-gpt-assistant.delete.delete\') }}\n				</mt-button>\n			</template>\n		</template>\n\n		<template #content>\n			<div class="fel-mt-3">\n\n				<mt-card v-if="!isDeleted && (\'undefined\' !== typeof file && Object.keys(file).length)" positionIdentifier="index">\n					<h2>{{ fileId }}</h2>\n                    <pre class="fel-mb-3">{{ file }}</pre>\n                    <template v-if="file.purpose == \'assistants\'">\n                        <p v-html="$t(\'fel-gpt-assistant.files.canNotDownloadFile\')"></p>\n                    </template>\n				</mt-card>\n\n				<mt-card v-if="isDeleted || (\'undefined\' === typeof file)" positionIdentifier="index">\n					<mt-button\n                        variant="primary"\n						ghost="true"\n						:aria-label="$t(\'fel-gpt-assistant.delete.backToIndex\')"\n						@click="this.$router.push({name: \'fel.gpt.assistant.index\'})">\n						{{ $t(\'fel-gpt-assistant.delete.backToIndex\') }}\n					</mt-button>\n				</mt-card>\n\n			</div>\n		</template>\n\n	</sw-page>\n{% endblock %}\n',mixins:[p.getByName("notification"),p.getByName("fel-input-helper-mixin"),p.getByName("fel-openai-mixin"),p.getByName("fel-plugin-config-mixin")],data(){return{isDeleted:!1,file:{},fileId:this.$route.params.id}},created(){this.detailPage=!0,this.getPluginConfig().then(e=>{this.fetchFiles(),this.fetchFile()})},methods:{fetchFile(){this.httpClientGet(`${this.pluginConfig.apiUrl}/files/${this.fileId}`,e=>{this.file=e.data})}},metaInfo(){return{title:this.$createTitle()}}});let{Component:f,Mixin:u}=Shopware;f.register("fel-gpt-assistant-index",{template:'{% block fel_gpt_assistant_index %}\n	<sw-page class="fel-gpt-assistant-index fel-assistant">\n\n		<template #smart-bar-actions v-if="canRun">\n			<mt-button\n				variant="primary"\n				v-html="$t(\'fel-gpt-assistant.general.createAssistant\')"\n				@click="this.$router.push({name: \'fel.gpt.assistant.create\'})" />\n		</template>\n\n		<template #content>\n\n			<template v-if="isLoading">\n				<mt-skeleton-bar v-for="idx in pluginConfig.setLoadSkeleton" />\n			</template>\n\n			<template v-if="canRun && !isLoading">\n\n				{# assistants list #}\n				<sw-grid\n					v-if="assistantList.data"\n					:items="assistantList.data"\n					:selectable="false"\n					:table="false">\n					<template #columns="{ item }">\n						<sw-grid-column flex="minmax(200px, 1fr)" :label="$t(\'fel-gpt-assistant.general.assistantName\')">\n							<router-link\n								class="fel-flex fel-flex-space-between"\n								:title="item.id"\n								:to="{ name: \'fel.gpt.assistant.detail\', params: {id: item.id} }">\n								{{ item.name }}\n							</router-link>\n							<template v-if="activeFilesList.vectorStores[item.id]">\n								<small>{{ activeFilesList.vectorStores[item.id] }}</small>\n							</template>\n						</sw-grid-column>\n						<sw-grid-column flex="minmax(30px, 74px)" class="fel-grid-small-cell">\n							<template v-if="item.id == pluginConfig.assistantId">\n								<mt-icon name="regular-checkmark" color="#1abc9c"></mt-icon>\n							</template>\n							<template v-else>\n								<router-link\n									:title="$t(\'fel-gpt-assistant.delete.delete\')"\n									:to="{ name: \'fel.gpt.assistant.delete\', params: {id: item.id} }">\n									<mt-icon name="regular-times-s"></mt-icon>\n								</router-link>\n							</template>\n						</sw-grid-column>\n					</template>\n				</sw-grid>\n\n				{# Vector Store / create #}\n				<div class="fel-oai-vector-store-wrapper fel-openai-file-manager">\n\n					<div class="fel-oai-vector-store-help">\n						<mt-card class="sw-data-grid__row fel-my-0" positionIdentifier="index">\n							<div class="sw-grid__cell-content fel-flex-space-between">\n								<h2>{{ $t(\'fel-gpt-assistant.files.vectorStore.createVectorStore\') }}</h2>\n								<sw-help-text\n									:text="$t(\'fel-gpt-assistant.files.vectorStore.aboutVectorStore\')"\n									:width="240"\n									tooltipPosition="left"\n									:showDelay="100"\n									:hideDelay="100">\n								</sw-help-text>\n							</div>\n							<div class="sw-field sw-block-field sw-contextual-field sw-field--text sw-field--default">\n								<form\n									action="#"\n									method="post"\n									class="fel-flex fel-create-vector-store-form"\n									@submit.prevent="createVectorStore($event)">\n									<div class="sw-block-field__block fel-w-100 fel-my-1">\n										<label for="fel-sw-create-vector-store" class="sw-button">\n											<mt-icon name="regular-database" :small="true"></mt-icon>\n										</label>\n										<input\n											id="fel-sw-create-vector-store"\n											name="fel-sw-create-vector-store"\n											type="text"\n											value=""\n											:placeholder="$t(\'fel-gpt-assistant.files.vectorStore.setName\')"\n											required="true">\n									</div>\n									<button class="sw-button sw-button--ghost" v-html="$t(\'fel-gpt-assistant.general.create\')"></button>\n								</form>\n							</div>\n						</mt-card>\n					</div>\n\n					{# Vector Store list #}\n					<div class="sw-grid sw-grid--normal">\n						<div class="sw-grid__header">\n							<div class="sw-grid-column sw-grid__cell sw-grid-column--left">\n								<div class="sw-grid__cell-content fel-flex fel-flex-space-between" :title="vectorStoreList.length">\n									<span>{{ $t(\'fel-gpt-assistant.files.vectorStore.name\') }}</span>\n									<small>{{ vectorStoreList ? vectorStoreList.length : 0 }}</small>\n								</div>\n							</div>\n						</div>\n						<div class="sw-grid__body">\n							<div role="row" class="sw-grid-row sw-grid__row--1" v-for="vectorStore in vectorStoreList">\n								<div class="sw-grid-column sw-grid__cell sw-grid-column--left" v-if="vectorStore.name">\n									<div class="sw-grid__cell-content fel-flex fel-flex-space-between">\n										<div class="fel-vector-store-list-container">\n											<div class="fel-vector-store-list-header">\n												<h3 class="fel-mb-1">\n													<router-link\n														:title="vectorStore.id"\n														:to="{ name: \'fel.gpt.assistant.vectorStore\', params: {id: vectorStore.id} }">\n														{{ vectorStore.name || vectorStore.id }}\n													</router-link>\n												</h3>\n											</div>\n											<div class="fel-vector-store-list-body fel-flex">\n												<small>{{ vectorStore.status }}</small>\n												<small>{{ $t(\'fel-gpt-assistant.files.files\') }}: {{ vectorStore.file_counts.total }}</small>\n												<small>usage_bytes: {{ vectorStore.usage_bytes }}</small>\n											</div>\n										</div>\n										<div class="fel-vector-store-id-info-container">\n											<small>{{ vectorStore.id }}</small>\n										</div>\n									</div>\n								</div>\n							</div>\n						</div>\n					</div>\n\n				</div>\n\n				{# OpenAI files #}\n				<div class="fel-oai-files-upload-wrapper fel-openai-file-manager">\n\n					{# upload #}\n					<div class="fel-oai-files-upload-help">\n						<mt-card class="sw-data-grid__row fel-my-0" positionIdentifier="index">\n							<div class="fel-flex-space-between">\n								<h2>{{ $t(\'fel-gpt-assistant.general.uploadFile\') }}</h2>\n								<sw-help-text\n									:text="$t(\'fel-gpt-assistant.files.uploadHelp\')"\n									:width="240"\n									tooltipPosition="left"\n									:showDelay="100"\n									:hideDelay="100">\n								</sw-help-text>\n							</div>\n							<div class="fel-flex">\n								<label\n									class="sw-button sw-button--primary"\n									for="fel-sw-upload-file"\n									:title="`${$t(\'fel-gpt-assistant.general.uploadFile\')}: ${allowedFileTypes.join(\', \')}`">\n									<mt-icon name="regular-upload" :small="true"></mt-icon>\n									<input\n										v-bind="$attrs"\n										id="fel-sw-upload-file"\n										name="fel-sw-upload-file"\n										type="file"\n										:accept="allowedFileTypes.join(\',\')"\n										@change="uploadFile($event)">\n								</label>\n							</div>\n						</mt-card>\n					</div>\n\n					{# files list #}\n					<div class="sw-grid sw-grid--normal">\n						<div class="sw-grid__header">\n							<div class="sw-grid-column sw-grid__cell">\n								<div class="sw-grid__cell-content fel-flex fel-flex-space-between">\n									<span>{{ $t(\'fel-gpt-assistant.general.openAIFiles\') }}</span>\n									<small>{{ filesList.data ? Object.keys(filesList.data).length : 0 }}</small>\n								</div>\n							</div>\n						</div>\n						<div class="sw-grid__body">\n							<div role="row"\n								class="sw-grid-row sw-grid__row--1 fel-files-grid"\n								v-for="file,id in filesList.data">\n								<template v-if="file.filename && file.id">\n									<div class="sw-grid-column sw-grid__cell sw-grid-column--left fel-files-grid-filename">\n										<div class="sw-grid__cell-content">\n											<router-link\n												:title="file.id"\n												:to="{ name: \'fel.gpt.assistant.file\', params: {id: file.id} }"\n												@click="deleteFile(file.id)">\n												<span>{{ file.filename || \'empty\' }}</span>\n											</router-link>\n											<small class="text-ghost">{{ file.bytes }} bytes, {{ file.status }}</small>\n										</div>\n									</div>\n									<div class="sw-grid-column sw-grid__cell fel-grid-small-cell fel-files-grid-delete">\n										<div class="sw-grid__cell-content">\n											<a class="router-link-active"\n												:aria-label="$t(\'fel-gpt-assistant.delete.delete\')"\n												:title="$t(\'fel-gpt-assistant.delete.delete\')"\n												@click="deleteFile(file.id)">\n												<mt-icon name="regular-times-s" class="mt-icon--small"></mt-icon>\n											</a>\n										</div>\n									</div>\n								</template>\n							</div>\n						</div>\n					</div>\n\n				</div>\n\n			</template>\n\n			{# Footnotes #}\n			<template v-if="!isLoading">\n				<div class="fel-mt-3">\n					<template v-if="!canRun && !isLoading">\n						<mt-card class="fel-mb-3" positionIdentifier="index">\n							<div class="fel-content-box">\n								<h2 class="fel-alert">{{ $t(\'fel-gpt-assistant.general.missingOpenAiKey\') }}</h2>\n								<hr >\n								<p class="fel-size-xs">{{ $t(\'fel-gpt-assistant.general.requiredApiKeyInfo\') }}</p>\n								<hr >\n								<p>\n									<router-link :to="{ name: \'sw.extension.config\', params: {namespace: \'FelAIProductAdvisor\'} }">\n										{{ $t(\'fel-gpt-assistant.general.goToConfiguration\') }}\n									</router-link>\n								</p>\n							</div>\n						</mt-card>\n					</template>\n					<template v-if="1 === this.availableModels.length">\n						<mt-card class="fel-mb-3" positionIdentifier="index">\n							<div class="fel-content-box">\n								<p class="fel-notification-info">\n									{{ $t(\'fel-gpt-assistant.error.newModelsNotAvailable\') }}\n									<a target="_blank" rel="noopener" href="//help.openai.com/en/articles/7102672-how-can-i-access-gpt-4">\n										{{ $t(\'fel-gpt-assistant.error.howDoIGetNewerModels\') }}\n									</a>\n								</p>\n							</div>\n						</mt-card>\n					</template>\n					<mt-card class="fel-mb-3" positionIdentifier="index">\n						<div class="fel-content-box fel-size-xxs">\n							<p>{{ $t(\'fel-gpt-assistant.general.description\') }}</p>\n							<hr />\n							<p>{{ $t(\'fel-gpt-assistant.general.noDataOnServerNote\') }}</p>\n						</div>\n					</mt-card>\n				</div>\n\n				{# useful links to OpenAI #}\n				<div class="fel-mb-3">\n					<mt-card :title="$t(\'fel-gpt-assistant.general.usefulLinks\')" positionIdentifier="index">\n						<ul class="fel-description-list">\n							<li>\n								<a href="https://status.openai.com/" target="_blank" rel="noopener" class="mt-external-link">\n									{{ $t(\'fel-gpt-assistant.general.openAIStatus\') }}\n								</a>\n							</li>\n							<li>\n								<a href="https://platform.openai.com/usage" target="_blank" rel="noopener" class="mt-external-link">\n									{{ $t(\'fel-gpt-assistant.general.openAIUsage\') }}\n								</a>\n							</li>\n							<li>\n								<a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener" class="mt-external-link">\n									{{ $t(\'fel-gpt-assistant.general.openAIApiKey\') }}\n								</a>\n							</li>\n							<li>\n								<a href="https://platform.openai.com/tokenizer" target="_blank" rel="noopener" class="mt-external-link">\n									{{ $t(\'fel-gpt-assistant.general.openAITokenizer\') }}\n								</a>\n							</li>\n						</ul>\n					</mt-card>\n				</div>\n\n			</template>\n\n		</template>\n	</sw-page>\n{% endblock %}\n',mixins:[u.getByName("notification"),u.getByName("fel-plugin-config-mixin"),u.getByName("fel-openai-mixin")],data(){return{isLoading:!0,vectorStore:{},file:{}}},created(){this.getPluginConfig().then(e=>{this.fetchFiles(),this.fetchVectorStores(),this.fetchAssistants()})},metaInfo(){return{title:this.$createTitle()}}});let{Component:m,Mixin:h,Context:g}=Shopware,{Criteria:v}=Shopware.Data;m.register("fel-gpt-assistant-logging-index",{template:'{% block fel_gpt_assistant_logging_index %}\n	<sw-page class="fel-gpt-assistant-logging-index fel-assistant">\n		<template #content>\n\n			<template v-if="isLoading">\n				<mt-skeleton-bar v-for="idx in pluginConfig.setLoadSkeleton" />\n			</template>\n\n			<template>\n\n				{# no logs found #}\n				<template v-if="pageError.dontExists">\n					<template v-if="!repository.total">\n						<div class="fel-alert-container">\n							<div class="fel-content-box mt-alert--warning">\n								{{ $t(\'fel-gpt-assistant.logs.ui.options.noLogsNoOptions\') }}\n							</div>\n						</div>\n					</template>\n					<template v-else>\n						<div class="fel-alert-container">\n							<div class="fel-content-box sw-alert--error">\n								<div class="fel-alert-error-inner">\n									{{ $t(\'fel-gpt-assistant.logs.ui.pageError.dontExists\') }}\n									<small>({{ optQueries.page }} / {{ optQueries.pages }})</small>\n									<template v-if="1 !== optQueries.page">\n										<hr />\n										<a\n											class="mt-button mt-button--small"\n											:href="createLogLink(null, {page: 1})"\n											@click="goToHref($event.target)">\n											{{ $t(\'fel-gpt-assistant.delete.backToIndex\') }}\n										</a>\n									</template>\n								</div>\n							</div>\n						</div>\n					</template>\n				</template>\n\n				{# logs found #}\n				<template v-else>\n					{# logs grid #}\n					<div class="fel-chat-logs-container">\n						<sw-grid\n							v-if="optQueries.pages"\n							v-model="repository"\n							class="fel-chat-log-main"\n							:selectable="false"\n							:items="repository">\n							<template #columns="{ item }">\n\n									<sw-grid-column flex="minmax(min-content, max-content)" label="x">\n										<button\n											type="button"\n											class="mt-button mt-button--x-small"\n											:title="$t(\'fel-gpt-assistant.logs.ui.deleteThisLogEntry\')"\n											:data-delete-id="item.id"\n											@click="deleteLogEntry($event)">\n\n											<mt-icon name="regular-times" size="14px" />\n										</button>\n									</sw-grid-column>\n\n									<sw-grid-column\n										flex="minmax(85%, auto)"\n										:label="`${$t(\'fel-gpt-assistant.logs.ui.threadMessages\')} (${optQueries.currentTotal} - ${optQueries.page}/${optQueries.pages})`">\n										<div class="fel-chat-logged-chat">\n											<p class="fel-chat-log-datetime">\n												<small title="createdAt">\n													{{ item.createdAt }}\n												</small>\n												<template v-if="item.updatedAt">\n													<small title="updatedAt">\n														{{ item.updatedAt }}\n													</small>\n												</template>\n												<small\n													v-if="\'undefined\' !== typeof item.threadMessages.length"\n													:title="$t(\'fel-gpt-assistant.general.messages\')">\n													{{ item.threadMessages.length }}\n												</small>\n												<template v-if="item.threadDeleted">\n													<small class="fel-chat-is-deleted" :title="$t(\'fel-gpt-assistant.logs.ui.deletedByUser\')">\n														<mt-icon name="regular-ban" size="14px" color="red" />\n													</small>\n												</template>\n												<template v-else>\n													<small class="fel-chat-is-pending" :title="$t(\'fel-gpt-assistant.logs.ui.chatIsPending\')">\n														<mt-icon name="regular-eye" size="14px" color="green" />\n													</small>\n												</template>\n											</p>\n											<div class="fel-chat-messages">\n												<template v-for="message in item.threadMessages">\n													<div :class="\'chat-\' + message.role">\n														<div class="chat-inner" v-if="message.value">{{ message.value.trim() }}</div>\n													</div>\n												</template>\n											</div>\n										</div>\n									</sw-grid-column>\n\n							</template>\n						</sw-grid>\n					</div>\n				</template>\n\n				{# pagination #}\n				<div class="sw-grid__pagination fel-pagination">\n\n					{# pagination links | first / prev | numbers | next / last #}\n					<div class="fel-pagination-wrapper">\n						<div class="sw-pagination" v-if="optQueries.pages">\n							<nav class="sw-pagination__list fel-pagination-to-prev-steps" aria-label="fel-paginator-backwards">\n								<template v-for="pageNum, label in pagination.steps">\n									<template v-if="[\'first\',\'prev\'].includes(label)">\n										<a\n											:href="createLogLink(null, {page: pageNum})"\n											class="sw-pagination__page-button sw-pagination__page-button-prev fel-step-nav"\n											@click="pageNum != optQueries.page ? goToHref($event.target) : null"\n											:disabled="pageNum == null"\n											:title="$t(`fel-gpt-assistant.logs.${label}`)">\n											<template v-if="\'first\' == label">&laquo;&laquo;</template>\n											<template v-else>&laquo;</template>\n										</a>\n									</template>\n								</template>\n							</nav>\n							<ul class="sw-pagination__list fel-pagination-numbered-links" v-if="optQueries.pages">\n								<li v-for="pageNum in pagination.numbers"  class="sw-pagination__list-item">\n									<a\n										:href="createLogLink(null, {page: pageNum})"\n										:class="\'sw-pagination__list-button\' + (pageNum == optQueries.page ? \' is-active\' : \'\')"\n										@click="pageNum != optQueries.page ? goToHref($event.target) : null">\n										{{ pageNum }}\n									</a>\n								</li>\n							</ul>\n							<nav class="sw-pagination__list fel-pagination-to-next-steps" aria-label="fel-paginator-forward">\n								<template v-for="pageNum, label in pagination.steps">\n									<template v-if="[\'next\',\'last\'].includes(label)">\n										<a\n											:href="createLogLink(null, {page: pageNum})"\n											class="sw-pagination__page-button sw-pagination__page-button-prev fel-step-nav"\n											@click="pageNum != optQueries.page ? goToHref($event.target) : null"\n											:disabled="pageNum == null || pageNum == false"\n											:title="$t(`fel-gpt-assistant.logs.${label}`)">\n											<template v-if="\'next\' == label">&raquo;</template>\n											<template v-else>&raquo;&raquo;</template>\n										</a>\n									</template>\n								</template>\n							</nav>\n						</div>\n					</div>\n\n					{# pagination details #}\n					<div class="fel-pagination-details-wrapper">\n						<div class="sw-pagination sw-pagination__list fel-pagination-details">\n							<div>\n								{{ $t(\'fel-gpt-assistant.logs.page\') }}\n								<template v-if="pagination.numbersMap">\n									<select @change="goToUrl($event)" :title="$t(\'fel-gpt-assistant.logs.page\')">\n										<option\n											v-for="num in pagination.numbersMap"\n											:value="createLogLink(null, {page: num})"\n											:selected="num == optQueries.page"\n											:disabled="num == optQueries.page">\n											{{ num }} / {{ optQueries.pages }}\n										</option>\n									</select>\n								</template>\n								<template v-else>\n									{{ optQueries.page }} / {{ optQueries.pages }}\n								</template>\n							</div>\n							<div>\n								{{ $t(\'fel-gpt-assistant.logs.limit\') }}\n								<select @change="goToUrl($event)">\n									<option\n										v-for="num in logsLimitList"\n										:value="createLogLink(null, {limit: num})"\n										:selected="num == optQueries.limit"\n										:disabled="num == optQueries.limit">\n										{{ num }}\n									</option>\n								</select>\n							</div>\n							<div>\n								<select @change="goToUrl($event)">\n									<option\n										v-for="label in sortByList"\n										:value="createLogLink(null, {sort: label})"\n										:selected="label == optQueries.sort"\n										:disabled="label == optQueries.sort">\n										{{ $t(`fel-gpt-assistant.logs.ui.sort.${label}`) }}\n									</option>\n								</select>\n							</div>\n							<template v-if="!pageError.dontExists">\n								<div :title="$t(\'fel-gpt-assistant.logs.total\')">\n									<button\n										type="button"\n										:class="\'fel-opt-btn fel-toggle-filterable-options\' + (optQueries.filterable ? \' fel-contains-active\' : \'\')"\n										data-action="toggle"\n										data-target=".fel-filterable-options"\n										data-target-class="fel-is-hidden"\n										data-self-class="active"\n										data-active-if="not"\n										data-query-all="false"\n										@click="felMultiBtn($event)">\n										{{ repository.total }}\n									</button>\n								</div>\n								<div v-if="optQueries.pages">\n									<span\n										class="fel-opt-btn fel-toggle-more-options"\n										data-action="toggle"\n										data-target=".fel-more-options"\n										data-target-class="fel-is-hidden"\n										data-self-class="active"\n										data-active-if="not"\n										data-query-all="false"\n										@click="felMultiBtn($event)">\n										<sw-icon name="regular-bars-s" />\n									</span>\n								</div>\n							</template>\n						</div>\n						{# pagination options #}\n						<div class="fel-filterable-options fel-is-hidden" v-if="optQueries.pages">\n							<nav class="fel-fitlerable-container" aria-label="fel-paginator-options">\n								<div class="fel-fitlerable-wrapper" v-if="filterableCols.salesChannelId">\n									<div class="fel-fitlerable-header">\n										{{ $tc(\'fel-gpt-assistant.logs.ui.salesChannelId\') }}\n										<a\n											:href="createLogLink(null, {filterable: null})"\n											@click="goToHref($event.target)"\n											class="mt-button mt-button--primary-ghost mt-button--x-small"\n											:disabled="!optQueries.filterable"\n											>\n											{{ $tc(\'fel-gpt-assistant.general.reset\') }}\n										</a>\n									</div>\n									<ul>\n										<template v-for="salesChannelId in filterableCols.salesChannelId">\n											<li v-if="null != salesChannelId && \'\' != salesChannelId">\n												<a\n													:href="createLogLink(null, {filterable: `salesChannelId:${salesChannelId}`})"\n													@click="goToHref($event.target)"\n													:class="\'mt-button mt-button--small \' + (optQueries.filterable && optQueries.filterable.includes(salesChannelId) ? \'active mt-button--primary fel-link-disabled\' : \'\')">\n													{{ salesChannelId }}\n												</a>\n											</li>\n										</template>\n									</ul>\n								</div>\n								<div class="fel-fitlerable-wrapper" v-if="filterableCols.assistantId">\n									<div class="fel-fitlerable-header">\n										{{ $tc(\'fel-gpt-assistant.general.assistants\') }}\n									</div>\n									<ul>\n										<template v-for="assistantId in filterableCols.assistantId">\n											<li v-if="null != assistantId && \'\' != assistantId">\n												<a\n													:href="createLogLink(null, {filterable: `assistantId:${assistantId}`})"\n													@click="goToHref($event.target)"\n													:title="assistantId"\n													:class="\'mt-button mt-button--small \' + (optQueries.filterable && optQueries.filterable.includes(assistantId) ? \'active mt-button--primary fel-link-disabled\' : \'\')">\n													{{ \'undefined\' !== typeof assistantListMap[assistantId]\n														? assistantListMap[assistantId].name\n														: assistantId }}\n												</a>\n											</li>\n										</template>\n									</ul>\n								</div>\n							</nav>\n						</div>\n\n						{# more options (dowwnload / delete / etc) #}\n						<div class="fel-more-options fel-is-hidden" v-if="optQueries.pages">\n							<div class="fel-more-log-options">\n								<div class="fel-download-options sw-button-group" :title="$t(\'fel-gpt-assistant.logs.ui.options.downloadVisible\')">\n									<button class="sw-button sw-button--small fel-opt-btn fel-btn-info">\n										<sw-icon name="regular-download" />\n									</button>\n									<button\n										class="sw-button sw-button--small fel-download-as-json"\n										@click="exportVisibleToFile(\'gpt-chatbot-log.json\')">\n										JSON\n									</button>\n									<button class="sw-button sw-button--small fel-download-as-csv"\n										@click="exportVisibleToFile(\'gpt-chatbot-log.csv\')">\n										CSV\n									</button>\n								</div>\n								<div class="fel-delete-options sw-button-group">\n									<button class="sw-button sw-button--small fel-opt-btn fel-btn-info"\n										:title="$t(\'fel-gpt-assistant.logs.ui.options.deleteVisible\')">\n										<sw-icon name="regular-trash" />\n									</button>\n									<button class="sw-button sw-button--small sw-button--danger"\n										@click="deleteVisibleLogs()">\n										{{ $t(\'fel-gpt-assistant.logs.ui.options.deleteVisible\') }}\n									</button>\n								</div>\n							</div>\n						</div>\n\n					</div>\n				</div>\n			</template>\n\n			<mt-card class="fel-mt-3" positionIdentifier="index">\n				<div class="fel-content-box">\n					<p class="fel-size-xxs">{{ $t(\'fel-gpt-assistant.logs.mainDescription\') }}</p>\n				</div>\n			</mt-card>\n\n		</template>\n	</sw-page>\n{% endblock %}\n',inject:["repositoryFactory"],mixins:[h.getByName("notification"),h.getByName("fel-plugin-config-mixin"),h.getByName("fel-openai-mixin"),h.getByName("fel-paginator-mixin"),h.getByName("fel-export-to-file"),h.getByName("fel-helper-mixin")],data(){return{isLoading:!0,pageTitleInfo:null,repository:[],pagination:{},prefetchFilterable:{},columnNames:["id","salesChannelId","assistantId","threadId","threadDeleted","createdAt","updatedAt","threadMessages","errors"],filterableCols:{salesChannelId:{},assistantId:{}},logsLimitList:[1,5,10,25,50,100,500],sortByList:["createdAt_desc","createdAt_asc","updatedAt_desc","updatedAt_asc"],pageError:{dontExists:!1},logAssistantList:[]}},created(){this.getPluginConfig().then(e=>{this.fetchAssistants(),this.fetchLogs()})},computed:{logsRepository(){return this.repositoryFactory.create("fel_openai_assistants_log")}},methods:{exportVisibleToFile(e){let t=[];this.repository.forEach((e,s)=>{void 0!==e.id&&t.push(e)}),this.exportToFile(e,t,this.columnNames)},deleteVisibleLogs(){confirm(this.$tc("fel-gpt-assistant.logs.ui.options.deleteVisibleConfirm"))&&new Promise((e,t)=>{this.repository.forEach((t,s)=>{void 0!==t.id&&this.logsRepository.delete(t.id,g.api).then(()=>{s===this.repository.length-1&&e()})})}).then(()=>{location.reload()})},deleteLogEntry(e){if(confirm(this.$tc("fel-gpt-assistant.logs.ui.deleteThisLogEntry"))){let t=e.target,s=t.dataset.deleteId;s&&this.logsRepository.delete(s,g.api).then(()=>{var e=t.parentNode.parentNode.parentNode;"row"===e.getAttribute("role")&&(e.remove(),location.reload())})}return!1},goToHref(e){return this.goToUrl(e.href)},goToUrl(e){return"object"==typeof e?(e=e.target.value,location.href=e,location.reload()):(this.$router.push(e),this.fetchLogs())},getFilterableColIds(e){let t=new v;return t.addGrouping(e),this.logsRepository.search(t,g.api).then(t=>{let s=[];if(void 0!==t.length)for(var n=0;n<t.length;n++)s.push(t[n][e]);this.prefetchFilterable[e]=s})},prefetchFilterableCols(){for(var e=Object.keys(this.filterableCols),t=0;t<e.length;t++)this.getFilterableColIds(e[t]);this.filterableCols=this.prefetchFilterable},fetchLogs(){let e=new v;this.prefetchFilterableCols(),this.refreshOptQueries(),e.setTotalCountMode(1),e.setPage(this.optQueries.page),e.setLimit(this.optQueries.limit),this.optQueries.sort||(this.optQueries.sort=this.sortByList[0]);let[t,s]=this.optQueries.sort.split("_");if(s&&["asc","desc"].includes(s.toLowerCase())&&this.sortByList.includes(this.optQueries.sort)&&e.addSorting(v.sort(t,s)),void 0!==this.optQueries.setFilterCriteria){var n=this.optQueries.setFilterCriteria;for(let t=0;t<n.length;t++){let s=n[t];s.filterName&&s.filterIds&&e.addFilter(v.equalsAny(s.filterName,s.filterIds))}}this.logsRepository.search(e,g.api).then(async e=>{if(void 0!==e.length){for(var t=0;t<e.length;t++){let s=e[t];try{s.createdAt=new Date(Date.parse(s.createdAt)).toUTCString(),s.updatedAt&&(s.updatedAt=new Date(Date.parse(s.updatedAt)).toUTCString())}catch(e){}if(s.threadMessages)try{s.threadMessages=JSON.parse(s.threadMessages).reverse()}catch(e){s.threadMessages=[{value:e.message,datetime:new Date().toUTCString(),role:"system"}]}if(s.errors)try{s.errors=JSON.parse(s.errors)}catch(e){s.errors=[{value:e.message}]}}this.repository=e,this.optQueries.pages=Math.ceil(e.total/this.optQueries.limit),this.optQueries.currentLength=this.repository.length,this.optQueries.currentTotal=this.repository.total,this.pagination=this.getLogPagination(),this.pageTitleInfo=`${this.optQueries.page}/${this.optQueries.pages} (${this.optQueries.currentTotal})`,this.pageError=this.pagination.pageError}})}},metaInfo(){return{title:`${this.$tc("fel-gpt-assistant.logs.page")} ${this.pageTitleInfo} - ${this.$createTitle()}`}}});let{Component:b,Mixin:w}=Shopware;b.register("fel-gpt-assistant-vector-store",{template:'{% block fel_gpt_assistant_vector_store %}\n	<sw-page class="fel-gpt-assistant-vector-store fel-assistant">\n\n		<template #smart-bar-actions>\n			<template v-if="detailPage">\n				<mt-button\n					variant="critical"\n					v-if="!isDeleted"\n					v-html="$t(\'fel-gpt-assistant.delete.delete\')"\n                    @click="deleteVectorStore(vectorStore.id)" />\n			</template>\n		</template>\n\n		<template #content>\n\n			<div class="fel-sw-card-view">\n\n				<mt-card positionIdentifier="index">\n					<h2 class="fel-title fel-flex fel-flex-space-between fel-flex-align-center fel-my-0">\n						{{ vectorStore.name }} <small>{{ vectorStore.id }}</small>\n					</h2>\n				</mt-card>\n\n				{# if delete, back to index #}\n				<mt-card v-if="isDeleted && !isLoading" positionIdentifier="index">\n					<mt-button\n						variant="primary"\n						ghost="true"\n						v-html="$t(\'fel-gpt-assistant.delete.backToIndex\')"\n						@click="this.$router.push({name: \'fel.gpt.assistant.index\'})" />\n				</mt-card>\n\n				{# files manager container #}\n				<div class="fel-file-manager-container fel-flex" v-if="!isDeleted && vectorStore.id">\n					{# attach files #}\n					<div class="fel-file-manager-attach-files fel-man-inner">\n						<h2 class="fel-flex-space-between fel-flex-align-center fel-title">\n							{{ $t(\'fel-gpt-assistant.files.vectorStore.attachFiles\') }}\n							<small>{{ filesList.data ? Object.keys(filesList.data).length - vectorStoreFiles.length : 0 }}</small>\n						</h2>\n						<div class="mt-block-field__block">\n							<select id="fel--sw--assistant_files"\n								class="fel-multi-select fel-w-100"\n								name="fel--sw--assistant_files"\n								v-model="toSubmit[\'fel--sw--assistant_files\']"\n								@change="onAssistantInput($event)"\n								multiple>\n								<template v-for="data in filesList.data">\n									<option\n										:value="data.id"\n										v-if="!vectorStoreFilesList.includes(data.id)"\n										v-html="data.filename + \' | \' + data.id">\n									</option>\n								</template>\n							</select>\n						</div>\n						<div class="mt-block-reset-files-sel fel-oai-files-wrapper fel-flex-space-between">\n							<mt-button\n								class="fel-attach-selected-files"\n								variant="primary"\n								size="small"\n								:disabled="!toSubmit[\'fel--sw--assistant_files\'].length"\n								:isLoading="false"\n								v-html="$t(\'fel-gpt-assistant.files.vectorStore.attachFiles\')"\n								@click="attachFilesToVectorStore(\'#fel--sw--assistant_files\')" />\n							<mt-button\n								class="fel-reset-files-select"\n								size="small"\n								:block="true"\n								v-html="$t(\'fel-gpt-assistant.general.reset\')"\n								@click="resetSelect(\'#fel--sw--assistant_files\')" />\n							<label for="fel-sw-upload-file"\n								class="mt-button mt-button--small"\n								:title="`${$t(\'fel-gpt-assistant.general.uploadFile\')}: ${allowedFileTypes.join(\', \')}`">\n								<mt-icon name="regular-upload" size="16px" />\n								<input\n									type="file"\n									id="fel-sw-upload-file"\n									name="fel-sw-upload-file"\n									:accept="allowedFileTypes.join(\',\')"\n									@change="uploadFile($event, true)">\n							</label>\n						</div>\n					</div>\n\n					{# attached files #}\n					<div class="fel-file-manager-attached-files fel-man-inner">\n						<h2 class="fel-flex-space-between fel-flex-align-center fel-title">\n							{{ $t(\'fel-gpt-assistant.files.filesAttached\') }}\n							<small v-if="vectorStoreFiles">{{ vectorStoreFiles.length }}</small>\n						</h2>\n						<div class="sw-block-field__block">\n							<select id="fel--sw--assistant_attached_files"\n								class="fel-multi-select fel-w-100"\n								name="fel--sw--assistant_attached_files"\n								@change="onAssistantInput($event)"\n								size="5">\n								<template v-for="data in vectorStoreFiles">\n									<option :value="data.id">\n										<template v-if="filesList.data && \'undefined\' !== typeof filesList.data[data.id][\'filename\']">\n											{{ filesList.data[data.id][\'filename\'] }} |\n										</template>\n										{{ data.id }}\n									</option>\n								</template>\n							</select>\n						</div>\n						<div class="sw-block-reset-files-sel fel-oai-files-wrapper fel-flex-space-between">\n							<mt-button\n								class="fel-detach-selected-files"\n								variant="primary"\n								:disabled="!toSubmit[\'fel--sw--assistant_attached_files\'].length || !vectorStoreFiles.length"\n								v-html="$t(\'fel-gpt-assistant.files.vectorStore.detachFiles\')"\n								@click="detachFilesFromVectorStore(\'#fel--sw--assistant_attached_files\')" />\n							<mt-button\n								class="fel-reset-files-select fel-reset-attached-files fel-m-0"\n								:block="true"\n								v-html="$t(\'fel-gpt-assistant.general.reset\')"\n								@click="resetSelect(\'#fel--sw--assistant_attached_files\')" />\n						</div>\n					</div>\n				</div>\n\n				{# vector store object #}\n				<mt-card v-if="!isDeleted && vectorStore.id" positionIdentifier="index">\n                    <pre class="fel-mb-1">{{ vectorStore }}</pre>\n				</mt-card>\n\n			</div>\n\n		</template>\n\n	</sw-page>\n{% endblock %}\n',mixins:[w.getByName("notification"),w.getByName("fel-input-helper-mixin"),w.getByName("fel-openai-mixin"),w.getByName("fel-plugin-config-mixin")],data(){return{isDeleted:!1,vectorStore:{},vectorStoreFiles:{},vectorStoreFilesList:[],file:{},data:{},vectorStoreId:this.$route.params.id,vectorStoreUrl:null}},created(){this.detailPage=!0,this.getPluginConfig().then(e=>{this.vectorStoreUrl=`${e.apiUrl}/vector_stores/${this.vectorStoreId}`,this.fetchVectorStores(),this.fetchFiles(),this.fetchAssistants(),this.fetchVectorStore(),this.fetchVectorStoreFiles()})},methods:{refreshVectorStore(){this.fetchVectorStore(),this.fetchVectorStoreFiles()},detachFilesFromVectorStore(e){var t=this.$el.querySelector(`${e} :checked`);t&&this.httpClientDelete(`${this.vectorStoreUrl}/files/${t.value}`,e=>{e.data.deleted?setTimeout(()=>{this.vectorStoreFilesList=[],this.fetchVectorStoreFiles()},1e3):this.createNotificationError({message:"error"})})},attachFilesToVectorStore(e){var t=this.$el.querySelectorAll(`${e} :checked`),s=[];if(t.length)for(var n=0;n<t.length;n++)s.push(t[n].value);this.httpClientPost(`${this.vectorStoreUrl}/file_batches`,{file_ids:s},e=>{this.fetchVectorStore(),this.fetchVectorStoreFiles()})},fetchVectorStore(){this.httpClientGet(`${this.vectorStoreUrl}`,e=>{this.vectorStore=e.data})},fetchVectorStoreFiles(){this.httpClientGet(`${this.vectorStoreUrl}/files`,e=>{if(e.data&&void 0!==e.data.data){this.vectorStoreFiles=e.data.data;for(var t=0;t<this.vectorStoreFiles.length;t++)this.vectorStoreFilesList.push(this.vectorStoreFiles[t].id)}})},deleteVectorStore(e){if(!0==!confirm(`${this.$t("fel-gpt-assistant.delete.delete")}?`))return!1;e&&this.httpClientDelete(`${this.pluginConfig.apiUrl}/vector_stores/${e}`,t=>{void 0!==t.data&&t.data.deleted?(this.isDeleted=!0,this.createNotificationSuccess({message:e})):this.createNotificationError({message:"error"})})}},metaInfo(){return{title:this.$createTitle()}}});var _=JSON.parse('{"fel-gpt-assistant":{"general":{"assistant":"Assistent","assistants":"Assistenten","assistantId":"Assistent-ID","assistantName":"Assistenten-Name","configuration":"Konfiguration","create":"Erstellen","createAssistant":"Assistent erstellen","delete":"L\xf6schen","deleted":"Gel\xf6scht","description":"Diese Seite fungiert als Vermittler zwischen Ihnen und Ihrem OpenAI-Konto, das mit Ihrem OpenAI-API-Schl\xfcssel verbunden ist.","example":"Beispiel","fixIt":"L\xf6sen","goToConfiguration":"Konfiguration","mainMenuItemGeneral":"Benutzerdefinierter GPT-Assistent","message":"Nachricht","messages":"Chat-Nachrichten","missingOpenAiKey":"Fehlender OpenAI-API-Schl\xfcssel","noDataOnServerNote":"Die Daten auf dieser Seite werden auf den OpenAI-API-Servern gespeichert und verwaltet.","openAIApiKey":"OpenAI-API-Schl\xfcssel","openAIAssistants":"OpenAI-Assistenten","openAIFiles":"OpenAI-Dateien","openAIStatus":"OpenAI Status","openAITokenizer":"OpenAI-Tokenisierer","openAIUsage":"OpenAI-Nutzung","showFunctions":"Funktionen anzeigen","updateAssistant":"Aktualisieren","uploadFile":"Dateien zu OpenAI hochladen","usefulLinks":"N\xfctzliche Links","reset":"Zur\xfccksetzen","temperature":"Temperatur","temperatureDescription":"Die Einstellung „Temperatur“ passt die Zuf\xe4lligkeit der Antworten an. Eine niedrigere Temperatur f\xfchrt zu vorhersehbareren und konsistenteren Antworten. Die Skala reicht von 0,1 bis 2, wobei 2 eine hohe Zuf\xe4lligkeit anzeigt.","requiredApiKeyInfo":"F\xfcr den Zugriff auf diese Seite wird ein g\xfcltiger OpenAI-API-Schl\xfcssel ben\xf6tigt, der f\xfcr „Alle Verkaufskan\xe4le“ freigegeben ist. Wenn Sie mit der Einrichtung ihrer Assistenten fertig sind, k\xf6nnen sie den globalen Schl\xfcssel wieder l\xf6schen. Dar\xfcber hinaus haben Sie die M\xf6glichkeit, jedem Verkaufskanal einen individuellen Schl\xfcssel zuzuweisen.","noAssistantsFound":"Keine Assistenten f\xfcr den angegebenen OpenAI-API-Schl\xfcssel gefunden.","aiToolsTranslation":{"category_product_search":"Search products in categories.","get_meta_information":"Erhalte alle Informationen, die du \xfcber den Shop wissen musst","get_manufacturer":"Hersteller abrufen","get_product_properties":"Liste der Eigenschafts-und Hersteller-IDs mit den zugeh\xf6rigen Namen, wie sie vom Shop verwendet werden, abrufen","get_product_details":"Produktdetails abrufen","product_search":"F\xfchre eine Produktsuche mit der vom Benutzer bereitgestellten Texteingabe durch","get_date_time":"Datum, Uhrzeit und Zeitzone abrufen","go_to_url":"Weiterleitung zu einer URL. Nur einmal aufrufen und als letzte Funktion","get_categories":"Kategorien abrufen","get_countries":"L\xe4nder abrufen","get_delivery_times":"Lieferzeiten abrufen","get_payment_methods":"Zahlungsmethoden abrufen","get_chatbot_name":"Chatbot-Namen abrufen","get_product_properties_extended":"Liste mit gruppierten Produkteigenschaften und -optionen, die vom Shop verwendet werden, abrufen."}},"config":{"configPage":"Konfigurationsseite","configPageNote":"Diese Seite zeigt Abschnitte der Konfigurationseinstellungen an, die es uns erm\xf6glichen, Unterst\xfctzung bei bestimmten Konfigurationen zu bieten.","chatbotName":"Chatbot-Name","chatbotNameInfo":"Der hier eingegebene Name wird als Chatbot-Name neben dem Avatar angezeigt. Mithilfe der sogenannten „Instructions“ k\xf6nnen Sie Ihrem Chatbot einen Namen geben und ihm sogar Charaktereigenschaften zuweisen.","fileAlternate":"Datei-Alternative","fileAlternateInfo":"Sie k\xf6nnen dieses Feld verwenden, wenn Sie ihrem Assistenten keine Dateien zuweisen m\xf6chten. Der Assistent bestimmt eigenst\xe4ndig, wann er die Daten braucht und fordert sie entsprechend an. Diese Herangehensweise erm\xf6glicht flexible Datenanpassungen ohne den Aufwand, den Dateien mit sich bringen. Stellen Sie sicher, dass Sie die Funktion „Erhalte alle Informationen, die du \xfcber den Shop wissen musst“ f\xfcr den Assistenten aktivieren.","fileAlternateInfoExtra":"In den Beispieldaten finden Sie „BLOCKs“, die bestimmten Funktionen entsprechen, die individuell f\xfcr jeden Assistenten aktiviert werden k\xf6nnen. Wenn Sie diese Funktionen nicht nutzen m\xf6chten, f\xfcllen Sie bitte die entsprechenden BLOCKs mit relevanten Daten. Andernfalls entfernen Sie alle unn\xf6tigen BLOCKs."},"files":{"vectorStore":{"name":"Vektor-Speicher","setName":"Vektor-Speicher Name","createVectorStore":"Vektor-Speicher erstellen","noFilesAttached":"Keine Dateien angeh\xe4ngt","attachFiles":"Dateien anh\xe4ngen","detachFiles":"Dateien abtrennen","filenameInvalid":"Dateiname enth\xe4lt ung\xfcltige Zeichen","fileIsEmpty":"Datei ist leer","refreshStore":"Aktualisieren","attachVectorStoreToAssistant":"Einen Vektor-Speicher an Ihren Assistenten anh\xe4ngen.","aboutVectorStore":"Vektor-Speicherobjekte erm\xf6glichen dem Dateisuchtool das Durchsuchen Ihrer Dateien. Das Hinzuf\xfcgen einer Datei zu einem Vektor-Speicher verarbeitet diese automatisch, segmentiert, bettet sie ein und speichert sie in einer Vektordatenbank, die sowohl Stichwort- als auch semantische Suche unterst\xfctzt. Jeder Vektor-Speicher kann bis zu 10.000 Dateien aufnehmen. Vektor-Speicher k\xf6nnen sowohl an Assistenten als auch an Threads angeh\xe4ngt werden. Derzeit k\xf6nnen Sie maximal einen Vektor-Speicher an einen Assistenten anh\xe4ngen."},"allowedFileTypes":"Erlaubte Dateitypen","canNotDownloadFile":"Nicht berechtigt, Dateien f\xfcr Assistenten herunterzuladen (OpenAI sagt nein)","filesAttached":"Angehangene Dateien:","fileIsInUse":"In Verwendung","files":"Dateien","saveFile":"Als Datei auf Ihrem Computer speichern","fileAlternateNote":"Das Plugin bietet ein Konfigurationsfeld als Alternative zum Datei-Upload, das die notwendigen Daten speichert. Diese Daten k\xf6nnen vom Chatbot bei Bedarf \xfcber einen Funktionsaufruf abgerufen werden. Diese Funktion erm\xf6glicht eine dynamischere Interaktion mit den Daten, sodass sie jederzeit f\xfcr den Chatbot abrufbar und in Echtzeit nutzbar sind.","uploadHelp":"Um sicherzustellen, dass Ihr Chatbot mit allen notwendigen Informationen ausgestattet ist, erstellen Sie bitte eine umfassende Liste, die das spezifische Wissen, die Daten oder Anweisungen, die Sie einbeziehen m\xf6chten, detailliert beschreibt. Sie k\xf6nnen bspw. Inhalte, wie Ihre FAQ-Seite, direkt in die Datei kopieren und einf\xfcgen, ohne \xc4nderungen vorzunehmen. Denken Sie an die Datei als eine detaillierte Brosch\xfcre, die alle wesentlichen Informationen bereitstellt, die ein Mitarbeiter ben\xf6tigen w\xfcrde, um Kundenanfragen zu beantworten."},"create":{"dynamicFileSettedButFunctionDisabled":"Sie verwenden dynamische Inhalte, aber die Funktion zum Zugriff auf diese Daten ist derzeit deaktiviert. Aktivieren Sie die Funktion: „Erhalte alle Informationen, die du \xfcber den Shop wissen musst“","selectAllowedFunctions":"Chatbot-F\xe4higkeiten anpassen: W\xe4hlen Sie aus, welche Funktionen der Chatbot nutzen darf","selectAllowedFunctionsInfo":"Diese Funktionen verwenden die Standarddaten von Shopware. Lassen Sie sie deaktiviert, wenn Sie die Informationen bereits in hochgeladenen Dateien oder im dynamischen Feld auf der Konfigurationsseite haben.","selectFilesForAssistant":"W\xe4hlen Sie Dateien aus, die der Chatbot verwenden soll","selectFilesForAssistantInfo":"Die maximale Anzahl Dateien pro Assistent betr\xe4gt 20, die zul\xe4ssige Dateigr\xf6\xdfe liegt bei rund 500 MB pro Datei.","selectFilesForAssistantInfoHelp":"500 MB entsprechen etwa dem Inhalt von 30 bis 50 B\xfcchern. Wenn jede Zeile 100 Zeichen enth\xe4lt, m\xfcssten Sie rund 5 Millionen (+-500.000) Zeilen schreiben, um 500 MB voll zu kriegen. Das ist ziemlich viel Inhalt, selbst mit Copy&Paste noch ein Akt. Das entspricht ungef\xe4hr 20 bis 25 Token pro Zeile, multipliziert mit rund 5 Millionen Zeilen. Und das ist nur eine von m\xf6glichen 20 Dateien. Falls Sie trotz der dynamischen Alternative f\xfcr Dateien auf der Konfigurationsseite Dateien verwenden m\xf6chten, ziehen Sie einfache Textformate wie „TXT“ vor. Formate wie „PDF“ sind extrem aufgebl\xe4ht, um ein f\xfcr Menschen lesbares Format sicher zu stellen. F\xfcr die KI k\xf6nnen wir uns das sparen, der gen\xfcgt blanker Text im einfachsten Format.","selectModelToUse":"Modell ausw\xe4hlen","setAsDefaultAssistant":"Als Standard festlegen","setInstructions":"Anweisungen","setInstructionsInfo":"Bitte passen Sie die Anweisungen nach Ihren Bed\xfcrfnissen an. Wenn Sie Dateien verwenden, stellen Sie sicher, dass Sie dies in den Anweisungen deutlich machen, damit die KI die Dateien im Anhang ber\xfccksichtigen kann. Es ist wichtig, dass die KI explizit darauf hingewiesen wird, um eine optimale Verarbeitung zu gew\xe4hrleisten. Beachten Sie jedoch, dass das Modell gpt-3.5-turbo Dateien nicht effektiv unterst\xfctzt. Obwohl es keine Fehler verursacht, Dateien anzuh\xe4ngen, kann die KI diese nicht laden und es k\xf6nnen andere Fehler auftreten.","gptThreeFileReadingInfo":"Bitte beachten Sie, dass das Modell „gpt-3.5-turbo“ Datei-Anh\xe4nge nicht wirklich unterst\xfctzt. Die Dateien werden nicht oder nur willk\xfcrlich geladen und selbst die Anweisungen funktionieren nicht mehr wie ohne Datei-Anhang. Um diese Probleme zu vermeiden, nutzen Sie das Feld „Datei-Alternative“ in der Plugin-Konfiguration, das Ihnen erlaubt, den Gebrauch von Dateien komplett zu umgehen.","form":{"setAssistantName":"Assistenten-Name","setAssistantNameHelp":"Nur f\xfcr interne Zwecke"}},"delete":{"backToIndex":"Zur\xfcck zur \xdcbersicht","delete":"L\xf6schen","deleteAssistant":"Sie sind dabei, den Assistenten zu l\xf6schen"},"error":{"howDoIGetNewerModels":"Wie kann ich auf GPT-4 zugreifen?","modelIsLimited":"Um die volle Funktionalit\xe4t zu gew\xe4hrleisten, empfehlen wir die Auswahl von GPT-4 oder einer neueren Version.","newModelsNotAvailable":"Ihr Konto hat keinen Zugriff auf Modelle wie GPT-4 und h\xf6her.","functionsNeedNewerModel":"Die Produktsuche und die Abfrage von Produktdetails funktionieren nur wie erwartet mit den Modellen GPT-4 und neueren Versionen."},"logs":{"mainMenuItemGeneral":"Protokolle","mainDescription":"Die hier angezeigten Chat-Protokolle stellen die endg\xfcltigen, optimierten Gespr\xe4che dar, die von der OpenAI API automatisch verfeinert werden, wenn die Chats zu lang werden. Im Chatbox-Interface jedoch werden die Chats genau so aufgezeichnet und angezeigt, wie sie stattfinden, ohne jegliche \xc4nderungen.","page":"Seite","pages":"Seiten","limit":"Limit","total":"Gesamt","first":"Erste Seite","prev":"Vorherige Seite","next":"N\xe4chste Seite","last":"Letzte Seite","ui":{"salesChannelId":"SalesChannelId","pageError":{"dontExists":"Seite nicht gefunden"},"deletedByUser":"Chat wurde vom Benutzer gel\xf6scht","chatIsPending":"Chat ist offen, noch nicht gel\xf6scht","deleteThisLogEntry":"Diesen Protokolleintrag l\xf6schen","threadMessages":"Threads","options":{"deleteVisible":"Sichtbare l\xf6schen","deleteVisibleConfirm":"Alle sichtbaren Protokolleintr\xe4ge l\xf6schen?","downloadVisible":"Sichtbare herunterladen","noLogsNoOptions":"Keine Protokolle gefunden"},"sort":{"createdAt_desc":"Erstellt absteigend","createdAt_asc":"Erstellt aufsteigend","updatedAt_desc":"Aktualisiert absteigend","updatedAt_asc":"Aktualisiert aufsteigend"}}},"api-test-button":{"assistantIdCheckOk":"Assistenten-ID erfolgreich \xfcberpr\xfcft.","assistantIdCheckFailed":"Assistenten-ID-Pr\xfcfung fehlgeschlagen","title":"API-Test","success":"Verbindung wurde erfolgreich getestet","error":"Verbindung konnte nicht hergestellt werden. Bitte \xfcberpr\xfcfen Sie die Zugangsdaten","button":"API Verbindung testen","Key validation failed":"Der OpenAI-API-Schl\xfcssel ist ung\xfcltig, erwartetes Muster: sk-..."}}}'),y=JSON.parse('{"fel-gpt-assistant":{"general":{"assistant":"Assistant","assistants":"Assistants","assistantId":"Assistant ID","assistantName":"Assistant Name","configuration":"Configuration","delete":"Delete","deleted":"Deleted","create":"Create","createAssistant":"Create Assistant","description":"This page acts as a middleman between you and your OpenAI Account, which is connected to your OpenAI API Key.","example":"Example","fixIt":"Fix it","goToConfiguration":"Config","mainMenuItemGeneral":"AI Product Advisor","message":"Message","messages":"Messages","missingOpenAiKey":"Missing OpenAI API Key","noDataOnServerNote":"The data on this page is saved and managed on the OpenAI API Servers.","openAIApiKey":"OpenAI API Keys","openAIAssistants":"OpenAI Assistants","openAIFiles":"OpenAI Files","openAIStatus":"OpenAI Status","openAITokenizer":"OpenAI Tokenizer","openAIUsage":"OpenAI Usage","showFunctions":"Show Functions","updateAssistant":"Update","uploadFile":"Upload Files to OpenAI","usefulLinks":"Useful links","reset":"Reset","temperature":"Temperature","temperatureDescription":"The \\"Temperature\\" setting adjusts the randomness of responses. A lower temperature results in more predictable and consistent responses. The scale ranges from 0.1 to 2, with 2 indicating a high level of randomness.","requiredApiKeyInfo":"To access this page, a valid OpenAI API key authorized for \\"All Sales Channels\\" is required. Additionally, you have the option to assign an individual key to each Sales Channel.","noAssistantsFound":"No assistants were detected for the provided OpenAI API Key.","aiToolsTranslation":{"category_product_search":"Search products in categories","get_meta_information":"Get all the information you need to know about the Shop","get_manufacturer":"Get manufacturer","get_product_properties":"Get a list of property and manufacturer IDs with their names as used by the shop","get_product_details":"Get product details","product_search":"Perform a product search with the user-provided text query","get_date_time":"Get date, time and timezone","go_to_url":"Redirect to a URL. Call only once and as last function","get_categories":"Get categories","get_countries":"Get countries","get_delivery_times":"Get delivery times","get_payment_methods":"Get payment methods","get_chatbot_name":"Get chatbot name","get_product_properties_extended":"Get a list of grouped product properties and attributes utilized by the shop"}},"config":{"configPage":"Configuration Page","configPageNote":"This page displays sections of the configuration settings, enabling us to provide assistance with certain preferences and configurations.","chatbotName":"Chatbot Name","chatbotNameInfo":"The name entered here will be displayed as the chatbot name next to the avatar. With the help of the so-called “Instructions” you can give your chatbot a name and give it even more character traits.","fileAlternate":"File alternate","fileAlternateInfo":"You can utilize this field if you prefer not to attach files to your assistant. The assistant will autonomously determine when to load the data. This approach allows for flexible data adjustments without the hassle of handling files. Be sure to enable the \'Get all the information you need to know about the Shop\' function for the Assistant.","fileAlternateInfoExtra":"In the example data, you\'ll find \'BLOCKs\' corresponding to specific functions that can be activated individually for each assistant. If you choose not to utilize these functions, please populate the respective BLOCKs with relevant data. Otherwise, feel free to remove any unnecessary BLOCKs."},"files":{"vectorStore":{"name":"Vector Store","setName":"Vector Store Name","createVectorStore":"Create a Vector Store","noFilesAttached":"No attached files","attachFiles":"Attach files","detachFiles":"Detach files","filenameInvalid":"Filename contains invalid character","fileIsEmpty":"File is empty","refreshStore":"Refresh","attachVectorStoreToAssistant":"Attach a Vector Store to your Assistant.","aboutVectorStore":"Vector Store objects give the File Search tool the ability to search your files. Adding a file to a vector_store automatically parses, chunks, embeds and stores the file in a vector database that\'s capable of both keyword and semantic search. Each vector_store can hold up to 10,000 files. Vector stores can be attached to both Assistants and Threads. Today, you can attach at most one vector store to an assistant."},"allowedFileTypes":"Allowed file types","canNotDownloadFile":"Not allowed to download files of purpose: assistants","filesAttached":"Attached files","fileIsInUse":"In use","files":"Files","saveFile":"Save as file on your Computer","fileAlternateNote":"The plugin offers an alternative to file upload by providing a configuration field that stores the necessary data. This data can be accessed by the chatbot through a function call whenever needed. This feature allows for more dynamic interaction with the data, making it readily available for the chatbot to retrieve and use in real-time.","uploadHelp":"To ensure your chatbot is fully equipped with all necessary information, please compile a comprehensive list detailing the specific knowledge, data, or instructions you wish to include. You may copy and paste content, such as your FAQ page, into the file without any modifications. Think of the file as a detailed brochure that provides all essential information an employee would need to accurately respond to customer inquiries."},"create":{"dynamicFileSettedButFunctionDisabled":"You are utilizing dynamic content, yet the function required to access the data is currently disabled. Please allow the function: ","selectAllowedFunctions":"Customize Chatbot Abilities: Choose Which Functions to Enable","selectAllowedFunctionsInfo":"These functions use Shopware\'s standard data; Leave it disabled if you already have the information in uploaded files or in the dynamic field on the configuration page.","selectFilesForAssistant":"Select Files the Chatbot should use","selectFilesForAssistantInfo":"The maximum number of files per assistant is limited to 20, the permitted file size is around 500 MB per file.","selectFilesForAssistantInfoHelp":"500 MB corresponds to roughly the content of 30 to 50 books. If each line contains 100 characters, you would need to write around 5 million (+-500,000) lines to fill 500 MB. That\'s quite a lot of content, even with copy and paste. This equates to approximately 20 to 25 tokens per line, multiplied by around 5 million lines. And that\'s just one of potentially 20 files. If you still wish to use files despite the dynamic alternative for files on the configuration page, prefer simple text formats such as \\"TXT\\". Formats like \\"PDF\\" are excessively bloated to maintain a human-readable format, which is unnecessary for the AI. Plain text suffices for the AI.","selectModelToUse":"Select the Model","setAsDefaultAssistant":"Set as default","setInstructions":"Instructions","setInstructionsInfo":"Please adjust the instructions according to your needs. If you are using files, make sure to clearly indicate this in the instructions so that the AI can take the files in the attachment into account. It is important to explicitly inform the AI to ensure optimal processing. However, note that the GPT-3.5-Turbo model does not effectively support files. Although attaching files does not cause errors, the AI cannot load them, and other errors may occur.","gptThreeFileReadingInfo":"Please be aware that the \'gpt-3.5-turbo\' model does not support file attachments as effectively as the \'gpt-4-turbo\' model. It may fail to read them completely or encounter other issues. To avoid these problems, use the \'File Alternate\' field in the Plugin Configuration, which allows you to bypass the need for file attachments altogether.","form":{"setAssistantName":"Assistant name","setAssistantNameHelp":"For internal use only"}},"delete":{"backToIndex":"Back to overview","delete":"Delete","deleteAssistant":"You are about to delete the Assistant"},"error":{"howDoIGetNewerModels":"How can I access GPT-4?","modelIsLimited":"To ensure full functionality, we recommend selecting GPT-4 or a newer version.","newModelsNotAvailable":"Your account does not have access to models like GPT-4 and beyond.","functionsNeedNewerModel":"The product search and querying product details only function as expected with models GPT-4 and newer."},"logs":{"mainMenuItemGeneral":"Chat Logs","mainDescription":"The chat logs displayed on this page represent the final, optimized conversations, which the OpenAI API automatically refines when chats become excessively lengthy. In the chatbox interface, however, chats are recorded and shown exactly as they occur, with custom modifications.","page":"page","pages":"pages","limit":"limit","total":"total","first":"First Page","prev":"Previous Page","next":"Next Page","last":"Last Page","ui":{"salesChannelId":"SalesChannelId","pageError":{"dontExists":"Page not found"},"deletedByUser":"Chat was deleted by the user","chatIsPending":"Chat is active and not deleted","deleteThisLogEntry":"Remove This Entry","threadMessages":"Thread Messages","options":{"deleteVisible":"Delete Visible","deleteVisibleConfirm":"Are you sure you want to delete all visible entries?","downloadVisible":"Download Visible","noLogsNoOptions":"No logs found"},"sort":{"createdAt_desc":"Created desc","createdAt_asc":"Created asc","updatedAt_desc":"Updated desc","updatedAt_asc":"Updated asc"}}},"api-test-button":{"assistantIdCheckOk":"Assistant ID check passed","assistantIdCheckFailed":"Assistant ID check failed","title":"API Test","success":"Connection was successfully tested","error":"Connection could not be established. Please check the access data","button":"Test API connection","Key validation failed":"OpenAI API Key is not valid, expected pattern: sk-..."}}}');Shopware.Module.register("fel-gpt-assistant",{type:"plugin",name:"Example",title:"fel-gpt-assistant.general.mainMenuItemGeneral",description:"GPT Assistant Helper",color:"#ff3d58",icon:"regular-products",snippets:{"de-DE":_,"en-GB":y},routes:{logs:{component:"fel-gpt-assistant-logging-index",path:"logs",meta:{parentPath:"fel.gpt.assistant.index"}},index:{component:"fel-gpt-assistant-index",path:"index"},detail:{component:"fel-gpt-assistant-detail",path:"detail/:id",meta:{parentPath:"fel.gpt.assistant.index"}},delete:{component:"fel-gpt-assistant-delete",path:"delete/:id",meta:{parentPath:"fel.gpt.assistant.index"}},create:{component:"fel-gpt-assistant-create",path:"create",meta:{parentPath:"fel.gpt.assistant.index"}},vectorStore:{component:"fel-gpt-assistant-vector-store",path:"vector_store/:id",meta:{parentPath:"fel.gpt.assistant.index"}},file:{component:"fel-gpt-assistant-file",path:"file/:id",meta:{parentPath:"fel.gpt.assistant.index"}}},navigation:[{label:"fel-gpt-assistant.general.mainMenuItemGeneral",color:"#ff3d58",path:"fel.gpt.assistant.index",position:100,parent:"sw-marketing"},{label:"fel-gpt-assistant.logs.mainMenuItemGeneral",color:"#ff3d58",path:"fel.gpt.assistant.logs",position:100,parent:"fel.gpt.assistant.index"}]}),s(249),s(817),Shopware.Component.register("sw-cms-el-chatbot",{template:'\n	{% block sw_cms_element_chatbot %}\n		<div class="sw-cms-el-chatbot">\n			<div class="chatbot-window">\n				<div class="messages">\n					<div v-for="message in messages" :class="\'message \' + message.sender">\n						{{ message.text }}\n					</div>\n				</div>\n				<div class="input-area">\n					<input type="text" v-model="userMessage" @keyup.enter="sendMessage" placeholder="Type a message..."/>\n					<button @click="sendMessage">Send</button>\n				</div>\n			</div>\n		</div>\n	{% endblock %}\n\n',mixins:["cms-element"],data(){return{messages:[],userMessage:""}},computed:{cmsAssistantId(){return this.element.config.cmsAssistantId.value}},methods:{sendMessage(){""!==this.userMessage.trim()&&(this.messages.push({sender:"user",text:this.userMessage}),this.userMessage="")}}}),Shopware.Component.register("sw-cms-el-config-chatbot",{template:'\n	{% block sw_cms_element_chatbot_config %}\n		<sw-text-field \n			v-model="cmsAssistantId2" \n			label="Assistant ID 2" \n			placeholder="Enter assistant ID..." >\n		</sw-text-field>\n		<sw-text-field \n			v-model="element.config.cmsAssistantId.value"\n			label="Assistant ID"\n			:copyable="false" \n			:copyabletooltip="false" \n			placeholder="asst_c2dFEeAbsGiZMxmzKvpqqjlF">\n		</sw-text-field>\n	{% endblock %}\n\n\n\n',mixins:["cms-element"],computed:{cmsAssistantId:{get(){return this.element.config.cmsAssistantId.value},set(e){this.element.config.cmsAssistantId.value=e}}},methods:{onElementUpdate(e){this.cmsAssistantId=e,this.$emit("element-update",this.element)}}}),s(717),Shopware.Component.register("sw-cms-el-preview-chatbot",{template:'\n	{% block sw_cms_element_chatbot_preview %}\n		<div class="sw-cms-el-preview-chatbot">\n			<div class="chatbot-preview-box">\n				Chatbot Preview\n			</div>\n		</div>\n	{% endblock %}\n\n'}),Shopware.Service("cmsService").registerCmsElement({name:"chatbot",label:"sw-cms.elements.chatbot.label",component:"sw-cms-el-chatbot",configComponent:"sw-cms-el-config-chatbot",previewComponent:"sw-cms-el-preview-chatbot",defaultConfig:{cmsAssistantId:{source:"static",value:""}}}),s(410);let{Mixin:A}=Shopware;Shopware.Component.register("sw-cms-el-cms-advisor",{template:'\n{% block sw_cms_element_cms_advisor %}\n<div class="sw-cms-el-cms-advisor">\n    <div\n        v-if="element.config.content.source === \'mapped\'"\n        class="sw-cms-el-cms-advisor__mapping-preview content-editor"\n        v-html="$sanitize(demoValue)"\n    ></div>\n\n    <sw-text-editor\n        v-else\n        v-model:value="element.config.content.value"\n        :disabled="disabled"\n        :vertical-align="element.config.verticalAlign.value"\n        :allow-inline-data-mapping="true"\n        :is-inline-edit="true"\n        sanitize-input\n        sanitize-field-name="app_cms_block.template"\n        enable-transparent-background\n        @blur="onBlur"\n        @update:value="onInput"\n    />\n</div>\n{% endblock %}\n',mixins:[A.getByName("cms-element")],data(){return{editable:!0,demoValue:""}},watch:{cmsPageState:{deep:!0,handler(){this.updateDemoValue()}},"element.config.content.source":{handler(){this.updateDemoValue()}}},created(){this.createdComponent()},methods:{createdComponent(){this.initElementConfig("cms-advisor")},updateDemoValue(){"mapped"===this.element.config.content.source&&(this.demoValue=this.getDemoValue(this.element.config.content.value))},onBlur(e){this.emitChanges(e)},onInput(e){this.emitChanges(e)},emitChanges(e){e!==this.element.config.content.value&&(this.element.config.content.value=e,this.$emit("element-update",this.element))}}});let{Mixin:S}=Shopware;Shopware.Component.register("sw-cms-el-config-cms-advisor",{template:'\n{% block sw_cms_el_config_cms_advisor %}\n<sw-tabs\n    position-identifier="sw-cms-element-config-cms-advisor"\n    class="sw-cms-el-config-cms-advisor__tabs"\n    default-item="content"\n>\n\n    <template #default="{ active }">\n        \n        {% block sw_cms_el_config_cms_advisor_tab_content %}\n        <sw-tabs-item\n            :title="$tc(\'sw-cms.elements.general.config.tab.content\')"\n            name="content"\n            :active-tab="active"\n        >\n            {{ $tc(\'sw-cms.elements.general.config.tab.content\') }}\n        </sw-tabs-item>\n        {% endblock %}\n        \n        {% block sw_cms_el_cms_advisor_config_tab_options %}\n        <sw-tabs-item\n            :title="$tc(\'sw-cms.elements.general.config.tab.settings\')"\n            name="settings"\n            :active-tab="active"\n        >\n            {{ $tc(\'sw-cms.elements.general.config.tab.settings\') }}\n        </sw-tabs-item>\n        {% endblock %}\n    </template>\n\n    <template\n        #content="{ active }"\n    >\n        \n        {% block sw_cms_el_cms_advisor_config_content %}\n        <sw-container\n            v-if="active === \'content\'"\n            class="sw-cms-el-config-cms-advisor__tab-content"\n        >\n            <sw-cms-mapping-field\n                v-model:config="element.config.content"\n                :label="$tc(\'sw-cms.elements.text.config.label.content\')"\n                value-types="string"\n            >\n\n                <sw-confirm-field placeholder="Enter value...">\n                </sw-confirm-field>\n\n                <sw-file-input v-model="selectedFile" \n                    label="My file input" \n                    :allowedmimetypes="[\'text/csv\',\'text/xml\']" \n                    :maxfilesize="8*1024*1024"\n                >\n                </sw-file-input>\n\n\n                <sw-text-editor\n                    :value="element.config.content.value"\n                    :allow-inline-data-mapping="true"\n                    sanitize-input\n                    sanitize-field-name="app_cms_block.template"\n                    enable-transparent-background\n                    @update:value="onInput"\n                    @blur="onBlur"\n                />\n\n                <template #preview="{ demoValue }">\n                    <div class="sw-cms-el-config-cms-advisor__mapping-preview">\n                        <div v-html="$sanitize(demoValue)"></div>\n                    </div>\n                </template>\n            </sw-cms-mapping-field>\n        </sw-container>\n        {% endblock %}\n\n        \n        {% block sw_cms_el_cms_advisor_config_settings %}\n        <sw-container\n            v-if="active === \'settings\'"\n            class="sw-cms-el-config-cms-advisor__tab-settings"\n        >\n            \n            {% block sw_cms_el_cms_advisor_config_settings_vertical_align %}\n            <sw-select-field\n                v-model:value="element.config.verticalAlign.value"\n                :label="$tc(\'sw-cms.elements.general.config.label.verticalAlign\')"\n                :placeholder="$tc(\'sw-cms.elements.general.config.label.verticalAlign\')"\n            >\n                <option value="flex-start">\n                    {{ $tc(\'sw-cms.elements.general.config.label.verticalAlignTop\') }}\n                </option>\n                <option value="center">\n                    {{ $tc(\'sw-cms.elements.general.config.label.verticalAlignCenter\') }}\n                </option>\n                <option value="flex-end">\n                    {{ $tc(\'sw-cms.elements.general.config.label.verticalAlignBottom\') }}\n                </option>\n            </sw-select-field>\n            {% endblock %}\n        </sw-container>\n        {% endblock %}\n    </template>\n</sw-tabs>\n{% endblock %}\n',mixins:[S.getByName("cms-element")],created(){this.createdComponent()},methods:{createdComponent(){this.initElementConfig("text")},onBlur(e){this.emitChanges(e)},onInput(e){this.emitChanges(e)},emitChanges(e){e!==this.element.config.content.value&&(this.element.config.content.value=e,this.$emit("element-update",this.element))}}}),s(0),Shopware.Component.register("sw-cms-el-preview-cms-advisor",{template:'\n{% block sw_cms_element_cms_advisor_preview %}\n<div class="sw-cms-el-preview-cms-advisor">\n    <h2>Shopping Advisor</h2>\n    <p>\n        Lorem ipsum dolor sit amet, consetetur sadipscing elitr,\n        sed diam nonumy eirmod tempor invidunt ut labore et dolore magna.\n    </p>\n</div>\n{% endblock %}\n'}),Shopware.Service("cmsService").registerCmsElement({name:"cms-advisor",label:"sw-cms.elements.cms-advisor.label",component:"sw-cms-el-cms-advisor",configComponent:"sw-cms-el-config-cms-advisor",previewComponent:"sw-cms-el-preview-cms-advisor",defaultConfig:{content:{source:"static",value:`
                <h2>AI Shopping Advisor</h2>
                <p>Lorem ipsum dolor sit amet, consetetur sadipscing elitr, 
                sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, 
                sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. 
                Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. 
                Lorem ipsum dolor sit amet, consetetur sadipscing elitr, 
                sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. 
                At vero eos et accusam et justo duo dolores et ea rebum. 
                Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.</p>
            `.trim()},verticalAlign:{source:"static",value:null}}})}()}();