{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_footer %}
    <template id="fel-contact-to-chat-template">
        <div class="fel-contact-to-chat-button-container mb-2">
            <button type="button" class="btn btn-warning fel-ext-btn fel-ask-chat-first fel-fade-in" data-callback="addToChatSubmit">
                {{ 'fel-contact.chat.askChatFirst'|trans }}
            </button>
            {% if 'fel-contact.chat.customMessage'|trans is not empty %}
                <small class="fel-contact-to-chat-custom-message">{{ 'fel-contact.chat.customMessage'|trans }}</small>
            {% endif %}
        </div>
        <div class="fel-contact-to-chat-response-container fel-initial fel-is-hidden">
            <div class="fel-contact-to-chat-remove-container only-close">
                <button type="button"
                    class="btn fel-ext-btn fel-remove-response btn-close close"
                    title="{{ 'fel-contact.chat.remove'|trans }}"
                    aria-label="{{ 'fel-contact.chat.remove'|trans }}"
                    data-callback="removeChatMessage"
                    data-blur="true"></button>
            </div>
            <div class="fel-inner"></div>
        </div>
    </template>
    {{ parent() }}
{% endblock %}
