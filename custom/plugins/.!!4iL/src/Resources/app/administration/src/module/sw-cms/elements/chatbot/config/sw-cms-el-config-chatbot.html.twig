{% block sw_cms_element_chatbot_config %}
<div class="sw-cms-element-chatbot-config">
    <sw-tabs position-identifier="sw-cms-element-chatbot-config" default-item="ai-settings">
        <!-- TABS HEADER -->
        <template #default="{ active }">
            <sw-tabs-item :name="'ai-settings'">
                {{ $tc('sw-cms.elements.chatbot.config.tabAiSettings') }}
            </sw-tabs-item>
            <sw-tabs-item :name="'chat-design'">
                {{ $tc('sw-cms.elements.chatbot.config.tabChatDesign') }}
            </sw-tabs-item>
        </template>

        <!-- TABS CONTENT -->
        <template #content="{ active }">
            <!-- AI Settings Tab -->
            <div v-if="active === 'ai-settings'">
                <!-- Text Editor for Advisor Title -->
                <sw-text-field 
                    v-model="cmsAdvisorTitle" 
                    :label="$tc('sw-cms.elements.chatbot.config.advisorTitleLabel')" 
                    :placeholder="$tc('sw-cms.elements.chatbot.config.advisorTitlePlaceholder')" 
                    @update:value="onElementUpdate($event, 'cmsAdvisorTitle')">
                </sw-text-field>

                <!-- Text Editor for Advisor Name -->
                <sw-text-field 
                    v-model="cmsAdvisorName" 
                    :label="$tc('sw-cms.elements.chatbot.config.advisorNameLabel')" 
                    :placeholder="$tc('sw-cms.elements.chatbot.config.advisorNamePlaceholder')" 
                    @update:value="onElementUpdate($event, 'cmsAdvisorName')">
                </sw-text-field>

                <!-- Assistant Selection -->
                <div class="sw-field sw-block-field sw-field--default sw-contextual-field sw-field--text" :label="$tc('sw-cms.elements.chatbot.config.assistantLabel')">
                    <div class="sw-field__label">
                        <label for="sw-field--choose-asst" class="">{{ $tc('sw-cms.elements.chatbot.config.assistantLabel') }}</label>
                    </div>
                    <div class="sw-block-field__block">
                        <template v-if="assistantList?.data">
                            <select class="fel-select-field" v-model="cmsAssistantId" :placeholder="$tc('sw-cms.elements.chatbot.config.assistantPlaceholder')" @change="assistantMaker($event)">
                                <option v-for="data in assistantList.data" :key="data.id" :value="data.id">{{ data.name }}</option>
                            </select>
                        </template>
                    </div>
                </div>

                <!-- Category Selection -->
                <div class="sw-field sw-block-field sw-field--default sw-contextual-field sw-field--text" :label="$tc('sw-cms.elements.chatbot.config.categoryLabel')">
                    <div class="sw-field__label">
                        <label for="sw-field--choose-cat" class="">{{ $tc('sw-cms.elements.chatbot.config.categoryLabel') }}</label>
                    </div>
                    <template v-if="loadingCategory">
                        <!-- Display loading spinner while category list is loading -->
                        <span>{{ $tc('sw-cms.elements.chatbot.config.loadingCategories') }}</span>
                    </template>
                    <div class="sw-block-field__block">
                        <select class="fel-select-field" v-model="cmsCategoryId" @change="onElementUpdate($event.target.value, 'cmsCategoryId')">
                            <option v-for="category in categoryCollection" :key="category.id" :value="category.id">{{ category.name }}</option>
                        </select>
                    </div>
                </div>

                <!-- Text Editor for Greeting Message -->
                <sw-textarea-field 
                    v-model="cmsGreetingMessage" 
                    :label="$tc('sw-cms.elements.chatbot.config.greetingLabel')" 
                    :placeholder="$tc('sw-cms.elements.chatbot.config.greetingPlaceholder')" 
                    @update:value="onElementUpdate($event, 'cmsGreetingMessage')">
                </sw-textarea-field>

                <!-- Text Field for Product Advisor For -->
                <sw-textarea-field 
                    v-model="cmsProductAdvisorFor" 
                    :label="$tc('sw-cms.elements.chatbot.config.advisorForLabel')" 
                    :placeholder="$tc('sw-cms.elements.chatbot.config.advisorForPlaceholder')" 
                    @update:value="onElementUpdate($event, 'cmsProductAdvisorFor')">
                </sw-textarea-field>
            </div>

            <!-- Chat Design Tab -->
            <div v-if="active === 'chat-design'">
                <!-- Colorpicker for Chat Theme -->
                <sw-colorpicker 
                    v-model="cmsChatThemeColor" 
                    :label="$tc('sw-cms.elements.chatbot.config.themeColorLabel')" 
                    color-output="auto" 
                    :alpha="true" 
                    :disabled="false" 
                    :color-labels="true" 
                    :z-index="9999" 
                    @update:value="onElementUpdate($event, 'cmsChatThemeColor')">
                </sw-colorpicker>

                <!-- Chat Window Height -->
                <sw-number-field 
                    v-model="cmsProductAdvisorChatWindowHeight" 
                    :label="$tc('sw-cms.elements.chatbot.config.chatWindowHeightLabel')"
                    @update:value="onElementUpdate($event, 'cmsProductAdvisorChatWindowHeight')" 
                    numbertype="float" 
                    :step="null" 
                    :min="360" 
                    :max="null" 
                    :value="480" 
                    :digits="0" 
                    :filldigits="false" 
                    :allowempty="false">
                </sw-number-field>

                <!-- Border Radius Option -->
                <sw-checkbox-field 
                    v-model="cmsBorderRadius" 
                    :label="$tc('sw-cms.elements.chatbot.config.borderRadiusLabel')"
                    @update:value="onElementUpdate($event, 'cmsBorderRadius')">
                </sw-checkbox-field>

                <!-- Media Upload for Product Advisor Avatar -->
                <sw-cms-mapping-field 
                    v-model:config="element.config.cmsProductAdvisorAvatar" 
                    :label="$tc('sw-cms.elements.chatbot.config.avatarLabel')" 
                    value-types="entity" 
                    entity="media">
                    <sw-media-upload-v2 
                        variant="regular" 
                        :upload-tag="uploadTag" 
                        :source="previewSource" 
                        :allow-multi-select="false" 
                        :caption="$tc('sw-cms.elements.chatbot.config.uploadAvatarCaption')" 
                        @media-upload-sidebar-open="onOpenMediaModal" 
                        @media-upload-remove-image="onImageRemove"/>

                    <template #preview="{ demoValue }">
                        <div class="sw-cms-el-config-image__mapping-preview">
                            <!-- Use previewSource if available, otherwise fall back to demoValue.url -->
                            <img v-if="previewSource || demoValue.url" :src="previewSource || demoValue.url" alt="Product Advisor Avatar">
                            <sw-alert v-else class="sw-cms-el-config-image__preview-info" variant="info">{{ $tc('sw-cms.elements.chatbot.config.noPreviewAvailable') }}</sw-alert>
                        </div>
                    </template>
                </sw-cms-mapping-field>

                <sw-upload-listener :upload-tag="uploadTag" auto-upload @media-upload-finish="onImageUpload"/>

                <!-- Media Modal for Image Selection -->
                <sw-media-modal-v2 v-if="mediaModalIsOpen" variant="full" @media-modal-selection-change="onSelectionChanges" @modal-close="onCloseModal"/>
            </div>
        </template>
    </sw-tabs>
</div>
{% endblock %}

