<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service;

use Shopware\Storefront\Framework\Cookie\CookieProviderInterface;

class CustomCookieProvider implements CookieProviderInterface
{
    public function __construct(
        private CookieProviderInterface $originalService
    ) {
    }

    private const FEL_COOKIE_GROUP = [
        'snippet_name' => 'fel-gpt-assistant.chatbot',
        'snippet_description' => 'fel-gpt-assistant.cookie.privacy',
        'entries' => [
            [
                'snippet_name' => 'fel-gpt-assistant.cookie.name',
                'snippet_description' => 'fel-gpt-assistant.cookie.description',
                'cookie' => 'fel-chatbot-localstorage-accepted',
                'value' => 'allowed',
                'expiration' => '30',
            ]
        ],
    ];

    public function getCookieGroups(): array
    {
        return array_merge(
            $this->originalService->getCookieGroups(), [
                self::FEL_COOKIE_GROUP
            ]
        );
    }
}
