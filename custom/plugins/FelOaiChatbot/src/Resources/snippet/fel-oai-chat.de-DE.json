{"fel-oai-chat": {"chatbot": "<PERSON><PERSON><PERSON>", "chat": {"avatarTitle": "<PERSON><PERSON><PERSON>", "box": "Chatbox", "categories": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON> au<PERSON>", "delete": "<PERSON><PERSON> l<PERSON>", "deleteChatMessage": "Frage und Antwort löschen", "errorOccurred": "Ein Fehler ist aufgetreten", "initial.greeting": "Wie kann ich Ihnen helfen?", "onlineStatus": "online", "open": "<PERSON><PERSON> e<PERSON>", "placeholder": "Nachricht eingeben", "submit": "<PERSON><PERSON><PERSON><PERSON>", "toggleChatbox": "Chatbox umschalten", "toggleZoomChat": "Chatbox auf größer oder kleiner umstellen", "wait.a.second": "<PERSON><PERSON><PERSON> Si<PERSON> mir einen Moment", "wait.a.second.more": "<PERSON><PERSON><PERSON> Si<PERSON> mir noch einen Moment", "whats.your.purpose": "Wie könntest du mir helfen?", "notifications": "Benachrichtigungen", "productListing": {"noImageAssigned": "Bild nicht gefunden"}, "queryArguments": {"useFixedShortProperty": "Setze '%new%' statt '%old%'", "useFixedPropertySearchAgain": "und suche nochmal", "id": "ID", "query": "Suchbegriff", "categories": "<PERSON><PERSON><PERSON>", "properties": "Eigenschaften", "manufacturer": "<PERSON><PERSON><PERSON>", "price_min": "Mindestpreis", "price_max": "Höchstpreis", "notFilterable": "Nicht filterbar", "notFoundCategories": "Kategorien nicht gefunden", "notFoundProperties": "Eigenschaften nicht gefunden", "order": "Sortierung", "_truncate": "Gekürzt"}}, "cookie": {"name": "Lokaler Speicher", "description": "Aktivieren Sie diese Option, damit der Chatbot das Gespräch lokal in Ihrem Browser speichert und den Chat seitenübergreifend aufrechterhält.", "privacy": "Es werden keine persönlichen Daten mit der KI geteilt. Nur Ihre Eingaben im Chat werden an die KI übermittelt."}, "error": {"chat": {"queryFailedTruncatedTo": "Keine Produkte für den Suchbegriff <b>„%from%“</b> gefunden, geändert zu <b>„%to%“</b>  und nochmal versucht.", "usedArguments": "Gesucht nach", "propsInDiffGroups": "Einige Eigenschaften werden in verschiedenen Filtern verwendet, was <PERSON><PERSON><PERSON> Suchergebnisse beeinflussen kann."}, "input": {"max.length": "Eingabelänge überschritten, max: %max%", "is.not.string": "Die Eingabe liegt nicht im Textformat vor. <PERSON><PERSON><PERSON>, dass Ihre Eingabe eine Textzeichenfolge ist"}}}}