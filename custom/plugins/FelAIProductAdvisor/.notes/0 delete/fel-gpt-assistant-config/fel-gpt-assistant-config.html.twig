{% block fel_gpt_assistant_config %}
	<sw-page class="fel-gpt-assistant-config fel-assistant">

		<template #smart-bar-actions v-if="canRun">
			<router-link
				class="mt-button mt-button--ghost"
				:title="$t('fel-gpt-assistant.general.configuration')"
				:aria-label="$t('fel-gpt-assistant.general.configuration')"
				:to="{ name: 'fel.gpt.assistant.config', params: {} }"
				:disabled="true">
				<mt-icon name="regular-cog"></mt-icon>
			</router-link>
			<router-link
				class="mt-button mt-button--primary mt-button--small"
				:aria-label="$t('fel-gpt-assistant.general.createAssistant')"
				:to="{ name: 'fel.gpt.assistant.create', params: {} }"
				:disabled="canUpdate">
				{{ $t('fel-gpt-assistant.general.createAssistant') }}
			</router-link>
		</template>

		<template #content>
			<mt-card-view class="fel-sw-card-view">

				{# head info #}
				<mt-card class="mt-card__content" positionIdentifier="index">
					<div class="mt-field__label fel-label" for="fel--sw--assistant_instruction">
						<div class="fel-content-box">
							<h2>{{ $t('fel-gpt-assistant.general.configuration') }}</h2>
							<hr />
							<p class="fel-size-xxs">{{ $t('fel-gpt-assistant.config.configPageNote') }}</p>
							<hr />
							<p>
								<router-link
									:to="{name: 'sw.extension.config', params: {namespace: 'FelAIProductAdvisor'}}"
									v-html="$t('fel-gpt-assistant.general.goToConfiguration')">
								</router-link>
							</p>
						</div>
					</div>
				</mt-card>

				{# openAi static data #}
				<mt-card class="fel-dynamic-content-textarea" positionIdentifier="index">
					<sw-textarea-field
						type="textarea"
						name="openAiStaticInformationField"
						v-model="openAiStaticInformation.field"
						@input="onPluginConfigChange(this.event.target)"
						:value="openAiStaticInformation.field"
						:disabled="isLoading">

						<template v-slot:label>
							<div class="fel-label fel-content-box mb-1">
								<div class="fel-flex-space-between mb-1">
									{{ $t('fel-gpt-assistant.config.fileAlternate') }}
									<sw-help-text
										:text="$t('fel-gpt-assistant.config.fileAlternateInfoExtra')"
										:width="240"
										tooltipPosition="left"
										:showDelay="100"
										:hideDelay="100">
									</sw-help-text>
								</div>
								<hr />
								<p class="fel-size-xxs">{{ $t('fel-gpt-assistant.config.fileAlternateInfo') }}</p>
							</div>
						</template>
					</sw-textarea-field>



					<div class="sw-field fel-submit-box">

						{# HERE #}


						{# <sw-button-group> #}

							<sw-button
								variant="primary"
								:aria-label="$t('fel-gpt-assistant.general.updateAssistant')"
								@click="updateFileAlternate('openAiStaticInformationField')"
								:disabled="!openAiStaticInformation.canUpdate">
								{{ $t('fel-gpt-assistant.general.updateAssistant') }}
							</sw-button>

							{# <sw-button
								:aria-label="$t('fel-gpt-assistant.files.saveFile')"
								:link="'data:plain/text;charset=utf-8,' + encodeURIComponent(openAiStaticInformation.field)"
								download="shop-chatbot-dynamic-knowledge-base.txt"
								:disabled="!openAiStaticInformation.field">
								{{ $t('fel-gpt-assistant.files.saveFile') }}
							</sw-button> #}

							{# <sw-button
								v-if="!openAiStaticInformation.field"
								:aria-label="$t('fel-gpt-assistant.general.example')"
								@click="setExampleData()">
								{{ $t('fel-gpt-assistant.general.example') }}
							</sw-button> #}
						{# </sw-button-group> #}
					</div>

				</mt-card>

				{# Useful links to OpenAI #}
				<mt-card positionIdentifier="index">
					<dl class="sw-description-list mt-description-list">
						<dt>{{ $t('fel-gpt-assistant.general.usefulLinks') }}</dt>
						<dd>
							<a href="https://status.openai.com/" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAIStatus') }}
							</a>
						</dd>
						<dd>
							<a href="https://platform.openai.com/usage" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAIUsage') }}
							</a>
						</dd>
						<dd>
							<a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAIApiKey') }}
							</a>
						</dd>
						<dd>
							<a href="https://platform.openai.com/assistants" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAIAssistants') }}
							</a>
						</dd>
						<dd>
							<a href="https://platform.openai.com/files" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAIFiles') }}
							</a>
						</dd>
						<dd>
							<a href="https://platform.openai.com/tokenizer" target="_blank" rel="noopener" class="mt-external-link">
								{{ $t('fel-gpt-assistant.general.openAITokenizer') }}
							</a>
						</dd>
					</dl>
				</mt-card>

			</mt-card-view>

		</template>

	</sw-page>
{% endblock %}
