

```html
{# REMOTE-BUTTON - toggle chatbox from everywhere
    <button type="button"
        class="fel-toggle-chatbot-button-remote fel-btn"
        data-callback="toggleChatbot"
        data-toggle-class="active"
    >{{ 'chat.box'|trans }}</button>
#}
```


```php
use Shopware\Core\Content\Media\Aggregate\MediaThumbnail\MediaThumbnailCollection;
use Shopware\Core\Content\Media\MediaEntity;
use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerEntity;
use Shopware\Core\Content\Product\Aggregate\ProductMedia\ProductMediaEntity;
use Shopware\Core\Content\Product\ProductCollection;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Content\Product\SalesChannel\Listing\Filter as SwFilter;
use Shopware\Core\Content\Product\SalesChannel\SalesChannelProductEntity;
use Shopware\Core\Content\Property\Aggregate\PropertyGroupOption\PropertyGroupOptionCollection;
use Shopware\Core\Content\Property\Aggregate\PropertyGroupOption\PropertyGroupOptionEntity;
use Shopware\Core\Content\Property\PropertyGroupEntity;
use Shopware\Core\Content\Seo\SeoUrl\SeoUrlEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer as DAL;
use Shopware\Core\Framework\DataAbstractionLayer\Entity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityCollection;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\OrFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\RangeFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Log\Package\SearchTerm;
use Shopware\Core\Framework\Struct\Collection;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Framework\Routing\Router;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\AndFilter;


// try store-api search, if internal fails
if (0 === $items['total']) {
    $filter = [
        $this->defaultSetting['product.filter.active'],
    ];

    if ($data['properties'] ?? null) {
        $filter[] = [
            'type' => 'equalsAny',
            'field' => 'product.properties.name',
            'value' => $data['properties'],
        ];
    }

    $allowedProperties = $this->aiConfig->getOpenAiFilterAllowedProperties();

    $search = $this->storeApiClient('POST', '/search-suggest', [
        'limit' => $this->defaultSetting['limit'],
        'no-aggregations' => true,
        'filter' => $filter,
        'search' => $data['query'] ?? '',
        'includes' => [
            'media' => ['id', 'thumbnails'],
            'product' => ['id', 'name', 'manufacturer', 'parentId', 'properties', 'cover.media.thumbnails'],
        ],
        'associations' => [
            'properties' => [
                'associations' => [
                    'group' => []
                ]
            ]
        ]
    ]);

    $storeApiItems = [];
    if ($elements = ($search['elements'] ?? null)) {
        foreach($elements as $i => $item) {
            $storeApiItems[$i] = [
                'name' => $item['name'],
                'url' => $this->router->generate('frontend.detail.page', ['productId' => $item['id']], Router::ABSOLUTE_URL),
                'image' => null,
                'properties' => null,
                'manufacturer' => null,
            ];

            if (empty($storeApiItems[$i]['image']) AND $thumbnail = ($item['cover']['media']['thumbnails'][0]['url'] ?? null)) {
                $storeApiItems[$i]['image'] = $thumbnail;
            }

            if (empty($storeApiItems[$i]['properties']) AND $properties = ($item['properties'] ?? null)) {
                $setProperties = [];
                foreach($properties as $property) {
                    if ($property['name'] ?? null) {
                        if ($allowedProperties) {
                            if ($groupId = ($property['group']['id'] ?? null)) {
                                if (in_array($groupId, $allowedProperties)) {
                                    $setProperties[] = $property['translated']['name'] ?? $property['name'];
                                }
                            }
                        } else {
                            $setProperties[] = $property['translated']['name'] ?? $property['name'];
                        }
                    }
                }
                $storeApiItems[$i]['properties'] = implode(', ', $setProperties);
            }

            if (empty($storeApiItems[$i]['manufacturer']) AND $manufacturers = ($item['manufacturer'] ?? null)) {
                $setManufacturer = [];
                foreach($manufacturers as $manufacturer) {
                    if ($manufacturer['name'] ?? null) {
                        $setManufacturer[] = $manufacturer['translated']['name'] ?? $manufacturer['name'];
                    }
                }
                $storeApiItems[$i]['manufacturer'] = implode(', ', $setManufacturer);
            }
        }

        $items = $storeApiItems;
    }
}

```


```php
$manufacturers = $this->get_manufacturer(false);
$properties = $this->get_product_properties(false);

// search properties given in arguments
if ($data['properties'] ?? null) {
    $propertyIds = [];
    if (is_array($data['properties']) AND $properties AND is_array($properties)) {
        foreach($data['properties'] as $property) {
            $searchPropertyIds = array_search(strtolower($property), array_map('strtolower', $properties));
            if ($searchPropertyIds) {
                $propertyIds[] = $searchPropertyIds;
            }
        }
        if ($propertyIds) {
            $filter[] = [
                'type' => 'equalsAny',
                'field' => 'product.propertyIds',
                'value' => $propertyIds,
            ];
        }
    }
}

```

```js

this.createNotificationSuccess({message: 'success'});

this.createNotificationError({message: 'error'});

this.setConfigValue('chatbotName', 'Newest Name');

```

```tpl
<script>
    {% sw_include '@Storefront/storefront/assistant-dev.js' %}
</script>

<sw-icon name="regular-checkmark-xxs"></sw-icon>

<template v-if="isLoading">
    <sw-skeleton variant="detail-bold" />
    {#
        variant="xxx"
            detail-bold
            listing
            tree-item
            tree-item-nested
            media
            gallery
    #}
    <sw-skeleton />
</template>


<sw-text-field
    v-model="assistant.name"
    name="fel--sw--assistant_name"
    value=""
    :copyable="false"
    :copyableTooltip="false"
    maxlength="70"
    @input="onAssistantInput(this.event)"
>
    <template v-slot:label>
        {{ $t('fel-gpt-assistant.create.form.setAssistantName') }}
    </template>
    <template v-slot:suffix>
        {{ $t('fel-gpt-assistant.create.form.setAssistantNameHelp') }}
    </template>
</sw-text-field>



<!-- red square -->
<sw-color-badge color="red"></sw-color-badge>
<!-- green square -->
<sw-color-badge color="green"></sw-color-badge>

<sw-help-text
    text="Lorem ipsum dolor sit amet"
    :width="200"
    tooltipPosition="right"
    :showDelay="100"
    :hideDelay="100"
/>

<sw-card-view>
    <sw-card
        title="Title"
        subtitle="Subtitle"
        :hero="false"
        :isLoading="false"
        :large="false"
        :avatar="null"
        :headerRight="null"
        :footer="null"
        :toolbar="null"
        positionIdentifier="index"
        default="<p>Duis autem vel eum iriure dolor in hendrerit in vulputate velit e.</p>"
    >
        <template slot="default"><div /></template>
    </sw-card>
</sw-card-view>

```

```xml
<!--
    Admin controller
-->
<!-- routes -->
<?xml version="1.0" encoding="UTF-8" ?>
<routes xmlns="http://symfony.com/schema/routing"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/routing
        https://symfony.com/schema/routing/routing-1.0.xsd">

    <import resource="../../**/Administration/Controller/*Controller.php" type="annotation" />
</routes>

<!-- service -->
<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Fel\CustomGptAgent\Administration\Controller\ApiController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Fel\CustomGptAgent\Service\OpenAI\OpenAIService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
        </service>

    </services>
</container>

```
```php
namespace Fel\CustomGptAgent\Administration\Controller;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Fel\CustomGptAgent\Service\OpenAI\OpenAIService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Shopware\Core\Framework\Context;

#[Route(defaults: ['_routeScope' => ['api']])]
class ApiController extends AbstractController
{
    public function __construct(
        private SystemConfigService $configService,
        private OpenAIService $openAiService
    ) {
    }

    #[Route(
        path: '/api/fel/gpt/assistant/admin/{id?}',
        name: 'api.fel.gpt.assistant.admin',
        defaults: ['auth_required' => false, 'XmlHttpRequest' => true],
        methods: ['POST', 'GET']
    )]
    public function gptAdministrator(?string $id, Request $request, Context $context): JsonResponse
    {
        return new JsonResponse([
        ], 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
    }
}
```



```js
import Debouncer from 'src/helper/debouncer.helper';
import DomAccess from 'src/helper/dom-access.helper';
import Iterator from 'src/helper/iterator.helper';
import Storage from 'src/helper/storage/storage.helper';
import Plugin from 'src/plugin-system/plugin.class';
import HttpClient from 'src/service/http-client.service';
// import DeviceDetection from 'src/helper/device-detection.helper';
// import ElementReplaceHelper from 'src/helper/element-replace.helper.js';
// import NativeEventEmitter from 'src/helper/emitter.helper';
// import {default as FelStorage, default as Storage, default as StorageSingleton} from 'src/helper/storage/storage.helper';
// import CookieStorage from 'src/helper/storage/cookie-storage.helper';
// import ViewportDetection from 'src/helper/viewport-detection.helper';
// import CmsSlotOptionValidatorHelper from 'src/plugin/cms-slot-reload/helper/cms-slot-option-validator.helper';
// import CmsSlotReloadService from 'src/plugin/cms-slot-reload/service/cms-slot-reload.service';
// import CookiePermissionPlugin from 'src/plugin/cookie/cookie-permission.plugin';
// import FadingPlugin from 'src/plugin/fading/fading.plugin';
// import OffCanvasSingleton from 'src/plugin/offcanvas/offcanvas.plugin';
// import RemoteClickPlugin from 'src/plugin/remote-click/remote-click.plugin';
// import VariantSwitchPlugin from "src/plugin/variant-switch/variant-switch.plugin";
// import Backdrop from 'src/utility/backdrop/backdrop.util';
// import FormSerializeUtil from 'src/utility/form/form-serialize.util.js';
// import HistoryUtil from 'src/utility/history/history.util';
// import ButtonLoadingIndicatorUtil from 'src/utility/loading-indicator/button-loading-indicator.util';
// import ElementLoadingIndicatorUtil from 'src/utility/loading-indicator/element-loading-indicator.util';
// import LoadingIndicatorUtil from 'src/utility/loading-indicator/loading-indicator.util';
// import PageLoadingIndicatorUtil from 'src/utility/loading-indicator/page-loading-indicator.util';
// import PseudoModalUtil from 'src/utility/modal-extension/pseudo-modal.util';

export default class AssistantPlugin extends Plugin {

    init() {
        window.AssistantPlugin = this;
        // window.AssistantStorage = Storage;
        // window.Backdrop = Backdrop;
        // window.ButtonLoadingIndicatorUtil = ButtonLoadingIndicatorUtil;
        // window.CmsSlotOptionValidatorHelper = CmsSlotOptionValidatorHelper;
        // window.CmsSlotReloadService = CmsSlotReloadService;
        // window.CookiePermissionPlugin = CookiePermissionPlugin;
        window.Debouncer = Debouncer;
        // window.DeviceDetection = DeviceDetection;
        window.DomAccess = DomAccess;
        // window.ElementLoadingIndicatorUtil = ElementLoadingIndicatorUtil;
        // window.ElementReplaceHelper = ElementReplaceHelper;
        // window.FadingPlugin = FadingPlugin;
        // window.FormSerializeUtil = FormSerializeUtil;
        window.HistoryUtil = HistoryUtil;
        window.HttpClient = HttpClient;
        window.Iterator = Iterator;
        // window.LoadingIndicatorUtil = LoadingIndicatorUtil;
        // window.NativeEventEmitter = NativeEventEmitter;
        // window.OffCanvasSingleton = OffCanvasSingleton;
        // window.PageLoadingIndicatorUtil = PageLoadingIndicatorUtil;
        // window.PseudoModalUtil = PseudoModalUtil;
        window.RemoteClickPlugin = RemoteClickPlugin;
        // window.VariantSwitchPlugin = VariantSwitchPlugin;
        // window.ViewportDetection = ViewportDetection;
        window.Storage = Storage;
        // window.FelStorage = Storage;
        // window.CookieStorage = CookieStorage;
        // window.StorageSingleton = StorageSingleton;
        // console.log(window.AssistantPlugin);
        // console.log(window.FelStorage);
    }

}
```

## Media

```xml
<component name="sw-media-field">
    <name>openAiStaticInformationFile</name>
    <entity>pluginMedia</entity>
    <label>Data files</label>
    <label lang="de-DE">Datendateien</label>
    <helpText>Please activate the 'Get Shop Information' function for the assistant you are utilizing.</helpText>
    <helpText lang="de-DE">Bitte aktivieren Sie die Funktion 'Get Shop Information' für den von Ihnen verwendeten Assistenten.</helpText>
</component>
```
```php
// get media
$media = $this->container->get('media.repository')
    ->search(
        (new Criteria())->addFilter(new EqualsFilter('id', $id)),
        $this->context
    )
    ->getElements();
```















## Other



```html
<script>
    window.onload = function() {
        // consoleLogWindow();
        // new Assistant;
        document.querySelector('[name="fel-chatbot-user-input"]').value = 'wie ist dein name?';
        setTimeout(() => {document.querySelector('.fel-toggle-chatbot-button').click()}, 250);
        setTimeout(() => {document.querySelector('.fel-submit-chat-btn').click()}, 250);
    }
</script>
<style>
    #fel-chatbot.active {
        bottom: 0 !important;
        right: 0 !important;
        max-width: 100% !important;
        height: 100% !important;
    }
</style>
```
```php

$test = 0;
if ($test) {

    /*
        get_product_properties
        query_db
        get_meta_information
        get_chatbot_name
        get_countries
        get_delivery_time
        get_manufacturer
        get_payment_methods
    */
    $useFunction = 'query_db';

    // $setArguments = '{\"query\": \"T-Shirt\",\"properties\":'
    //     . '[\"Cotton\", \"Children\", \"Woman\", \"S\", \"XL\"]'
    //     // . '[\"Cotton\", \"Children\", \"Woman\", \"S\", \"L\"]'
    //     . ',\"manufacturer\":[\"Shopware Clothing\"]}';

    $firstFn = 0 ? '                                {
        "id": "call_JQY6ieyxlKF2khmbBxWxO1Rd",
        "type": "function",
        "function": {
            "name": "get_product_properties",
            "arguments": ""
        }
    },' : null;

    $setArguments = '{\"query\":\"T-Shirt\",\"properties\":[\"Red\",\"XXL\",\"Cotton\"],\"price_max\":\"155\"}';
    // $setArguments = '{\"query\":\"\",\"properties\":[]}';
    $setArguments = '{\"id\":\"018a240768dc732db6b9f845008f8938\"}';


    // {"query":"t-shirt","properties":["rot"]}
    // {\"query\": \"Advanced variant\",\"properties\":[\"Cotton\", \"Children\", \"Woman\"], \"manufacturer\":[\"Shopware Clothing\"]}
    $j = '{
        "id": "chatblock-1828764327' . time() . '",
        "datetime": "' . date(DATE_RFC850) . '",
        "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
        "threadMessages": [
            {
                "value": "Mein Name ist Fely. Wie kann ich Ihnen helfen?",
                "annotations": [],
                "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                "role": "assistant",
                "id": "msg_nJOtTm04M8mGXd1dqD3T4pJj"
            }
        ],
        "actionRequired": {
            "id": "run_OE0ADdpYKyTRuqtcdT0p03ep",
            "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
            "status": "requires_action",
            "required_action": {
                "type": "submit_tool_outputs",
                "submit_tool_outputs": {
                    "tool_calls": [
                        ' . $firstFn . '
                        {
                            "id": "call_QQY6ieyxlKF2khiBxWxO1xx",
                            "type": "function",
                            "function": {
                                "name": "' . $useFunction . '",
                                "arguments": "' . $setArguments . '"
                            }
                        }
                    ]
                }
            },
            "last_error": null,
            "model": "gpt-4-1106-preview",
            "instructions": "You are a customer support chatbot",
            "tools": [],
            "file_ids": [],
            "metadata": []
        },
        "actionRequiredResponse": [],
        "trackError": [],
        "waitCycle": 2,
        "error": null,
        "_post": {"threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz","runId": "run_OE0ADdpYKyTRuqtcdT0p03ep","userInput": ""},
        "_messages": {}
    }
    ';

    if ('create-thread' === $fn) {
        sleep(0);
    } else {
        sleep(0);
    }

    $j = json_decode($j, true);

    if ('create-thread' !== $fn AND ($j['actionRequired'] ?? null)) {
        $actionRequired = $this->openAiService->requiredAction($j['actionRequired'], $request, $salesContext, $context, $this->container);
        $j['actionExecuted'] = $actionRequired;

        $finalOut = [];
        foreach($actionRequired as $data) {
            $finalOut[] = $data['output'];
        }

        if (1 === count($finalOut)) {
            $finalOut = $finalOut[0];
        }

        $tempCopy = $finalOut;

        $finalOut = json_encode(json_decode($finalOut), 480);

        if (empty($finalOut)) {
            $finalOut = $tempCopy;
        }

        $j['threadMessages'][0] = [
            'id' => 'assistant-' . date(DATE_RFC850),
            'role' => 'assistant',
            'datetime' => date(DATE_RFC850),
            'value' => '<pre>' . print_r($finalOut, true) . '</pre>'
        ];
    }

    return new JsonResponse($j, 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
}

```