{"fel-gpt-assistant": {"trainer": {"name": "5 Elements Trainer", "descriptions": ["This tool, which is newly in development, is designed to generate example data that largely conforms to required formats and uses real data from your online shop. It randomly incorporates prepositions such as 'from', 'with', 'in', and phrases like 'budget from' into the generated user inputs to simulate natural language patterns. These elements are simply thrown in without specific context, before and between the nouns, which means that the generated user inputs must be cleaned up in the end. While technical data can be processed effortlessly, the simulated human inputs unfortunately require post-processing.", "We have optimized the plugin and the standard instructions for the 'gpt-3.5-turbo' model to take advantage of its cost-efficiency within the Assistants API, which is inherently more expensive than other OpenAI services due to its complexity. The use of GPT-3.5 is now virtually cost-free, and anyone with an OpenAI account can use GPT-3.5, while GPT-4 is available only to 'Plus' users and its use with the API is also quite costly.", "We recommend creating 5 to 10 standard examples based on queries you expect from your customers to give the AI a clear direction. Given the extensive knowledge of GPT-3.5, not many more examples are usually needed. However, you can add more examples if needed, should the AI exhibit unexpected reactions. If the AI generates gibberish in response to certain questions, you can directly modify the instructions to steer the AI towards the desired outcomes. The standard example includes a chat example for 'Contact details'. Without this example, the AI would invent contact details if it is the user's very first question to the chatbot. The example data is stored in your browser, not on the server. If you delete the cookies in your browser, the examples disappear."], "startTrainer": "Start trainer", "fewShotExamplesNote": {"title": "Important Note on Using Few-Shot Examples:", "descriptions": ["The default examples effectively utilize the demo data from Shopware 6. The chatbot precisely and seamlessly sets all the necessary properties, potentially working perfectly for you right out of the box. This minimizes the need for additional training. While training is primarily beneficial for optimizing search functionalities, all other features are already performing well.", "Please be aware that few-shot examples do not function as intended when they are simply included in files uploaded as attachments to the assistant. To effectively utilize few-shot learning techniques, these examples must be directly incorporated into the instructions provided to the assistant.", "Ensure that you append few-shot examples to the end of the instructions you provide to the assistant. This placement ensures that the assistant can correctly interpret and utilize the examples, enabling it to tailor its responses effectively based on the scenarios outlined in these examples."]}, "ui": {"assistantResponse": "Assistant response", "info": "Info", "introduction": "Introduction", "description": "Description", "manufacturer": "<PERSON><PERSON><PERSON>", "maxPrice": "Max price", "minPrice": "Min price", "produktName": "Product Name", "properties": "Properties", "start": "Start", "sorting": "Sort", "total": "Total", "userQuery": "User query", "defaultExamples": "Default examples", "defaultExamplesAlreadyCapable": "The provided default examples effectively utilize the demo data provided in Shopware 6. The chatbot seamlessly sets all the properties illustrated in the examples below with precision. They may work perfectly for you right out of the box, eliminating the need for additional training. Training is primarily required for optimizing search functionalities; all other features are already performing well.", "replaceDefaultWithCustom": "Replace default examples with custom", "defaultReplaced": "The standard instructions have been replaced. Please check the new instructions.", "gptModelsComparison": "This table presents a comparison between gpt-4-turbo and gpt-3.5-turbo.", "gptModelsComparisonHeader": {"model": "Model", "requests": "API requests", "tokens": "Tokens", "price": "Price"}, "storage": {"adjustBeforeReplace": "Final adjustments before replace.", "capture": "Capture", "captured": "Captured", "deleted": "Deleted", "Localstorage": "Localstorage", "gotExamples": "Examples in Localstorage", "localStorageNote": "The localStorage is a Browser-Storage, like a Cookie.", "lastAdjustmentsNote": "Make final adjustments here before capturing.", "loadExamples": "Load examples from localStorage", "deleteExamples": "Delete examples", "usageNotification": "Please review the output to ensure there are no discrepancies. It's crucial that the property and manufacturer IDs provided in user queries are accurately reflected in the assistant's responses. This ensures that the chat scenarios are realistic and that the tool is properly translating user intent into the structured queries that guide the AI. Pay special attention to the 'properties' under the chats to verify that the IDs used by the assistant exactly match those specified by the user. Consistency in these details is essential for optimal AI performance and accurate data handling.", "howToUseNotification": "If the output appears correct, replace the default examples in the instructions with your own and verify that they function as expected. Please note that changes may take a few minutes to become effective. The first chat example generated by this tool will be automatically included in your set of examples, but it can be manually removed if not needed."}, "availableSortings": {"name-asc": "Name ascending", "name-desc": "Name descending", "price-asc": "Price ascending", "price-desc": "Price descending", "topseller": "Topseller"}}, "step": {"1": {"Instructions": ["It's crucial to teach the AI exactly how to interpret and process terms and properties in User queries to correctly locate products in your shop. Clear and precise instructions prevent the AI from generating irrelevant or incorrect information, known as 'hallucinating'.", "If you encounter any inconsistencies in the AI's responses, you can address these by providing targeted examples. Occasionally, it may be necessary to explicitly specify in the instructions exactly how you expect the AI to behave. Clearly explaining the issues helps guide the AI to respond as you intend.", "This tool generates initial example queries by randomly inserting prepositions such as 'from', 'with', 'in', and phrases like 'budget from' to simulate natural language patterns. These elements are placed without specific context to maintain the structural integrity of a naturally phrased query. Ensure that prepositions and phrases align logically with the rest of the query. Modify the queries to make logical sense in terms of grammar and context. Correct any awkward or incorrect use of language that have resulted from the random insertion process. Make sure that all parts of the query are relevant and necessary. Remove or replace any parts that do not contribute to a clear and effective query.", "Pluralize user inputs and singularize the query for the assistant. For example, convert the user input 'T-Shirts' to 'T-Shirt' for the assistant.", "Let's start with the most basic query where the user asks for a specific product", "<b>I am searching for a [Product].</b>", "This tool ensures that <b>[Product]</b> is accurately included in the assistant's 'example' response, which is a simulated reply designed to mirror the user query. This precise alignment is crucial for ensuring optimal AI performance."]}}}}}