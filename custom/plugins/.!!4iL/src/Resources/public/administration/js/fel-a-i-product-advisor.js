(function(){var e={942:function(){},953:function(){},138:function(){},87:function(e,t,s){var n=s(942);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),s(346).Z("fb455240",n,!0,{})},773:function(e,t,s){var n=s(953);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),s(346).Z("10d15225",n,!0,{})},843:function(e,t,s){var n=s(138);n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.id,n,""]]),n.locals&&(e.exports=n.locals),s(346).Z("0488e73f",n,!0,{})},346:function(e,t,s){"use strict";function n(e,t){for(var s=[],n={},a=0;a<t.length;a++){var i=t[a],o=i[0],l={id:e+":"+a,css:i[1],media:i[2],sourceMap:i[3]};n[o]?n[o].parts.push(l):s.push(n[o]={id:o,parts:[l]})}return s}s.d(t,{Z:function(){return h}});var a="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!a)throw Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=a&&(document.head||document.getElementsByTagName("head")[0]),l=null,c=0,r=!1,d=function(){},m=null,u="data-vue-ssr-id",v="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e,t,s,a){r=s,m=a||{};var o=n(e,t);return g(o),function(t){for(var s=[],a=0;a<o.length;a++){var l=i[o[a].id];l.refs--,s.push(l)}t?g(o=n(e,t)):o=[];for(var a=0;a<s.length;a++){var l=s[a];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete i[l.id]}}}}function g(e){for(var t=0;t<e.length;t++){var s=e[t],n=i[s.id];if(n){n.refs++;for(var a=0;a<n.parts.length;a++)n.parts[a](s.parts[a]);for(;a<s.parts.length;a++)n.parts.push(p(s.parts[a]));n.parts.length>s.parts.length&&(n.parts.length=s.parts.length)}else{for(var o=[],a=0;a<s.parts.length;a++)o.push(p(s.parts[a]));i[s.id]={id:s.id,refs:1,parts:o}}}}function f(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function p(e){var t,s,n=document.querySelector("style["+u+'~="'+e.id+'"]');if(n){if(r)return d;n.parentNode.removeChild(n)}if(v){var a=c++;t=b.bind(null,n=l||(l=f()),a,!1),s=b.bind(null,n,a,!0)}else t=y.bind(null,n=f()),s=function(){n.parentNode.removeChild(n)};return t(e),function(n){n?(n.css!==e.css||n.media!==e.media||n.sourceMap!==e.sourceMap)&&t(e=n):s()}}var w=function(){var e=[];return function(t,s){return e[t]=s,e.filter(Boolean).join("\n")}}();function b(e,t,s,n){var a=s?"":n.css;if(e.styleSheet)e.styleSheet.cssText=w(t,a);else{var i=document.createTextNode(a),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(i,o[t]):e.appendChild(i)}}function y(e,t){var s=t.css,n=t.media,a=t.sourceMap;if(n&&e.setAttribute("media",n),m.ssrId&&e.setAttribute(u,t.id),a&&(s+="\n/*# sourceURL="+a.sources[0]+" */\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),e.styleSheet)e.styleSheet.cssText=s;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(s))}}}},t={};function s(n){var a=t[n];if(void 0!==a)return a.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,s),i.exports}s.d=function(e,t){for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="bundles/felaiproductadvisor/",window?.__sw__?.assetPath&&(s.p=window.__sw__.assetPath+"/bundles/felaiproductadvisor/"),function(){"use strict";s(87),Shopware.Component.register("sw-cms-el-chatbot",{template:'\n	{% block sw_cms_element_chatbot %}\n		<div class="sw-cms-el-chatbot">\n			<div class="chatbot-window">\n				<div class="messages">\n					<div v-for="message in messages" :class="\'message \' + message.sender">\n						{{ message.text }}\n					</div>\n				</div>\n				<div class="input-area">\n					<input type="text" v-model="userMessage" @keyup.enter="sendMessage" placeholder="Type a message..."/>\n					<button @click="sendMessage">Send</button>\n				</div>\n			</div>\n		</div>\n	{% endblock %}\n\n',mixins:["cms-element"],data(){return{messages:[],userMessage:""}},computed:{cmsAssistantId(){return this.element.config.cmsAssistantId.value}},methods:{sendMessage(){""!==this.userMessage.trim()&&(this.messages.push({sender:"user",text:this.userMessage}),this.userMessage="")}}}),s(773);let{Component:e,Mixin:t}=Shopware;e.register("sw-cms-el-config-chatbot",{template:'{% block sw_cms_element_chatbot_config %}\n<div class="sw-cms-element-chatbot-config">\n    <sw-tabs position-identifier="sw-cms-element-chatbot-config" default-item="ai-settings">\n        \n        <template #default="{ active }">\n            <sw-tabs-item :name="\'ai-settings\'">\n                {{ $tc(\'sw-cms.elements.chatbot.config.tabAiSettings\') }}\n            </sw-tabs-item>\n            <sw-tabs-item :name="\'chat-design\'">\n                {{ $tc(\'sw-cms.elements.chatbot.config.tabChatDesign\') }}\n            </sw-tabs-item>\n        </template>\n\n        \n        <template #content="{ active }">\n            \n            <div v-if="active === \'ai-settings\'">\n                \n                <sw-text-field \n                    v-model="cmsAdvisorTitle" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.advisorTitleLabel\')" \n                    :placeholder="$tc(\'sw-cms.elements.chatbot.config.advisorTitlePlaceholder\')" \n                    @update:value="onElementUpdate($event, \'cmsAdvisorTitle\')">\n                </sw-text-field>\n\n                \n                <sw-text-field \n                    v-model="cmsAdvisorName" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.advisorNameLabel\')" \n                    :placeholder="$tc(\'sw-cms.elements.chatbot.config.advisorNamePlaceholder\')" \n                    @update:value="onElementUpdate($event, \'cmsAdvisorName\')">\n                </sw-text-field>\n\n                \n                <div class="sw-field sw-block-field sw-field--default sw-contextual-field sw-field--text" :label="$tc(\'sw-cms.elements.chatbot.config.assistantLabel\')">\n                    <div class="sw-field__label">\n                        <label for="sw-field--choose-asst" class="">{{ $tc(\'sw-cms.elements.chatbot.config.assistantLabel\') }}</label>\n                    </div>\n                    <div class="sw-block-field__block">\n                        <template v-if="assistantList?.data">\n                            <select class="fel-select-field" v-model="cmsAssistantId" :placeholder="$tc(\'sw-cms.elements.chatbot.config.assistantPlaceholder\')" @change="assistantMaker($event)">\n                                <option v-for="data in assistantList.data" :key="data.id" :value="data.id">{{ data.name }}</option>\n                            </select>\n                        </template>\n                    </div>\n                </div>\n\n                \n                <div class="sw-field sw-block-field sw-field--default sw-contextual-field sw-field--text" :label="$tc(\'sw-cms.elements.chatbot.config.categoryLabel\')">\n                    <div class="sw-field__label">\n                        <label for="sw-field--choose-cat" class="">{{ $tc(\'sw-cms.elements.chatbot.config.categoryLabel\') }}</label>\n                    </div>\n                    <template v-if="loadingCategory">\n                        \n                        <span>{{ $tc(\'sw-cms.elements.chatbot.config.loadingCategories\') }}</span>\n                    </template>\n                    <div class="sw-block-field__block">\n                        <select class="fel-select-field" v-model="cmsCategoryId" @change="onElementUpdate($event.target.value, \'cmsCategoryId\')">\n                            <option v-for="category in categoryCollection" :key="category.id" :value="category.id">{{ category.name }}</option>\n                        </select>\n                    </div>\n                </div>\n\n                \n                <sw-textarea-field \n                    v-model="cmsGreetingMessage" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.greetingLabel\')" \n                    :placeholder="$tc(\'sw-cms.elements.chatbot.config.greetingPlaceholder\')" \n                    @update:value="onElementUpdate($event, \'cmsGreetingMessage\')">\n                </sw-textarea-field>\n\n                \n                <sw-textarea-field \n                    v-model="cmsProductAdvisorFor" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.advisorForLabel\')" \n                    :placeholder="$tc(\'sw-cms.elements.chatbot.config.advisorForPlaceholder\')" \n                    @update:value="onElementUpdate($event, \'cmsProductAdvisorFor\')">\n                </sw-textarea-field>\n            </div>\n\n            \n            <div v-if="active === \'chat-design\'">\n                \n                <sw-colorpicker \n                    v-model="cmsChatThemeColor" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.themeColorLabel\')" \n                    color-output="auto" \n                    :alpha="true" \n                    :disabled="false" \n                    :color-labels="true" \n                    :z-index="9999" \n                    @update:value="onElementUpdate($event, \'cmsChatThemeColor\')">\n                </sw-colorpicker>\n\n                \n                <sw-number-field \n                    v-model="cmsProductAdvisorChatWindowHeight" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.chatWindowHeightLabel\')"\n                    @update:value="onElementUpdate($event, \'cmsProductAdvisorChatWindowHeight\')" \n                    numbertype="float" \n                    :step="null" \n                    :min="360" \n                    :max="null" \n                    :value="480" \n                    :digits="0" \n                    :filldigits="false" \n                    :allowempty="false">\n                </sw-number-field>\n\n                \n                <sw-checkbox-field \n                    v-model="cmsBorderRadius" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.borderRadiusLabel\')"\n                    @update:value="onElementUpdate($event, \'cmsBorderRadius\')">\n                </sw-checkbox-field>\n\n                \n                <sw-cms-mapping-field \n                    v-model:config="element.config.cmsProductAdvisorAvatar" \n                    :label="$tc(\'sw-cms.elements.chatbot.config.avatarLabel\')" \n                    value-types="entity" \n                    entity="media">\n                    <sw-media-upload-v2 \n                        variant="regular" \n                        :upload-tag="uploadTag" \n                        :source="previewSource" \n                        :allow-multi-select="false" \n                        :caption="$tc(\'sw-cms.elements.chatbot.config.uploadAvatarCaption\')" \n                        @media-upload-sidebar-open="onOpenMediaModal" \n                        @media-upload-remove-image="onImageRemove"/>\n\n                    <template #preview="{ demoValue }">\n                        <div class="sw-cms-el-config-image__mapping-preview">\n                            \n                            <img v-if="previewSource || demoValue.url" :src="previewSource || demoValue.url" alt="Product Advisor Avatar">\n                            <sw-alert v-else class="sw-cms-el-config-image__preview-info" variant="info">{{ $tc(\'sw-cms.elements.chatbot.config.noPreviewAvailable\') }}</sw-alert>\n                        </div>\n                    </template>\n                </sw-cms-mapping-field>\n\n                <sw-upload-listener :upload-tag="uploadTag" auto-upload @media-upload-finish="onImageUpload"/>\n\n                \n                <sw-media-modal-v2 v-if="mediaModalIsOpen" variant="full" @media-modal-selection-change="onSelectionChanges" @modal-close="onCloseModal"/>\n            </div>\n        </template>\n    </sw-tabs>\n</div>\n{% endblock %}\n\n',inject:["repositoryFactory"],mixins:["cms-element","notification","fel-assistant-manager-plugin-config-mixin","fel-assistant-manager-openai-mixin"],data(){return{mediaModalIsOpen:!1,initialFolderId:null,customAssistantList:null,assistantList:{data:[]},cmsCategoryId:null,categoryCollection:[],context:Shopware.Context.api,loadingCategory:!0,previewSource:null}},computed:{mediaRepository(){return this.repositoryFactory.create("media")},categoryRepository(){return this.repositoryFactory.create("category")},uploadTag(){return`cms-element-media-config-${this.element.id}`},cmsCategoryId:{get(){return this.element.config.cmsCategoryId?.value||null},set(e){this.element.config.cmsCategoryId.value=e,this.$emit("element-update",this.element)}},cmsAssistantId:{get(){return this.element.config.cmsAssistantId?.value||""},set(e){this.element.config.cmsAssistantId.value=e,this.$emit("element-update",this.element)}},cmsAdvisorTitle:{get(){return this.element.config.cmsAdvisorTitle?.value||""},set(e){this.element.config.cmsAdvisorTitle.value=e,this.$emit("element-update",this.element)}},cmsAdvisorName:{get(){return this.element.config.cmsAdvisorName?.value||""},set(e){this.element.config.cmsAdvisorName.value=e,this.$emit("element-update",this.element)}},cmsGreetingMessage:{get(){return this.element.config.cmsGreetingMessage?.value||""},set(e){this.element.config.cmsGreetingMessage.value=e,this.$emit("element-update",this.element)}},cmsChatThemeColor:{get(){return this.element.config.cmsChatThemeColor?.value||"#6ed59f"},set(e){this.element.config.cmsChatThemeColor.value=e,this.$emit("element-update",this.element)}},cmsProductAdvisorFor:{get(){return this.element.config.cmsProductAdvisorFor?.value||""},set(e){this.element.config.cmsProductAdvisorFor.value=e,this.$emit("element-update",this.element)}},cmsProductAdvisorChatWindowHeight:{get(){return this.element.config.cmsProductAdvisorChatWindowHeight?.value||""},set(e){this.element.config.cmsProductAdvisorChatWindowHeight.value=e,this.$emit("element-update",this.element)}},cmsBorderRadius:{get(){return this.element.config.cmsBorderRadius?.value||!1},set(e){this.element.config.cmsBorderRadius.value=e,this.$emit("element-update",this.element)}},cmsProductAdvisorAvatar:{get(){return this.element.config.cmsProductAdvisorAvatar?.value||null},set(e){this.element.config.cmsProductAdvisorAvatar.value=e,this.$emit("element-update",this.element)}}},created(){this.createdComponent()},methods:{async createdComponent(){if(this.initElementConfig("chatbot"),this.element.config.cmsProductAdvisorAvatar&&this.element.config.cmsProductAdvisorAvatar.value){let e=this.element.config.cmsProductAdvisorAvatar.value;try{let t=await this.mediaRepository.get(e,Shopware.Context.api);t&&t.url&&(this.previewSource=t.url)}catch(e){}}this.element.config.cmsAdvisorName||this.$set(this.element.config,"cmsAdvisorName",{value:""}),this.element.config.cmsCategoryId||this.$set(this.element.config,"cmsCategoryId",{value:null}),await this.fetchPluginConfig(),await this.fetchAssistants(),await this.fetchCategories()},async fetchCategories(){this.loadingCategory=!0;let e=new Shopware.Data.Criteria(1,100);if(e.addAssociation("media"),this.categoryCollection=await this.categoryRepository.search(e,this.context),this.element.config.cmsCategoryId&&this.element.config.cmsCategoryId.value){let e=this.categoryCollection.find(e=>e.id===this.element.config.cmsCategoryId.value);e&&(this.cmsCategoryId=e.id)}this.loadingCategory=!1},onElementUpdate(e,t){this.element.config[t].value=e,this.$emit("element-update",this.element)},onImageUpload({targetId:e}){this.mediaRepository.get(e).then(e=>{this.element.config.cmsProductAdvisorAvatar.value=e.id,this.updateElementData(e),this.$emit("element-update",this.element)})},onImageRemove(){this.element.config.cmsProductAdvisorAvatar.value=null,this.updateElementData(),this.$emit("element-update",this.element)},onSelectionChanges(e){let t=e[0];this.element.config.cmsProductAdvisorAvatar.value=t.id,this.previewSource=t.url,this.updateElementData(t),this.$emit("element-update",this.element)},updateElementData(e=null){let t=null===e?null:e.id;this.element.data?(this.$set(this.element,"mediaId",t),this.$set(this.element,"media",e)):this.$set(this.element,"data",{mediaId:t,media:e})},onOpenMediaModal(){this.mediaModalIsOpen=!0},onCloseModal(){this.mediaModalIsOpen=!1},assistantMaker(e){let t=e.target.value;this.customAssistantList=t,this.cmsAssistantId=t}}}),s(843),Shopware.Component.register("sw-cms-el-preview-chatbot",{template:'\n	{% block sw_cms_element_chatbot_preview %}\n		<div class="sw-cms-el-preview-chatbot">\n			<div class="chatbot-preview-box">\n				AI Product Advisor\n			</div>\n		</div>\n	{% endblock %}\n\n'}),Shopware.Service("cmsService").registerCmsElement({name:"chatbot",label:"sw-cms.elements.chatbot.label",component:"sw-cms-el-chatbot",configComponent:"sw-cms-el-config-chatbot",previewComponent:"sw-cms-el-preview-chatbot",defaultConfig:{cmsAssistantId:{source:"static",value:""},cmsAdvisorTitle:{source:"static",value:""},cmsAdvisorName:{source:"static",value:""},cmsCategoryId:{source:"static",value:null,required:!1,entity:{name:"category"}},cmsGreetingMessage:{source:"static",value:"Hello! How can I assist you today?"},cmsChatThemeColor:{source:"static",value:"#6ed59f"},cmsProductAdvisorFor:{source:"static",value:""},cmsProductAdvisorChatWindowHeight:{source:"static",value:""},cmsBorderRadius:{source:"static",value:!1},cmsProductAdvisorAvatar:{source:"static",value:null,required:!1,entity:{name:"media"}}}})}()})();