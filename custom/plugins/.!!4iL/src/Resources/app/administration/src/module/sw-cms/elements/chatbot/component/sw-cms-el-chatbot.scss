// <plugin root>/src/Resources/app/administration/src/module/sw-cms/elements/chatbot/component/sw-cms-el-chatbot.scss
.sw-cms-el-chatbot {
    .chatbot-window {
        border: 1px solid #ccc;
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 400px;

        .messages {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
            background-color: #f9f9f9;

            .message {
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 5px;

                &.user {
                    background-color: #d1e7dd;
                    align-self: flex-end;
                }

                &.assistant {
                    background-color: #f1f1f1;
                    align-self: flex-start;
                }
            }
        }

        .input-area {
            display: flex;
            padding: 10px;
            border-top: 1px solid #ccc;
            background-color: #fff;

            input {
                flex: 1;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-right: 10px;
            }

            button {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                background-color: #007bff;
                color: #fff;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }
}
