php translation-extractor/extract-translations-improved.php custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration
PHP Deprecated:  Creation of dynamic property TranslationExtractor::$srcDir is deprecated in /var/www/site/shopware.loc/development_test/translation-extractor/extract-translations-improved.php on line 29
Found 56 source files and 8 translation files.
Scanning source files for translation keys...
Found key in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-check/fel-assistant-manager-content-check.html.twig
Found template literal in helper function: 'contentEditor.editable.${isConfigName}.name' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-check/fel-assistant-manager-content-check.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/index.js
Found dynamic helper function call with variable 'key' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/index.js
Found dynamic helper function call with variable 'key' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/index.js
Found key in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'editPluginConfigName' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found dynamic helper function call with variable 'key' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found template literal in helper function: 'contentEditor.editable.${isConfigName}.name' in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-editor/fel-assistant-manager-content-editor.html.twig
Found 733 direct translation key usages and approximately 78 dynamic usages in source files.
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.generalNotesForTags.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.generalNotesForTags.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.generalNotesForTags.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.tagNamingFlexibility.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.tagNamingFlexibility.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.keepTagNamesSimpleAndClear.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.keepTagNamesSimpleAndClear.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.keepTagNamesSimpleAndClear.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useTagAliasesForSimilarTerms.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useTagAliasesForSimilarTerms.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useTagAliasesForSimilarTerms.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.avoidUsingSpecialCharacters.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.avoidUsingSpecialCharacters.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.avoidUsingSpecialCharacters.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useActiveVoiceForTags.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useActiveVoiceForTags.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.useActiveVoiceForTags.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.reviewExistingTagsBeforeAddingNewOnes.title in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.reviewExistingTagsBeforeAddingNewOnes.list.one in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found prefix 'fel-assistant-manager.tags-tips.' for key: fel-assistant-manager.tags-tips.reviewExistingTagsBeforeAddingNewOnes.list.two in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-tag-create/fel-assistant-manager-tag-create.html.twig
Found unique key part 'general.mainMenuItemGeneral' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Found unique key 'fel-assistant-manager.general.no' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-index/fel-assistant-manager-index.html.twig
Found unique key 'fel-assistant-manager.general.assistant' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-index/fel-assistant-manager-index.html.twig
Note: Key 'fel-assistant-manager.general.assistantName' is specific to the administration context.
Note: Key 'fel-assistant-manager.general.configuration' is specific to the administration context.
Found unique key part 'general.contentEditorMainMenuItemGeneral' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Found unique key 'fel-assistant-manager.general.delete' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-delete/fel-assistant-manager-delete.html.twig
Note: Key 'fel-assistant-manager.general.example' is specific to the administration context.
Found unique key 'fel-assistant-manager.general.message' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-chat/fel-assistant-manager-chat.html.twig
Note: Key 'fel-assistant-manager.general.openAIStatus' is specific to the administration context.
Note: Key 'fel-assistant-manager.general.openAITokenizer' is specific to the administration context.
Note: Key 'fel-assistant-manager.general.openAIUsage' is specific to the administration context.
Found unique key 'fel-assistant-manager.general.update' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-create/fel-assistant-manager-create.html.twig
Found key 'log' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-api-storage.mixin.js
Found key 'fel-assistant-manager.create.responseFormat' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-input-helper.mixin.js
Note: Key 'fel-assistant-manager.contentEditor.inheritanceRestored' is specific to the administration context.
Note: Key 'fel-assistant-manager.catchErrors.completedRuns' is specific to the storefront context.
Found key 'error' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Note: Key 'fel-assistant-manager.catchErrors.errorNotFound' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.failedAssistantNotFound' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.managerCanNotRunNoplugins' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.missingAssstantId' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.failedNoFilesFound' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.failedOaiServerError' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.failedToReadMessage' is specific to the storefront context.
Found key 'failedToLoadVectorStore' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-index/index.js
Special handling for key: fel-assistant-manager.catchErrors.invalidSalesChannelDomain
Note: Key 'fel-assistant-manager.catchErrors.missingAccessData' is specific to the storefront context.
Note: Key 'fel-assistant-manager.catchErrors.missingPrefetchedProductProperties' is specific to the storefront context.
Found key 'unknownErrorOccured' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-api-request.mixin.js
Found key 'stored' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Found key 'deleted' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-openai.mixin.js
Found key 'delete' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-api-storage.mixin.js
Note: Key 'fel-assistant-manager.filter.filterCategories' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.filterManufacturer' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.filterProperties' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.generalName' is specific to the administration context.
Found key 'autoSubmit' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Found key 'categories' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Found key 'false' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Note: Key 'fel-assistant-manager.filter.ui.filteringFailedNoResultsFound' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.ui.hideFilter' is specific to the administration context.
Found key 'limit' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-logs-repository.mixin.js
Found key 'liveFilter' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-openai.mixin.js
Found key 'noResults' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-live-filter.mixin.js
Found key 'order' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-input-helper.mixin.js
Found key 'query' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Note: Key 'fel-assistant-manager.filter.ui.setVisibleToConfig' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.ui.showFilter' is specific to the administration context.
Note: Key 'fel-assistant-manager.filter.ui.showSelectedOnly' is specific to the administration context.
Found key 'start' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-helper.mixin.js
Found key 'submit' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Found key 'type' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-storefront.mixin.js
Found unique key 'fel-assistant-manager.contentCheck.salesChannelDomain' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-content-check/fel-assistant-manager-content-check.html.twig
Found unique key part 'log.mainMenuItemGeneral' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Note: Key 'fel-assistant-manager.log.addNewLog' is specific to the administration context.
Found key 'fel-assistant-manager.log.answer' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-log-list/index.js
Found unique key part 'log.assistantId' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-log-create/index.js
Found unique key part 'log.assistantLogs' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Note: Key 'fel-assistant-manager.log.categoryId' is specific to the administration context.
Found unique key 'fel-assistant-manager.log.import' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-log-create/fel-assistant-manager-log-create.html.twig
Found unique key part 'log.searchTerms' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-logs-repository.mixin.js
Found unique key part 'log.tagAliases' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/mixins/fel-assistant-manager-logs-repository.mixin.js
Note: Key 'fel-assistant-manager.log.filter.assistant' is specific to the administration context.
Note: Key 'fel-assistant-manager.log.filter.label' is specific to the administration context.
Note: Key 'fel-assistant-manager.log.filter.all' is specific to the administration context.
Note: Key 'fel-assistant-manager.log.tag.backToTags' is specific to the administration context.
Found unique key 'fel-assistant-manager.log.tag.tag' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-create/fel-assistant-manager-create.html.twig
Found key 'fel-assistant-manager.log.tag.tagAlias' in Vue.js component file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/page/fel-assistant-manager-log/fel-assistant-manager-log-list/index.js
Found unique key part 'chatlog.mainMenuItemGeneral' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Note: Key 'fel-assistant-manager.chatlog.addNew' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.cancel' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.details' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.save' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.selectItemsToDelete' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.column.salesChannelId' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.column.assistantId' is specific to the administration context.
Note: Key 'fel-assistant-manager.chatlog.column.runsCount' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.allow' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.decline' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.description' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.failedToCreateThread' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.failedToSendMessage' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.failedWhileCheckingStatus' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.failedWhileFetchingMessages' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.failedWhileSendingYourData' is specific to the administration context.
Found unique key part 'chat.mainMenuItemGeneral' using simple string search in file: custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/index.js
Note: Key 'fel-assistant-manager.chat.notifications' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.routeDescription' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.suggestions' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.withRunId' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.noRunId' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.missingRequired.salesChannelId' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.missingRequired.storefrontUrl' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.missingRequired.felOpenAiApiKey' is specific to the administration context.
Note: Key 'fel-assistant-manager.chat.missingRequired.felOpenAiAssistantId' is specific to the administration context.
Note: Key 'fel-assistant-manager.exception.failedToInitThread' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.maxLoopAttemptsReached' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingActionInRequest' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingAssistantIdForCreateThread' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingAssistantIdForSendMessage' is specific to the storefront context.
Special handling for key: fel-assistant-manager.exception.missingMessageForCreateThread
Note: Key 'fel-assistant-manager.exception.missingMessageForSendMessage' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingMessageIdForDeleteMessage' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingOpenaiApiKey' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingOpenaiBaseUrl' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingRunIdForCheckStatus' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForCheckStatus' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForDeleteMessage' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForDeleteThread' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForFetchMessages' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForSendMessage' is specific to the storefront context.
Note: Key 'fel-assistant-manager.exception.missingThreadIdForGetThread' is specific to the storefront context.

Unused Translations by Parent Key:
{
    "fel-assistant-manager.exception": 16,
    "fel-assistant-manager.chat": 13,
    "fel-assistant-manager.catchErrors": 10,
    "fel-assistant-manager.delete": 9,
    "fel-assistant-manager.general": 6,
    "fel-assistant-manager.filter.ui": 5,
    "fel-assistant-manager.chatlog": 5,
    "fel-assistant-manager.filter": 4,
    "fel-assistant-manager.chat.missingRequired": 4,
    "fel-assistant-manager.fel-test-api-key.error": 4,
    "fel-assistant-manager.contentCheck": 3,
    "fel-assistant-manager.log.filter": 3,
    "fel-assistant-manager.chatlog.column": 3,
    "fel-assistant-manager.instructionIssues": 3,
    "fel-assistant-manager.notifications": 3,
    "fel-assistant-manager.files.vectorStore": 2,
    "fel-assistant-manager.storageManager": 2,
    "fel-assistant-manager.log": 2,
    "fel-assistant-manager.contentEditor": 1,
    "fel-assistant-manager.instructions.chatbot": 1,
    "fel-assistant-manager.storageManager.cacheName": 1,
    "fel-assistant-manager.log.tag": 1,
    "fel-assistant-manager.fel-test-api-key.success": 1
}

Unused Translations by Context:
  administration: 47 keys
  storefront: 26 keys
  unknown: 29 keys

Unused Administration Translations (47 total):
[
    "fel-assistant-manager.chat.allow",
    "fel-assistant-manager.chat.decline",
    "fel-assistant-manager.chat.description",
    "fel-assistant-manager.chat.failedToCreateThread",
    "fel-assistant-manager.chat.failedToSendMessage",
    "fel-assistant-manager.chat.failedWhileCheckingStatus",
    "fel-assistant-manager.chat.failedWhileFetchingMessages",
    "fel-assistant-manager.chat.failedWhileSendingYourData",
    "fel-assistant-manager.chat.missingRequired.felOpenAiApiKey",
    "fel-assistant-manager.chat.missingRequired.felOpenAiAssistantId",
    "fel-assistant-manager.chat.missingRequired.salesChannelId",
    "fel-assistant-manager.chat.missingRequired.storefrontUrl",
    "fel-assistant-manager.chat.noRunId",
    "fel-assistant-manager.chat.notifications",
    "fel-assistant-manager.chat.routeDescription",
    "fel-assistant-manager.chat.suggestions",
    "fel-assistant-manager.chat.withRunId",
    "fel-assistant-manager.chatlog.addNew",
    "fel-assistant-manager.chatlog.cancel",
    "fel-assistant-manager.chatlog.column.assistantId",
    "fel-assistant-manager.chatlog.column.runsCount",
    "fel-assistant-manager.chatlog.column.salesChannelId",
    "fel-assistant-manager.chatlog.details",
    "fel-assistant-manager.chatlog.save",
    "fel-assistant-manager.chatlog.selectItemsToDelete",
    "fel-assistant-manager.contentEditor.inheritanceRestored",
    "fel-assistant-manager.filter.filterCategories",
    "fel-assistant-manager.filter.filterManufacturer",
    "fel-assistant-manager.filter.filterProperties",
    "fel-assistant-manager.filter.generalName",
    "fel-assistant-manager.filter.ui.filteringFailedNoResultsFound",
    "fel-assistant-manager.filter.ui.hideFilter",
    "fel-assistant-manager.filter.ui.setVisibleToConfig",
    "fel-assistant-manager.filter.ui.showFilter",
    "fel-assistant-manager.filter.ui.showSelectedOnly",
    "fel-assistant-manager.general.assistantName",
    "fel-assistant-manager.general.configuration",
    "fel-assistant-manager.general.example",
    "fel-assistant-manager.general.openAIStatus",
    "fel-assistant-manager.general.openAITokenizer",
    "fel-assistant-manager.general.openAIUsage",
    "fel-assistant-manager.log.addNewLog",
    "fel-assistant-manager.log.categoryId",
    "fel-assistant-manager.log.filter.all",
    "fel-assistant-manager.log.filter.assistant",
    "fel-assistant-manager.log.filter.label",
    "fel-assistant-manager.log.tag.backToTags"
]

Unused Storefront Translations (26 total):
[
    "fel-assistant-manager.catchErrors.completedRuns",
    "fel-assistant-manager.catchErrors.errorNotFound",
    "fel-assistant-manager.catchErrors.failedAssistantNotFound",
    "fel-assistant-manager.catchErrors.failedNoFilesFound",
    "fel-assistant-manager.catchErrors.failedOaiServerError",
    "fel-assistant-manager.catchErrors.failedToReadMessage",
    "fel-assistant-manager.catchErrors.managerCanNotRunNoplugins",
    "fel-assistant-manager.catchErrors.missingAccessData",
    "fel-assistant-manager.catchErrors.missingAssstantId",
    "fel-assistant-manager.catchErrors.missingPrefetchedProductProperties",
    "fel-assistant-manager.exception.failedToInitThread",
    "fel-assistant-manager.exception.maxLoopAttemptsReached",
    "fel-assistant-manager.exception.missingActionInRequest",
    "fel-assistant-manager.exception.missingAssistantIdForCreateThread",
    "fel-assistant-manager.exception.missingAssistantIdForSendMessage",
    "fel-assistant-manager.exception.missingMessageForSendMessage",
    "fel-assistant-manager.exception.missingMessageIdForDeleteMessage",
    "fel-assistant-manager.exception.missingOpenaiApiKey",
    "fel-assistant-manager.exception.missingOpenaiBaseUrl",
    "fel-assistant-manager.exception.missingRunIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForDeleteMessage",
    "fel-assistant-manager.exception.missingThreadIdForDeleteThread",
    "fel-assistant-manager.exception.missingThreadIdForFetchMessages",
    "fel-assistant-manager.exception.missingThreadIdForGetThread",
    "fel-assistant-manager.exception.missingThreadIdForSendMessage"
]

Unused Unknown Context Translations (29 total):
[
    "fel-assistant-manager.contentCheck.domain",
    "fel-assistant-manager.contentCheck.readableFormat",
    "fel-assistant-manager.contentCheck.salesChannelAccessKey",
    "fel-assistant-manager.delete.cancel",
    "fel-assistant-manager.delete.confirm",
    "fel-assistant-manager.delete.confirmDeleteLog",
    "fel-assistant-manager.delete.confirmDeleteTag",
    "fel-assistant-manager.delete.confirmDeleteVectorStore",
    "fel-assistant-manager.delete.confirmMessage",
    "fel-assistant-manager.delete.no",
    "fel-assistant-manager.delete.selectItemsToDelete",
    "fel-assistant-manager.delete.yes",
    "fel-assistant-manager.fel-test-api-key.error.baseUrlNotValid",
    "fel-assistant-manager.fel-test-api-key.error.missingApiKey",
    "fel-assistant-manager.fel-test-api-key.error.missingBaseUrl",
    "fel-assistant-manager.fel-test-api-key.error.trimApiKey",
    "fel-assistant-manager.fel-test-api-key.success.notificationLabel",
    "fel-assistant-manager.files.vectorStore.fileIsEmpty",
    "fel-assistant-manager.files.vectorStore.noFilesAttached",
    "fel-assistant-manager.instructionIssues.logFunctionNote",
    "fel-assistant-manager.instructionIssues.productPropertyNote",
    "fel-assistant-manager.instructionIssues.searchLogsAggressiveUsage",
    "fel-assistant-manager.instructions.chatbot.gptThree",
    "fel-assistant-manager.notifications.errorWhileImportingLogs",
    "fel-assistant-manager.notifications.logsImported",
    "fel-assistant-manager.notifications.noFileSelected",
    "fel-assistant-manager.storageManager.cacheName.managerIsLoading",
    "fel-assistant-manager.storageManager.items",
    "fel-assistant-manager.storageManager.storages"
]

Unused Translations List (102 total):
[
    "fel-assistant-manager.catchErrors.completedRuns",
    "fel-assistant-manager.catchErrors.errorNotFound",
    "fel-assistant-manager.catchErrors.failedAssistantNotFound",
    "fel-assistant-manager.catchErrors.failedNoFilesFound",
    "fel-assistant-manager.catchErrors.failedOaiServerError",
    "fel-assistant-manager.catchErrors.failedToReadMessage",
    "fel-assistant-manager.catchErrors.managerCanNotRunNoplugins",
    "fel-assistant-manager.catchErrors.missingAccessData",
    "fel-assistant-manager.catchErrors.missingAssstantId",
    "fel-assistant-manager.catchErrors.missingPrefetchedProductProperties",
    "fel-assistant-manager.chat.allow",
    "fel-assistant-manager.chat.decline",
    "fel-assistant-manager.chat.description",
    "fel-assistant-manager.chat.failedToCreateThread",
    "fel-assistant-manager.chat.failedToSendMessage",
    "fel-assistant-manager.chat.failedWhileCheckingStatus",
    "fel-assistant-manager.chat.failedWhileFetchingMessages",
    "fel-assistant-manager.chat.failedWhileSendingYourData",
    "fel-assistant-manager.chat.missingRequired.felOpenAiApiKey",
    "fel-assistant-manager.chat.missingRequired.felOpenAiAssistantId",
    "fel-assistant-manager.chat.missingRequired.salesChannelId",
    "fel-assistant-manager.chat.missingRequired.storefrontUrl",
    "fel-assistant-manager.chat.noRunId",
    "fel-assistant-manager.chat.notifications",
    "fel-assistant-manager.chat.routeDescription",
    "fel-assistant-manager.chat.suggestions",
    "fel-assistant-manager.chat.withRunId",
    "fel-assistant-manager.chatlog.addNew",
    "fel-assistant-manager.chatlog.cancel",
    "fel-assistant-manager.chatlog.column.assistantId",
    "fel-assistant-manager.chatlog.column.runsCount",
    "fel-assistant-manager.chatlog.column.salesChannelId",
    "fel-assistant-manager.chatlog.details",
    "fel-assistant-manager.chatlog.save",
    "fel-assistant-manager.chatlog.selectItemsToDelete",
    "fel-assistant-manager.contentCheck.domain",
    "fel-assistant-manager.contentCheck.readableFormat",
    "fel-assistant-manager.contentCheck.salesChannelAccessKey",
    "fel-assistant-manager.contentEditor.inheritanceRestored",
    "fel-assistant-manager.delete.cancel",
    "fel-assistant-manager.delete.confirm",
    "fel-assistant-manager.delete.confirmDeleteLog",
    "fel-assistant-manager.delete.confirmDeleteTag",
    "fel-assistant-manager.delete.confirmDeleteVectorStore",
    "fel-assistant-manager.delete.confirmMessage",
    "fel-assistant-manager.delete.no",
    "fel-assistant-manager.delete.selectItemsToDelete",
    "fel-assistant-manager.delete.yes",
    "fel-assistant-manager.exception.failedToInitThread",
    "fel-assistant-manager.exception.maxLoopAttemptsReached",
    "fel-assistant-manager.exception.missingActionInRequest",
    "fel-assistant-manager.exception.missingAssistantIdForCreateThread",
    "fel-assistant-manager.exception.missingAssistantIdForSendMessage",
    "fel-assistant-manager.exception.missingMessageForSendMessage",
    "fel-assistant-manager.exception.missingMessageIdForDeleteMessage",
    "fel-assistant-manager.exception.missingOpenaiApiKey",
    "fel-assistant-manager.exception.missingOpenaiBaseUrl",
    "fel-assistant-manager.exception.missingRunIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForDeleteMessage",
    "fel-assistant-manager.exception.missingThreadIdForDeleteThread",
    "fel-assistant-manager.exception.missingThreadIdForFetchMessages",
    "fel-assistant-manager.exception.missingThreadIdForGetThread",
    "fel-assistant-manager.exception.missingThreadIdForSendMessage",
    "fel-assistant-manager.fel-test-api-key.error.baseUrlNotValid",
    "fel-assistant-manager.fel-test-api-key.error.missingApiKey",
    "fel-assistant-manager.fel-test-api-key.error.missingBaseUrl",
    "fel-assistant-manager.fel-test-api-key.error.trimApiKey",
    "fel-assistant-manager.fel-test-api-key.success.notificationLabel",
    "fel-assistant-manager.files.vectorStore.fileIsEmpty",
    "fel-assistant-manager.files.vectorStore.noFilesAttached",
    "fel-assistant-manager.filter.filterCategories",
    "fel-assistant-manager.filter.filterManufacturer",
    "fel-assistant-manager.filter.filterProperties",
    "fel-assistant-manager.filter.generalName",
    "fel-assistant-manager.filter.ui.filteringFailedNoResultsFound",
    "fel-assistant-manager.filter.ui.hideFilter",
    "fel-assistant-manager.filter.ui.setVisibleToConfig",
    "fel-assistant-manager.filter.ui.showFilter",
    "fel-assistant-manager.filter.ui.showSelectedOnly",
    "fel-assistant-manager.general.assistantName",
    "fel-assistant-manager.general.configuration",
    "fel-assistant-manager.general.example",
    "fel-assistant-manager.general.openAIStatus",
    "fel-assistant-manager.general.openAITokenizer",
    "fel-assistant-manager.general.openAIUsage",
    "fel-assistant-manager.instructionIssues.logFunctionNote",
    "fel-assistant-manager.instructionIssues.productPropertyNote",
    "fel-assistant-manager.instructionIssues.searchLogsAggressiveUsage",
    "fel-assistant-manager.instructions.chatbot.gptThree",
    "fel-assistant-manager.log.addNewLog",
    "fel-assistant-manager.log.categoryId",
    "fel-assistant-manager.log.filter.all",
    "fel-assistant-manager.log.filter.assistant",
    "fel-assistant-manager.log.filter.label",
    "fel-assistant-manager.log.tag.backToTags",
    "fel-assistant-manager.notifications.errorWhileImportingLogs",
    "fel-assistant-manager.notifications.logsImported",
    "fel-assistant-manager.notifications.noFileSelected",
    "fel-assistant-manager.storageManager.cacheName.managerIsLoading",
    "fel-assistant-manager.storageManager.items",
    "fel-assistant-manager.storageManager.storages"
]

All Translations Summary:
{
    "fel-assistant-manager": 654,
    "fel-assistant-manager.tags-tips": 21,
    "fel-assistant-manager.tags-tips.generalNotesForTags": 3,
    "fel-assistant-manager.tags-tips.generalNotesForTags.list": 2,
    "fel-assistant-manager.tags-tips.tagNamingFlexibility": 2,
    "fel-assistant-manager.tags-tips.keepTagNamesSimpleAndClear": 3,
    "fel-assistant-manager.tags-tips.keepTagNamesSimpleAndClear.list": 2,
    "fel-assistant-manager.tags-tips.useTagAliasesForSimilarTerms": 3,
    "fel-assistant-manager.tags-tips.useTagAliasesForSimilarTerms.list": 2,
    "fel-assistant-manager.tags-tips.avoidUsingSpecialCharacters": 3,
    "fel-assistant-manager.tags-tips.avoidUsingSpecialCharacters.list": 2,
    "fel-assistant-manager.tags-tips.useActiveVoiceForTags": 3,
    "fel-assistant-manager.tags-tips.useActiveVoiceForTags.list": 2,
    "fel-assistant-manager.tags-tips.reviewExistingTagsBeforeAddingNewOnes": 3,
    "fel-assistant-manager.tags-tips.reviewExistingTagsBeforeAddingNewOnes.list": 2,
    "fel-assistant-manager.general": 81,
    "fel-assistant-manager.general.aiToolsTranslation": 20,
    "fel-assistant-manager.files": 20,
    "fel-assistant-manager.files.vectorStore": 13,
    "fel-assistant-manager.create": 15,
    "fel-assistant-manager.create.form": 2,
    "fel-assistant-manager.delete": 12,
    "fel-assistant-manager.contentEditor": 53,
    "fel-assistant-manager.contentEditor.editable": 46,
    "fel-assistant-manager.contentEditor.editable.enablePlugin": 2,
    "fel-assistant-manager.contentEditor.editable.openAiWhitelistPropertyGroups": 2,
    "fel-assistant-manager.contentEditor.editable.openAiBlacklistPropertyGroups": 2,
    "fel-assistant-manager.contentEditor.editable.openAiBlacklistPropertyOptions": 2,
    "fel-assistant-manager.contentEditor.editable.openAiBlacklistManufacturer": 2,
    "fel-assistant-manager.contentEditor.editable.openAiBlacklistCategory": 2,
    "fel-assistant-manager.contentEditor.editable.openAiFilterCategoryLevel": 2,
    "fel-assistant-manager.contentEditor.editable.openAiReturnCategoriesWithIds": 2,
    "fel-assistant-manager.contentEditor.editable.swApiSetPropertyIdsAsKeys": 2,
    "fel-assistant-manager.contentEditor.editable.openAiFilterMaxOptions": 2,
    "fel-assistant-manager.contentEditor.editable.openAiSearchLimit": 2,
    "fel-assistant-manager.contentEditor.editable.felOpenAiAssistantId": 2,
    "fel-assistant-manager.contentEditor.editable.openAiChatbotName": 2,
    "fel-assistant-manager.contentEditor.editable.openAiMetaInformationField": 2,
    "fel-assistant-manager.contentEditor.editable.openAiInitAdditionalInstructions": 2,
    "fel-assistant-manager.contentEditor.editable.openAiFaqField": 2,
    "fel-assistant-manager.contentEditor.editable.notFound": 2,
    "fel-assistant-manager.contentEditor.editable.idsNotFound": 2,
    "fel-assistant-manager.contentEditor.editable.felIdsToNamesFinished": 2,
    "fel-assistant-manager.contentEditor.editable.openAiFilterMaxDescriptionLengthSearchResults": 2,
    "fel-assistant-manager.contentEditor.editable.openAiFilterMaxDescriptionLength": 2,
    "fel-assistant-manager.contentEditor.editable.openAiOrganizationId": 2,
    "fel-assistant-manager.contentEditor.editable.openAiUserInputMaxLength": 2,
    "fel-assistant-manager.catchErrors": 82,
    "fel-assistant-manager.catchSuccess": 17,
    "fel-assistant-manager.catchInfo": 9,
    "fel-assistant-manager.catchWarning": 3,
    "fel-assistant-manager.backendAssistant": 7,
    "fel-assistant-manager.backendAssistant.helper": 7,
    "fel-assistant-manager.instructions": 3,
    "fel-assistant-manager.instructions.chatbot": 2,
    "fel-assistant-manager.form": 3,
    "fel-assistant-manager.form.willBeSavedIn": 2,
    "fel-assistant-manager.filter": 30,
    "fel-assistant-manager.filter.ui": 19,
    "fel-assistant-manager.contentCheck": 30,
    "fel-assistant-manager.storageManager": 13,
    "fel-assistant-manager.storageManager.cacheName": 4,
    "fel-assistant-manager.log": 104,
    "fel-assistant-manager.log.descriptions": 5,
    "fel-assistant-manager.log.priorities": 3,
    "fel-assistant-manager.log.download": 3,
    "fel-assistant-manager.log.filter": 18,
    "fel-assistant-manager.log.tag": 31,
    "fel-assistant-manager.chatlog": 33,
    "fel-assistant-manager.chatlog.column": 10,
    "fel-assistant-manager.chat": 67,
    "fel-assistant-manager.chat.tool": 9,
    "fel-assistant-manager.chat.tool.productProperties": 2,
    "fel-assistant-manager.chat.tool.logMessages": 2,
    "fel-assistant-manager.chat.tool.toolsPerformance": 2,
    "fel-assistant-manager.chat.autoChat": 9,
    "fel-assistant-manager.chat.missingRequired": 4,
    "fel-assistant-manager.instructionIssues": 8,
    "fel-assistant-manager.notifications": 4,
    "fel-assistant-manager.exception": 16,
    "fel-assistant-manager.fel-test-api-key": 14,
    "fel-assistant-manager.fel-test-api-key.success": 4,
    "fel-assistant-manager.fel-test-api-key.error": 7,
    "fel-assistant-manager.fel-custom-tool": 9
}

Statistics:
Total Translations: 656
Used Translations: 554
Unused Translations: 102
Usage Percentage: 84.45%

Context-Specific Statistics:
  administration Unused: 47 (7.16%)
  storefront Unused: 26 (3.96%)
  unknown Unused: 29 (4.42%)
