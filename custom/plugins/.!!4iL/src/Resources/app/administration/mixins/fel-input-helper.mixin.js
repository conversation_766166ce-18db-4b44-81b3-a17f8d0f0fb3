const { Component, Mixin } = Shopware;

Mixin.register('fel-input-helper-mixin', {

    data() {
        return {
            isCreated: false,
            canCreate: false,
            canUpdate: false,
            isUpdated: false,
            availableFunctionsObject: {},
            toSubmit: {
                'fel--sw--assistant_name': null,
                'fel--sw--assistant_tools': [],
                'fel--sw--assistant_files': [],
                'fel--sw--assistant_vector_store': [],
                'fel--sw--assistant_attached_files': [],
                'fel--sw--assistant_instruction': null,
                'fel--sw--assistant_model': null,
                'fel--sw--assistant_temperature': null,
            },
            toSubmitRequired: [
                'fel--sw--assistant_name',
                'fel--sw--assistant_instruction',
                'fel--sw--assistant_model',
                'fel--sw--assistant_temperature',
            ],
        };
    },

    methods: {

        checkIfRetrievalIsInTools(searchIn) {
            for (var i = 0; i < searchIn.length; i++) {
                if ('retrieval' === searchIn[i].type) {
                    return true;
                }
            }
            return false;
        },

        createOrUpdateAssistant(assistantId) {
            this.checkRequirements();

            if (this.canCreate) {
                assistantId = assistantId ? `/${assistantId}` : '' ;

                var submit = {
                    name: this.toSubmit['fel--sw--assistant_name'],
                    tools: [],
                    instructions: this.toSubmit['fel--sw--assistant_instruction'],
                    model: this.toSubmit['fel--sw--assistant_model'],
                    temperature: this.toSubmit['fel--sw--assistant_temperature'],
                    tool_resources: {
                        file_search: {vector_store_ids: this.toSubmit['fel--sw--assistant_vector_store']},
                    }
                };

                if (this.toSubmit['fel--sw--assistant_vector_store'].length) {
                    submit.tools.push({type: 'file_search'});
                }

                if (this.toSubmit['fel--sw--assistant_tools'].length) {
                    var getFnList = this.availableFunctionsObject.list;
                    for (var i = 0; i < this.toSubmit['fel--sw--assistant_tools'].length; i++) {
                        if ('undefined' !== typeof getFnList[this.toSubmit['fel--sw--assistant_tools'][i]]) {
                            submit.tools.push({type: 'function', function: getFnList[this.toSubmit['fel--sw--assistant_tools'][i]]});
                        }
                    }
                }

                this.httpClientPost(`${this.pluginConfig.apiUrl}/assistants${assistantId}`, submit, (response) => {
                    this.canCreate = false;
                    this.canUpdate = false;
                    if ('undefined' !== typeof response.data.id) {
                        this.isCreated = response.data.id;
                        this.isUpdated = true;
                        this.createNotificationSuccess({message: `${response.data.name}\n${response.data.id}`});
                    }
                    else if ('undefined' !== typeof response.data) {
                        this.createNotificationError({message: JSON.stringify(response.data)});
                    }
                });
            }
        },

        resetSelect(querySel) {
            var getActiveOptions = document.querySelectorAll(`${querySel} option:checked`);
            if (getActiveOptions.length) {
                for (let i = 0; i < getActiveOptions.length; i++) {
                    getActiveOptions[i].selected = null;
                }
                querySel = querySel.replace(/[\#|.]/g, '');
                this.toSubmit[querySel] = [];
                this.canUpdate = true;
            }
        },

        onAssistantInput(event) {
            if ('undefined' !== typeof event.target) {
                var elName = event.target.name;
                var setValue = event.target.value;
                this.canUpdate = true;

                if ('fel--sw--assistant_tools' === elName) {
                    var getCheckedFunctions = document.body.querySelectorAll(`[name="${elName}"]:checked`);
                    setValue = [];
                    for (var i = 0; i < getCheckedFunctions.length; i++) {
                        setValue.push(getCheckedFunctions[i].value);
                    }
                }
                else if ('undefined' !== typeof event.target.options) {
                    var getSelectedOptions = this.$el.querySelectorAll('select[name="fel--sw--assistant_files"] option:checked');
                    if (20 >= getSelectedOptions.length) {
                        setValue = [];
                        for (var i = 0; i < event.target.options.length; i++) {
                            var option = event.target.options[i];
                            if (option.selected) {
                                setValue.push(option.value);
                            }
                        }
                    }
                }

                if ('fel--sw--assistant_temperature' === elName) {
                    setValue = parseFloat(setValue);
                }

                this.toSubmit[elName] = setValue;

                this.checkRequirements();
            }
        },

        checkRequirements() {
            this.canCreate = true;
            for (var i = 0; i < this.toSubmitRequired.length; i++) {
                var isRequired = this.toSubmitRequired[i];
                var checkStr = this.toSubmit[isRequired];

                if (checkStr && 'function' === typeof checkStr.trim) {
                    checkStr = checkStr.trim();
                }
                if ('' == checkStr || null == checkStr || !checkStr) {
                    this.canCreate = false;
                }
            }
            this.modelError = 'gpt-3.5-turbo' === this.toSubmit['fel--sw--assistant_model'];
        },

    },

});