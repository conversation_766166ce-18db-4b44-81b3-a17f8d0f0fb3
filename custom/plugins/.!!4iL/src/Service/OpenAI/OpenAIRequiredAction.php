<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Framework\Routing\Router;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Exception;
use const JSON_UNESCAPED_SLASHES;
use const JSON_UNESCAPED_UNICODE;
use function array_column;
use function array_diff;
use function array_filter;
use function array_key_first;
use function array_keys;
use function array_merge;
use function array_pop;
use function array_slice;
use function array_sum;
use function array_unique;
use function array_values;
use function count;
use function date;
use function explode;
use function implode;
use function in_array;
use function ini_get;
use function intval;
use function is_array;
use function is_iterable;
use function is_numeric;
use function is_string;
use function json_decode;
use function json_encode;
use function ltrim;
use function method_exists;
use function str_starts_with;
use function strip_tags;
use function strtolower;
use function substr;
use function trim;
use function usort;

class OpenAIRequiredAction
{
    const FEL_PRETTY_PRINT = JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE;

    private array $swApiHeader = [];

    private mixed $swApiUrl = null;

    private array $productProperties = [];

    private array $parsedArguments = [];

    private array $notifications = [];

    private array $storeApiError = [];

    public function __construct(
        private SystemConfigService $configService,
        private HttpClientInterface $httpClient,
        private Router $router,
        private Request $request,
        private SalesChannelContext $salesContext,
        private OpenAIConfigService $aiConfig,
        private TranslatorInterface $translator
    ) {
        $this->swApiUrl = $request->attributes->get('sw-storefront-url');
        $this->swApiHeader = [
            'accept' => 'application/json',
            'content-type' => 'application/json',
            'sw-access-key' => $salesContext->getSalesChannel()->getAccessKey(),
        ];
    }

    private function addNotification(string $role, mixed $message, ?bool $merge = false): self
    {
        if ($merge) {
            $this->notifications[$role] = array_merge($this->notifications[$role] ?? [], $message);
        } else {
            $this->notifications[$role][] = $message;
        }

        return $this;
    }

    private function getNotifications(): array
    {
        return $this->notifications;
    }

    public function requiredAction(array $toolCalls, array $post = []): array
    {
        $toolResponse = fn($id, $out) => ['tool_call_id' => $id, 'output' => $out];
        $executed = [];
        foreach($toolCalls as $fnCall) {
            if ($method = ($fnCall['function']['name'] ?? null)) {
                if (method_exists($this, $method)) {
                    $validate = [];

                    if ($args = $fnCall['function']['arguments']) {
                        $args = json_decode($args, true);
                    }

                    if ($args) {
                        if ($id = ($args['id'] ?? null) AND (is_array($id) OR is_string($id))) {
                            $validate['id'] = $id;
                        }
                        if ($query = ($args['query'] ?? null)) {
                            $validate['query'] = is_iterable($query) ? implode(' ', $query) : $query ;
                        }
                        if (($args['categories'] ?? null) AND is_array($args['categories'])) {
                            $validate['categories'] = $args['categories'];
                        }
                        if (($args['properties'] ?? null) AND is_array($args['properties'])) {
                            $validate['properties'] = $args['properties'];
                        }
                        if (($args['manufacturer'] ?? null) AND is_array($args['manufacturer'])) {
                            $validate['manufacturer'] = $args['manufacturer'];
                        }
                        if (($args['price_min'] ?? null) AND is_numeric($args['price_min'])) {
                            $validate['price_min'] = (int) $args['price_min'];
                        }
                        if (($args['price_max'] ?? null) AND is_numeric($args['price_max'])) {
                            $validate['price_max'] = (int) $args['price_max'];
                        }
                        if (($args['limit'] ?? null) AND is_numeric($args['limit'])) {
                            $validate['limit'] = $args['limit'];
                        }
                        if (($args['order'] ?? null) AND is_string($args['order'])) {
                            $validate['order'] = $args['order'];
                        }
                        if (($args['url'] ?? null) AND is_string($args['url'])) {
                            $validate['url'] = $args['url'];
                        }
                        if ($args['action'] ?? null) {
                            $validate['action'] = $args['action'];
                        }
                    }

                    $executed[] = $toolResponse($fnCall['id'], $this->{$method}($validate));
                } else {
                    $executed[] = $toolResponse($fnCall['id'], "Function not found: {$method}");
                }
            } else if ($fnCall['id'] ?? null) {
                $executed[] = $toolResponse($fnCall['id'], 'An error occured');
            }
        }

        return $executed;
    }

    /** Standard '/store-api' client */
    private function storeApiClient(string $method, string $path, array $content = []): array
    {
        try {
            return $this->httpClient->request($method, $this->swApiUrl.'/store-api/'.ltrim($path, '/'), [
                'headers' => $this->swApiHeader,
                'json' => $content
            ])->toArray();
        } catch(Exception $e) {
            $this->storeApiError[] = $e->getMessage();
        }

        return [];
    }

    public function getStoreApiError(): array
    {
        return $this->storeApiError;
    }

    /** Callable functions (required_action) */

    private function go_to_url(array $url): string
    {
        if ($url = ($url['url'] ?? null)) {
            $canRedirect = true;
            if (str_starts_with($url, 'http')) {
                $storeUrl = $this->request->attributes->get('sw-sales-channel-absolute-base-url');
                if (!str_starts_with($url, $storeUrl)) {
                    $canRedirect = false;
                }
            }
            if ($canRedirect) {
                $exec = ['exec' => __FUNCTION__, 'url' => $url];
            }
        }

        return json_encode($exec ?? 'Redirection failed');
    }

    private function ui_action(array $args): string
    {
        return json_encode(['exec' => __FUNCTION__, 'args' => $args]);
    }

    private function get_meta_information(): string
    {
        return json_encode(['output' => [
            'message' => 'Diese Funktion ist für den Produktberater nicht verfügbar.',
            'available' => false
        ]]);
    }

    private function get_date_time(): string
    {
        return json_encode(date('D, Y-M-d H:i:s ') . ini_get('date.timezone'));
    }

    private function get_chatbot_name(): string
    {
        return json_encode(['output' => [
            'name' => 'Produktberater',
            'description' => 'Ein Berater für Produkte in unserem Shop.'
        ]]);
    }

    private function get_countries(): string
    {
        $entity = $this->storeApiClient('POST', '/country', [
            'includes' => ['country' => ['name', 'translated']]
        ]);
        $response = [];

        if ($entity['elements'] ?? null) {
            foreach($entity['elements'] as $el) {
                if ($name = ($el['translated']['name'] ?? $el['name'] ?? null)) {
                    $response[] = $name;
                }
            }
        }

        return json_encode($response ? ['countries' => $response] : 'No countries found', self::FEL_PRETTY_PRINT);
    }

    private function get_payment_methods(): string
    {
        $pMethods = $this->storeApiClient('POST', '/payment-method', [
            'includes' => ['payment_method' => ['name', 'description', 'translated']]
        ]);
        $response = [];

        if ($pMethods['elements'] ?? null) {
            foreach($pMethods['elements'] as $el) {
                if ($name = ($el['translated']['name'] ?? $el['name'] ?? null)) {
                    $response[] = [
                        'name' => $name,
                        'description' => $el['translated']['description'] ?? $el['description'] ?? null,
                    ];
                }
            }
        }

        return json_encode($response ? ['payment_methods' => $response] : 'No payment methods found', self::FEL_PRETTY_PRINT);
    }

    private function get_delivery_times(): string
    {
        $search = $this->storeApiClient('POST', '/shipping-method', [
            'includes' => [
                'delivery_time'   => ['name', 'translated'],
                'shipping_method' => ['name', 'description', 'translated', 'deliveryTime'],
            ],
        ]);
        $response = [];

        if ($search['elements'] ?? null) {
            foreach($search['elements'] as $el) {
                if ($name = ($el['translated']['name'] ?? $el['name'] ?? null)) {
                    $response[$name] = [
                        'name' => $name,
                        'delivery_time' => $el['deliveryTime']['translated']['name'] ?? $el['deliveryTime']['name'] ?? null,
                        'description' => $el['translated']['description'] ?? $el['description'] ?? null,
                    ];
                }
            }
        }

        return json_encode($response ? ['delivery_times' => array_values($response)] : 'No delivery times found', self::FEL_PRETTY_PRINT);
    }

    private function get_categories(?array $args = []): string
    {
        return json_encode([
            'categories' => $this->get_product_properties($args, false)['categories'] ?? 'No categories found'
        ], self::FEL_PRETTY_PRINT);
    }

    private function get_manufacturer(?array $args = []): string
    {
        return json_encode([
            'manufacturer' => $this->get_product_properties($args, false)['properties']['Manufacturer'] ?? 'No manufacturer'
        ], self::FEL_PRETTY_PRINT);
    }

    private function get_product_properties(?array $args = [], bool $jsonEncoded = true, bool $fullMap = false): array|string
    {
        if (!$fullMap AND ($args['categories'] ?? null)) {
            if ($catFilter = $this->createCategoryCriteriaFromNames($args['categories'])) {
                $this->addNotification('system', 'Properties and categories are reduced for requested categories.');
                $filter[] = $catFilter;
            }
        }

        $fetch = $this->storeApiClient('POST', '/product', [
            'limit' => 1,
            'filter' => $filter ?? [],
            'includes' => [
                'category' => ['id', 'name', 'breadcrumb', 'translated', 'level'],
                'product_manufacturer' => ['id', 'name', 'translated'],
                'product' => ['id'],
                'property_group' => ['id', 'name', 'translated', 'position', 'displayType', 'filterable', 'visibleOnProductDetailPage'],
            ],
            'aggregations' => [
                [
                    'name' => 'fel_categories',
                    'type' => 'entity',
                    'field' => 'product.categories.id',
                    'definition' => 'category',
                ],[
                    'name' => 'fel_manufacturer',
                    'type' => 'entity',
                    'field' => 'product.manufacturerId',
                    'definition' => 'product_manufacturer',
                ],[
                    'name' => 'fel_options_groups',
                    'type' => 'entity',
                    'field' => 'product.options.group.id',
                    'definition' => 'property_group',
                ],[
                    'name' => 'fel_properties_groups',
                    'type' => 'entity',
                    'field' => 'product.properties.group.id',
                    'definition' => 'property_group',
                ],[
                    'name' => 'fel_options',
                    'type' => 'terms',
                    'field' => 'product.options.group.name',
                    'definition' =>' property_group',
                    'aggregation' => [
                        'name' => 'options',
                        'type' => 'entity',
                        'field' => 'product.options.id',
                        'definition' => 'property_group_option',
                    ]
                ],[
                    'name' => 'fel_properties',
                    'type' => 'terms',
                    'field' => 'product.properties.group.name',
                    'definition' =>' property_group',
                    'aggregation' => [
                        'name' => 'options',
                        'type' => 'entity',
                        'field' => 'product.properties.id',
                        'definition' => 'property_group_option',
                    ]
                ],
            ],
        ]);

        $felOptionGroups = [
            $fetch['aggregations']['fel_properties_groups']['entities'] ?? [],
            $fetch['aggregations']['fel_options_groups']['entities'] ?? [],
        ];

        $mappedBuckets = array_merge(
            ['properties' => $fetch['aggregations']['fel_properties']['buckets'] ?? []],
            ['options'    => $fetch['aggregations']['fel_options']['buckets'] ?? []]
        );

        $groupMap = [];
        $groupCount = [];
        $mapProperties = [
            'categoryList' => [],
            'propertyMap' => [
                'manufacturer' => [],
                'properties' => [],
                'options' => [],
            ]
        ];
        $response = [
            '_meta_data' => [],
            'properties' => [],
            'categories' => []
        ];
        $maxOptions = $this->aiConfig->getOpenAiFilterMaxOptions();
        $allowedProperties = $this->aiConfig->getOpenAiFilterAllowedProperties();
        $blacklistPropertyOptions = $this->aiConfig->getOpenAiBlacklistPropertyOptions();

        // categories
        if ($aggrCategories = ($fetch['aggregations']['fel_categories']['entities'] ?? [])) {
            usort($aggrCategories, fn($a, $b) => $a['level'] <=> $b['level']);

            foreach($aggrCategories as $category) {
                if ($breadcrumb = ($category['translated']['breadcrumb'] ?? $category['breadcrumb'] ?? null)) {
                    $name = $category['translated']['name'] ?? $category['name'];
                    if ($fullMap) {
                        $mapProperties['categoryList'][$category['id']] = $name;
                    } else {
                        if (!in_array($category['id'], $this->aiConfig->getBlacklistCategory())) {
                            $setSlice = $category['level'] >= 5
                                ? intval(count($breadcrumb) / 2) : $this->aiConfig->getOpenAiFilterCategoryLevel() ;
                            $response['categories'][] = [
                                'name' => $name,
                                'parent' => array_slice($breadcrumb, $setSlice, -1),
                                'url' => $this->router->generate('frontend.navigation.page', ['navigationId' => $category['id']]),
                            ];
                        }
                    }
                }
            }
        }

        // option / property groups
        if (!$fullMap AND $felOptionGroups) {
            foreach($felOptionGroups as $felOptionGroup) {
                foreach($felOptionGroup as $entity) {
                    if (($entity['id'] ?? null) AND !isset($groupMap[$entity['id']])) {
                        $groupMap[$entity['id']] = $entity;
                    }
                }
            }
        }

        // options / properties
        if ($mappedBuckets) {
            foreach($mappedBuckets as $propGroup => $buckets) {
                foreach($buckets as $bucket) {
                    if ($groupName = ($bucket['key'] ?? null)) {
                        if ($bucket['options']['entities'] ?? null) {
                            foreach($bucket['options']['entities'] as $entity) {
                                $name = $entity['translated']['name'] ?? $entity['name'];

                                if ($fullMap) {
                                    $mapProperties['groupMap'][$propGroup][$entity['groupId']] = $groupName;
                                    $mapProperties['propertyMap'][$propGroup][$entity['id']] = $name;
                                    $mapProperties['properties'][$groupName][$entity['id']] = $name;
                                } else {
                                    $groupEntity = $groupMap[$entity['groupId']];
                                    $groupCount[$entity['groupId']] ??= 0;

                                    if ($groupEntity['visibleOnProductDetailPage']) {
                                        if ($groupCount[$entity['groupId']] === $maxOptions) {
                                            break;
                                        }

                                        $response['properties'][$groupName] ??= [];
                                        if (!in_array($name, $response['properties'][$groupName])) {
                                            $isAllowed = true;

                                            if ($allowedProperties AND !in_array($entity['groupId'], $allowedProperties)) {
                                                $isAllowed = false;
                                            }
                                            if ($isAllowed AND $blacklistPropertyOptions AND in_array($entity['id'], $blacklistPropertyOptions)) {
                                                $isAllowed = false;
                                            }
                                            if ($isAllowed) {
                                                $response['properties'][$groupName][] = $name;
                                                ++$groupCount[$entity['groupId']];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (!$fullMap) {
                $response['properties'] = array_filter($response['properties']);
            }
        }

        // manufacturer
        if ($manufacturers = ($fetch['aggregations']['fel_manufacturer']['entities'] ?? [])) {
            $groupCount['FEL_SW_Manufacturer'] = 0;
            foreach($manufacturers as $manufacturer) {
                if ($name = ($manufacturer['translated']['name'] ?? $manufacturer['name'] ?? null)) {
                    if ($fullMap) {
                        $mapProperties['propertyMap']['manufacturer'][$manufacturer['id']] = $name;
                    } else {
                        if (!in_array($manufacturer['id'], $this->aiConfig->getBlacklistManufacturer())) {
                            $response['properties']['Manufacturer'][] = $name;
                            if (++$groupCount['FEL_SW_Manufacturer'] === $maxOptions) {
                                break;
                            }
                        }
                    }
                }
            }
        }

        if ($fullMap) {
            $response = $mapProperties;
        } else {
            $response['_meta_data'] = [
                'notification' => $this->getNotifications(),
                'count' => [
                    'properties' => array_sum($groupCount),
                    'categories' => count($response['categories']),
                ],
            ];
        }

        return $jsonEncoded ? json_encode($response, self::FEL_PRETTY_PRINT) : $response ;
    }

    private function get_product_details(array $args = []): string
    {
        if (($args['id'] ?? null) AND is_string($args['id'])) {
            $get = $this->product_search($args, false);
            $get['exec'] = 'product_details';
            $getDescription = $get['fetch']['elements'][0]['translated']['description']
                ?? $get['fetch']['elements'][0]['description']
                ?? 'No description' ;

            if (1 === $get['total'] AND ($get['items']['products'][0] ?? null)) {
                $get['items']['products'][0]['description'] = trim(strip_tags(substr(
                    $getDescription, 0, $this->aiConfig->getOpenAiFilterMaxDescriptionLength()
                )));
            } else {
                $get = null;
            }
        }

        return json_encode($get ?? 'Product not found', self::FEL_PRETTY_PRINT);
    }

    public function product_search(array $args, bool $jsonEncoded = true): array|string
    {
        $this->productProperties = $this->get_product_properties(null, false, true);
        $this->parsedArguments = $this->parseRequiredArgs($args);

        $getProducts = $this->runProductSearch($args);

        if ($categoryMap = ($getProducts['fetch']['aggregations']['fel_categories']['entities'] ?? [])) {
            $categoryMap = $this->getProductsCategoryMap($categoryMap);
        }

        $getProducts = array_merge([
            'exec' => 'product_search',
            'total' => 0,
            'totalAll' => 0,
            'args' => $getProducts['getFilter']['args'],
            'propertyData' => $this->createPropertyParameter(),
            'categoryMap' => $categoryMap,
            'items' => [
                '_meta_data' => [
                    'products_total' => $getProducts['total'],
                    'products_children' => $getProducts['totalAll'],
                    'limit' => $this->aiConfig->getOpenAiSearchLimit(),
                    'notification' => $this->getNotifications(),
                ],
                'products' => $getProducts['fetch']['error'] ?? $this->truncateSearchResult($getProducts['fetch']['elements'] ?? []),
            ],
        ], $getProducts);

        return $jsonEncoded ? json_encode($getProducts, self::FEL_PRETTY_PRINT) : $getProducts ;
    }

    /**
     * helper
     */

    private function runTruncateSearch(array $args)
    {
        $tryAgain = false;
        $args['_truncate']['count'] ??= 0;

        if (!empty($args['query']) AND 5 >= $args['_truncate']['count']) {
            $args['_truncate']['originalQuery'] ??= $args['query'];
            $newQuery = array_filter(explode(' ', $args['query']));
            array_pop($newQuery);
            if ($newQuery) {
                $tryAgain = true;
                ++$args['_truncate']['count'];
                $args['query'] = implode(' ', $newQuery);
                $this->parsedArguments['parsed']['parameter']['search'] = $args['query'];
            }
        }

        return [
            'tryAgain' => $tryAgain,
            'tries' => $args['_truncate']['count'],
            'args' => $args,
        ];
    }

    private function runProductSearch(array $args)
    {
        $getProducts = $this->getFilteredProducts($args);

        if (0 === $getProducts['total']) {
            $try = $this->runTruncateSearch($args);

            if (true === $try['tryAgain']) {
                return $this->runProductSearch($try['args']);
            }
        }

        if (isset($args['_truncate']['originalQuery'])) {
            $this->addNotification('user', $this->translator->trans('fel-gpt-assistant.error.chat.queryFailedTruncatedTo',
                ['%from%' => $args['_truncate']['originalQuery'], '%to%' => $args['query']]
            ));
        }

        if (isset($this->parsedArguments['toFix']['propsInDiffGroups'])) {
            $this->addNotification('system', 'Some properties are located in different property groups, the search is less strict.');
        }

        return $getProducts;
    }

    private function getFilteredProducts(array $args): array
    {
        $getFilter = $this->createProductFilterFromArgs($args);
        $setLimit = 5;
        $fetch = $this->storeApiClient('POST', '/product', [
            'limit'    => $setLimit + 1,
            'filter'   => $getFilter['filter'],
            'sort'     => $getFilter['sort'],
            'includes' => ['product' => ['id']],
            'aggregations' => [
                [
                    'name' => 'fel_parent_ids',
                    'type' => 'filter',
                    'filter' => [[
                        'type' => 'not',
                        'operator' => 'or',
                        'queries' => [
                            ['type' => 'equals', 'field' => 'product.childCount', 'value' => 0],
                            ['type' => 'equals', 'field' => 'product.parentId', 'value' => null]
                        ]
                    ]],
                    'aggregation' => [
                        'name' => 'fel_parent_ids',
                        'type' => 'terms',
                        'field' => 'product.parentId',
                        'definition' => 'product',
                    ]
                ],
                [
                    'name' => 'fel_not_parent_ids',
                    'type' => 'filter',
                    'filter' => [
                        ['type' => 'equals', 'field' => 'product.childCount', 'value' => 0],
                        ['type' => 'equals', 'field' => 'product.parentId', 'value' => null]
                    ],
                    'aggregation' => [
                        'name' => 'fel_not_parent_ids',
                        'type' => 'terms',
                        'field' => 'product.id',
                        'definition' => 'product',
                    ]
                ]
            ],
        ]);

        $total = $fetch['total'] ?? 0;
        if ($setLimit >= $total AND isset($fetch['elements'])) {
            $productIds = array_column($fetch['elements'], 'id');
        } else {
            $productIds = array_column(array_merge(
                $fetch['aggregations']['fel_parent_ids']['buckets'] ?? [],
                $fetch['aggregations']['fel_not_parent_ids']['buckets'] ?? []
            ), 'key');
        }

        $fetchProducts = $this->getProductsByIds($productIds, $getFilter);

        return [
            'total' => $fetchProducts['total'] ?? 0,
            'totalAll' => $total,
            'getFilter' => $getFilter,
            'fetch' => $fetchProducts,
        ];
    }

    private function getProductsByIds(array|string $ids, array $setFilter): array
    {
        if ($ids) {
            return $this->storeApiClient('POST', '/product', [
                'total-count-mode' => 1,
                'limit' => $this->aiConfig->getOpenAiSearchLimit(),
                'filter' => [[
                    'type' => 'equalsAny',
                    'field' => 'product.id',
                    'value' => is_string($ids) ? [$ids] : $ids,
                ]],
                'sort' => $setFilter['sort'] ?? [],
                'includes' => [
                    'category' => ['id', 'name', 'translated', 'parentId', 'breadcrumb', 'path', 'level'],
                    'calculated_price' => ['totalPrice'],
                    'calculated_cheapest_price' => ['totalPrice'],
                    'delivery_time' => ['name', 'translated.name'],
                    'media_thumbnail' => ['id', 'url'],
                    'media' => ['id', 'thumbnails'],
                    'product_manufacturer' => ['id', 'name', 'translated.name'],
                    'product_media' => ['id', 'media'],
                    'property_group_option' => ['id', 'name', 'translated', 'position', 'colorHexCode', 'groupId', 'group'],
                    'property_group' => ['id', 'name', 'translated', 'position', 'displayType', 'filterable', 'visibleOnProductDetailPage'],
                    'product' => [
                        'active',
                        'available',
                        'calculatedMaxPurchase',
                        'calculatedPrice.totalPrice',
                        'categoryIds',
                        'categoryTree',
                        'childCount',
                        'cover',
                        'deliveryTime',
                        'description',
                        'ean',
                        'id',
                        'isCloseout',
                        'isNew',
                        'manufacturer',
                        'manufacturerId',
                        'markAsTopseller',
                        'maxPurchase',
                        'minPurchase',
                        'name',
                        'optionIds',
                        'options',
                        'parentId',
                        'productNumber',
                        'propertyIds',
                        'properties',
                        'purchaseSteps',
                        'shippingFree',
                        'stock',
                        'translated',
                    ],
                ],
                'associations' => [
                    'manufacturer' => [],
                    'options' => ['associations' => ['group' => []]],
                    'properties' => ['associations' => ['group' => []]],
                ],
                'aggregations' => [[
                    'name' => 'fel_categories',
                    'type' => 'entity',
                    'field' => 'categories.id',
                    'definition' => 'category',
                ]]
            ]);
        }

        return ['error' => 'No results'];
    }

    private function createCategoryCriteriaFromNames(array $categories): array
    {
        $query = [];
        foreach($categories as $cat) {
            $query[] = ['score' => 100, 'query' => ['type' => 'equals', 'field' => 'name', 'value' => $cat]];
        }

        $catIds = array_column($this->storeApiClient('POST', '/category', [
            'query' => $query, 'includes' => ['category' => ['id']]
        ])['elements'] ?? [], 'id');

        $filter = [];
        foreach($catIds as $cat) {
            $filter[] = ['type' => 'equalsAny', 'field' => 'product.categoryTree', 'value' => $cat];
        }

        return ['type' => 'multi', 'operator' => 'or', 'queries' => $filter];
    }

    private function getProductsCategoryMap(array $categories): array
    {
        usort($categories, fn($a, $b) => $b['level'] <=> $a['level']);

        $r = [];
        foreach($categories as $category) {
            $r[] = [
                'id' => $category['id'],
                'name' => $category['translated']['name'] ?? $category['name'],
                'breadcrumb' => array_slice(
                    $category['translated']['breadcrumb'] ?? $category['breadcrumb'],
                    $this->aiConfig->getOpenAiFilterCategoryLevel()
                ),
            ];
        }

        return $r;
    }

    private function truncateSearchResult(array $products): array
    {
        $r = [];
        foreach($products as $product) {
            $r[] = [
                'id' => $product['id'],
                'name' => $product['translated']['name'] ?? $product['name'],
                'url' => $this->router->generate('frontend.detail.page', ['productId' => $product['id']]),
            ];
        }

        return $r ? $r : ['No products found'] ;
    }

    private function createProductFilterFromArgs(array $args): array
    {
        $sort = [];
        $groupedFilter = [];
        $parsed = $this->parsedArguments['parsed'];
        $filter = [['type' => 'equals', 'field' => 'product.active', 'value' => true]];

        // filter sorting
        if ($parsed['order'] ?? null) {
            $sort[] = $parsed['order'];
        }

        // filter price range
        if ($parsed['price'] ?? null) {
            $filter[] = $parsed['price'];
        }

        // filter categories
        if ($parsed['categories'] ?? null) {
            $groupedFilter['categories'] = $parsed['categories'];
        }

        // filter properties
        if ($parsed['properties'] ?? null) {
            foreach($parsed['properties'] as $groupName => $setFilter) {
                if ($setFilter) {
                    $groupedFilter[$groupName] = $setFilter;
                }
            }
        }

        // filter query
        if ($args['query'] ?? null) {
            $setQueries = [];
            foreach(explode(' ', $args['query']) as $q) {
                if (!empty($q)) {
                    $setQueries[] = [
                        'type' => 'multi',
                        'operator' => 'or',
                        'queries' => [
                            ['type' => 'contains', 'field' => 'product.name', 'value' => $q],
                            ['type' => 'contains', 'field' => 'product.description', 'value' => $q]
                        ],
                    ];
                }
            }
            if ($setQueries) {
                $filter[] = ['type' => 'multi', 'operator' => 'and', 'queries' => $setQueries];
            }
        }

        // filter ids
        if ($args['id'] ?? null) {
            if (is_string($args['id'])) {
                $args['id'] = [$args['id']];
            }
            if (is_array($args['id'])) {
                foreach($args['id'] as $id) {
                    if (Uuid::isValid($id)) {
                        $groupedFilter['ids'][] = [
                            'type' => 'multi',
                            'operator' => 'or',
                            'queries' => [
                                ['type' => 'equals', 'field' => 'product.id', 'value' => $id],
                                ['type' => 'equals', 'field' => 'product.parentId', 'value' => $id]
                            ]
                        ];
                    } else {
                        $groupedFilter['ids'][] = [
                            'type' => 'multi',
                            'operator' => 'or',
                            'queries' => [
                                ['type' => 'equals', 'field' => 'product.productNumber', 'value' => $id],
                            ]
                        ];
                    }
                }
            }
        }

        // set all
        if ($groupedFilter) {
            $filterGroup = [];
            foreach($groupedFilter as $setFilterGroup) {
                $filterGroup[] = [
                    'type' => 'multi',
                    'operator' => 'or',
                    'queries' => $setFilterGroup,
                ];
            }
            $filter[] = [
                'type' => 'multi',
                'operator' => 'and',
                'queries' => $filterGroup,
            ];
        }

        return [
            'args' => $args,
            'filter' => $filter,
            'sort' => $sort,
        ];
    }

    private function createPropertyParameter()
    {
        $setParam = array_filter($this->parsedArguments['parsed']['parameter']);

        if ($setParam['properties'] ?? null) {
            $setParam['properties'] = implode('|', $setParam['properties']);
        }

        if ($setParam['manufacturer'] ?? null) {
            $setParam['manufacturer'] = implode('|', $setParam['manufacturer']);
        }

        return ['setParameter' => $setParam];
    }

    private function parseRequiredArgs(array $args): array
    {
        $r = [
            'categories' => [],
            'order' => [],
            'price' => [],
            'parameter' => [],
            'properties' => [],
        ];

        $productProperties = $this->productProperties;
        $propertyMap = $productProperties['propertyMap'];
        $trackProperties = [];
        $trackPropsGroups = [];
        $toFix = [];

        // sort
        if ($args['order'] ?? null) {
            $order = explode('-', $args['order']);
            if ('topseller' === ($order[0] ?? null)) {
                $r['parameter']['order'] = $order[0];
                $r['order'] = ['field' => 'product.sales', 'order' => 'desc'];
            } else {
                if ($order[1] ?? null) {
                    $r['parameter']['order'] = $args['order'];
                    $r['order'] = ['field' => $order[0], 'order' => $order[1]];
                }
            }
        }

        // price range
        $setPriceRange = array_filter([
            'gte' => $args['price_min'] ?? null,
            'lte' => $args['price_max'] ?? null,
        ]);
        if ($setPriceRange) {
            $r['price'] = [
                'type' => 'range',
                'field' => 'product.price',
                'parameters' => $setPriceRange,
            ];
            $r['parameter']['min-price'] = $args['price_min'] ?? null;
            $r['parameter']['max-price'] = $args['price_max'] ?? null;
        }

        // categories
        if ($args['categories'] ?? null) {
            $args['categories'] = array_values(array_unique($args['categories']));
            $notFound = [];
            foreach($args['categories'] as $category) {
                if (Uuid::isValid($category)) {
                    $r['categories'][] = ['type' => 'equalsAny', 'field' => 'product.categoryTree', 'value' => $category];
                } else {
                    if ($keys = array_keys($productProperties['categoryList'], $category)) {
                        $countKeys = count($keys);
                        if (1 < $countKeys) {
                            $this->addNotification('system', "The category name '{$category}' is used for a total of '{$countKeys}' categories.");
                        }
                        foreach($keys as $catId) {
                            $r['categories'][] = ['type' => 'equalsAny', 'field' => 'product.categoryTree', 'value' => $catId];
                        }
                    } else {
                        $notFound[] = $category;
                    }
                }
            }
            if ($notFound) {
                $this->addNotification('not found', ['categories' => $notFound], true);
            }
        }

        // manufacturer / options / properties
        if ($args['properties'] ?? null) {
            $args['properties'] = array_unique($args['properties']);

            foreach($args['properties'] as $property) {
                if ($keys = array_keys($propertyMap['manufacturer'], $property)) {
                    foreach($keys as $manufacturerId) {
                        $r['parameter']['manufacturer'][] = $manufacturerId;
                        $r['properties']['manufacturer'][] = ['type' => 'equalsAny', 'field' => 'product.manufacturerId', 'value' => $manufacturerId];
                    }
                } else {
                    foreach($productProperties['properties'] as $groupName => $properties) {
                        if (Uuid::isValid($property)) {
                            $getOptName = $propertyMap['properties'][$property] ?? $propertyMap['options'][$property] ?? null ;
                            if (isset($properties[$property]) AND $getOptName) {
                                $property = $getOptName;
                            }
                        }

                        if (!isset($trackProperties[$property])) {
                            $trackProperties[$property] = null;
                        }

                        if ($keys = array_keys($properties, $property)) {
                            $groupName = 'manufacturer' === strtolower($groupName) ? 'manufacturer_2' : $groupName ;
                            $trackProperties[$property] = true;

                            if ($trackPropsGroups[$property] ?? null) {
                                $getExistingGroup = array_key_first($trackPropsGroups[$property]);
                                if (!isset($r['propsInDiffGroups'][$getExistingGroup])) {
                                    $toFix['propsInDiffGroups'][$getExistingGroup] = $getExistingGroup;
                                }
                                if (!isset($r['propsInDiffGroups'][$groupName])) {
                                    $toFix['propsInDiffGroups'][$groupName] = $groupName;
                                }
                            } else {
                                if (!isset($trackPropsGroups[$property][$groupName])) {
                                    $trackPropsGroups[$property][$groupName] = $groupName;
                                }
                            }

                            foreach($keys as $propertyId) {
                                if ($trackProps = in_array($property, $propertyMap['properties'])) {
                                    $r['properties'][$groupName][] = [
                                        'type' => 'equalsAny', 'field' => 'product.propertyIds', 'value' => $propertyId
                                    ];
                                }
                                if ($trackOpts = in_array($property, $propertyMap['options'])) {
                                    $r['properties'][$groupName][] = [
                                        'type' => 'equalsAny', 'field' => 'product.optionIds', 'value' => $propertyId
                                    ];
                                }
                                if ($trackProps OR $trackOpts) {
                                    $r['parameter']['properties'][] = $propertyId;
                                }
                            }
                        }
                    }
                }
            }

            if ($toFix['propsInDiffGroups'] ?? null) {
                $updateFilter = [];
                foreach($toFix['propsInDiffGroups'] as $groupName) {
                    if (isset($r['properties'][$groupName])) {
                        $updateFilter = array_merge($updateFilter, $r['properties'][$groupName]);
                        unset($r['properties'][$groupName]);
                    }
                }
                $r['properties']['propsInDiffGroups'] = $updateFilter;
                $r['parameter']['properties'] = array_values(array_diff($r['parameter']['properties'], array_column($updateFilter, 'value')));
            }

            if ($trackProperties AND $trackProperties = array_keys($trackProperties, null, true)) {
                $this->addNotification('not found', ['properties' => $trackProperties], true);
            }
        }

        // search query
        if ($args['query'] ?? null) {
            $r['parameter']['search'] = $args['query'];
        }

        return [
            'toFix' => $toFix,
            'args' => $args,
            'parsed' => $r,
        ];
    }

}
