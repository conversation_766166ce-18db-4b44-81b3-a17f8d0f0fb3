// Enhanced product display styling
.fel-chat-product-list {
    margin: 0;
    padding: 0;
    list-style: none;
    
    li {
        padding: 0 0 .5rem;
        margin-bottom: 15px;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .fel-item-wrapper {
            border: 1px solid #e9e9e9;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
            background-color: #fff;
            
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            
            .fel-item-thumbnail {
                height: 180px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f8f9fa;
                overflow: hidden;
                
                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    margin: 0;
                    padding: 0;
                    box-shadow: none;
                }
                
                .alert-warning {
                    height: 150px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0;
                }
            }
            
            .fel-item-header {
                padding: 15px 15px 5px;
                
                h2 {
                    margin: 0 0 8px;
                    padding: 0;
                    border: 0 none;
                    font-size: 16px;
                    line-height: 1.3;
                    
                    a {
                        color: var(--fel-chatbot-theme-color, var(--bs-primary));
                        text-decoration: none;
                        
                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }
            
            .fel-subtitle {
                padding-top: .25rem;
                display: flex;
                line-height: 1;
                margin-bottom: 10px;
                
                .fel-price {
                    font-weight: 600;
                    color: #e4021b;
                    
                    + .fel-manufacturer {
                        margin-left: .4rem;
                        padding-left: .4rem;
                        max-width: 140px;
                        border-left: 1px solid rgba(var(--bs-dark-rgb), .4);
                        color: #666;
                    }
                }
            }
            
            .fel-item-body {
                padding: 0 15px;
            }
            
            .fel-description {
                padding: .2rem 0;
                max-height: 44px;
                overflow: hidden;
                color: #666;
                font-size: 14px;
                line-height: 1.4;
                margin-bottom: 10px;
            }
            
            .fel-product-properties {
                padding: .2rem 0 .6rem;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                margin: 0 -3px 10px;
                
                div {
                    flex: 0;
                    margin: 3px;
                    padding: 4px 8px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #495057;
                }
                
                span {
                    margin: .3rem .3rem 0 0;
                    padding: 3px 5px;
                    display: inline-block;
                    font-size: 82%;
                    background: rgba(var(--bs-dark-rgb), .1);
                    border-radius: 3px;
                }
            }
            
            .fel-item-footer {
                padding: 0 15px 15px;
                
                .btn {
                    width: 100%;
                    border-color: rgba(var(--bs-dark-rgb), .1);
                    background-color: var(--fel-chatbot-theme-color, var(--bs-primary));
                    color: #fff;
                    transition: background-color 0.2s;
                    
                    &:hover {
                        background-color: var(--fel-chatbot-theme-color-hover, var(--bs-primary-dark, #0b5ed7));
                    }
                }
            }
        }
    }
}

// Responsive styling for product display
@media only screen and (min-width: 640px) {
    .fel-chat-product-list {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -8px;
        
        li {
            flex: 0 0 calc(50% - 16px);
            margin: 0 8px 16px;
            
            &.fel-is-single-product {
                flex: 0 0 calc(100% - 16px);
            }
        }
    }
}

@media only screen and (min-width: 768px) {
    .fel-chat-product-list {
        li {
            flex: 0 0 calc(33.333% - 16px);
            
            &.fel-is-single-product {
                flex: 0 0 calc(50% - 16px);
                margin-left: auto;
                margin-right: auto;
            }
        }
    }
}

// Category list styling
.fel-chat-link-category-list-wrapper {
    margin-top: 15px;
    
    .fel-chat-link-category-ul-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        li {
            margin: 0;
            padding: 0;
            
            a, span {
                display: block;
                padding: 8px 12px;
                border-radius: 4px;
                text-decoration: none;
                transition: all 0.2s;
                
                &:hover {
                    transform: translateY(-2px);
                }
            }
        }
    }
}

// Search arguments styling
.fel-chat-search-args {
    margin-top: 15px;
    
    .fel-chat-search-args-header {
        margin-bottom: 10px;
    }
    
    .fel-chat-search-args-list {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        
        &.fel-is-hidden {
            display: none;
        }
        
        &.fel-fade-in {
            animation: felFadeIn .15s ease-in-out;
            animation-fill-mode: both;
        }
    }
    
    .fel-chat-search-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;
        
        @media (min-width: 640px) {
            grid-template-columns: 1fr 1fr;
        }
        
        li {
            margin: 0;
            padding: 0;
        }
    }
    
    .fel-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        
        .fel-tag {
            display: inline-block;
            padding: 4px 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-size: 12px;
            color: #495057;
        }
    }
}

// Button styling
.fel-btn {
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
        transform: translateY(-2px);
    }
}
