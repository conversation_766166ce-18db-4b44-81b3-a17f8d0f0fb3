import template from './fel-gpt-assistant-config.html.twig';

const { Component, Mixin } = Shopware;

Component.register('fel-gpt-assistant-config', {
    template,

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('fel-input-helper-mixin'),
        Mixin.getByName('fel-openai-mixin'),
        Mixin.getByName('fel-plugin-config-mixin'),
    ],

    data() {
        return {
            openAiChatMetaChatbot: {
                name: null,
                canUpdate: false
            },
            openAiStaticInformation: {
                field: null,
                canUpdate: false
            },
        };
    },

    created() {
        this.getPluginConfig().then((pluginConfig) => {
            this.openAiStaticInformation.field = this.getPluginConfigFor('openAiStaticInformationField');
            this.openAiChatMetaChatbot.name = this.getPluginConfigFor('openAiChatMetaChatbotName');
            this.setStaticDataTextareaRows();
            this.fetchModels();
        });
    },

    methods: {

        setExampleData() {
            var getExampleData = this.exampleFileData();
            this.openAiStaticInformation.field = getExampleData;
            if ('string' === typeof getExampleData) {
                this.openAiStaticInformation.canUpdate = true;
                this.setStaticDataTextareaRows();
            }
        },

        updateFileAlternate(configName) {
            this.setPluginConfigValue(configName, this.openAiStaticInformation.field);
            this.openAiStaticInformation.canUpdate = false;
            this.createNotificationSuccess({message: configName});
            this.setStaticDataTextareaRows();
        },

        updateChatbotname(configName) {
            this.setPluginConfigValue(configName, this.openAiChatMetaChatbot.name);
            this.openAiChatMetaChatbot.canUpdate = false;
            this.createNotificationSuccess({message: configName});
        },

        onPluginConfigChange($event) {
            this.isLoading = true;
            this.canUpdate = true;
            if ('openAiStaticInformationField' === $event.name) {
                if (!this.openAiStaticInformation.canUpdate) {
                    this.openAiStaticInformation.canUpdate = true;
                }
            }
            if (!this.openAiChatMetaChatbot.canUpdate && 'openAiChatMetaChatbotName' === $event.name) {
                this.openAiChatMetaChatbot.canUpdate = true;
            }
            this.isLoading = false;
        },

        setPluginConfigValue(configName, setValue) {
            return this.setConfigValue(`${this.configName}.${configName}`, setValue);
        },

        getPluginConfigFor(configName) {
            var configKeyName = `${this.configName}.${configName}`;
            if ('undefined' !== typeof this.pluginConfig.pluginConfig) {
                if ('undefined' !== typeof this.pluginConfig.pluginConfig[configKeyName]) {
                    return this.pluginConfig.pluginConfig[configKeyName];
                }
            }
            return null;
        },

        getStaticDataTextarea() {
            return this.$el.querySelector('[name="openAiStaticInformationField"]');
        },

        setStaticDataTextareaRows() {
            var getEl = this.getStaticDataTextarea();
            if (getEl && 'string' === typeof this.openAiStaticInformation.field) {
                getEl.rows = this.openAiStaticInformation.field.split("\n").length;
            }
        }

    },

    metaInfo() {
        return {
            title: this.$createTitle()
        };
    }

});