{% apply spaceless %}
{% set felConfig = config('FelCustomGptAgent.config') %}
{% set categoryListingMax = 6 %}
{% set setBreadcrumbLength = 2 %}
{% set isProductSearch = 'product_search' === isSearch %}
{% set descriptionLength = isProductSearch ? 64 : 300 %}
{% set productsTotal = products|length %}
{% set isSingleProduct = 1 == productsTotal ? ' fel-is-single-product' : '' %}
{% set isSearchClass = isSearch ? ' fel-is-search-result-listing' : '' %}
{% set propertyParams = productsResponse.propertyData.setParameter %}
{% set productMediaIds = [] %}
{% for product in products %}
    {% if product.cover.media.id %}
        {% set productMediaIds = productMediaIds|merge([product.cover.media.id]) %}
    {% endif %}
{% endfor %}
{% if productMediaIds %}
    {% set mediaCollection = searchMedia(productMediaIds, context.context) %}
{% endif %}
<div class="fel-chat-product-listing-wrapper">
    <ul class="fel-chat-product-list{{ isSearchClass }}">
        {% for product in products %}
            {% set setQueryProperties = product.parentId ? {} : propertyParams.properties %}
            {% set felSeoUrl = url('frontend.detail.page', {productId: product.id, properties: setQueryProperties}) %}
            {% if felSeoUrl %}
                {% set isLastProductClass = true == (loop.index == productsTotal) ? ' fel-product-list-last-product' : '' %}
                {% set translatedName = product.translated.name ?? product.name %}
                {% set translatedDescription = (product.translated.description ?? product.description)|striptags|trim|sw_sanitize %}
                {% set useTranslatedDescription = translatedDescription|slice(0, descriptionLength)|split(' ')|slice(0, -1)|join(' ') %}
                {% set felHighlightProduct = (product.isNew || product.markAsTopseller) ? ' fel-highlight' : '' %}
                {% set felBuyable = product.available and product.childCount <= 0 and product.calculatedMaxPurchase > 0 %}
                <li class="fel-chat-product-list-item{{ isLastProductClass ~ isSingleProduct }}"
                    data-ref-id="{{ product.parentId ?? product.id }}"
                    data-is-available="{{ felBuyable ? 'true' : 'false' }}">
                    <div class="fel-item-wrapper">
                        <div class="fel-item-thumbnail mb-3">
                            {% set productMediaId = product.cover.media.id %}
                            {% if mediaCollection.elements[productMediaId] is not empty %}
                                {% set productThumbnail %}
                                    {% sw_thumbnails 'fel-chat-product-list-thumbnail' with {
                                        media: mediaCollection.elements[productMediaId],
                                        sizes: {default: '320px'},
                                        attributes: {
                                            class: 'fel-product-list-thumbnail',
                                            title: translatedName,
                                            alt: translatedName,
                                            loading: 'lazy',
                                        }
                                    } %}
                                {% endset %}
                            {% endif %}
                            {% if productThumbnail is not empty %}
                                {{ productThumbnail }}
                            {% else %}
                                <p class="alert alert-warning d-flex justify-content-center align-items-center">
                                    <b>{{ 'fel-oai-chat.chat.productListing.noImageAssigned'|trans|sw_sanitize }}</b>
                                </p>
                            {% endif %}
                        </div>
                        <div class="fel-item-header mb-1">
                            <h2 title="{{ 'detail.productNumberLabel'|trans|sw_sanitize }} {{ product.productNumber }}">
                                <a href="{{ felSeoUrl }}">{{ translatedName }}</a>
                            </h2>
                            <div class="fel-subtitle">
                                {% set getCheapestPrice = product.calculatedCheapestPrice.totalPrice ?? product.calculatedPrice.totalPrice ?? null %}
                                {% if getCheapestPrice %}
                                    <small class="fel-price" title="{{ 'listing.filterPriceDisplayName'|trans|sw_sanitize }}">
                                        {{ getCheapestPrice|currency }}
                                    </small>
                                {% endif %}
                                {% if product.manufacturer %}
                                    <small class="fel-manufacturer fel-text-ellipsis" title="{{ 'listing.filterManufacturerDisplayName'|trans|sw_sanitize }}">
                                        <b>{{ product.manufacturer.translated.name ?? product.manufacturer.name }}</b>
                                    </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="fel-item-body fel-body-description pb-1 {% if product.options or product.properties %}mb-0{% else %}mb-1{% endif %}">
                            <div class="{{ isProductSearch ? 'fel-description' : 'fel-long-description' }}">
                                <span>{{ useTranslatedDescription }} {% if descriptionLength < translatedDescription|length %}…{% endif %}</span>
                            </div>
                            {% if product.options or product.properties %}
                                <div class="fel-product-properties-container">
                                    <div class="fel-product-properties">
                                        {% set felPropOptions = null %}
                                        {% set felPropProperties = null %}
                                        {% for property in product.options|sort((a, b) => a.name|length <=> b.name|length) %}
                                            {% if property.group.visibleOnProductDetailPage and '-' != property.translated.name %}
                                                {% set felPropOptions %}
                                                    {{ felPropOptions }}
                                                    <div class="fel-product-properties-item fel-is-option fel-btn"
                                                        data-callback="chatToggleProperties"
                                                        title="{{ property.group.translated.name }}: {{ property.translated.name }}">
                                                        <b>{{ property.translated.name }}</b>
                                                    </div>
                                                {% endset %}
                                            {% endif %}
                                        {% endfor %}
                                        {% for property in product.properties|sort((a, b) => a.name|length <=> b.name|length) %}
                                            {% if property.group.visibleOnProductDetailPage and '-' != property.translated.name %}
                                                {% set felPropProperties %}
                                                    {{ felPropProperties }}
                                                    <div class="fel-product-properties-item fel-is-property fel-btn"
                                                        data-callback="chatToggleProperties"
                                                        title="{{ property.group.translated.name }}: {{ property.translated.name }}">
                                                        <b>{{ property.translated.name }}</b>
                                                    </div>
                                                {% endset %}
                                            {% endif %}
                                        {% endfor %}
                                        {{ felPropOptions }}
                                        {% if felPropOptions and felPropProperties %}
                                            <div class="fel-product-properties-item fel-prop-divider"><span>|</span></div>
                                        {% endif %}
                                        {{ felPropProperties }}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        <div class="fel-item-footer">
                            <a class="btn btn-light d-block" href="{{ felSeoUrl }}">{{ 'listing.boxProductDetails'|trans|sw_sanitize }}</a>
                        </div>
                    </div>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
    {% if propertyParams.search and isProductSearch %}
        <div class="fel-chat-link-search-page mt-2 mb-2">
            <a class="btn btn-info d-block" href="{{ path('frontend.search.page', propertyParams) }}">
                {{ 'header.searchAllResults'|trans|sw_sanitize }}
            </a>
        </div>
    {% endif %}
    {% set toggleSearchArgsID = 'fel-' ~ random() ~ '_' ~ random() %}
    {% set toggleSearchArgsBtn %}
        <span class="btn btn-outline-primary d-block fel-btn"
            data-callback="chatToggleSearchArgs"
            data-target-el="#{{ toggleSearchArgsID }}"
            data-toggle-class="fel-is-hidden,mt-1,mb-1,fel-fade-in"
            data-toggle-self="btn-primary,btn-outline-primary,active">
            {{ 'fel-oai-chat.error.chat.usedArguments'|trans|sw_sanitize }}
        </span>
    {% endset %}
    {% if productsResponse.categoryMap is not empty %}
        <div class="fel-chat-link-category-list-wrapper mt-2">
            <ul class="fel-chat-link-category-ul-list">
                <li class="fel-chat-category-list-header" title="{{ productsResponse.categoryMap|length }}">
                    <span class="btn disabled fel-categories-list-header">
                        {{ 'fel-oai-chat.chat.categories'|trans|sw_sanitize }}
                    </span>
                </li>
                {% for categoryData in productsResponse.categoryMap|slice(0, categoryListingMax) %}
                    {% set breadcrumbLength = categoryData.breadcrumb|length %}
                    {% set breadcrumbStart = breadcrumbLength - setBreadcrumbLength %}
                    {% set breadcrumbStart = breadcrumbStart < 1 ? 0 : breadcrumbStart %}
                    {% set visibleBreadcrumbs = categoryData.breadcrumb|slice(breadcrumbStart, -1) %}
                    <li>
                        <a href="{{ path('frontend.navigation.page', propertyParams|merge({navigationId: categoryData.id})) }}"
                            class="btn btn-dark{% if breadcrumbStart %} fel-breadcrumb-trancate{% endif %}"
                            title="{{ categoryData.breadcrumb|join(" » ") }}">
                            <span class="fel-dir-rtl">
                                {% if visibleBreadcrumbs|length and setBreadcrumbLength > 1 %}
                                    <em>{{ visibleBreadcrumbs|join(' » ') }} »</em>
                                {% endif %}
                                <b>{{ categoryData.name }}</b>
                            </span>
                        </a>
                    </li>
                {% endfor %}
                <li class="fel-chat-search-args-header-li">
                    {{ toggleSearchArgsBtn }}
                </li>
            </ul>
        </div>
    {% endif %}
    <div class="fel-chat-search-args">
        {% if productsResponse.categoryMap is empty %}
            <div class="fel-chat-search-args-header">
                {{ toggleSearchArgsBtn }}
            </div>
        {% endif %}
        <div id="{{ toggleSearchArgsID }}" class="fel-chat-search-args-list fel-is-hidden">
            {% set productsResponseArgs = productsResponse.arguments %}
            {% if productsResponse.parsedArguments.toFix.notFilterable is not empty %}
                {% set productsResponseArgs = productsResponseArgs|merge({
                    notFilterable: productsResponse.parsedArguments.toFix.notFilterable
                }) %}
            {% endif %}
            {% if productsResponse.items._meta_data.notification.not_found.properties is not empty %}
                {% set productsResponseArgs = productsResponseArgs|merge({
                    notFoundProperties: productsResponse.items._meta_data.notification.not_found.properties
                }) %}
            {% endif %}
            {% if productsResponse.items._meta_data.notification.not_found.categories is not empty %}
                {% set productsResponseArgs = productsResponseArgs|merge({
                    notFoundCategories: productsResponse.items._meta_data.notification.not_found.categories
                }) %}
            {% endif %}
            <ul class="fel-chat-search-grid">
                {% set dividerAdded = false %}
                {% set notificationDivider %}
                    <li class="fel-grid-parent fel-chat-search-error-notifications">
                        <h3>{{ 'fel-oai-chat.chat.notifications'|trans|sw_sanitize }}</h3>
                    </li>
                {% endset %}
                {% for key, val in productsResponseArgs %}
                    {% set felTagsHeadlineClass = key in ['notFilterable', 'notFoundProperties', 'notFoundCategories'] ? ' text-warning' : null %}
                    {% if felTagsHeadlineClass and false == dividerAdded %}
                        {% set dividerAdded = true %}
                        {{ notificationDivider }}
                    {% endif %}
                    {% if val %}
                        <li class="fel-grid-parent fel-chat-search-queryArguments">
                            <div class="grid-key">
                                <h3 class="fel-headline m-0 p-0{{ felTagsHeadlineClass }}">
                                    {{ ('fel-oai-chat.chat.queryArguments.' ~ key)|trans|sw_sanitize }}
                                </h3>
                            </div>
                            <div class="fel-args-content fel-tags">
                                {% if val is iterable %}
                                    {% for prop in val %}
                                        <span class="fel-tag{% if 4 < val|length %} fel-flex-auto{% endif %}" title="{{ prop }}">
                                            {{ prop }}
                                        </span>
                                    {% endfor %}
                                {% else %}
                                    <span class="fel-tag" title="{{ val }}">{{ val }}</span>
                                {% endif %}
                            </div>
                        </li>
                    {% endif %}
                    {% if (loop.last and productsResponse.parsedArguments.toFix._truncate is not empty) and false == dividerAdded %}
                        {% set dividerAdded = true %}
                        {{ notificationDivider }}
                    {% endif %}
                {% endfor %}
                {# notification truncated #}
                {% if productsResponse.parsedArguments.toFix._truncate is not empty %}
                    {% if productsResponse.parsedArguments.toFix._truncate is not empty %}
                        <li class="fel-grid-parent fel-chat-search-error-queryFailedTruncatedTo">
                            <div class="grid-key">
                                <h3 class="fel-headline text-warning m-0 p-0">
                                    {{ 'fel-oai-chat.chat.queryArguments._truncate'|trans|sw_sanitize }}
                                </h3>
                            </div>
                            <div class="fel-args-content">
                                <span class="fel-tag-line">{{ 'fel-oai-chat.error.chat.queryFailedTruncatedTo'|trans({
                                    '%from%': productsResponse.parsedArguments.toFix._truncate.originalQuery,
                                    '%to%': productsResponse.args.query
                                })|sw_sanitize }}</span>
                            </div>
                        </li>
                    {% endif %}
                {% endif %}
            </ul>
            {# notification props in diff groups #}
            {% if productsResponse.parsedArguments.toFix.propsInDiffGroups is not empty %}
                <hr class="mt-0 mb-2" />
                <div class="fel-chat-search-error-propsInDiffGroups">
                    <div class="fel-chat-search-error-propsInDiffGroups-header mb-2">
                        {% set propsInDiffGroupsNote = 'fel-oai-chat.error.chat.propsInDiffGroups'|trans %}
                        <b class="m-0" title="{{ propsInDiffGroupsNote }}">{{ propsInDiffGroupsNote|sw_sanitize }}</b>
                    </div>
                    <div class="fel-chat-search-error-propsInDiffGroups-body">
                        <span class="btn btn-outline-dark d-block fel-btn"
                            data-callback="chatToggleSearchArgs"
                            data-target-el="#{{ toggleSearchArgsID }}-props-toggle"
                            data-toggle-class="fel-is-hidden,fel-fade-in,mt-2"
                            data-toggle-self="btn-dark,btn-outline-dark,active">
                            {{ productsResponse.parsedArguments.toFix.propsInDiffGroups|keys|join(', ') }}
                        </span>
                        <div id="{{ toggleSearchArgsID }}-props-toggle" class="fel-props-in-diff-groups-fixer fel-is-hidden">
                            {% for group, props in productsResponse.parsedArguments.toFix.propsInDiffGroups %}
                                <div class="fix-group-btn-group mb-2">
                                    {% for prop in props %}
                                        {% set newPropName = group ~ ':' ~ prop %}
                                        {% set newShortPrompt = 'fel-oai-chat.chat.queryArguments.useFixedShortProperty'|trans({
                                            '%old%': prop,
                                            '%new%': newPropName,
                                        }) %}
                                        <button type="button"
                                            class="btn btn-outline-primary fel-btn m-1"
                                            title="{{ newShortPrompt }}"
                                            data-callback="chatWorkerFixProperties"
                                            data-target-id="#{{ toggleSearchArgsID }}-props-toggle"
                                            data-target-selector="[data-shared-prop='{{ prop }}']"
                                            data-shared-prop="{{ prop }}"
                                            data-short-prompt="{{ newShortPrompt }}"
                                            data-short-prompt-end="{{ 'fel-oai-chat.chat.queryArguments.useFixedPropertySearchAgain'|trans }}"
                                        >{{ newPropName }}</button>
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endapply %}
