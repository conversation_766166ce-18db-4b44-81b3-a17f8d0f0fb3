{% set chatConfig = config('FelAIProductAdvisor.config') %}
{% set chatEnabled = chatConfig.enablePlugin %}
{% set felPluginOptions = {
        page: {
            refId: page.product.parentId ?? page.product.id ?? null,
            categoryPath: page.header.navigation.active.get('breadcrumb')|keys|join('|'),
        },
        translation: {
            removeChatMessage: 'fel-gpt-assistant.chat.deleteChatMessage'|trans,
            errorClearChat: 'fel-gpt-assistant.chat.errorClearChat'|trans
        },
        config: {
            maxProductsInChat: chatConfig.maxProductsInChat ?? 4
        }
    } %}
{% if chatConfig.openAiApiKey is empty or chatConfig.openAiAssistantId is empty %}
	{% set chatEnabled = false %}
{% elseif chatConfig.authenticatedUserOnly %}
	{% if context.customer is empty %}
		{% set chatEnabled = false %}
	{% else %}
		{% if chatConfig.authenticatedUserOnlyGroup and context.customer.groupId not in chatConfig.authenticatedUserOnlyGroup %}
			{% set chatEnabled = false %}
		{% endif %}
	{% endif %}
{% endif %}
{% if chatEnabled %}
	{% set getCustomAvatarId = chatConfig.chatbotCustomAvatar %}
	{% set chatbotAvatarUrl = asset("bundles/felaiproductadvisor/chatbot-avatar-#{chatConfig.chatbotAvatar}-sm.png", 'asset') %}
	{% set chatbotLoaderFile = asset("bundles/felaiproductadvisor/fel-loader-#{chatConfig.chatbotLoadingIcon}.svg", 'asset') %}
	{% if getCustomAvatarId %}
		{% set mediaCollection = searchMedia([getCustomAvatarId], context.context) %}
		{% set getMediaFile = mediaCollection.get(getCustomAvatarId) %}
		{% if getMediaFile.url %}
			{% set chatbotAvatarUrl = getMediaFile.url %}
		{% endif %}
	{% endif %}
	{% set fel_chatbot_avatar %}
	<img src="{{ chatbotAvatarUrl }}" alt="{{ 'fel-gpt-assistant.chat.avatarTitle'|trans }}"/>
	{% endset %}

	{% block fel_chatbot_template %}
		<template
			id="fel-chatbot-template">
			{# greetings container #}
			<div class="fel-chatbot-greeting-container fel-fade-in pb-3 h-100 d-flex justify-content-center align-items-center">
				<div class="fel-chatbot-greeting">
					<div class="fel-chatbot-avatar text-center">
						{{ fel_chatbot_avatar }}
					</div>
					<div class="fel-chatbot-greeting-text text-center pt-1 pb-2">
						<button type="button" class="btn btn-link fel-btn" title="{{ 'fel-gpt-assistant.chat.submit'|trans }}" data-callback="addToChatSubmit" data-prompt="{{ 'fel-gpt-assistant.chat.whats.your.purpose'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.initial.greeting'|trans }}">
							{{ 'fel-gpt-assistant.chat.initial.greeting'|trans }}
						</button>
					</div>
					<div class="fel-chatbot-config-wrapper py-3 bg-white">
						<div class="fel-cookie-consent">
							<div class="px-3">
								{% set felLocalStorageConsent = app.request.cookies.get('fel-chatbot-localstorage-accepted') %}
								<div class="form-text form-check">
									<input type="checkbox" name="fel-allow-localstorage" id="fel-allow-localstorage" class="form-check-input fel-checkbox" data-callback="toggleLocalStorage" {% if 'allowed' == felLocalStorageConsent %} checked {% endif %}>
									<label for="fel-allow-localstorage" class="form-check-label align-text-top ps-1">
										{{ 'fel-gpt-assistant.cookie.description'|trans }}
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			{# loading indicator container #}
			<div class="fel-chatbot-load-container fel-fade-in">
				<div class="fel-chatbot-loader text-center py-3 fel-loading-icon-{{ chatConfig.chatbotLoader }}" style="background-image: url({{ chatbotLoaderFile }}); background-repeat: no-repeat"></div>
				<div class="fel-chatbot-loader-info text-center" data-second-run="{{ 'fel-gpt-assistant.chat.wait.a.second.more'|trans }}">
					{{ 'fel-gpt-assistant.chat.wait.a.second'|trans }}
				</div>
			</div>
		</template>
	{% endblock %}

	<div id="fel-chatbot" class="fel-chat-initial fel-zoom-intial" data-options="{{ felPluginOptions|json_encode }}" data-assistant-gpt-plugin>
		<div class="fel-chatbot-container">
			{% block fel_chatbot_options_buttons %}
				<div class="fel-chatbot-header">
					<div class="fel-chatbot-options d-flex justify-content-between">
						<div class="align-items-center fel-header-chatbot-info">
							<div class="fel-chatbot-avatar-container">
								<div class="fel-chatbot-avatar text-center">
									{{ fel_chatbot_avatar }}
								</div>
							</div>
							<div class="px-3 text-light">
								{% if chatConfig.openAiChatMetaChatbotName %}
									<p class="m-0">
										<b>{{ chatConfig.openAiChatMetaChatbotName }}</b>
									</p>
								{% endif %}
								<p class="d-flex align-items-center fel-online-status-wrapper m-0">
									<span class="online-status-dot text-success me-1"></span>
									<span>{{ 'fel-gpt-assistant.chat.onlineStatus'|trans }}</span>
								</p>
							</div>
						</div>
						<div class="d-flex align-items-center">
							<div class="btn-group">
								<button type="button" class="fel-delete-thread-button fel-btn btn btn-small btn-primary" data-callback="deleteChatThread" title="{{ 'fel-gpt-assistant.chat.delete'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.delete'|trans }}">
									{% sw_icon 'arrow-360-right' style {'class': 'help pe-none'} %}
								</button>
								<button type="button" class="fel-zoom-button fel-btn fel-ui-btn btn btn-small btn-primary" data-callback="toggleChatZoom" title="{{ 'fel-gpt-assistant.chat.toggleZoomChat'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.toggleZoomChat'|trans }}">
									{% sw_icon 'code' style {'class': 'editor-expand pe-none'} %}
								</button>
								<button type="button" class="fel-toggle-chatbot-button fel-btn btn fel-ui-btn btn-small btn-primary toggle-close" title="{{ 'fel-gpt-assistant.chat.close'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.close'|trans }}" data-callback="toggleChatbot" data-toggle-class="active">
									{% sw_icon 'x' style {'class': 'x pe-none'} %}
								</button>
								<button type="button" class="fel-toggle-chatbot-button fel-btn fel-ui-btn btn btn-small btn-primary toggle-open" title="{{ 'fel-gpt-assistant.chat.open'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.open'|trans }}" data-callback="toggleChatbot" data-toggle-class="active">
									{{ 'fel-gpt-assistant.chat.open'|trans }}
								</button>
							</div>
						</div>
					</div>
				</div>
			{% endblock %}

			{% block fel_chatbot_messages_container %}
				<div id="fel-chatbot-messages"></div>
				<hr class="fel-chat-divider m-0"/>
			{% endblock %}

			{% block fel_chatbot_form_user %}
				<form id="fel-chatbot-form" class="bg-light" action="{{ seoUrl('frontend.fel.chatbox.message') }}" method="post">
					<input type="hidden" name="cmsAssistantIdElement" value="{{ cmsAssistantId }}">
					<div class="fel-chatbot-inputs w-100">
						<input type="hidden" name="fel-chatbot-run-id" value="">
						<input type="hidden" name="fel-chatbot-thread-id" value="">
						<input type="text" class="form-control input bg-white p-3 border-0" name="fel-chatbot-user-input" value="" placeholder="{{ 'fel-gpt-assistant.chat.placeholder'|trans }}" required="true" minlength="2" maxlength="{{ chatConfig.openAiUserInputMaxLength }}" autocomplete="off">
					</div>
					<div class="fel-submit-btn bg-white">
						<button type="submit" class="fel-submit-chat-btn btn bg-white h-100 px-3" title="{{ 'fel-gpt-assistant.chat.submit'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.submit'|trans }}">
							{% sw_icon 'paperplane' style {'class': 'forward pe-none'} %}
						</button>
					</div>
				</form>
			{% endblock %}
		</div>
	</div>
{% endif %}
