<div class="fel-test-api-button-container">
    <sw-button-process
        :isLoading="isLoading"
        :processSuccess="isSaveSuccessful"
        @process-finish="saveFinish"
        @click="check"
    >{{ $tc('fel-gpt-assistant.api-test-button.button') }}</sw-button-process>
    <template v-if="generalNotification">
        <div v-if="generalNotification.id && 'assistant' === generalNotification.object">
            <p class="alert alert-info fel-alert">
                {{ $t('fel-gpt-assistant.api-test-button.assistantIdCheckOk') }}: <b>{{ generalNotification.name }} ({{ generalNotification.id }})</b>
            </p>
        </div>
    </template>
    <template v-if="generalError">
        <p class="alert alert-danger fel-alert">
            {{ generalError }}
        </p>
    </template>
    <template v-if="Object.keys(tempAssistantList).length">
        <div v-if="tempAssistantList.data.length">
            <h2>{{ $t('fel-gpt-assistant.general.openAIAssistants') }}</h2>
            <ul class="fel-temp-assistant-list">
                <li v-for="obj in tempAssistantList.data">
                    <span>
                        <b>{{ obj.name }}</b>
                        <small>{{ obj.model }}</small>
                    </span>
                    <span :title="$t('fel-gpt-assistant.general.assistantId')">{{ obj.id }}</span>
                </li>
            </ul>
        </div>
        <div v-else>
            <p class="alert fel-alert">
                {{ $t('fel-gpt-assistant.general.noAssistantsFound') }}
            </p>
        </div>
    </template>
</div>
