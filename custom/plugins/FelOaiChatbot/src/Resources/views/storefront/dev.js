/* eslint-disable no-console */
/* eslint-disable no-alert */
/* eslint-disable no-unused-vars */
"use strict";

window.onload = () => {
	const { /** DEV ONLY */
		De<PERSON>un<PERSON>,
		<PERSON><PERSON><PERSON>ess,
		Iterator,
		CookieStorageHelper,
		Storage,
		COOKIE_CONFIGURATION_UPDATE,
		HttpClient
	} = window;
	const getPropmtInput = document.querySelector('[name="fel-chatbot-user-input"]');
	if (getPropmtInput) getPropmtInput.autocomplete = null;
	/** DEV ONLY ENDS */

	class FelOaiChatbotPluginDev {
		/** DEV ONLY */constructor() {
			Object.assign(this, {
				$emitter: window.FelOaiChatbotPlugin.$emitter,
				el: window.FelOaiChatbotPlugin.el,
				_pluginName: window.FelOaiChatbotPlugin._pluginName,
			});
			this.init()
		}/** constructor() only for dev */

		options = {
			behavior: 'smooth',
			compactView: 640 > window.innerWidth,
			controllerPath: {},
			selector: {
				chatMessages: '#fel-oai-chatbot-messages',
				greetingContainer: '.fel-chatbot-greeting-container',
				loadingContainer: '.fel-chatbot-load-container',
				submitChatButton: '.fel-submit-chat-btn',
				chatThreadId: '[name="fel-chatbot-thread-id"]',
				chatRunId: '[name="fel-chatbot-run-id"]',
				chatUserInput: '[name="fel-chatbot-user-input"]',
				chatMessageEl: 'chat-message-item',
				chatMessage: 'chat-message',
				cookieDefaultName: 'fel-oai-chatbot-localstorage-accepted',
			},
			layout: {
				scrollUpIsLeft: false,
			},
			page: {
				refId: null,
			},
			storage: {
				chat: 'FelOaiChatbotPlugin',
			},
			eventMapping: {
				form: {
					element: '#fel-oai-chatbot-form',
					events: [{ type: 'submit', handler: 'handleSubmit' }]
				},
				chat: {
					element: '[data-oai-chatbot-plugin]',
					events: [
						{ type: 'click',  handler: 'handleClick' },
						{ type: 'change', handler: 'handleChange' }
					]
				}
			},
			eventHandlerMap: {}
		};

		init() {
			if (!this.setPluginOptions()) return;

			this.setPluginVars();
			this.initializeEventSystem();
			this.initializeTemplates();
			this.initializeConsent();
			this.restoreLastState();
			this.setupShopwareIntegration();

			console.log(this)
		}

		// Setup plugin options and DOM elements
		setPluginOptions() {
			if ('undefined' === typeof DomAccess) return false;

			// Setup core elements
			this.assistantEl = this.el || this.domGet(document.body, '[data-oai-chatbot-plugin]', false);
			if (!this.assistantEl) return false;

			this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-oai-chatbot-form', false);
			if (!this.chatbotForm?.action || this.chatbotForm?.action === '') return false;

			this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages, false);

			// Parse options from DOM
			const pluginOptions = this.assistantEl.dataset.options;
			if (!pluginOptions) return true;

			try {
				const parsed = JSON.parse(pluginOptions);
				if (parsed) {
					this.options = this.objectMerge(this.options, parsed);
				}
				return true;
			} catch (error) {
				return false;
			}
		}

		// Initialize plugin variables
		setPluginVars() {
			this._client = new HttpClient();
			this.cookieAccepted = false;
			this.eventsRegistered = false;
			this.isLoading = false;
			this.isLoadingResponse = false;
			this.threadId = null;
			this.lastConversationId = null;
		}

		// Initialize template system
		initializeTemplates() {
			this.loadTemplates()
		}

		// Initialize consent system
		initializeConsent() {
			this.setLocalStorageConsent();
		}

		// Restore last state from storage
		restoreLastState() {
			this.setLastChatZoom();
			this.restoreLastChatState();
		}

		// Shopware Integration
		setupShopwareIntegration() {
			document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, (updated) => this.cookieCallback(updated));

			this.fixToggleChatScrollUpBtn(window.PluginManager.getPluginInstances('ScrollUp'));

			if (this.options.page.refId) {
				this.highlightActiveProductsInChat();
			}
		}

		cookieCallback(updatedCookies) {
			const cookieName = this.options.selector.cookieDefaultName;

			if (updatedCookies?.detail && undefined !== updatedCookies.detail?.[cookieName]) {
				this.cookieAccepted = updatedCookies.detail[cookieName];
			}

			this.setLocalStorageConsent();
		}

		fixToggleChatScrollUpBtn(scrollUpPlugins) {
			try {
				if (!scrollUpPlugins?.length) {
					throw new Error('Scroll up watcher failed');
				}
				const scrollBtn = scrollUpPlugins[0]?.el?.firstElementChild;
				if (scrollBtn) {
					const computedStyle = window.getComputedStyle(scrollBtn);
					const right = this.felParseInt(computedStyle.right);
					const rightOpen = right + this.felParseInt(computedStyle.width) + 3;
					const rightClosed = right;
					const rightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
					const assistantEl = this.assistantEl;
					const toggleButton = assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');

					toggleButton.style.height = computedStyle.height;
					assistantEl.style.bottom = !assistantEl.classList.contains('active') ? computedStyle.bottom : null;
					assistantEl.dataset.setBottom = computedStyle.bottom;

					if (false === this.options.layout.scrollUpIsLeft) {
						if (!assistantEl.classList.contains('active')) {
							assistantEl.style.right = rightVal + 'px';
							assistantEl.dataset.setRight = rightVal + 'px';
						}
						scrollUpPlugins[0].$emitter.subscribe('toggleVisibility', () => {
							const newRightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
							assistantEl.style.right = assistantEl.classList.contains('active') ? null : newRightVal + 'px';
							assistantEl.dataset.setRight = newRightVal + 'px';
						});
					}
				}
			} catch (error) {
				this.assistantEl.classList.add('fel-scroll-watch-failed');
			}
		}

		setLastChatZoom() {
			const getCurrent = this.storageGet(`${this.options.storage.chat}_chatZoom`);
			if ('active' === getCurrent) {
				const getZoomButton = this.assistantEl.querySelector('.fel-zoom-button');
				if (getZoomButton) {
					getZoomButton.classList.add('fel-active');
					this.assistantEl.classList.add('fel-zoom-chat');
				}
			}
		}

		restoreLastChatState() {
			if ('active' === this.storageGet(`${this.options.storage.chat}_chatOpen`)) {
				var getToggleButton = this.assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');
				if (getToggleButton) {
					getToggleButton.dispatchEvent(new MouseEvent('click', {bubbles: true}));
				}
			}
		}

		/**
		 * Quick gtter
		 */

	    getChatbotMessagesEl() {
			return this.domGet(this.assistantEl, this.options.selector.chatMessages, false);
		}

		getChatbotUserInput() {
			return this.domGet(this.chatbotForm, this.options.selector.chatUserInput, false);
		}

		getChatbotThreadId() {
			return this.domGet(this.chatbotForm, this.options.selector.chatThreadId, false);
		}

		getChatbotRunId() {
			return this.domGet(this.chatbotForm, this.options.selector.chatRunId, false);
		}

		getChatSubmitButton() {
			return this.domGet(this.assistantEl, this.options.selector.submitChatButton, false);
		}

		scrollThreadIntoView(force) {
			if (!force && !this.options.compactView) {
				const getActiveProducts = this.getActiveProductsInChat();

				if (getActiveProducts.length) {
					return this._scrollIntoView(getActiveProducts[0], 150);
				}
			}

			const getLastUserMsgId = this.domGetAll(this.assistantEl, '.fel-conversation-pair', false);
			if (getLastUserMsgId.length) {
				this._scrollIntoView(getLastUserMsgId[getLastUserMsgId.length - 1], 200);
			}
		}

		_scrollIntoView(selector, timeout=50) {
			const getTarget = 'object' === typeof selector ? selector : this.domGet(this.assistantEl, selector, false);
			if (getTarget) {
				this.debounce(() => {getTarget.scrollIntoView({behavior: this.options.behavior, block: 'start'})}, timeout);
			}
		}

		/**
		 * Create a conversation pair container with user message
		 */
		createConversationPair(conversationId, userMessage) {
			const useLoadingTemplate = this.template.loading.cloneNode(true);
			const translation = this.options.translation;

			const conversationHtml = `
				<div class="fel-conversation-pair fel-chat-fade-in" data-conversation-id="${conversationId}">
					<div class="fel-user-message chat-message-item user-message-item">
						<div class="chat-message user-message">
							<div class="fel-inner" data-datetime="${this.getCurrentTime()}">
								${this.escapeHtml(userMessage)}
							</div>
						</div>
					</div>
					<div class="chat-message-item chatbot-message-item">
						<div class="chat-message chatbot-message">
							<div class="fel-inner">
								<div class="fel-assistant-placeholder">${translation.waitASecond}</div>
							</div>
							<div class="fel-delete-message">
								<button class="fel-btn fel-btn-sm fel-delete-conversation"
									data-callback="removeConversationPair"
									data-conversation-id="${conversationId}"
									title="${translation.removeChatMessage}">×</button>
							</div>
						</div>
					</div>
					${useLoadingTemplate.outerHTML}
				</div>
			`;

			this.putHtml(this.getChatbotMessagesEl(), 'beforeend', conversationHtml);
			this.removeGreetingContainer();
			this.scrollThreadIntoView(true);
		}

		/**
		 * Handle successful API response
		 */
		handleSuccessfulResponse(data, conversationId) {
			// Update thread information
			if (data.threadId) {
				this.getChatbotThreadId().value = data.threadId;
				this.threadId = data.threadId;
				this.assistantEl.classList.add('contains-thread');
			}

			// Process assistant messages from last_thread (skip index 0 which is user message)
			if (data.threadMessages && data.threadMessages.last_thread && Array.isArray(data.threadMessages.last_thread)) {
				this.addAssistantResponseToConversation(conversationId, data.threadMessages.last_thread.slice(1));
			}

			// Handle product search results
			if (data.renderedProducts) {
				this.addProductsToConversation(conversationId, data.renderedProducts);
			}

			// Handle required actions
			if (data.requiredActions) {
				this.handleRequiredActions(data.requiredActions);
			}

			this.chatToStorage(data.threadId);
			this.getChatbotUserInput().focus();
		}

		// Handle error response
		handleErrorResponse(response, conversationId) {
			let errorMessage;
			if (!response?.success && response?.data?.message) {
				errorMessage = response.data.message;
			} else {
				errorMessage = response?.error?.message || response?.exception || 'Unknown error occurred';
			}

			this.addAssistantResponseToConversation(conversationId, [{
				role: 'assistant',
				content: [{
					type: 'text',
					text: { value: `<div class="fel-error-message">${this.escapeHtml(errorMessage)}</div>` }
				}]
			}]);
		}

		// Add assistant response to conversation
		addAssistantResponseToConversation(conversationId, messages) {
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);
			if (!conversation) return;

			const assistantMessage = conversation.querySelector('.chatbot-message .fel-inner');

			if (assistantMessage && messages.length > 0) {
				let combinedContent = '';

				// Process all assistant messages from the response
				messages.forEach(message => {
					if (message && message.role === 'assistant') {
						// Extract content from OpenAI API structure
						let messageContent = '';
						if (message.content && Array.isArray(message.content)) {
							// OpenAI API format: content is an array of content objects
							messageContent = message.content
								.filter(content => content.type === 'text')
								.map(content => content.text.value)
								.join('');
						} else if (message.value) {
							// Legacy format
							messageContent = message.value;
						} else if (message.content) {
							// Simple string content
							messageContent = message.content;
						}

						if (messageContent) {
							combinedContent += messageContent;
						}
					}
				});

				if (combinedContent) {
					assistantMessage.innerHTML = combinedContent;
				}
			}
		}

		// Replace first <ul> in assistant response with rendered products
		addProductsToConversation(conversationId, renderedProducts) {
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);
			if (!conversation) return;

			const assistantMessage = conversation.querySelector('.chatbot-message .fel-inner');
			if (assistantMessage) {
				// Find the first <ul> element in the assistant response
				const firstUl = assistantMessage.querySelector('ul');

				if (firstUl) {
					const tempDiv = document.createElement('div');
					tempDiv.innerHTML = renderedProducts;
					firstUl.replaceWith(...tempDiv.childNodes);
				} else {
					assistantMessage.innerHTML += renderedProducts;
				}
			}
		}

		// Handle required actions from the API response
		handleRequiredActions(requiredActions) {
			if (requiredActions.redirectTo) {
				if (requiredActions.redirectTo !== location.href) {
					location.href = requiredActions.redirectTo;
				}
			}
		}

		/**
		 * Callable clickEvent helper
		 */

		toggleChatbot(_, el) {
			const getToggleClass = el.dataset.toggleClass;
			if (getToggleClass) {
				this.assistantEl.classList.toggle(getToggleClass);

				if (this.assistantEl.classList.contains('active')) {
					this.assistantEl.style.right = null;
					this.assistantEl.style.bottom = null;
					this.scrollThreadIntoView();
					if (this.getChatbotUserInput()) {
						this.getChatbotUserInput().focus();
					}
					this._addBackdrop();
				} else {
					this.assistantEl.style.right = this.assistantEl.dataset.setRight;
					this.assistantEl.style.bottom = this.assistantEl.dataset.setBottom;
					this._removeBackdrop();
				}

				this._storageChatbotState();
			}
		}

		toggleChatZoom(_, el) {
			el.classList.toggle('fel-active');
			if (this.assistantEl) {
				if (el.classList.contains('fel-active')) {
					this.assistantEl.classList.add('fel-zoom-chat');
				} else {
					this.assistantEl.classList.remove('fel-zoom-chat');
				}
				this._storageChatbotZoom();
				if (this.getChatbotUserInput()) {
					this.getChatbotUserInput().focus();
				}
			}
		}

		addToChatSubmit(_, el) {
			const getStrToSet = el.dataset.prompt;
			if (getStrToSet) {
				this.getChatbotUserInput().value = getStrToSet;
				this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
			}
		}

		// Remove conversation pair
		removeConversationPair(event) {
			const button = event.target;
			const conversationId = button.dataset.conversationId;
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);

			if (conversation) {
				conversation.remove();
				this.chatToStorage(this.getChatbotThreadId().value);

				// Check if chat is empty
				const remainingConversations = this.assistantEl.querySelectorAll('.fel-conversation-pair');
				if (remainingConversations.length === 0) {
					this.deleteChatThread();
				}
			}
		}

		chatToggleProperties(event) {
			event.target.parentNode.classList.toggle('fel-show-properties');
		}

		chatToggleSearchArgs(event) {
			const el = event.target;
			const targetEl = this.domGet(this.assistantEl, el.dataset.targetEl, false);
			if (targetEl) {
				el.dataset.toggleClass.split(',').map(v => targetEl.classList.toggle(v));
				el.dataset.toggleSelf.split(',').map(v => el.classList.toggle(v));
				this._scrollIntoView(targetEl.parentNode, 150);
			}
		}

		chatWorkerFixProperties(event) {
			const el = event.target;
			const getStrToSet = el.dataset.shortPrompt;
			const sharedProp = el.dataset.sharedProp;
			const targetId = el.dataset.targetId;
			const targetSelector = el.dataset.targetSelector;
			const getActivePrompts = () => {
				const map = {shortPrompts: [], shortPromptsEnd: null};
				const getAll = this.domGetAll(this.assistantEl, `${targetId} [data-shared-prop].active`, false);
				if (getAll.length) {
					for (var i = 0; i < getAll.length; i++) {
						if (!map.shortPromptsEnd) {
							map.shortPromptsEnd = getAll[i].dataset.shortPromptEnd;
						}
						map.shortPrompts.push(getAll[i].dataset.shortPrompt);
					}
				}
				return map;
			};
			if (getStrToSet && sharedProp) {
				const getSharedEls = this.domGetAll(this.assistantEl, `${targetId} ${targetSelector}`, false);
				if (el.classList.contains('active')) {
					el.classList.remove('active');
				} else {
					el.classList.add('active');
				}
				for (var i = 0; i < getSharedEls.length; i++) {
					if (el !== getSharedEls[i]) {
						getSharedEls[i].disabled = el.classList.contains('active') ? true : null ;
					}
				}
				const chatbotUserInput = this.domGet(this.assistantEl, this.options.selector.chatUserInput, false);
				const activePrompts = getActivePrompts();
				if (chatbotUserInput) {
					const getMaxLength = chatbotUserInput.getAttribute('maxlength') || 50;
					chatbotUserInput.value = '';
					if (activePrompts.shortPrompts.length) {
						let newVal =`${activePrompts.shortPrompts.join(', ')} ${activePrompts.shortPromptsEnd}`;
						if (newVal.length > getMaxLength) {
							newVal = newVal.slice(0, getMaxLength);
						}
						chatbotUserInput.value = newVal;
					}
				}
			}
		}

		/**
		 * Consent helper
		 */

		setLocalStorageConsent() {
			this.cookieAccepted = CookieStorageHelper.getItem(this.options.selector.cookieDefaultName) === 'allowed';

			this.updateConsentUI(this.cookieAccepted);

			document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, this.handleConsentChangeEvent.bind(this));
		}

		handleConsentChangeEvent(event) {
			const cookieName = this.options.selector.cookieDefaultName;

			if (!event?.detail || typeof event.detail[cookieName] === 'undefined') {
				return;
			}

			this.cookieAccepted = event.detail[cookieName] === 'allowed';
			this.updateConsentUI(this.cookieAccepted);

			// Update storage on change
			if (this.cookieAccepted) {
				this._storageChatbotState();
				this._storageChatbotZoom();
			} else {
				this.clearLocalStorage();
			}
		}

		updateConsentUI(hasConsent) {
			const consentCheckbox = this.domGet(this.assistantEl, '[name="fel-allow-localstorage"]', false);
			if (consentCheckbox) {
				consentCheckbox.checked = hasConsent;
			}

			this.assistantEl.classList.toggle('fel-localstorage-is-allowed', hasConsent);
			this.assistantEl.classList.toggle('fel-localstorage-is-disabled', !hasConsent);
		}

		checkIfLocalStorageIsAllowed() {
			return this.cookieAccepted
				? this.cookieAccepted
				: ('allowed' || true) === CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
		}

		resetChatState() {
			this.assistantEl.classList.remove('contains-thread');
			this.getChatbotRunId().value = '';
			this.getChatbotThreadId().value = '';
			this.chatClearStorage();

			if (this.getChatbotMessagesEl()) {
				this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
				if (this.checkIfLocalStorageIsAllowed()) {
					this.setCheckedConsentLocalStorageTemplate(true);
				}
			}

			this.getChatbotUserInput().focus();
		}

		setCheckedConsentLocalStorageTemplate(localstorage) {
			const getGreetingTemplate = this.template.greeting.querySelector('[name="fel-allow-localstorage"]');
			const getOriginalEl = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');

			if (getGreetingTemplate) {
				getGreetingTemplate.checked = localstorage ? true : null;
			}
			if (getOriginalEl) {
				getOriginalEl.checked = localstorage ? true : null;
			}
		}

		/**
		 * Storage management
		 */

		storageGet() {
			return Storage.getItem(...arguments);
		}

		// setItem(keyName, keyValue)
		storageSet() {
			if (this.checkIfLocalStorageIsAllowed()) {
				Storage.setItem(...arguments);
			}
		}

		storageRemove() {
			Storage.removeItem(...arguments);
		}

		_storageChatbotState(setState) {
			this.storageSet(`${this.options.storage.chat}_chatOpen`,
				setState || this.assistantEl.classList.contains('active') ? 'active' : 'not-active'
			);
		}

		_storageChatbotZoom(setState) {
			this.storageSet(`${this.options.storage.chat}_chatZoom`,
				setState || this.assistantEl.classList.contains('fel-zoom-chat') ? 'active' : 'not-active'
			);
		}

		chatClearStorage() {
			this.storageRemove(this.options.storage.chat);
			this.storageRemove(`${this.options.storage.chat}_threadId`);
		}

		chatToStorage(threadId) {
			const getChat = this.getChatbotMessagesEl();
			if (getChat) {
				this.storageSet(this.options.storage.chat, getChat.innerHTML);
				if (threadId) {
					this.storageSet(`${this.options.storage.chat}_threadId`, threadId);
				}
			}
		}

		clearLocalStorage() {
			const storageKeys = [
				this.options.storage.chat,
				`${this.options.storage.chat}_threadId`,
				`${this.options.storage.chat}_chatOpen`,
				`${this.options.storage.chat}_chatZoom`
			];
			storageKeys.forEach(key => Storage.removeItem(key));
		}

		// change event from input
		toggleLocalStorage(_, el) {
			const isChecked = el.checked;
			const cookieName = this.options.selector.cookieDefaultName;
			CookieStorageHelper.setItem(cookieName, isChecked ? 'allowed' : 'denied');

			// Trigger consent change handler
			this.handleConsentChangeEvent({ detail: { [cookieName]: isChecked ? 'allowed' : 'denied' } });
		}

		/**
		 * Utility methods
		 */

		// beforebegin afterbegin beforeend afterend
		putHtml(el, toPosition, html) {
			el.insertAdjacentHTML(toPosition, html);
		}

		putToMessages(message) {
			this.putHtml(this.getChatbotMessagesEl(), 'beforeend', message);
		}

		domGet() {
			return DomAccess.querySelector(...arguments);
		}

		domGetAll() {
			return DomAccess.querySelectorAll(...arguments);
		}

		foreach(toIterate, callback) {
			return Iterator.iterate(toIterate, function(value, key) {
				callback(key, value);
			});
		}

		// debounce(callback, delay, immediate = false)
		debounce() {
			if (typeof Debouncer === 'function') {
				return Debouncer.debounce(...arguments).apply();
			} else if(typeof arguments[0] === 'function') {
				return setTimeout(() => {arguments[0].apply()}, arguments[1] || 0);
			}
		}

		objectMerge(...objects) {
			const m = (t, s) => {
				Object.entries(s).forEach(([k, v]) => {
					t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
				});
				return t;
			}
			return objects.reduce(m, {});
		}

		felParseInt(str) {
			return parseInt(str, 10);
		}

		escapeHtml(text) {
			const div = document.createElement('div');
			div.textContent = text;
			return div.innerHTML;
		}

		getCurrentTime() {
			return new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
		}

		// Generate unique conversation ID
		generateConversationId() {
			return 'conv-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
		}

		loadTemplates() {
			this.template = { greeting: '', loading: '', messageItem: '' };
			const templates = document.getElementById('fel-oai-chatbot-template');
			if (templates) {
				this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
				if (this.template.greeting) {
					const getChatFromStorage = this.storageGet(this.options.storage.chat);
					this.putToMessages(getChatFromStorage ? getChatFromStorage : this.template.greeting.outerHTML);
					if (getChatFromStorage) {
						this.assistantEl.classList.add('contains-thread');
						this.getChatbotThreadId().value = this.storageGet(this.options.storage.chat + '_threadId');
					}
				}
				this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
			}
		}

		_addBackdrop() {
			if (this.options.compactView) {
				this.putHtml(this.assistantEl, 'afterend', `
					<div class="modal-backdrop fel-chat-modal-backdrop fel-chat-fade-in show" onclick="this.remove()"></div>
				`);
			}
		}

		_removeBackdrop() {
			const getBackdrop = document.body.querySelector('.fel-chat-modal-backdrop');
			if (getBackdrop) {
				getBackdrop.remove();
			}
		}

		removeGreetingContainer() {
			const checkGreetings = this.domGet(this.assistantEl, `${this.options.selector.greetingContainer} .fel-chatbot-greeting`, false);
			if (checkGreetings) {
				this.assistantEl.classList.remove('fel-chat-initial');
				checkGreetings.classList.add('fel-chat-fade-out');
				this.debounce(() => { checkGreetings.parentNode.remove() }, 500);
			}
		}

		getActiveProductsInChat() {
			if (this.options.page.refId) {
				return this.assistantEl.querySelectorAll(`.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`);
			}
			return [];
		}

		highlightActiveProductsInChat() {
			if (this.options.page.refId) {
				const activeProductsSelector = `.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`;
				if (this.domGet(this.assistantEl, activeProductsSelector, false)) {
					this.embedStyle(`
						#fel-oai-chatbot.active.fel-zoom-chat ${activeProductsSelector} .fel-item-wrapper {
							box-shadow: var(--fel-hl-active-products-box-shadow);
						}
						#fel-oai-chatbot.active:not(.fel-zoom-chat) ${activeProductsSelector} img {
							box-shadow: var(--fel-hl-active-products-box-shadow);
						}
					`);
				}
			}
		}

		embedStyle(styleString) {
			let highlightId = 'fel-embed-highlight-active-products';
			let getStyleEl = document.querySelector(`#${highlightId}`);
			if (!getStyleEl) {
				getStyleEl = document.createElement('style');
				getStyleEl.id = highlightId;
				document.head.append(getStyleEl);
			}
			return getStyleEl.textContent = styleString;
		}

		getConversationByIdOrLast(conversationId) {
			conversationId = conversationId || this.lastConversationId;
			let getConversation;

			if (conversationId) {
				getConversation = this.domGet(this.getChatbotMessagesEl(), `.fel-conversation-pair[data-conversation-id="${conversationId}"]`, false);

				if (!getConversation) {
					const searchAll = this.domGetAll(this.getChatbotMessagesEl(), `.fel-conversation-pair[data-conversation-id]`, false);

					if (searchAll.length) {
						getConversation = searchAll[searchAll.length -1];
					}
				}
			}

			return getConversation;
		}

		addIsLoading() {
			this.getChatbotUserInput().disabled = true;
			this.isLoading = true;
			this.assistantEl.classList.add('fel-chatbot-is-loading');

			const getConversation = this.getConversationByIdOrLast();
			const searchLoadingIndicator = this.domGet(this.assistantEl, this.options.selector.loadingContainer, false);

			if (!searchLoadingIndicator && getConversation) {
				try {
					getConversation.append(this.template.loading);
					this._scrollIntoView(this.template.loading);
				} catch (error) {}
			}
		}

		removeIsLoading() {
			const loadingIndicators = this.domGetAll(this.assistantEl, this.options.selector.loadingContainer, false);

			if (loadingIndicators.length) {
				loadingIndicators.forEach(indicator => indicator.remove());
			}

			this.assistantEl.classList.remove('fel-chatbot-is-loading');
			this.isLoading = false;
			this.getChatbotUserInput().disabled = null;
		}

		/**
		 * http helper
		 */

		clientPost(url, sendMessage, callback) {
			this.addIsLoading();

			this._client.post(url, JSON.stringify(sendMessage), response => {
				if (response) {
					try {
						const parse = JSON.parse(response);
						if (parse) {
							response = parse;
						}
					} catch (error) {
						this.removeIsLoading();
						var div = document.createElement('div');
						div.appendChild(document.createTextNode(error));
						return callback({error: {exception: div.innerHTML.substring(0, 200)}});
					}
				}

				this.removeIsLoading();

				return callback(response);
			});
		}

		deleteChatThread() {
			const threadId = this.getChatbotThreadId()?.value;

			if (threadId && this.options.controllerPath.delete) {
				this.clientPost(this.options.controllerPath.delete, { threadId }, () => { this.resetChatState() });
			} else {
				this.resetChatState();
			}
		}

		_chatEventCallback(event, el) {
			if ('fel-oai-chatbot-form' === el.id) {
				event.preventDefault();

				if (this.getChatbotUserInput()) {
					const userMessage = this.getChatbotUserInput().value.trim();
					if (!userMessage) return false;

					const conversationId = this.generateConversationId();
					this.createConversationPair(conversationId, userMessage);
					this.getChatbotUserInput().value = '';
					this.lastConversationId = conversationId;

					const requestData = {
						message: userMessage,
						threadId: this.getChatbotThreadId().value || null
					};

					if (this.$emitter) {
						this.$emitter.publish('felOaiChatbotThreadCreated');
					}

					this.clientPost(this.options.controllerPath.thread, requestData, response => {
						if (response && response.success) {
							this.handleSuccessfulResponse(response.data, conversationId);
						} else {
							this.handleErrorResponse(response, conversationId);
						}
						if (this.$emitter) {
							this.$emitter.publish('FelOaiChatbotMessageResponse');
						}
					});
				}
				return false;
			}
		}

		/**
		 * Event system
		 */

		handleSubmit(event) {
			try {
				return this._chatEventCallback(event, event.target);
			} catch (error) {
				event.preventDefault();
				return this.putToMessages(error.message || 'error');
			}
		}

		handleChange(event) {
			const el = event.target;
			if (el.classList.contains('fel-checkbox')) {
				event.preventDefault();

				const { blur, callback } = el.dataset;

				if (blur) el.blur();

				if (typeof this[callback] === 'function') {
					this[callback](event, el);
				}
			}
			return false;
		}

		handleClick(event) {
			const el = event.target.closest('.fel-btn');
			if (!el) return;

			const {
				callback,
				preventDefault,
				stopPropagation,
				blur
			} = el.dataset;

			if (preventDefault) event.preventDefault();
			if (stopPropagation) event.stopPropagation();
			if (blur) el.blur();

			if (!callback) return;

			if (typeof this[callback] === 'function') {
				return this[callback](event, el);
			}

			if (typeof window[callback] === 'function') {
				return window[callback](event, el);
			}
		}

		// Handler

		initializeEventSystem() {
			const eventFunctionMap = this.options.eventHandlerMap;

			this.handleEvent = (event) => {
				if (this.isLoading && !event.target.classList.contains('fel-ui-btn')) {
					if (event.type === 'submit') {
						event.preventDefault();
					}
					return false;
				}
				if (eventFunctionMap?.[event.type]) {
					return this[eventFunctionMap[event.type]](event, event.target);
				}
			};

			this.registerEvents();
		}

		registerEvents() {
			Object.entries(this.options.eventMapping).forEach(([_, config]) => {
				this._handleEventRegistering('addEventListener', config.element, config.events);
			});
		}

		destroy() {
			Object.entries(this.options.eventMapping).forEach(([_, config]) => {
				this._handleEventRegistering('removeEventListener', config.element, config.events);
			});
		}

		_handleEventRegistering(execFn, element, eventMap) {
			element = this.domGet(document.body, element, false);
			if (!element) return;

			if (!Array.isArray(eventMap)) {
				eventMap = Object.values(eventMap);
			}

			eventMap.forEach(eventConfig => {
				if (!eventConfig ||
					!eventConfig.type ||
					!eventConfig.handler ||
					typeof this[eventConfig.handler] !== 'function'
				) return;

				this.options.eventHandlerMap[eventConfig.type] = eventConfig.handler;

				element[execFn](eventConfig.type, this, eventConfig.capture || false);
			});
		}

	}

    new FelOaiChatbotPluginDev();


	class FelContactToChatbotPluginDev {
		/** DEV ONLY */constructor() {this.init()}/** constructor() only for dev */

		init() {
			// window.FelContactToChatbotPlugin;
			this.$emitter = window.$emitterContactToChatbot;
		}

		/**
		 * Initialize contact form assistant integration
		 */
		initContactFormAssistant() {
			const contactForms = this.findContactForms();

			console.log(contactForms)

			contactForms.forEach(form => {
				this.addAiAssistantButton(form);
			});
		}

		/**
		 * Find contact forms on the page (Shopware-proof!)
		 */
		findContactForms() {
			const forms = [];

			// Shopware-specific selectors (because they love duplicate IDs! 🤦‍♂️)
			const selectors = [
				'form[action*="/form/contact"]',           // Shopware contact forms
				'form[action*="/form/"]',                 // Any Shopware form
				'form[data-form-validation="true"]',      // Shopware form validation
				'form[data-form-preserver="true"]',       // Shopware form preserver
				'form[id*="cms-form"]',                   // CMS forms (even with duplicate IDs!)
				'form[action*="contact"]',                // Generic contact forms
				'form.contact-form',                      // CSS class based
				'form[data-form-type="contact"]',         // Data attribute based
				'.cms-element-form form',                 // CMS element wrapper
				'[data-cms-element-type="form"] form'     // CMS element type
			];

			selectors.forEach(selector => {
				const foundForms = document.querySelectorAll(selector);
				foundForms.forEach(form => {
					// Extra validation to make sure it's really a contact form
					if (!forms.includes(form) && this.isContactForm(form)) {
						forms.push(form);
					}
				});
			});

			return forms;
		}

		/**
		 * Validate if a form is actually a contact form
		 */
		isContactForm(form) {
			const action = form.action || '';
			const id = form.id || '';
			const className = form.className || '';

			// Check for contact-related patterns
			const contactPatterns = [
				'/form/contact',
				'/contact',
				'contact-form',
				'cms-form-contact',
				'kontakt'  // German shops
			];

			// Check if any pattern matches
			const isContact = contactPatterns.some(pattern =>
				action.includes(pattern) ||
				id.includes(pattern) ||
				className.includes(pattern)
			);

			// Additional check: look for typical contact form fields
			const hasContactFields = form.querySelector('input[name*="email"], input[type="email"], textarea[name*="message"], textarea[name*="nachricht"]');

			return isContact || hasContactFields;
		}

		/**
		 * Add AI assistant button above contact form (Shopware-proof!)
		 */
		addAiAssistantButton(form) {
			// Generate unique identifier for this specific form instance
			const formId = this.generateFormId(form);

			// Check if button already exists for this specific form
			if (form.parentElement.querySelector(`.fel-ai-assistant-button[data-form-id="${formId}"]`)) {
				return;
			}

			const buttonText = this.options.config?.contactFormButtonText || 'Ask AI Assistant first?';
			const buttonHtml = `
				<div class="fel-ai-assistant-button-container mb-3" data-form-id="${formId}">
					<div class="alert alert-info d-flex align-items-center">
						<div class="flex-grow-1">
							<strong>💬 ${this.escapeHtml(buttonText)}</strong>
							<p class="mb-0 small">Get instant answers before filling out the form.</p>
						</div>
						<button type="button" class="btn btn-primary ms-3 fel-ai-assistant-button"
								data-callback="openAiAssistant"
								data-form-id="${formId}">
							Try AI Assistant
						</button>
					</div>
				</div>
			`;

			// Insert button before the form
			form.insertAdjacentHTML('beforebegin', buttonHtml);
		}

		/**
		 * Generate unique form ID (because Shopware loves duplicate IDs!)
		 */
		generateFormId(form) {
			// Create a unique identifier based on form position and attributes
			const formIndex = Array.from(document.querySelectorAll('form')).indexOf(form);
			const action = form.action || '';
			const id = form.id || '';

			// Create hash-like identifier
			return `form-${formIndex}-${action.split('/').pop()}-${id}`.replace(/[^a-zA-Z0-9-]/g, '');
		}

		/**
		 * Open AI assistant when contact form button is clicked
		 */
		openAiAssistant(event) {
			const button = event.target;

			// Show the chatbot
			if (this.assistantEl) {
				this.assistantEl.style.display = 'block';
				this.assistantEl.scrollIntoView({ behavior: 'smooth' });

				// Focus on the input
				const userInput = this.getChatbotUserInput();
				if (userInput) {
					userInput.focus();

					// Optional: Add a helpful message
					const helpMessage = this.options.config?.contactFormHelpMessage ||
						"Hi! I'm here to help. What would you like to know?";

					// You could auto-start a conversation here if desired
					// userInput.value = helpMessage;
				}
			}

			// Hide the button container after use
			const buttonContainer = button.closest('.fel-ai-assistant-button-container');
			if (buttonContainer) {
				buttonContainer.style.display = 'none';
			}
		}

	}
};
