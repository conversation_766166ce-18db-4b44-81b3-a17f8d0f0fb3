const { Mixin } = Shopware;

Mixin.register('fel-helper-mixin', {

    methods: {
        /**
         * @attr dataset.action[click|toggle]
         * @attr dataset.target[selector]
         * @attr dataset.targetClass[classname]
         * @attr dataset.queryAll['true'|'false']
         * @attr dataset.selfClass[classname]
         * @attr dataset.activeIf['not'|'set']
         */
        felMultiBtn(event) {
            var el = event.target,
                ds = el.dataset
                all = 'true' == ds.queryAll,
                qSelector = all ? 'querySelectorAll' : 'querySelector';
            switch(ds.action) {
                case 'click':
                    var getTarget = this.$el[qSelector](ds.target);
                    if (null !== getTarget && (getTarget || 'undefined' !== typeof getTarget.length)) {
                        if (!all) {
                            getTarget = [getTarget];
                        }
                        if ('undefined' !== typeof getTarget.length) {
                            for (let i = 0; i < getTarget.length; i++) {
                                getTarget[i].dispatchEvent(new MouseEvent('click', {bubbles: true}));
                            }
                        }
                    }
                break;
                case 'toggle':
                    var activeIf = ds.activeIf || 'set',
                        targetClass = ds.targetClass,
                        selfClass = ds.selfClass,
                        getTarget = this.$el[qSelector](ds.target);
                    if (null === getTarget) {
                        getTarget = document[qSelector](ds.target);
                    }
                    if (!all && getTarget) {
                        getTarget = [getTarget];
                    }
                    if (null !== getTarget && 'undefined' !== typeof getTarget.length) {
                        for (let i = 0; i < getTarget.length; i++) {
                            getTarget[i].classList.toggle(targetClass);
                            var checkActive = getTarget[i].classList.contains(targetClass);
                            if (selfClass) {
                                var setToggleSelf;
                                if ('not' === activeIf) {
                                    setToggleSelf = checkActive ? 'remove' : 'add';
                                }
                                else if ('set' === activeIf) {
                                    setToggleSelf = checkActive ? 'add' : 'remove';
                                }
                                if (setToggleSelf) {
                                    el.classList[setToggleSelf](...selfClass.split('|'));
                                }
                            }
                        }
                    }
                break;
            }
        },
    },

});
