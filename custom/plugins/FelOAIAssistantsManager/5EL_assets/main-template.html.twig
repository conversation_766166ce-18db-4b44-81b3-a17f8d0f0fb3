<script>

// Reload page
setTimeout(() => {this.$router.go()}, 7000);

// Simulate delay
await new <PERSON>(resolve => setTimeout(resolve, 5000));


// Copy&paste to main mixin for dev
mounted() {
    Shopware.State.commit('adminMenu/expandSidebar');
},
computed: {
    getAdminMenuExpander() { return Shopware.State.get('adminMenu').isExpanded },
},
watch: {
    'getAdminMenuExpander': function(newVal, oldVal) {
        if (!newVal || !oldVal) {
            Shopware.State.commit('adminMenu/expandSidebar');
        }
    },
},
// will all be removed, once finished
</script>


{% block fel_assistant_manager_content_editor %}
<sw-page class="fel-assistant-manager-content-editor fel-content-extension fel-assistant-manager">

    {# Main page title #}
    <template #smart-bar-header>
        <h2>Lorem ipsum page</h2>
    </template>

    {# Ation buttons #}
    <template #smart-bar-actions>
        <fel-api-loading-indicator :componentsAreLoading="isLoading || getManagerIsLoading()"></fel-api-loading-indicator>
    </template>

    {# Sidebar #}
    <template #side-content>
        <sw-card>
            Lorem ipsum dolor
        </sw-card>
    </template>

    {# Content #}
    <template #content>
        <template v-if="isLoading">
 			<div class="fel-full-height-skeleton-bar">
			    <mt-skeleton-bar v-for="idx in pluginConfig.setLoadSkeleton" :key="idx" />
            </div>
        </template>
        <template v-else>

            <div class="fel-assistant-manager-app-container">


                <mt-banner
                    positionIdentifier="index"
                    class="fel-size-xs fel-border-radius-2"
                    :closable="true"
                    @close.click="onClose"
                >
                    Your content here...
                </mt-banner>


                <mt-card-view style="position: relative; height: 400px;">
                    <mt-card title="Card-1" large>
                        Lorem ipsum dolor sit amet
                    </mt-card>
                    <mt-card title="Card-2" large>
                        Lorem ipsum dolor sit amet
                    </mt-card>
                </mt-card-view>

                <mt-card
                    :closable="true"
                    :hero="false"
                    :isLoading="false"
                >
                    {# Header #}
                    <template #title>title-title</template>
                    <template #subtitle>subtitle-subtitle</template>
                    <template #context-actions>context-actions-context-actions</template>
                    {# after Header #}
                    <template #tabs>tabs-tabs</template>
                    <template #toolbar>toolbar-toolbar</template>
                    {# Content #}
                    <template #default>default</template>
                    <template #grid="{ title }">grid</template>
                    {# Footer #}
                    <template #footer>footer-footer</template>
                </mt-card>

            </div>

            <fel-footer-component :isLoading="isLoading"></fel-footer-component>

        </template>
    </template>
</sw-page>
{% endblock %}




    <mt-checkbox label="Display toast icon?" v-model:checked="displayIcon" />
    <mt-checkbox
    label="Toast manually dismissible?"
    v-model:checked="dismissible"
    />
    <mt-checkbox label="Add action?" v-model:checked="action" />


<!--
    Repository handle
-->
<script>
    const { Component, Mixin, Data: { Criteria } } = Shopware;

    Shopware.Component.register('any-component', {
        inject: [
            'repositoryFactory',
        ],

        computed: {
            categoryRepository() {
                return this.repositoryFactory.create('category');
            },
        },

    });
</script>

<!--
    Input handle
-->
<template>
    enProp: {{ enProp }}

    <mt-text-field
      :model-value="enProp"
      @update:modelValue="updateEnProp"
    ></mt-text-field>
</template>
<script>
    Shopware.Component.register('fel-component', {
        data() {
            return {
                enProp: null,
            };
        },
        methods: {
            handleInputs(enProp) {
                this.enProp = enProp;

                return this.enProp;
            },
        },
    });
</script>



<script>
data() {
	return {
		selectedFiles: []
	}
}
computed: {
	options() {
		const data = this.filesList.data;

		return data.map(file => ({
			label: file.filename,
			value: file.id
		}));
	}
},
</script>

<mt-select :options="options"
	v-model="selectedFiles">
	<template #option="{ option }">{{ option.label }}</template>
</mt-select>
{{ selectedFiles }}



<!--
    ALERT: info, inherited, attention, critical, positive, neutral
-->
<mt-banner variant="critical">
	<template #default>
		ALERT
	</template>
</mt-banner>


<!--
	Default Card
    .fel-box
		&.fel-last
        &.fel-box-card

			.fel-box-content
				&:not(.fel-box-fill)

            .fel-box-header
			.fel-box-inner-content
            .fel-box-footer
-->
<div class="fel-assistant-manager-app-container">

    <div class="fel-box fel-box-card">
        <mt-card class="fel-border-radius-0" positionIdentifier="index">
            {# Header #}
            <template #before-card>
                <div class="fel-box-header fel-box-content fel-flex fel-flex-space-between">
                    HEADER
                </div>
            </template>

            {# Content #}
            <template #default>
                <div class="fel-box-inner-content fel-flex-between-center">
                    CONTENT
                </div>
            </template>

            {# Footer #}
            <template #after-card>
                <div class="fel-box-footer fel-box-content" :class="{'fel-box-fill': !isLoading}">
                    FOOTER
                </div>
            </template>
        </mt-card>
    </div>

</div>



<!--
    Toggle any
-->
<mt-button :class="{'is-active': getToggleControl('elementId')}" @click="setToggleControl('elementId')">
	Toggle Element
</mt-button>

<mt-button v-if="!getToggleControl('elementId')" @click="setToggleControl('elementId', true)">
	Show Element
</mt-button>

<mt-button v-if="getToggleControl('elementId')" @click="setToggleControl('elementId', false)">
	Hide Element
</mt-button>

<div v-if="getToggleControl('elementId')">
	Hidden content
</div>



{{ getToggleControl('elementId') }}

<mt-card
    class="fel-card"
    positionIdentifier="index"
    @custom-event="setToggleControl"
>
    <!-- Named slot example -->
    {# <template v-slot:header></template> #}

    <template #header></template>
    <template #footer></template>

    <template v-slot:default="slotProps">with access to {{ slotProp }}</template>

    <p>This is the default content.</p>

    <mt-button @click="setToggleControl('elementId')">Toggle Element</mt-button>
</mt-card>





<div
    v-show="getToggleControl('assistantFunctions')"
    class="fel-available-functions fel-mt-1 fel-px-1 fel-pb-1 fel-border-1">
    <template v-for="(fn, name) in availableFunctions().list" :key="name">
        <div v-if="!toSubmit['fel--sw--assistant_tools'].length ||
            (toSubmit['fel--sw--assistant_tools'].length && toSubmit['fel--sw--assistant_tools'].includes(name))" class="fel-oai-tool">

            <h3 class="fel-py-1">{{ name }}</h3>
            <p class="fel-size-xs fel-mb-1">{{ fn.description }}</p>

            <pre class="fel-oai-assistant-object fel-p-1 fel-border-1 fel-pre pre-wrap">{{ fn.parameters }}</pre>
        </div>
    </template>
</div>
