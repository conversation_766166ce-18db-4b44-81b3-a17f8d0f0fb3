<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_cms_element_cms_advisor %}
<div class="sw-cms-el-cms-advisor">
    <div
        v-if="element.config.content.source === 'mapped'"
        class="sw-cms-el-cms-advisor__mapping-preview content-editor"
        v-html="$sanitize(demoValue)"
    ></div>

    <sw-text-editor
        v-else
        v-model:value="element.config.content.value"
        :disabled="disabled"
        :vertical-align="element.config.verticalAlign.value"
        :allow-inline-data-mapping="true"
        :is-inline-edit="true"
        sanitize-input
        sanitize-field-name="app_cms_block.template"
        enable-transparent-background
        @blur="onBlur"
        @update:value="onInput"
    />
</div>
{% endblock %}
