const { Mixin } = Shopware;

Mixin.register('fel-export-to-file', {

    data() {
        return {
            jsonTextIndentation: 2,
            exportToContentType: {
                json: 'application/json;charset=utf-8',
                csv: 'text/csv;charset=utf-8',
                txt: 'text/plain;charset=utf-8',
            }
        };
    },

    methods: {

        setJsonTextIndentation(newVal) {
            this.jsonTextIndentation = newVal;
        },

        formatToJSON(data) {
            return JSON.stringify(data, null, this.jsonTextIndentation);
        },

        formatToCSV(data, columnNames) {
            if ('messagesTotal' !== columnNames[5]) {
                columnNames.splice(5, 0, 'messagesTotal');
            }

            let toExport = [columnNames];

            for (let i = 0; i < data.length; i++) {
                const logItem = data[i];
                if ('undefined' !== typeof logItem.id) {
                    let exportCol = [];
                    for (let eci = 0; eci < columnNames.length; eci++) {
                        let colName = columnNames[eci],
                            colValue = logItem[colName];
                        if ('messagesTotal' === colName) {
                            colValue = logItem.threadMessages.length;
                        }
                        else if (['createdAt', 'updatedAt'].includes(colName)) {
                            colValue = `"${colValue}"`;
                        }
                        else if ('errors' === colName) {
                            colValue = JSON.stringify(colValue);
                        }
                        else if ('threadMessages' === colName) {
                            let flattenChat = [];
                            for (let fci = 0; fci < colValue.length; fci++) {
                                let chatItem = colValue[fci];
                                if ('undefined' !== typeof chatItem.value) {
                                    let addNL = 'assistant' === chatItem.role ? "\n" : '';
                                    flattenChat.push(`${chatItem.role}: ${chatItem.value}${addNL}`);
                                }
                            }
                            colValue = JSON.stringify(flattenChat.join("\n"));
                        }
                        exportCol.push(colValue);
                    }
                    toExport.push(exportCol.join(", "));
                }
            }

            return toExport.join("\n");
        },

        exportToFile(filename, rows, columnNames, fileType) {
            try {
                let [setDate] = new Date().toISOString().split('.');
                let [newFilename] = filename.split('.');

                if (filename.endsWith('.json')) {
                    fileType = 'json';
                    var data = this.formatToJSON(rows);
                } else if (filename.endsWith('.csv')) {
                    fileType = 'csv';
                    var data = this.formatToCSV(rows, columnNames);
                } else if (filename.endsWith('.txt')) {
                    fileType = 'txt';
                    var data = rows.trim();
                }

                if ('undefined' !== typeof data && data) {
                    filename = `${newFilename}_${setDate}.${fileType}`;
                    filename = filename.replaceAll(':', '_');
                    this.dispatchDownloadEvent(filename, this.exportToContentType[fileType], data);
                }
            } catch (error) {
                console.error(error);
            }
        },

        dispatchDownloadEvent(filename, fileType, data) {
            var link = document.createElement('a');
            if ('undefined' !== typeof link.download) {
                var url = URL.createObjectURL(new Blob([data], {type: fileType}));
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.dispatchEvent(new MouseEvent('click'));
                document.body.removeChild(link);
            }
        },
    },

});
