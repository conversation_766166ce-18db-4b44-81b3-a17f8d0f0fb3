import template from './fel-oai-trainer.html.twig';
import './fel-oai-trainer.scss';

const { Mixin, Component } = Shopware;

Component.register('fel-oai-trainer-component', {
    template,

    inject: ['repositoryFactory', 'felApiService'],

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('fel-export-to-file'),
        Mixin.getByName('fel-helper-mixin'),
        Mixin.getByName('fel-input-helper-mixin'),
        Mixin.getByName('fel-openai-mixin'),
        Mixin.getByName('fel-plugin-config-mixin'),
    ],

    created() {
        this.productManufacturer;
        this.propertyGroup;
        this.propertyGroupOption;
    },

    computed: {
        examplesInStorage() {
            return null !== localStorage.getItem(this.storage.name);
        },
        getGenericExamplesForSearchQueries() {
            return this.genericExamplesForSearchQueries();
        },
        productManufacturer() {
            return this.httpClient.get('/product-manufacturer', {headers: this.felApiService.getBasicHeaders()})
                .then((response) => {
                    return this.manufacturerList = response.data.data;
                }).catch((error) => {console.error(error.message)});
        },
        propertyGroup() {
            return this.httpClient.get('/property-group', {headers: this.felApiService.getBasicHeaders()})
                .then((response) => {
                    return this.propertyGroupList = response.data.data;
                }).catch((error) => {console.error(error.message)});
        },
        propertyGroupOption() {
            return this.httpClient.get('/property-group-option', {headers: this.felApiService.getBasicHeaders()})
                .then((response) => {
                    return this.propertyGroupOptionsList = response.data.data;
                }).catch((error) => {console.error(error.message)});
        },
    },

    methods: {

        getArrayRandom(arr) {
            return arr[this.getRandom(arr)];
        },

        getRandom(rangeMax) {
            if ('undefined' !== typeof rangeMax.length) rangeMax = rangeMax.length;
            else if ('string' === typeof rangeMax) rangeMax = +rangeMax;
            return Math.floor(Math.random() * rangeMax);
        },

        objectFilter(toFilter) {
            return Object.fromEntries(Object.entries(toFilter).filter(([_, v]) => v != null));
        },

        objectLength(obj) {
            return Object.keys(obj).length;
        },

        initTool(event) {
            if (!this.objectLength(this.tempGetProductProperties.properties)) {
                this.tempUserQueryProductName =
                this.tempUserQueryProductNameInitial = this.getArrayRandom(this.randomProductNames);
            }
            this.tempUserPrompt = `${this.getArrayRandom(this.randomThreadStarter)} ${this.tempUserQueryProductName}`;
            this.enablePropertySelection =
            this.enableManufacturerSelection =
            this.enablePriceSelection = true;
            this.tempUserQuerySorting =
            this.tempUserQueryMaxPrice =
            this.tempUserQueryMinPrice = '';
            this._resetPropertySelection();
            this.handleSelection(event);
        },

        handleProductName(event) {
            if (event.target.value !== this.tempUserQueryProductNameInitial) {
                this.tempUserPrompt = this.tempUserPrompt.replace(this.tempUserQueryProductNameInitial, event.target.value);
                this.tempUserQueryProductNameInitial = event.target.value;
            }
            this.handleSelection(event);
        },

        handlePriceSelection(event) {
            let el = event.target,
                ds = el.dataset;
            if (0 > +el.value) {
                if ('min' === ds.price) {
                    this.tempUserQueryMinPrice = 0;
                } else if ('max' === ds.price) {
                    this.tempUserQueryMinPrice =
                    this.tempUserQueryMaxPrice = 0;
                }
            }
            switch(ds.price) {
                case 'min':
                    if (this.tempUserQueryMaxPrice && +el.value > this.tempUserQueryMaxPrice) {
                        this.tempUserQueryMaxPrice = +el.value;
                    }
                break;
                case 'max':
                    if (this.tempUserQueryMinPrice && +el.value < this.tempUserQueryMinPrice) {
                        this.tempUserQueryMinPrice = +el.value;
                    }
                break;
            }
            this.handleSelection(event);
        },

        handleOrderSelection(event) {
            var getSelected = event.target.querySelector(':checked');
            if (getSelected && !getSelected.dataset.reset) {
                this.tempSortingSetted = getSelected.text;
                if (!this.lastSortingSetted) {
                    this.lastSortingSetted = getSelected.text;
                }
            } else {
                this.tempSortingSetted = null;
                this.tempUserQuerySorting = null;
            }
            this.handleSelection(event);
        },

        _resetPropertySelection() {
            let getActiveProps = this._getPropertySelectionBtn(true);
            for (let i = 0; i < getActiveProps.length; i++) {
                var ds = getActiveProps[i].dataset;
                if ('undefined' !== typeof this.tempGetProductProperties[ds.inGroup][ds.id]) {
                    delete this.tempGetProductProperties[ds.inGroup][ds.id];
                }
                getActiveProps[i].classList.remove('active');
            }
        },

        _activePropsSelection() {
            let getActiveProps = this._getPropertySelectionBtn(true);
            this.enablePropertySelection =
            this.enableManufacturerSelection = getActiveProps.length < this.rule.properties.maxSelection;
            return getActiveProps;
        },

        _getPropertySelectionBtn(onlyActive) {
            return this.$el.querySelectorAll(`
                [data-in-group="properties"]${onlyActive ? '.active' : ''},
                [data-in-group="manufacturer"]${onlyActive ? '.active' : ''}
            `);
        },

        handlePropertySelection(event) {
            let el = event.target,
                ds = el.dataset;

            if (this.enablePropertySelection) {
                el.classList.toggle('active');
                var isActive = el.classList.contains('active');

                if (isActive) {
                    if ('undefined' === typeof this.tempGetProductProperties.properties[event.target.dataset.id]) {
                        this.tempGetProductProperties[ds.inGroup] = Object.assign({}, this.tempGetProductProperties[ds.inGroup], {
                            [el.dataset.id]: el.dataset.name
                        });
                    }
                    var setKeywords = 'manufacturer' === ds.inGroup ? ['from'] : ['in', 'with', 'from'] ;
                    this.tempUserPrompt += ` ${this.getArrayRandom(setKeywords)} ${el.dataset.name}`;
                } else {
                    if ('undefined' !== typeof this.tempGetProductProperties[ds.inGroup][el.dataset.id]) {
                        delete this.tempGetProductProperties[ds.inGroup][el.dataset.id];
                        this.tempUserPrompt = this.tempUserPrompt.replace(el.dataset.name, '');
                    }
                }
            }

            this.tempGetProductProperties = Object
                .fromEntries(Object.entries(this.tempGetProductProperties).filter(([_, v]) => v != null));

            this._activePropsSelection();
            this.handleSelection(event);
        },

        handleSelection(event) {
            this.tempAssistantPrompt = `query: '${this.tempUserQueryProductName}'`;

            if (this.tempGetProductProperties.properties) {
                var getPropIds = Object.keys(this.tempGetProductProperties.properties);
                if (getPropIds.length) {
                    this.tempAssistantPrompt += `, properties: ['${getPropIds.join("', '")}']`;
                }
            }

            if (this.tempGetProductProperties.manufacturer) {
                var getManuIds = Object.keys(this.tempGetProductProperties.manufacturer);
                if (getManuIds.length) {
                    this.tempAssistantPrompt += `, manufacturer: ['${getManuIds.join("', '")}']`;
                }
            }

            if (this.tempUserQueryMinPrice && 0 < this.tempUserQueryMinPrice) {
                this.tempAssistantPrompt += `, price_min: ${this.tempUserQueryMinPrice}`;
                this.tempUserPrompt += ` budget from ${this.tempUserQueryMinPrice}`;
                var matches = this.tempUserPrompt.match(/ budget from \d+/g);
                if (null !== matches && 1 < matches.length) {
                    this.tempUserPrompt = this.tempUserPrompt.replace(matches[0], '');
                }
            }

            if (this.tempUserQueryMaxPrice && 0 < this.tempUserQueryMaxPrice) {
                this.tempAssistantPrompt += `, price_max: ${this.tempUserQueryMaxPrice}`;
                this.tempUserPrompt += ` budget to ${this.tempUserQueryMaxPrice}`;
                var matches = this.tempUserPrompt.match(/ budget to \d+/g);
                if (null !== matches && 1 < matches.length) {
                    this.tempUserPrompt = this.tempUserPrompt.replace(matches[0], '');
                }
            }

            if (this.tempUserQuerySorting) {
                this.tempAssistantPrompt += `, order: '${this.tempUserQuerySorting}'`;
                if (this.tempSortingSetted) {
                    this.tempUserPrompt = this.tempUserPrompt.replace(`${this.lastSortingSetted}`, '');
                    this.tempUserPrompt += ` ${this.tempSortingSetted}`;
                    this.lastSortingSetted = this.tempSortingSetted;
                }
            } else if (this.lastSortingSetted) {
                this.tempUserPrompt = this.tempUserPrompt.replace(` ${this.lastSortingSetted}`, '');
            }

            if (0 == this.tempUserQueryMinPrice && 0 == this.tempUserQueryMaxPrice) {
                this.tempUserPrompt = this.tempUserPrompt
                    .replace('budget from 1 budget to 1', ' ')
                    .replace('budget from 1', ' ')
                    .replace('budget to 1', ' ');
            }

            this.tempUserPrompt = this.tempUserPrompt.replace(/\s+/g, ' ');
            this.tempAssistantPrompt = `product_search(${this.tempAssistantPrompt})`;
            this.finalRequiredProperties = JSON.stringify(this.tempGetProductProperties, null, 4);
            this.finalExampleContent = `user: "${this.tempUserPrompt}."\nassistant: ${this.tempAssistantPrompt}`;
        },

        resetActualSelection(event) {
            this._resetPropertySelection();
            this.tempUserPrompt =
            this.tempAssistantPrompt =
            this.tempUserQueryProductName =
            this.finalRequiredProperties =
            this.finalExampleContent =
            this.tempUserQuerySorting =
            this.tempUserQueryProductNameInitial = '';
            this.tempUserQueryMinPrice =
            this.tempUserQueryMaxPrice = null;
            this.tempUserQuerySorting = '';
            this.enablePropertySelection =
            this.enableManufacturerSelection =
            this.enablePriceSelection = false;
        },

        captureCurrentExample(event) {
            this._addToStorage({
                requiredProperties: this.finalRequiredProperties,
                conversation: this.finalExampleContent,
            });
        },

        _addToStorage(data) {
            var getCurrent = this._getFromStorage();
            if (null === getCurrent) {
                this._setToStorage(JSON.stringify([data]));
            } else {
                var parsed = JSON.parse(getCurrent);
                parsed.push(data);
                this._setToStorage(JSON.stringify(parsed));
            }
            this.storage.removed = false;
            this.storage.added = true;
            this.parseFromStorage();
        },

        _getFromStorage() {
            return localStorage.getItem(this.storage.name);
        },

        _deleteFromStorage() {
            localStorage.removeItem(this.storage.name);
            this.storage.removed = true;
            this.storage.merged = null;
            this.storage.added = false;
        },

        _setToStorage(data) {
            return localStorage.setItem(this.storage.name, data);
        },

        parseFromStorage() {
            let parsedItems = {
                requiredProperties: {
                    properties: {},
                    manufacturer: {},
                },
                conversation: [],
            };
            this.storage.parsed = JSON.parse(this._getFromStorage());
            if (null !== typeof this.storage.parsed && null !== typeof this.storage.parsed.length) {
                for (var i = 0; i < this.storage.parsed.length; i++) {
                    var itemData = this.storage.parsed[i];
                    parsedItems.conversation.push(itemData.conversation)
                    var props = itemData.requiredProperties;
                    if (props) {
                        try {
                            props = JSON.parse(props);
                            if ('object' === typeof props) {
                                parsedItems.requiredProperties.properties = Object.assign({}, parsedItems.requiredProperties.properties, props.properties);
                                parsedItems.requiredProperties.manufacturer = Object.assign({}, parsedItems.requiredProperties.manufacturer, props.manufacturer);
                            }
                        } catch (error) {}
                    }
                }
                this.storage.merged = {
                    requiredProperties: parsedItems.requiredProperties,
                    conversation: parsedItems.conversation
                };
            }
        },

        exampleCollectionToString(objToStr) {
            var str = `${objToStr.title}\n\n`;
            str += `return ${JSON.stringify(objToStr.requiredProperties, null, 4)}\n\n`;
            str += `${objToStr.defaultContactExample}\n\n`;
            for (var i = 0; i < objToStr.conversation.length; i++) {
                var conversation = objToStr.conversation[i];
                str += `${conversation}\n\n`;
            }
            this.finalExamplesStringLength = str.split("\n").length;
            this.finalExamplesStringPlain = str.trim();
            return str;
        },

        replaceDefaultExampleWithCustom() {
            var getInstrEl = this.$el.parentNode.parentNode.querySelector('[name="fel--sw--assistant_instruction"]');
            if (getInstrEl) {
                var getEditable = this.$el.querySelector('[name="fel-oai-trainer-combined-response"]');
                if (getEditable) {
                    getInstrEl.value = `${this.defaultInstructions()}\n\n${getEditable.value}`;
                } else {
                    getInstrEl.value = `${this.defaultInstructions()}\n\n${this.finalExamplesStringPlain}`;
                }
                this.createNotificationSuccess({message: this.$t('fel-gpt-assistant.trainer.ui.defaultReplaced')});
            }
        },
    },

    data() {
        return {
            assistant: {instructions: null},
            storage: {
                name: 'felTrainerData',
                removed: false,
                added: false,
                total: 0,
                parsed: null,
                merged: null,
            },
            process: {
                minSteps: 10,
            },
            rule: {
                properties: {
                    maxSelection: 50,
                }
            },
            enablePropertySelection: false,
            enableManufacturerSelection: false,
            enablePriceSelection: false,
            manufacturerList: {},
            propertyGroupList: {},
            propertyGroupOptionsList: {},
            tempGetProductProperties: {
                properties: {},
                manufacturer: {},
            },
            tempUserQueryProductNameInitial: '',
            tempUserQueryProductName: '',
            tempUserQueryMinPrice: null,
            tempUserQueryMaxPrice: null,
            tempUserQuerySorting: '',
            lastSortingSetted: null,
            tempUserPrompt: '',
            tempAssistantPrompt: '',
            tempExamplesTotal: 0,
            finalExampleContent: '',
            finalRequiredProperties: '',
            finalExamplesStringLength: 0,
            finalExamplesStringPlain: null,
            randomThreadStarter: [
                'I search a',
                'I search',
                'I need',
                'I\'m looking for',
                'I search a present',
                'I\'m searching for',
                'I want to search for',
                'I want to Find',
                'I am looking for',
                'Search for',
                'I am in need of',
            ],
            randomProductNames: [
                'Pants',
                'Octaplex',
                'SkyMountain',
                'Pullover',
                'Squirrel',
                'T-Shirts',
                'Mapoly',
                'Belly-flop',
                'Concrete',
                'Smartphone',
                'Shoes',
            ],
        };
    },

});
