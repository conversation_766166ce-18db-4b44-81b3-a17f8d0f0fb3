<?php

use Fel\CustomGptAgent\Service\OpenAI\OpenAIService;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Fel\CustomGptAgent\Storefront\Controller\ChatbotController;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction;


$createTempPropsFile = __DIR__ . '/temp_props.php';
$useFunction = $_GET['use_function'] ?? null;
$updateFile = 'temp-props' === ($_GET['refresh'] ?? null);
$handleFile = $updateFile;

// get chatbot path
$reflectionMethod = new ReflectionMethod(ChatbotController::class, 'chatbot');
$attributes = $reflectionMethod->getAttributes(Route::class);
$chatbotPath = null;
foreach($attributes as $attribute) {
    if ($path = $attribute->newInstance()->getPath()) {
        $path = array_filter(explode('/', $path));
        array_pop($path);
        $chatbotPath = '/' . implode('/', $path);
        break;
    }
}

// dump($updateFile);

if ('product_search' === $useFunction AND !$updateFile) {
    if (file_exists($createTempPropsFile) OR is_file($createTempPropsFile)) {
        $filemtime = filemtime($createTempPropsFile);

        if (date('m.d', $filemtime) === date('m.d', time())) {
            $getProductProperties = include_once $createTempPropsFile;
            $handleFile = false;
            return;
        } else {
            $handleFile = true;
        }
    } else {
        $handleFile = true;
    }
}



function requiredAction($run, Request $request, SalesChannelContext $salesContext, $httpClient, $router, $translator): array
{
    if ($toolCalls = ($run['required_action']['submit_tool_outputs']['tool_calls'] ?? null)) {
        // $oaiRaObj = (new OpenAIRequiredAction($httpClient, $router, $translator));

        // $exec = $oaiRaObj
        //     ->setSwConfigs($request->attributes->get('sw-storefront-url'), $salesContext->getSalesChannel()->getAccessKey())
        //     ->requiredAction($toolCalls, $post = []);
    }

    return $exec ?? [];
}


// get product properties and categories
$getProductProperties = json_decode('{
    "id": "chatblock-1828764327' . time() . '",
    "datetime": "' . date(DATE_RFC850) . '",
    "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
    "threadMessages": [
        {
            "value": "Mein Name ist Fely. Wie kann ich Ihnen helfen?",
            "annotations": [],
            "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
            "role": "assistant",
            "id": "msg_nJOtTm04M8mGXd1dqD3T4pJj"
        }
    ],
    "actionRequired": {
        "id": "run_OE0ADdpYKyTRuqtcdT0p03ep",
        "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
        "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
        "status": "requires_action",
        "required_action": {
            "type": "submit_tool_outputs",
            "submit_tool_outputs": {
                "tool_calls": [
                    {
                        "id": "call_QQY6ieyxlKF2khiBxWxO1xx",
                        "type": "function",
                        "function": {
                            "name": "get_product_properties",
                            "arguments": ""
                        }
                    }
                ]
            }
        },
        "last_error": null,
        "model": "gpt-4-1106-preview",
        "instructions": "You are a customer support chatbot",
        "tools": [],
        "file_ids": [],
        "metadata": []
    },
    "actionRequiredResponse": [],
    "trackError": [],
    "waitCycle": 2,
    "error": null,
    "_post": {"threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz","runId": "run_OE0ADdpYKyTRuqtcdT0p03ep","userInput": ""},
    "_messages": {}
}', true);


if ($handleFile) {
    $getProductProperties = requiredAction($getProductProperties['actionRequired'], $request, $salesContext, $this->httpClient, $this->router, $this->translator);
    $getProductProperties = $getProductProperties[0]['output']['output'] ?? $getProductProperties;

    if ($usePops = ($getProductProperties[0]['output'] ?? $getProductProperties)) {
        $getProductProperties = $usePops;

        if ($handleFile AND $getProductProperties) {
            $toVar = var_export($getProductProperties, true);
            file_put_contents($createTempPropsFile, '<?php return ' . $toVar . ";\n");
            @dump('Temp Properties File created');
        }
    } else {
        // $getProductProperties = [];
    }
}

// dump($getProductProperties);


function flattenCategories(array $arr)
{
    $r = [];

    foreach($arr as $item) {
        // if ($item['children'] ?? null) {
        //     $r = array_merge($r, $this->flattenCategories($item['children']));
        // }
        // unset($item['children']);
        // $r[] = $item;
    }

    return $r;
}



function filterToolsubmitResponse(array $actionResponse)
{
    $r = [];
    $actions = [];
    foreach($actionResponse as $i => $data) {
        $output = $data['output'];

        if (is_string($output)) {
            $output = json_decode($output, true);

            if (is_array($output) AND ($output['exec'] ?? null)) {
                if ('product_search' === $output['exec'] AND !isset($actions['product_search'])) {
                    $data['output'] = json_encode(array_values($output['output']));
                    $actions['product_search'] = $output;
                }
                if ('redirect' === $output['exec'] AND !isset($actions['redirectTo'])) {
                    $actions['redirectTo'] = $output['url'];
                }
            }
        }

        $r[] = $data;
    }
    return [
        'output' => $r,
        'actions' => $actions,
    ];
}
