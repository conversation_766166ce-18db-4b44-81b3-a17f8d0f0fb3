{"name": "eslint", "version": "1.0.0", "main": "index.js", "type": "module", "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@eslint/js": "^9.21.0", "@shopware-ag/storefront-eslint-rules": "file:packages/@shopware-ag/storefront-eslint-rules", "@shopware-ag/admin-eslint-rules": "file:packages/@shopware-ag/admin-eslint-rules", "@shopware-ag/admin-stylelint-rules": "file:packages/@shopware-ag/admin-stylelint-rules", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-inclusive-language": "^2.2.1", "eslint-plugin-vue": "^10.0.0", "eslint-plugin-vuejs-accessibility": "^2.4.1", "globals": "^16.0.0", "prettier": "3.5.3", "typescript-eslint": "^8.24.1", "vue-eslint-parser": "^10.1.1", "stylelint": "^16.14.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-scss": "^6.11.1"}}