
// dfgdsfg

// chatWorkerFixProperties

function devTo(event) {
    const el = event.target;
    const getStrToSet = el.dataset.shortPrompt;
    const sharedProp = el.dataset.sharedProp;

    const getActivePrompts = () => {
        const getAll = document.querySelectorAll(`#fel-chatbot [data-shared-prop].active`);
        const map = {shortPrompts: [], shortPromptsEnd: null};
        if (getAll.length) {
            for (var i = 0; i < getAll.length; i++) {
                if (!map.shortPromptsEnd) {
                    map.shortPromptsEnd = getAll[i].dataset.shortPromptEnd;
                }
                map.shortPrompts.push(getAll[i].dataset.shortPrompt);
            }
        }
        return map;
    };

    if (getStrToSet && sharedProp) {
        const getSharedEls = document.querySelectorAll(`#fel-chatbot [data-shared-prop="${sharedProp}"]`);
        if (el.classList.contains('active')) {
            el.classList.remove('active');
        } else {
            el.classList.toggle('active');
        }
        for (var i = 0; i < getSharedEls.length; i++) {
            if (el !== getSharedEls[i]) {
                if (el.classList.contains('active')) {
                    getSharedEls[i].disabled = true;
                } else {
                    getSharedEls[i].disabled = null;
                }
            }
        }

        const getMaxLength = document.querySelector('#fel-chatbot input[name="query"]').max;

        if (getMaxLength) {
            document.querySelector('#fel-chatbot input[name="query"]').value = '';

            const activePrompts = getActivePrompts();
            if (activePrompts.shortPrompts.length) {
                let newVal = activePrompts.shortPrompts.join(', ');
                newVal += ` ${activePrompts.shortPromptsEnd}`;
                if (newVal.length > getMaxLength) {
                    newVal = newVal.slice(0, getMaxLength);
                }
                document.querySelector('#fel-chatbot input[name="query"]').value = newVal;
            }
        }
    }


}
