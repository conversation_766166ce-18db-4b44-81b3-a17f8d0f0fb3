
export default class CmsChatbotPlugin extends window.PluginBaseClass {
    init() {
        // <PERSON><PERSON> sicher, dass der Chatbot immer sichtbar ist
        const chatbotElement = this.el.querySelector('#fel-chatbot-advisor');
        if (chatbotElement) {
            chatbotElement.classList.add('active');
        }

        // Entferne den Code, der das Eingabefeld fokussiert und damit zum Chat scrollt
        // const chatInput = this.el.querySelector('[name="fel-chatbot-user-input"]');
        // if (chatInput) {
        //     setTimeout(() => {
        //         chatInput.focus();
        //     }, 500);
        // }

        this._registerEvents();
    }

    _registerEvents() {
        // Register event for the delete thread/start new thread button
        const deleteThreadButton = this.el.querySelector('.fel-delete-thread-btn');
        if (deleteThreadButton) {
            deleteThreadButton.addEventListener('click', this._onDeleteThread.bind(this));
        }

        const submitButton = this.el.querySelector('.fel-submit-chat-btn');
        if (submitButton) {
            submitButton.addEventListener('click', this._onSubmit.bind(this));
        }

        // Entferne eventuelle Referenzen zu den Toggle- und Zoom-Buttons
    }

    _onDeleteThread(event) {
        // Call the deleteChatThread method from the parent plugin if available
        const parentPlugin = window.PluginManager.getPluginInstanceFromElement(
            this.el, 'FelAssistantPlugin'
        );
        if (parentPlugin && typeof parentPlugin.deleteChatThread === 'function') {
            parentPlugin.deleteChatThread();
        }
    }

    _onSubmit(event) {
        const form = this.el.querySelector('#fel-chatbot-form');
        if (form) {
            const assistantIdInput = document.createElement('input');
            assistantIdInput.type = 'hidden';
            assistantIdInput.name = 'cms-assistant-id';
            assistantIdInput.value = this.options.cmsAssistantId;
            form.appendChild(assistantIdInput);
        }
    }
}


