<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor;

use Shopware\Core\Framework\Plugin;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\Config\Loader\DelegatingLoader;
use Symfony\Component\Config\Loader\LoaderResolver;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

/**
 * FelAIProductAdvisor plugin - OpenAI services only version
 *
 * <AUTHOR> KI-Boxer
 */
class FelAIProductAdvisor extends Plugin
{
    public function build(ContainerBuilder $container): void
    {
        parent::build($container);

        // Load only essential configuration
        $locator = new FileLocator(__DIR__ . '/Resources/config');

        $resolver = new LoaderResolver([
            new YamlFileLoader($container, $locator),
        ]);

        $configLoader = new DelegatingLoader($resolver);

        $confDir = \rtrim($this->getPath(), '/') . '/Resources/config';

        // Load only necessary configuration files
        if (file_exists($confDir . '/packages/monolog.yaml')) {
            $configLoader->load($confDir . '/packages/monolog.yaml');
        }
    }
}