<?php declare(strict_types=1);

namespace FelOaiChatbot\Storefront\Controller;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Exception;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ChatController extends StorefrontController
{
    /**
     * @var SystemConfigService $configService Retrieves system configuration settings
     * @var OpenAIChatService $openAIChatService Handles the interaction with OpenAI Assistant API
     */
    public function __construct(
        protected readonly SystemConfigService $configService,
        protected readonly OpenAIChatService $openAIChatService
    ) {
    }

    /**
     * Create or resume a thread - unified endpoint for thread management
     */
    #[Route(
        path: '/fel/chat/thread',
        name: 'frontend.fel.chat.thread',
        methods: ['POST'],
        defaults: ['XmlHttpRequest' => true]
    )]
    public function handleThread(RequestDataBag $dataBag, SalesChannelContext $salesContext): Response
    {
        $config = [
            'salesChannelContext'  => $salesContext,
            'loadCustomConfig'     => 'FelOaiChatbot',
            'felOpenAiApiKey'      => $this->configService->get('FelOaiChatbot.config.felOpenAiApiKey'),
            'felOpenAiAssistantId' => $this->configService->get('FelOaiChatbot.config.felOpenAiAssistantId'),
        ];

        try {
            $response = $this->openAIChatService->autoHandleRequest($dataBag, $config);

            // Handle product search rendering if needed
            if ($searchResponse = ($response['requiredActions']['product_search'][0] ?? null)) {
                $rendered = $this->renderView('@Storefront/storefront/component/product-listing.html.twig', $searchResponse);
                $response['renderedProducts'] = $rendered;
            }

            return $this->json([
                'success' => true,
                'data' => $response
            ]);

        } catch(Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode()
                ]
            ], 400);
        }
    }

    /**
     * Delete a thread
     */
    #[Route(
        path: '/fel/chat/delete-thread',
        name: 'frontend.fel.chat.delete_thread',
        methods: ['POST'],
        defaults: ['XmlHttpRequest' => true]
    )]
    public function deleteThread(RequestDataBag $dataBag, SalesChannelContext $salesContext): Response
    {
        $config = [
            'salesChannelContext'  => $salesContext,
            'loadCustomConfig'     => 'FelOaiChatbot',
            'felOpenAiApiKey'      => $this->configService->get('FelOaiChatbot.config.felOpenAiApiKey'),
            'felOpenAiAssistantId' => $this->configService->get('FelOaiChatbot.config.felOpenAiAssistantId'),
        ];

        try {
            $dataBag->set('delete_thread', true);
            $response = $this->openAIChatService->autoHandleRequest($dataBag, $config);

            return $this->json([
                'success' => true,
                'data' => $response
            ]);

        } catch(Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode()
                ]
            ], 400);
        }
    }
}
