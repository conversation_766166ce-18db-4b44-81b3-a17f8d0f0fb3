import Debouncer from 'src/helper/debouncer.helper';
import DomAccess from 'src/helper/dom-access.helper';
import Iterator from 'src/helper/iterator.helper';
import CookieStorageHelper from 'src/helper/storage/cookie-storage.helper';
import Storage from 'src/helper/storage/storage.helper';
import {COOKIE_CONFIGURATION_UPDATE} from 'src/plugin/cookie/cookie-configuration.plugin';
import HttpClient from 'src/service/http-client.service';

export default class FelOaiChatbotPlugin extends window.PluginBaseClass {
	static options = {
		behavior: 'smooth',
		compactView: 640 > window.innerWidth,
		selector: {
			chatMessages:      '#fel-chatbot-messages',
			greetingContainer: '.fel-chatbot-greeting-container',
			loadingContainer:  '.fel-chatbot-load-container',
			submitChatButton:  '.fel-submit-chat-btn',
			chatThreadId:      '[name="fel-chatbot-thread-id"]',
			chatRunId:         '[name="fel-chatbot-run-id"]',
			chatUserInput:     '[name="fel-chatbot-user-input"]',
			chatMessageEl:     'chat-message-item',
			chatMessage:       'chat-message',
			cookieDefaultName: 'fel-chatbot-localstorage-accepted',
		},
		layout: {
			scrollUpIsLeft: false,
		},
		page: {
			refId: null,
		},
		storage: {
			chat: 'felChatStorage',
		}
	};

	init() {
		window.Debouncer = Debouncer;
		window.DomAccess = DomAccess;
		window.Iterator = Iterator;
		window.CookieStorageHelper = CookieStorageHelper;
		window.Storage = Storage;
		window.COOKIE_CONFIGURATION_UPDATE = COOKIE_CONFIGURATION_UPDATE;
		window.HttpClient = HttpClient;

		window.FelOaiChatbotPlugin = this;
		// if (this.setPluginOptions()) {
		// 	this.setPluginVars();
		// 	this.loadTemplates();
		// 	this.registerEvents();
		// }
	}

	registerEvents() {
		this.handleEvent = (event) => {
			if (this.isLoading && !event.target.classList.contains('fel-ui-btn')) {
				event.preventDefault();
				return false;
			}
			switch (event.type) {
				case 'submit': return this.chatEventCallback(event, event.target);
				case 'change': return this.changeEventCallback(event, event.target);
				case 'click':  return this.clickEventCallback(event, event.target);
			}
		};
		this.setEventHandler('add');
		this.setFromLastVisit();
		this.swEventHelper();
		this.highlightActiveProductsInChat();
	}

	setFromLastVisit() {
		this.setLocalStorageIsAllowed();

		this.setLastChatZoom();
		if ('active' === this.storageGet(`${this.options.storage.chat}_chatOpen`)) {
			var getToggleButton = this.assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');
			if (getToggleButton) {
				getToggleButton.dispatchEvent(new MouseEvent('click', {bubbles: true}));
			}
		}
	}

	swEventHelper() {
        document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, (updated) => this.cookieCallback(updated));

		const scrollUpPlugins = window.PluginManager.getPluginInstances('ScrollUp');
		if (scrollUpPlugins.length) {
			this.fixToggleChatScrollUpBtn(scrollUpPlugins);
		}
	}

	fixToggleChatScrollUpBtn(scrollUpPlugins) {
		const scrollBtn = scrollUpPlugins[0].el?.firstElementChild;
		if (scrollBtn) {
			const computedStyle = window.getComputedStyle(scrollBtn);
			const right = this.felParseInt(computedStyle.right);
			const rightOpen = right + this.felParseInt(computedStyle.width) + 3;
			const rightClosed = right;
			const rightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
			const assistantEl = this.assistantEl;
			const toggleButton = assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');

			toggleButton.style.height = computedStyle.height;
			assistantEl.style.bottom = !assistantEl.classList.contains('active') ? computedStyle.bottom : null;
			assistantEl.dataset.setBottom = computedStyle.bottom;

			if (false === this.options.layout.scrollUpIsLeft) {
				if (!assistantEl.classList.contains('active')) {
					assistantEl.style.right = rightVal + 'px';
					assistantEl.dataset.setRight = rightVal + 'px';
				}
				scrollUpPlugins[0].$emitter.subscribe('toggleVisibility', () => {
					const newRightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
					assistantEl.style.right = assistantEl.classList.contains('active') ? null : newRightVal + 'px';
					assistantEl.dataset.setRight = newRightVal + 'px';
				});
			}
		}
	}

	checkIfLocalStorageIsAllowed() {
		return this.cookieAccepted ? this.cookieAccepted : ('allowed' || true) === CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
	}

	setLocalStorageIsAllowed() {
        const checkState = this.checkIfLocalStorageIsAllowed();
		if (checkState) {
			this.assistantEl.classList.add('fel-localstorage-is-allowed');
			this.assistantEl.classList.remove('fel-localstorage-is-disabled');
			if (this.assistantEl.classList.contains('active')) {
				this.storageSet(`${this.options.storage.chat}_chatOpen`, 'active');
			}
			if (this.assistantEl.classList.contains('fel-zoom-chat')) {
				this.storageSet(`${this.options.storage.chat}_chatZoom`, 'active');
			}
		} else {
			this.assistantEl.classList.add('fel-localstorage-is-disabled');
			this.assistantEl.classList.remove('fel-localstorage-is-allowed');
			this.clearLocalStorage();
		}
        this.setCheckedConsentLocalStorageTemplate(checkState);
	}

	cookieCallback(updatedCookies) {
		const cookieName = this.options.selector.cookieDefaultName;

        if (updatedCookies?.detail && undefined !== updatedCookies.detail?.[cookieName]) {
            this.cookieAccepted = updatedCookies.detail[cookieName];
        }

        this.setLocalStorageIsAllowed();
	}

	setCheckedConsentLocalStorageTemplate(localstorage) {
		const getGreetingTemplate = this.template.greeting.querySelector('[name="fel-allow-localstorage"]');
		const getOriginalEl = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');

		if (getGreetingTemplate) {
			getGreetingTemplate.checked = localstorage ? true : null;
		}
		if (getOriginalEl) {
			getOriginalEl.checked = localstorage ? true : null;
		}
	}

	// UI callback
	toggleLocalStorage(event, el) {
        this.cookieAccepted = el.checked;
        CookieStorageHelper.setItem(this.options.selector.cookieDefaultName, this.cookieAccepted ? 'allowed' : false);
		this.setLocalStorageIsAllowed();
		return el.blur();
	}

	/**
	 * Chat handler
	 */
    chatEventCallback(event, el) {
		try {
			return this._chatEventCallback(event, el);
		} catch (error) {
			return this.putToMessages('chatbot', el.message || 'error');
		}
	}

	checkIfMessageThrown(response) {
		return (response.error && 'undefined' !== typeof response.error.exception) ? response.error.exception : false ;
	}

    _chatEventCallback(event, el) {
		if ('fel-chatbot-form' === el.id) {
			event.preventDefault();

			if (this.getChatbotUserInput()) {
				const sendMessage = {
					threadId:  this.getChatbotThreadId().value,
					runId:     this.getChatbotRunId().value,
					userInput: this.getChatbotUserInput().value,
				};
				if ('' === sendMessage.runId) {
					this.isLoadingResponse = false;
					this.clientPost(`${this.controllerUrl}/create-thread`, sendMessage, response => {
						if (response) {
							if (this.checkIfMessageThrown(response)) {
								return this.putToMessages('chatbot', this.checkIfMessageThrown(response), '', '', ' fel-system-exception');
							} else if ('undefined' !== typeof response.id) {
                                if (this.$emitter)
                                    this.$emitter.publish('felAssistantThreadCreated');
								this.threadId = response.id;
								this.putToMessages('user', sendMessage.userInput, this.threadId, response.datetime);
								this.getChatbotUserInput().value = '';
								this.getChatbotRunId().value = response.runId;
								this.getChatbotThreadId().value = response.threadId;
								this.assistantEl.classList.add('contains-thread');
								return this.chatbotForm.dispatchEvent(new Event('submit', {bubbles: true, cancelable: true}));
							}
						}
					});
				}
				else {
					if (this.threadId) {
						this.isLoadingResponse = true;
						this.getChatbotRunId().value = '';
						this.scrollThreadIntoView(true);
						this.clientPost(`${this.controllerUrl}/run`, sendMessage, response => {
							if (response) {
								if (this.checkIfMessageThrown(response)) {
									this.putToMessages('chatbot', this.checkIfMessageThrown(response), '', '', ' fel-system-exception');
								} else if ('undefined' !== typeof response.id) {
									if ('undefined' !== typeof response.threadMessages) {
										let tMessages = response.threadMessages;
										let searchFailedObj = null;
										if ('undefined' !== typeof tMessages[0] &&
											'undefined' !== typeof tMessages[0].role &&
											'assistant' === tMessages[0].role
										) {
											let lastAssistantMessages = true;
											this.foreach(tMessages, (i, data) => {
												if ('user' === data.role) {
													lastAssistantMessages = false;
												}
												if (lastAssistantMessages) {
													if ('undefined' !== typeof response.uiActionRequired.product_search) {
														if ('undefined' !== typeof data.originalValue) {
															data = this.replaceAiProductList(data, response.uiActionRequired.product_search);
														}
														if (0 === response.uiActionRequired.product_search.total) {
															searchFailedObj = response.uiActionRequired.product_search;
														}
													} else {
														if (!data.value.includes('<p') && !data.value.includes('<ul')) {
															let plainTextEl = document.createElement('div');
															plainTextEl.classList.add('fel-text-plain', 'text-break');
															plainTextEl.insertAdjacentHTML('beforeend', data.value.trim());
															data.value = plainTextEl.outerHTML;
														}
													}
													this.putToMessages('chatbot', data.value, response.id, response.datetime);
													if (searchFailedObj) {
														let readable = [];
														this.foreach(searchFailedObj.args, (key, value) => {
															if ('_truncate' !== key) {
																try {
																	if (['object', 'array', 'iterator'].includes(typeof value)) {
																		value = value.join(', ');
																	}
																	readable.push(`${key}: ${JSON.stringify(value)}`)
																} catch (error) {}
															}
														});
														let plainTextEl = document.createElement('p');
														let errHeader = document.createElement('h2');
														plainTextEl.classList.add('fel-text-plain', 'text-break');
														plainTextEl.innerHTML = readable.join("\n");
														errHeader.innerHTML = this.options.translation.usedArguments;
														this.putToMessages('chatbot', errHeader.outerHTML + plainTextEl.outerHTML, '', response.datetime, ' fel-search-failed-props');
													}
												}
											});
										} else {
											this.putToMessages('chatbot', this.options.translation.errorClearChat, '', response.datetime);
										}
									}
									this.getChatbotUserInput().focus();
									this.chatToStorage(response.threadId);
									if ('undefined' !== typeof response.uiActionRequired && response.uiActionRequired) {
										this.uiActionRequired(response.uiActionRequired);
									}
								}
							}
                            if (this.$emitter)
							    this.$emitter.publish('felAssistantMessageResponse');
						});
					}
				}
			}
			return false;
		}
    }

	replaceAiProductList(data, productFetch) {
		let replaceUl = document.createElement('div');
		let replacWith = document.createElement('div');
		replaceUl.insertAdjacentHTML('beforeend', data.originalValue);
		replacWith.insertAdjacentHTML('beforeend', data.value);
		if (replaceUl.querySelector('ul')) {
			let ulsToString = replaceUl.querySelectorAll('ul');
			if (ulsToString.length) {
				if (1 === ulsToString.length && productFetch?.items?.products?.length) {
					for (var i = 0; i < productFetch.items.products.length; i++) {
						if (data.originalValue.includes(productFetch.items.products[i].id)) {
							replaceUl.querySelector('ul').replaceWith(replacWith);
							data.value = replaceUl.innerHTML;
							break;
						}
					}
				} else {
					data.value = data.originalValue;
				}
			}
		}
		return data;
	}

	uiActionRequired(uiAction) {
		if ('undefined' !== typeof uiAction.redirectTo && uiAction.redirectTo) {
			if (uiAction.redirectTo !== location.href) {
				location.href = uiAction.redirectTo;
			}
		}
	}

	deleteChatThread() {
		var getThreadId = this.getChatbotThreadId()?.value;

		if (getThreadId) {
			this.addIsLoading();
			this._client.post(`${this.controllerUrl}/delete-thread`, JSON.stringify({threadId: getThreadId}), response => {
				this.removeIsLoading();
				if (response) {
					try {
						response = JSON.parse(response);
					} catch (error) {}
					this.assistantEl.classList.remove('contains-thread');
					this.getChatbotRunId().value = '';
					this.getChatbotThreadId().value = '';
					this.chatClearStorage();
					if (this.getChatbotMessagesEl()) {
						this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
						if (this.checkIfLocalStorageIsAllowed()) {
							this.setCheckedConsentLocalStorageTemplate(true);
						}
					}
					this.getChatbotUserInput().focus();
				}
			});
		} else {
			this.chatClearStorage();
			this.assistantEl.classList.remove('contains-thread');
			if (this.getChatbotMessagesEl()) {
				this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
				if (this.checkIfLocalStorageIsAllowed()) {
					this.setCheckedConsentLocalStorageTemplate(false);
				}
			}
		}
	}

	clientPost(url, sendMessage, callback) {
		this.addIsLoading();
		this._client.post(url, JSON.stringify(sendMessage), response => {
			this.removeIsLoading();
			if (response) {
				try {
					const parse = JSON.parse(response);
					if (parse) {
						response = parse;
					}
				} catch (error) {
					var div = document.createElement('div');
					div.appendChild(document.createTextNode(error));
					return callback({error: {exception: div.innerHTML.substring(0, 200)}});
				}
			}
			return callback(response);
		});
	}

	_scrollIntoView(selector, timeout=50) {
		const getTarget = 'object' === typeof selector ? selector : this.domGet(this.assistantEl, selector, false);
		if (getTarget) {
			this.debounce(() => {getTarget.scrollIntoView({behavior: this.options.behavior, block: 'start'})}, timeout);
		}
	}

	scrollThreadIntoView(force) {
		if (!force && !this.options.compactView) {
			const getActiveProducts = this.getActiveProductsInChat();
			if (getActiveProducts.length) {
				return this._scrollIntoView(getActiveProducts[0], 150);
			}
		}
		const getLastUserMsgId = this.domGetAll(this.assistantEl, '.user-message-item', false);
		if (getLastUserMsgId.length) {
			this._scrollIntoView(getLastUserMsgId[getLastUserMsgId.length - 1], 200);
		}
	}

	addIsLoading() {
        const useTempLoaderId = 'fel-chat-loader-temp-id';
		let useTemplate = this.template.loading;
		this.isLoading = true;
		this.assistantEl.classList.add('fel-chatbot-is-loading');
		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = true;
			this.getChatbotUserInput().disabled = true;
		}
		this.removeGreetingContainer();
		if (this.isLoadingResponse) {
			var cloneLoader = useTemplate.cloneNode(true);
			var getInfoBox = this.domGet(cloneLoader, '.fel-chatbot-loader-info', false);
            cloneLoader.id = useTempLoaderId;
			if (getInfoBox && getInfoBox.dataset.secondRun) {
				this.domGet(cloneLoader, '.fel-chatbot-loader-info').innerHTML = getInfoBox.dataset.secondRun;
			}
			useTemplate = cloneLoader;
		}
		this.putToMessages('loading', useTemplate.outerHTML, useTempLoaderId);
	}

	removeIsLoading() {
		this.isLoading = false;
		this.assistantEl.classList.remove('fel-chatbot-is-loading');
		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = null;
			this.getChatbotUserInput().disabled = null;
		}
		let getLoader = this.domGetAll(this.assistantEl, '.fel-chatbot-load-container', false);
		if (getLoader.length) {
			for (let i = 0; i < getLoader.length; i++) {
				getLoader[i].classList.add('position-absolute', 'w-100');
				this.debounce(() => {getLoader[i].remove()}, 150);
			}
		}
	}

	removeGreetingContainer() {
		var checkGreetings = this.domGet(this.assistantEl, this.options.selector.greetingContainer, false);
		if (checkGreetings) {
			checkGreetings.remove();
			this.assistantEl.classList.remove('fel-chat-initial');
		}
	}

	getChatSubmitButton() {
		return this.domGet(this.assistantEl, this.options.selector.submitChatButton, false);
	}

    getChatbotMessagesEl() {
        return this.domGet(this.assistantEl, this.options.selector.chatMessages, false);
    }

    getChatbotUserInput() {
        return this.domGet(this.chatbotForm, this.options.selector.chatUserInput, false);
    }

	getChatbotThreadId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatThreadId, false);
    }

	getChatbotRunId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatRunId, false);
    }

	// click callbacks

	addToChatSubmit(event, el) {
		const getStrToSet = el.dataset.prompt;
		if (getStrToSet) {
			this.getChatbotUserInput().value = getStrToSet;
			this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
		}
	}

	toggleChatbot(event, el) {
		const getToggleClass = el.dataset.toggleClass;
		if (getToggleClass) {
			this.assistantEl.classList.toggle(getToggleClass);
			const useStorageName = `${this.options.storage.chat}_chatOpen`;
			if (this.assistantEl.classList.contains('active')) {
				this.assistantEl.style.right = null;
				this.assistantEl.style.bottom = null;
                this.storageSet(useStorageName, 'active');
				this.scrollThreadIntoView();
				if (this.getChatbotUserInput()) {
					this.getChatbotUserInput().focus();
				}
				this._addBackdrop();
			} else {
				this.assistantEl.style.right = this.assistantEl.dataset.setRight;
				this.assistantEl.style.bottom = this.assistantEl.dataset.setBottom;
				this.storageSet(useStorageName, 'not-active');
				this._removeBackdrop();
			}
		}
	}

	chatToggleProperties(event) {
		event.target.parentNode.classList.toggle('fel-show-properties');
	}

	chatToggleSearchArgs(event) {
		const el = event.target;
		const targetEl = this.domGet(this.assistantEl, el.dataset.targetEl, false);
		if (targetEl) {
			el.dataset.toggleClass.split(',').map(v => targetEl.classList.toggle(v));
			el.dataset.toggleSelf.split(',').map(v => el.classList.toggle(v));
		}
	}

	chatWorkerFixProperties(event) {
		const el = event.target;
		const getStrToSet = el.dataset.shortPrompt;
		const sharedProp = el.dataset.sharedProp;
		const targetId = el.dataset.targetId;
		const targetSelector = el.dataset.targetSelector;
		const getActivePrompts = () => {
			const map = {shortPrompts: [], shortPromptsEnd: null};
			const getAll = this.domGetAll(this.assistantEl, `${targetId} [data-shared-prop].active`, false);
			if (getAll.length) {
				for (var i = 0; i < getAll.length; i++) {
					if (!map.shortPromptsEnd) {
						map.shortPromptsEnd = getAll[i].dataset.shortPromptEnd;
					}
					map.shortPrompts.push(getAll[i].dataset.shortPrompt);
				}
			}
			return map;
		};
		if (getStrToSet && sharedProp) {
			const getSharedEls = this.domGetAll(this.assistantEl, `${targetId} ${targetSelector}`, false);
			if (el.classList.contains('active')) {
				el.classList.remove('active');
			} else {
				el.classList.toggle('active');
			}
			for (var i = 0; i < getSharedEls.length; i++) {
				if (el !== getSharedEls[i]) {
					getSharedEls[i].disabled = el.classList.contains('active') ? true : null ;
				}
			}
			const chatbotUserInput = this.domGet(this.assistantEl, this.options.selector.chatUserInput, false);
			const activePrompts = getActivePrompts();
			if (chatbotUserInput) {
				const getMaxLength = chatbotUserInput.getAttribute('maxlength') || 50;
				chatbotUserInput.value = '';
				if (activePrompts.shortPrompts.length) {
					let newVal =`${activePrompts.shortPrompts.join(', ')} ${activePrompts.shortPromptsEnd}`;
					if (newVal.length > getMaxLength) {
						newVal = newVal.slice(0, getMaxLength);
					}
					chatbotUserInput.value = newVal;
				}
			}
		}
	}

	_addBackdrop() {
		if (this.options.compactView) {
			this.putHtml(this.assistantEl, 'afterend', `
				<div class="modal-backdrop fel-modal-backdrop fel-fade-in show" onclick="this.remove()"></div>
			`);
		}
	}

	_removeBackdrop() {
		const getBackdrop = document.body.querySelector('.fel-modal-backdrop');
		if (getBackdrop) {
			getBackdrop.remove();
		}
	}

	toggleChatZoom(event, el) {
		el.classList.toggle('fel-active');
		if (this.assistantEl) {
			const useStorageName = `${this.options.storage.chat}_chatZoom`;
			if (el.classList.contains('fel-active')) {
				this.assistantEl.classList.add('fel-zoom-chat');
				this.storageSet(useStorageName, 'active');
			} else {
				this.assistantEl.classList.remove('fel-zoom-chat');
				this.storageSet(useStorageName, 'not-active');
			}
			if (this.getChatbotUserInput()) {
				this.getChatbotUserInput().focus();
			}
		}
	}

	setLastChatZoom() {
		const getCurrent = this.storageGet(`${this.options.storage.chat}_chatZoom`);
		if ('active' === getCurrent) {
			const getZoomButton = this.assistantEl.querySelector('.fel-zoom-button');
			if (getZoomButton) {
				getZoomButton.classList.add('fel-active');
				this.assistantEl.classList.add('fel-zoom-chat');
			}
		}
	}

	removeChatMessage(event) {
		if (event.target?.offsetParent?.offsetParent) {
			const targetParent = event.target.offsetParent.offsetParent;
			if (targetParent) {
				if (targetParent.previousElementSibling.classList.contains('user-message-item')) {
					targetParent.previousElementSibling.remove();
				}
				targetParent.remove();
				this.chatToStorage(this.getChatbotThreadId().value);
				const getStorage = this.storageGet(this.options.storage.chat);
				if ('' == getStorage.trim()) {
					this.deleteChatThread();
				}
				this.scrollThreadIntoView();
			}
		}
	}

	clickEventCallback(event, el) {
		if (!el.classList.contains('fel-btn')) {
			return;
		}

		const callback = el.dataset.callback;

		if (el.dataset.preventDefault) event.preventDefault();
		if (el.dataset.stopPropagation) event.stopPropagation();
		if (el.dataset.blur) el.blur();

		if (callback) {
			if (typeof this[callback] === 'function') {
				return this[callback](...arguments);
			} else if (typeof window[callback] === 'function') {
				return window[callback](...arguments);
			}
		}
	}

	changeEventCallback(event, el) {
		if (el.classList.contains('fel-checkbox')) {
			event.preventDefault();
			const callbackFn = el.dataset.callback;
			if ('function' === typeof this[callbackFn]) {
				this[callbackFn](event, el);
			}
		}
		return false;
	}

    // helper / shortcuts

	/**
	 * @param {*} messageFrom ['user', 'chatbot']
	 * @param {*} message
	 */
	putToMessages(messageFrom, message, id='', datetime='', addClass='') {
        let setId = '';
		if (id) {
			id = id.slice(4);
			setId = ` id="f-${id}"`;
		}
		if (datetime) {
			datetime = ` data-datetime="${datetime}"`;
		}
		const setDeleteBtn = 'chatbot' === messageFrom
			? `<div class="fel-delete-message">
				<span title="${this.options.translation.removeChatMessage}"
					class="btn fel-btn" data-callback="removeChatMessage">X</span>
				</div>`
			: '' ;

        const template = ['loading', 'greeting', 'append'].includes(messageFrom)
			? message
			: `<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex"${setId}>
				<div class="${this.options.selector.chatMessage} ${messageFrom}-message${addClass}">
					<div class="fel-inner"${datetime}>${setDeleteBtn} ${message}</div>
				</div>
			</div>`;

		this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);
	}

	// querySelector
	domGet() {
		return DomAccess.querySelector(...arguments);
	}

	domGetAll() {
		return DomAccess.querySelectorAll(...arguments);
	}

	// storage
	storageGet() {
		return Storage.getItem(...arguments);
	}

	// setItem(keyName, keyValue)
	storageSet() {
		if (this.checkIfLocalStorageIsAllowed()) {
			Storage.setItem(...arguments);
		}
	}

	storageRemove() {
		Storage.removeItem(...arguments);
	}

	// shorties
	chatClearStorage() {
		this.storageRemove(this.options.storage.chat);
		this.storageRemove(`${this.options.storage.chat}_threadId`);
	}

	chatToStorage(threadId) {
		const getChat = this.getChatbotMessagesEl();
		if (getChat) {
			const getLoader = this.domGetAll(getChat, this.options.selector.loadingContainer, false);
			if (getLoader.length) {
				this.foreach(getLoader, (idx, elm) => {
					elm.remove();
				});
			}
			this.storageSet(this.options.storage.chat, getChat.innerHTML);
			this.storageSet(`${this.options.storage.chat}_threadId`, threadId);
		}
	}

	clearLocalStorage() {
		this.storageRemove(`${this.options.storage.chat}`);
		this.storageRemove(`${this.options.storage.chat}_threadId`);
		this.storageRemove(`${this.options.storage.chat}_chatOpen`);
		this.storageRemove(`${this.options.storage.chat}_chatZoom`);
	}

	// misc
	foreach(toIterate, callback) {
		return Iterator.iterate(toIterate, function(value, key) {
			callback(key, value);
		});
	}

	objectMerge(...objects) {
		const m = (t, s) => {
			Object.entries(s).forEach(([k, v]) => {
				t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
			});
			return t;
		}
		return objects.reduce(m, {});
	}

	// beforebegin afterbegin beforeend afterend
	putHtml(el, toPosition, html) {
		el.insertAdjacentHTML(toPosition, html);
	}

	// debounce(callback, delay, immediate = false)
	debounce() {
		if (typeof Debouncer === 'function') {
			return Debouncer.debounce(...arguments).apply();
		} else if(typeof arguments[0] === 'function') {
			return setTimeout(() => {arguments[0].apply()}, arguments[1] || 0);
		}
	}

	felParseInt(str) {
		return parseInt(str, 10);
	}

	// init plugin vars / options

	loadTemplates() {
		this.template = {greeting: '', loading: '', messageItem: ''};
		const templates = document.getElementById('fel-chatbot-template');
		if (templates) {
			this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
			if (this.template.greeting) {
				const getChatFromStorage = this.storageGet(this.options.storage.chat);
				this.putToMessages('greeting', getChatFromStorage
					? getChatFromStorage
					: this.template.greeting.outerHTML
				);
				if (getChatFromStorage) {
					this.assistantEl.classList.add('contains-thread');
					this.getChatbotThreadId().value = this.storageGet(this.options.storage.chat + '_threadId');
				}
			}
			this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
		}
	}

	getActiveProductsInChat() {
		if (this.options.page.refId) {
			return this.assistantEl.querySelectorAll(`.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`);
		}
		return [];
	}

	embedStyle(styleString) {
		let highlightId = 'fel-embed-highlight-active-products';
		let getStyleEl = document.querySelector(`#${highlightId}`);
		if (!getStyleEl) {
			getStyleEl = document.createElement('style');
			getStyleEl.id = highlightId;
			document.head.append(getStyleEl);
		}
		return getStyleEl.textContent = styleString;
	}

	highlightActiveProductsInChat() {
		if (this.options.page.refId) {
			const activeProductsSelector = `.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`;
			if (this.domGet(this.assistantEl, activeProductsSelector, false)) {
				this.embedStyle(`
					#fel-chatbot.active.fel-zoom-chat ${activeProductsSelector} .fel-item-wrapper {
						box-shadow: var(--fel-hl-active-products-box-shadow);
					}
					#fel-chatbot.active:not(.fel-zoom-chat) ${activeProductsSelector} img {
						box-shadow: var(--fel-hl-active-products-box-shadow);
					}
				`);
			}
		}
	}

	setPluginVars() {
		this._client = new HttpClient();
		this.cookieAccepted = false;
		this.eventsRegistered = false;
		this.isLoading = false;
		this.isLoadingResponse = false;
		this.threadId = null;
        this.options.eventHandlerMap = this.chatbotForm ? [
			[this.chatbotForm, 'submit.felBtn', false],
			[this.assistantEl, 'click.felBtn', false],
			[this.assistantEl, 'change.felBtn', false],
		] : [];
	}

	setPluginOptions() {
		if ('undefined' === typeof DomAccess) {
			return false;
		}
        // assistant element
        this.assistantEl = this.domGet(document.body, '[data-assistant-gpt-plugin]', false);
        // assistant form
		this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-chatbot-form', false);
        // controller url
        this.controllerUrl = this.chatbotForm.action;
		// messages container
		this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages, false);
        // options
		let pluginOptions = this.assistantEl.dataset.options;
		if (pluginOptions && typeof pluginOptions === 'string') {
            try {
                pluginOptions = JSON.parse(pluginOptions);
                if (pluginOptions) {
                    this.options = this.objectMerge(this.options, pluginOptions);
                    return true;
                }
            } catch (error) {}
		}
		return false;
	}

	// event handler
	removeEvents() {
		this.setEventHandler('remove');
	}

	resetEvents() {
		this.removeEvents();
		this.registerEvents();
	}

	setEventHandler(setEvent) {
		var eventFn = {
			add: 'addEventListener',
			remove: 'removeEventListener',
		};
		if ('remove' === setEvent && !this.eventsRegistered) {
			return false;
		}
		if ('add' === setEvent && this.eventsRegistered) {
			this.removeEvents();
		}
		this.foreach(this.options.eventHandlerMap, (i, arr) => {
			if (typeof arr[1] !== 'undefined') {
				var names = arr[1].split('.');
				arr[0][eventFn[setEvent]](names[0], this, arr[2] || true);
			}
		});
		this.eventsRegistered = 'add' === setEvent;
	}

}
