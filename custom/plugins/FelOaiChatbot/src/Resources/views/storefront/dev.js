/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
"use strict";

window.onload = () => {
    const {
        PluginBase<PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>tor,
        CookieStorageHelper,
        Storage,
        COOKIE_CONFIGURATION_UPDATE,
        HttpClient,
        FelOaiChatbotPlugin
    } = window;

    class FelOaiChatbotPluginDEV {
        /** Core Setup */
        constructor() {
            this.init();
        }

        options = {
            ...window.FelOaiChatbotPlugin.options,
            eventMapping: {  // Changed from 'events' to 'eventMapping' to be more explicit
                form: {
                    element: '#fel-chatbot-form',
                    events: [{ type: 'submit', handler: 'handleSubmit' }]
                },
                chat: {
                    element: '[data-assistant-gpt-plugin]',
                    events: [
                        { type: 'click', handler: 'handleClick' },
                        { type: 'change', handler: 'handleChange' }
                    ]
                }
            }
        };

        /** Initialization */
        init() {
            if (!this.setPluginOptions()) return;

            this.initializeEventSystem();
            this.initializeTemplates();
            this.initializeConsent();
            this.restoreLastState();
        }

        /** Plugin State Management */
        setPluginOptions() {
            if ('undefined' === typeof window.DomAccess) return false;

            // Setup core elements
            this.assistantEl = this.domGet(document.body, '[data-assistant-gpt-plugin]', false);
            if (!this.assistantEl) return false;

            this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-chatbot-form', false);
            this.controllerUrl = this.chatbotForm?.action;
            this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages, false);

            // Parse options from DOM
            const pluginOptions = this.assistantEl.dataset.options;
            if (!pluginOptions) return true;

            try {
                const parsed = JSON.parse(pluginOptions);
                if (parsed) {
                    this.options = this.objectMerge(this.options, parsed);
                }
                return true;
            } catch (error) {
                return false;
            }
        }

        /** Event System */
        initializeEventSystem() {
            this.eventListeners = new Map();
            this.registerEvents();
        }

        /** Template System */
        initializeTemplates() {
            this.templateManager = {
                templates: {
                    greeting: '',
                    loading: '',
                    messageItem: ''
                },
                load: () => this.loadTemplates(),
                put: (from, msg, id, datetime, addClass) =>
                    this.putToMessages(from, msg, id, datetime, addClass)
            };
            this.templateManager.load();
        }

        /** Consent System */
        initializeConsent() {
            this.setLocalStorageConsent();
        }

        /** State Restoration */
        restoreLastState() {
            this.setLastChatZoom();
            this.restoreLastChatState();
        }

        setLastChatZoom() {
            const lastZoom = this.storageGet(`${this.options.storage.chat}_chatZoom`);
            if (lastZoom === 'zoomed') {
                this.assistantEl.classList.add('fel-zoom-chat');
            }
        }

        restoreLastChatState() {
            if ('active' === this.storageGet(`${this.options.storage.chat}_chatOpen`)) {
                var getToggleButton = this.assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');
                if (getToggleButton) {
                    getToggleButton.dispatchEvent(new MouseEvent('click', {bubbles: true}));
                }
            }
        }

        /**
         * Click handler
         */

        // Toggles the Chat interface in & out
        toggleChatbot(event, el) {
            const getToggleClass = el.dataset.toggleClass;
            if (getToggleClass) {
                this.assistantEl.classList.toggle(getToggleClass);
                const useStorageName = `${this.options.storage.chat}_chatOpen`;
                if (this.assistantEl.classList.contains('active')) {
                    this.assistantEl.style.right = null;
                    this.assistantEl.style.bottom = null;
                    this.storageSet(useStorageName, 'active');
                } else {
                    this.assistantEl.style.right = this.assistantEl.dataset.setRight;
                    this.assistantEl.style.bottom = this.assistantEl.dataset.setBottom;
                    this.storageSet(useStorageName, 'not-active');
                }
            }
        }

        toggleChatZoom(event, el) {
            const zoomClass = 'fel-zoom-chat';
            this.assistantEl.classList.toggle(zoomClass);

            // Store zoom state
            const useStorageName = `${this.options.storage.chat}_chatZoom`;
            this.storageSet(useStorageName,
                this.assistantEl.classList.contains(zoomClass) ? 'zoomed' : 'not-zoomed'
            );
        }

        // Data protection conform
        checkIfLocalStorageIsAllowed() {
            return this.cookieAccepted
				? this.cookieAccepted
				: ('allowed' || true) === CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
        }

        /**
         * Storage helper
         */

        storageGet() {
            return Storage.getItem(...arguments);
        }

        // setItem(keyName, keyValue)
        storageSet() {
            if (this.checkIfLocalStorageIsAllowed()) {
                Storage.setItem(...arguments);
            }
        }

        domGet() {
            return DomAccess.querySelector(...arguments);
        }

        domGetAll() {
            return DomAccess.querySelectorAll(...arguments);
        }

        // misc
        foreach(toIterate, callback) {
            return Iterator.iterate(toIterate, function(value, key) {
                callback(key, value);
            });
        }

        objectMerge(...objects) {
            const m = (t, s) => {
                Object.entries(s).forEach(([k, v]) => {
                    t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
                });
                return t;
            }
            return objects.reduce(m, {});
        }

        // beforebegin afterbegin beforeend afterend
        putHtml(el, toPosition, html) {
            el.insertAdjacentHTML(toPosition, html);
        }

        /**
         * @param {*} messageFrom ['user', 'chatbot']
         * @param {*} message
         */
        putToMessages(messageFrom, message, id='', datetime='', addClass='') {
            let setId = '';
            if (id) {
                id = id.slice(4);
                setId = ` id="f-${id}"`;
            }
            if (datetime) {
                datetime = ` data-datetime="${datetime}"`;
            }
            const setDeleteBtn = 'chatbot' === messageFrom
                ? `<div class="fel-delete-message">
                    <span title="${this.options.translation.removeChatMessage}"
                        class="btn fel-btn" data-callback="removeChatMessage">X</span>
                    </div>` : '' ;
            const template = ['loading', 'greeting', 'append'].includes(messageFrom)
                ? message : `<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex"${setId}>
                    <div class="${this.options.selector.chatMessage} ${messageFrom}-message${addClass}">
                        <div class="fel-inner"${datetime}>${setDeleteBtn} ${message}</div>
                    </div>
                </div>`;

            this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);
        }

        getChatbotMessagesEl() {
            return this.domGet(this.assistantEl, this.options.selector.chatMessages, false);
        }

        setLocalStorageConsent() {
            // Initial state from cookie
            this.cookieAccepted = CookieStorageHelper.getItem(this.options.selector.cookieDefaultName) === 'allowed';

            // Update UI state
            this.updateConsentUI(this.cookieAccepted);

            // Subscribe to cookie configuration changes
            document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, this.handleConsentChange.bind(this));
        }

        handleConsentChange(event) {
            const cookieName = this.options.selector.cookieDefaultName;

			console.log(event)

            // if (!event?.detail || typeof event.detail[cookieName] === 'undefined') {
            //     return;
            // }
			// console.log(event)

            this.cookieAccepted = event.detail[cookieName] === 'allowed';
            this.updateConsentUI(this.cookieAccepted);

            // Clear storage if consent is revoked
            if (!this.cookieAccepted) {
                this.clearLocalStorage();
            }
        }

        updateConsentUI(hasConsent) {
            // Update checkbox in greeting template
            const consentCheckbox = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');
            if (consentCheckbox) {
                consentCheckbox.checked = hasConsent;
            }

            // Update UI classes
            this.assistantEl.classList.toggle('fel-localstorage-is-allowed', hasConsent);
            this.assistantEl.classList.toggle('fel-localstorage-is-disabled', !hasConsent);
        }

        clearLocalStorage() {
            const storageKeys = [
                this.options.storage.chat,
                `${this.options.storage.chat}_threadId`,
                `${this.options.storage.chat}_chatOpen`,
                `${this.options.storage.chat}_chatZoom`
            ];

            storageKeys.forEach(key => Storage.removeItem(key));
        }

        toggleLocalStorage(event, el) {
            const isChecked = el.checked;
            CookieStorageHelper.setItem(this.options.selector.cookieDefaultName, isChecked ? 'allowed' : 'denied');

            // Trigger consent change handler
            this.handleConsentChange({
                detail: {
                    [this.options.selector.cookieDefaultName]: isChecked ? 'allowed' : 'denied'
                }
            });
        }

        /**
         * Event Management
         */
        handleSubmit(event) {
            event.preventDefault();
            console.log('Form submitted:', event);
        }

        handleChange(event) {
            console.log('Change event:', event);
        }

        handleClick(event) {
            const el = event.target.closest('.fel-btn');
            if (!el) return;

            const {
                callback,
                preventDefault,
                stopPropagation,
                blur
            } = el.dataset;

            if (preventDefault) event.preventDefault();
            if (stopPropagation) event.stopPropagation();
            if (blur) el.blur();

            if (!callback) return;

            // Try instance method first
            if (typeof this[callback] === 'function') {
                return this[callback](event, el);
            }

            // Fallback to window method
            if (typeof window[callback] === 'function') {
                return window[callback](event, el);
            }
        }

        loadTemplates() {
            this.template = {greeting: '', loading: '', messageItem: ''};
            const templates = document.getElementById('fel-chatbot-template');
            if (templates) {
                this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
                if (this.template.greeting) {
                    const getChatFromStorage = this.storageGet(this.options.storage.chat);
                    this.putToMessages('greeting', getChatFromStorage
                        ? getChatFromStorage
                        : this.template.greeting.outerHTML
                    );
                    if (getChatFromStorage) {
                        this.assistantEl.classList.add('contains-thread');
                    }
                }
                this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
            }
        }

        registerEvents() {
            Object.entries(this.options.eventMapping).forEach(([key, config]) => {
                const element = this.domGet(document.body, config.element, false);
                if (!element) {
                    console.warn(`Element not found for ${key}:`, config.element);
                    return;
                }

                this.eventListeners.set(key, {
                    element,
                    handlers: new Map()
                });

                // Ensure events is treated as an array
                const events = Array.isArray(config.events)
                    ? config.events
                    : Object.values(config.events);

                events.forEach(eventConfig => {
                    if (!eventConfig || !eventConfig.type || !eventConfig.handler) {
                        console.warn(`Invalid event config for ${key}:`, eventConfig);
                        return;
                    }

                    if (typeof this[eventConfig.handler] !== 'function') {
                        console.warn(`Handler ${eventConfig.handler} not found for ${key}`);
                        return;
                    }

                    const handler = this[eventConfig.handler].bind(this);
                    element.addEventListener(eventConfig.type, handler);
                    this.eventListeners.get(key).handlers.set(eventConfig.type, handler);
                });
            });
        }

        destroy() {
            this.eventListeners.forEach(({ element, handlers }) => {
                handlers.forEach((handler, type) => {
                    element.removeEventListener(type, handler);
                });
            });
            this.eventListeners.clear();
        }
    }

    new FelOaiChatbotPluginDEV();
}

