#!/usr/bin/php
<?php

// Check if we're in restore mode
if (isset($argv[1]) && $argv[1] === '--restore') {
    // Find the most recent backup file
    $backupFiles = glob('custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json.backup-*.json');

    if (empty($backupFiles)) {
        die("Error: No backup files found.\n");
    }

    // Sort by modification time (newest first)
    usort($backupFiles, function($a, $b) {
        return filemtime($b) - filemtime($a);
    });

    $latestBackup = $backupFiles[0];
    $snippetFile = 'custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json';

    // Restore from backup
    if (copy($latestBackup, $snippetFile)) {
        echo "Successfully restored from backup: $latestBackup\n";
        exit(0);
    } else {
        die("Error: Failed to restore from backup: $latestBackup\n");
    }
}

// Display usage information
if (isset($argv[1]) && ($argv[1] === '--help' || $argv[1] === '-h')) {
    echo "Usage: php restore-translations.php [options]\n";
    echo "Options:\n";
    echo "  --restore    Restore from the most recent backup\n";
    echo "  --help, -h   Display this help message\n";
    exit(0);
}

// List of keys to restore with their values
$keysToRestore = [
    'instructionIssues.searchLogsAggressiveUsage' => 'Avoid aggressive use of search_logs. Only search when necessary, not for every user query.',
    'instructionIssues.logFunctionNote' => 'Use the log function only when you encounter information gaps or need to document important issues.',
    'general.openAIStatus' => 'OpenAI Status',
    'general.openAIUsage' => 'OpenAI Usage',
    'general.openAITokenizer' => 'OpenAI Tokenizer',
    'filter.ui.filteringFailedNoResultsFound' => 'No results found. Please adjust your search criteria.',
    'filter.ui.setVisibleToConfig' => 'Select all visible',
    'chat.missingRequired.salesChannelId' => 'Sales Channel ID is required',
    'chat.missingRequired.storefrontUrl' => 'Storefront URL is required',
    'chat.missingRequired.felOpenAiApiKey' => 'OpenAI API Key is required',
    'chat.missingRequired.felOpenAiAssistantId' => 'Assistant ID is required'
];

// Path to the English snippet file
$snippetFile = 'custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json';

// Create a backup file
$backupFile = $snippetFile . '.backup-' . date('Y-m-d-H-i-s') . '.json';
if (!copy($snippetFile, $backupFile)) {
    die("Error: Could not create backup file $backupFile\n");
}
echo "Created backup file: $backupFile\n";

// Read the file
$content = file_get_contents($snippetFile);
if ($content === false) {
    die("Error: Could not read file $snippetFile\n");
}

// Parse the JSON
$data = json_decode($content, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error: Failed to parse JSON: " . json_last_error_msg() . "\n");
}

// Function to set a nested key in an array
function setNestedKey(&$array, $key, $value) {
    $parts = explode('.', $key);
    $current = &$array;

    foreach ($parts as $part) {
        if (!isset($current[$part]) || !is_array($current[$part])) {
            $current[$part] = [];
        }
        $current = &$current[$part];
    }

    $current = $value;
    return true;
}

// Add each key
$addedCount = 0;
foreach ($keysToRestore as $key => $value) {
    if (setNestedKey($data['fel-assistant-manager'], $key, $value)) {
        $addedCount++;
        echo "Added key: fel-assistant-manager.$key\n";
    }
}

// Save the modified JSON back to the file
$newContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
if (file_put_contents($snippetFile, $newContent) === false) {
    die("Error: Could not write to file $snippetFile\n");
}

echo "Done! Added $addedCount keys to $snippetFile\n";
