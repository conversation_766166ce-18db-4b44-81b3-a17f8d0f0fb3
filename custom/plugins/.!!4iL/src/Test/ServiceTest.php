<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Test;

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIService;
use Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter;
use Fel\AIProductAdvisor\Service\OpenAI\OpenAIClientAdapter;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Test controller to verify the refactored services
 */
#[Route(defaults: ['_routeScope' => ['api']])]
class ServiceTest
{
    public function __construct(
        private LoggerInterface $logger,
        private OpenAIService $openAIService,
        private OpenAIConfigAdapter $configAdapter,
        private OpenAIClientAdapter $clientAdapter,
        private ?OpenAIChatService $baseChatService = null,
        private ?OpenAIRequiredAction $baseRequiredAction = null
    ) {
        // Log service availability
        $this->logger->debug('ServiceTest: Constructor called', [
            'openAIService' => $this->openAIService ? 'Available' : 'Not available',
            'configAdapter' => $this->configAdapter ? 'Available' : 'Not available',
            'clientAdapter' => $this->clientAdapter ? 'Available' : 'Not available',
            'baseChatService' => $this->baseChatService !== null ? 'Available' : 'Not available',
            'baseRequiredAction' => $this->baseRequiredAction !== null ? 'Available' : 'Not available'
        ]);
    }

    #[Route(path: '/api/fel-ai-product-advisor/test-refactoring', name: 'api.action.fel-ai-product-advisor.test-refactoring', methods: ['GET'])]
    public function testRefactoring(): JsonResponse
    {
        $result = [
            'openAIService' => get_class($this->openAIService),
            'configAdapter' => get_class($this->configAdapter),
            'clientAdapter' => get_class($this->clientAdapter),
            'baseChatService' => $this->baseChatService !== null ? get_class($this->baseChatService) : 'Not available',
            'baseRequiredAction' => $this->baseRequiredAction !== null ? get_class($this->baseRequiredAction) : 'Not available',
            'timestamp' => (new \DateTime())->format('Y-m-d H:i:s')
        ];

        $this->logger->debug('ServiceTest: Test endpoint called', $result);

        return new JsonResponse($result);
    }
}
