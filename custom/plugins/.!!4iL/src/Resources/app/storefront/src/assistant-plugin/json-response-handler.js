/**
 * Verarbeitet die Antwort der KI im neuen JSON-Format
 * @param {string} response - Die Antwort der KI
 * @param {string} id - Optional: ID für das Nachrichtenelement
 * @param {string} datetime - Optional: Zeitstempel für die Nachricht
 * @param {string} addClass - Optional: Zusätzliche CSS-Klassen
 * @returns {boolean} - true, wenn die Verarbeitung erfolgreich war, sonst false
 */
export function processAiResponse(response, id='', datetime='', addClass='') {
    try {
        // Versuche, die Antwort als JSON zu parsen
        const data = JSON.parse(response);
        
        // Prüfe, ob die Antwort die erwartete Struktur hat
        if (!data.type || !data.content || !data.content.html) {
            console.warn('Invalid JSON response format, missing required fields');
            return false;
        }
        
        console.log('Processing structured JSON response:', data);
        
        // Verarbeite den HTML-Inhalt
        let html = data.content.html;
        
        // Verarbeite je nach Typ der Antwort
        switch (data.type) {
            case 'product_list':
                // Verarbeite Produkte, wenn vorhanden
                if (data.content.products && data.content.products.length > 0) {
                    return this._handleProductListResponse(data.content.products, html, id, datetime, addClass);
                } else {
                    // Versuche, Produkt-IDs aus dem HTML zu extrahieren
                    const productIds = this.extractProductIdsFromHtml(html);
                    if (productIds.length > 0) {
                        return this._handleProductListResponse(productIds, html, id, datetime, addClass);
                    }
                }
                break;
                
            case 'buttons':
                // Füge Buttons hinzu, wenn vorhanden
                if (data.content.buttons && data.content.buttons.length > 0) {
                    html += this._createButtonsHtml(data.content.buttons);
                }
                break;
                
            case 'error':
                // Füge Fehlerklasse hinzu
                addClass += ' fel-system-exception';
                break;
        }
        
        // Normale Nachricht zurückgeben
        this.putToMessages('chatbot', html, id, datetime, addClass);
        return true;
    } catch (e) {
        // Fallback für nicht-JSON-Antworten oder falsch formatierte JSON
        console.warn('AI response is not valid JSON, using legacy processing', e);
        return false;
    }
}

/**
 * Erstellt HTML für Buttons basierend auf einem Array von Button-Texten
 * @param {Array} buttons - Array mit Button-Texten
 * @returns {string} - HTML für die Buttons
 * @private
 */
export function _createButtonsHtml(buttons) {
    let buttonsHtml = '<div class="fel-chatbot-buttons mt-2">';
    
    // Hole die Theme-Farbe
    const chatbotElement = document.getElementById('fel-chatbot-advisor');
    const themeColor = chatbotElement ? chatbotElement.dataset.themeColor || '' : '';
    
    buttons.forEach(buttonText => {
        // Füge inline Styles für die Theme-Farbe hinzu
        const buttonStyle = themeColor ?
            `style="border-color: ${themeColor}; color: ${themeColor};"` : '';
        
        buttonsHtml += `<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"
            ${buttonStyle} data-callback="addToChatSubmit" data-prompt="${buttonText}">${buttonText}</button>`;
    });
    
    buttonsHtml += '</div>';
    return buttonsHtml;
}

/**
 * Verarbeitet eine Produktlisten-Antwort
 * @param {Array} productIds - Array mit Produkt-IDs
 * @param {string} html - Original-HTML
 * @param {string} id - ID für das Nachrichtenelement
 * @param {string} datetime - Zeitstempel
 * @param {string} addClass - Zusätzliche CSS-Klassen
 * @returns {boolean} - true, wenn erfolgreich
 * @private
 */
export function _handleProductListResponse(productIds, html, id, datetime, addClass) {
    console.log('Handling product list response with IDs:', productIds);
    
    // Ersetze den Output mit unserem benutzerdefinierten Produkt-Template
    const productTemplate = this.generateProductTemplate(productIds, html);
    
    // Erstelle ein spezielles Template für Produktanzeigen
    if (id) {
        id = ` id="${id}"`;
    }
    if (datetime) {
        datetime = ` data-datetime="${datetime}"`;
    }
    var setDeleteBtn = `<div class="fel-delete-message">
        <span title="${this.options.translation.removeChatMessage}"
            class="btn fel-btn" data-callback="removeChatMessage">X</span>
    </div>`;
    
    var template = `
    <div class="${this.options.selector.chatMessageEl} chatbot-message-item fel-fade-in d-flex"${id}>
        <div class="${this.options.selector.chatMessage} chatbot-message${addClass}">
            <div class="fel-inner"${datetime}>
                ${setDeleteBtn}
                <div class="fel-product-container">${productTemplate}</div>
            </div>
        </div>
    </div>`;
    
    // Füge das Template direkt ein
    this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);
    
    // Initialisiere das Laden der Produktdaten
    this.initProductLoading(productIds);
    
    // Aktualisiere die Theme-Farbe für alle Chat-Buttons
    setTimeout(() => {
        this.updateChatButtonsTheme();
        // Scroll to the bottom of the chat after adding a message
        this.scrollThreadIntoView();
    }, 50);
    
    return true;
}
