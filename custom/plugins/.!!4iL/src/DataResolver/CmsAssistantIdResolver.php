<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\DataResolver;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\Element\AbstractCmsElementResolver;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Psr\Log\LoggerInterface;

class CmsAssistantIdResolver extends AbstractCmsElementResolver
{
    public function __construct(
        private LoggerInterface $logger,
        private SystemConfigService $configService
    ) {}

    public function getType(): string
    {
        return 'cmsAssistantId';
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        $config = $slot->getFieldConfig();
        $cmsAssistantIdConfig = $config->get('cmsAssistantId');

        if (!$cmsAssistantIdConfig) {
            $this->logger->error('CmsAssistantIdResolver: cmsAssistantIdConfig not found');
            return null;
        }

        $cmsAssistantId = $cmsAssistantIdConfig->getValue();
        $this->logger->debug('CmsAssistantIdResolver: Collected cmsAssistantId', ['cmsAssistantId' => $cmsAssistantId]);

        $criteriaCollection = new CriteriaCollection();

        return $criteriaCollection;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $config = $slot->getFieldConfig();
        $this->logger->debug('CmsAssistantIdResolver: Retrieved slot configuration', ['config' => $config]);

        if ($config->has('cmsAssistantId')) {
            $cmsAssistantId = $config->get('cmsAssistantId')->getValue();
            $this->logger->debug('CmsAssistantIdResolver: Retrieved cmsAssistantId value', ['cmsAssistantId' => $cmsAssistantId]);
        } else {
            $this->logger->debug('CmsAssistantIdResolver: cmsAssistantId not found in config');
            $cmsAssistantId = null;
        }

        $this->logger->debug('CmsAssistantIdResolver: Enriching slot with cmsAssistantId', ['cmsAssistantId' => $cmsAssistantId]);
    }

    public function resolve(): ?string
    {
        $cmsAssistantId = $this->configService->get('FelAIProductAdvisor.config.cmsAssistantId');
        if (!$cmsAssistantId) {
            $this->logger->error('CmsAssistantIdResolver: cmsAssistantId not found in configuration');
            return null;
        }

        $this->logger->debug('CmsAssistantIdResolver: Resolved cmsAssistantId from configuration', ['cmsAssistantId' => $cmsAssistantId]);
        return $cmsAssistantId;
    }
}

