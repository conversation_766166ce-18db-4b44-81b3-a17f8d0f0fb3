<!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
{% block sw_cms_el_config_cms_advisor %}
<sw-tabs
    position-identifier="sw-cms-element-config-cms-advisor"
    class="sw-cms-el-config-cms-advisor__tabs"
    default-item="content"
>

    <template #default="{ active }">
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_el_config_cms_advisor_tab_content %}
        <sw-tabs-item
            :title="$tc('sw-cms.elements.general.config.tab.content')"
            name="content"
            :active-tab="active"
        >
            {{ $tc('sw-cms.elements.general.config.tab.content') }}
        </sw-tabs-item>
        {% endblock %}
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_el_cms_advisor_config_tab_options %}
        <sw-tabs-item
            :title="$tc('sw-cms.elements.general.config.tab.settings')"
            name="settings"
            :active-tab="active"
        >
            {{ $tc('sw-cms.elements.general.config.tab.settings') }}
        </sw-tabs-item>
        {% endblock %}
    </template>

    <template
        #content="{ active }"
    >
        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_el_cms_advisor_config_content %}
        <sw-container
            v-if="active === 'content'"
            class="sw-cms-el-config-cms-advisor__tab-content"
        >
            <sw-cms-mapping-field
                v-model:config="element.config.content"
                :label="$tc('sw-cms.elements.text.config.label.content')"
                value-types="string"
            >

                <sw-confirm-field placeholder="Enter value...">
                </sw-confirm-field>

                <sw-file-input v-model="selectedFile" 
                    label="My file input" 
                    :allowedmimetypes="['text/csv','text/xml']" 
                    :maxfilesize="8*1024*1024"
                >
                </sw-file-input>


                <sw-text-editor
                    :value="element.config.content.value"
                    :allow-inline-data-mapping="true"
                    sanitize-input
                    sanitize-field-name="app_cms_block.template"
                    enable-transparent-background
                    @update:value="onInput"
                    @blur="onBlur"
                />

                <template #preview="{ demoValue }">
                    <div class="sw-cms-el-config-cms-advisor__mapping-preview">
                        <div v-html="$sanitize(demoValue)"></div>
                    </div>
                </template>
            </sw-cms-mapping-field>
        </sw-container>
        {% endblock %}

        <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
        {% block sw_cms_el_cms_advisor_config_settings %}
        <sw-container
            v-if="active === 'settings'"
            class="sw-cms-el-config-cms-advisor__tab-settings"
        >
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_cms_el_cms_advisor_config_settings_vertical_align %}
            <sw-select-field
                v-model:value="element.config.verticalAlign.value"
                :label="$tc('sw-cms.elements.general.config.label.verticalAlign')"
                :placeholder="$tc('sw-cms.elements.general.config.label.verticalAlign')"
            >
                <option value="flex-start">
                    {{ $tc('sw-cms.elements.general.config.label.verticalAlignTop') }}
                </option>
                <option value="center">
                    {{ $tc('sw-cms.elements.general.config.label.verticalAlignCenter') }}
                </option>
                <option value="flex-end">
                    {{ $tc('sw-cms.elements.general.config.label.verticalAlignBottom') }}
                </option>
            </sw-select-field>
            {% endblock %}
        </sw-container>
        {% endblock %}
    </template>
</sw-tabs>
{% endblock %}
