<?php declare(strict_types=1);

namespace FelOaiChatbot\Service;

use Shopware\Storefront\Framework\Cookie\CookieProviderInterface;
use function array_merge;

class CustomCookieProvider implements CookieProviderInterface
{
    public function __construct(
        private CookieProviderInterface $originalService
    ) {
    }

    private const FEL_COOKIE_GROUP = [
        'snippet_name'        => 'fel-oai-chat.chatbot',
        'snippet_description' => 'fel-oai-chat.cookie.privacy',
        'entries'             => [[
            'snippet_name'        => 'fel-oai-chat.cookie.name',
            'snippet_description' => 'fel-oai-chat.cookie.description',
            'cookie'              => 'fel-oai-chatbot-localstorage-accepted',
            'value'               => 'allowed',
            'expiration'          => '30',
        ]],
    ];

    public function getCookieGroups(): array
    {
        return array_merge(
            $this->originalService->getCookieGroups(), [ self::FEL_COOKIE_GROUP ]
        );
    }
}
