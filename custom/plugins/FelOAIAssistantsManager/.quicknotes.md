# Generate Log Messages for Testing

**Description**

This test is basically a check to see if your server is processing requests that can take a bit longer and contain many subqueries properly. The assistant will then provide a short final report. But the final report is more of a mandatory nature. If the script has run through without any problems, the test has already been passed.

**Estimated number of tokens**

It's hard to say, the more data, the more tokens. If you have hundreds of product properties in your store, this test can take 50-100,000 tokens for completing the task, maybe more.

From our tests (23/11/2024):

-   around 9000 character text in "get_faqs" and 1000 character in "get_meta_information"
-   10.261 Characters | 2371 Tokens

-   31 product properties
-   19 Categories
-   4.199 Characters | 1,339 Tokens

The assistant has generated 30 logs with a total cost of: 29.331 tokens.

Note: this amount of token usage is only possible because of the "5 Elements Backend Assistants" setup. Usually, assistants don't do such tricks on command.

```instruction
We need a variety of log messages to test the message listing page thoroughly. Aim to generate realistic logs that simulate real-world scenarios. Follow the rules and mix the content types.

Rules:
- Call the three functions
    - "get_faqs()
    - "get_meta_data()
    - "get_product_properties()
        - Use the content of their responses to generate realistic questions, that might be asked by customers of this shop via contact form or via phone.
        - Try to derive the questions from the gathered information, what typical customer questions could be in this particular shop.
- Call "tag_for_logs()" with the "aliases" argument set to `true` to retrieve available aliases.
    - Use only tag aliases.
- Use search_logs() to check previous logs and avoid duplicates, etc.
    - Consider the searches performed as additional subtests that simply runs alongside the main task.
- Set "priorities" and "tags" matching the generated questions.
- Aim to create 20 logs in one go.

Challenge: Stress Test: 30 logs!
```

# Tools performance test

You are tasked with conducting a performance test to evaluate whether assistants can successfully call multiple functions within a single request without encountering technical or server issues.

Task:

-   Your primary objective is to make multiple function calls in a single request, ensuring smooth execution.
-   It doesn’t matter which functions you call or how many times you call them, as long as the procedure of making multiple calls is tested.
-   Optionally, after each function call, log a status report as a way of tracking progress.

Guidelines:

-   From a technical perspective, there are no restrictions on how many functions an assistant can call in a single request.
-   You have access to a variety of functions, which have been enabled to give you flexibility. Feel free to choose any functions for this test.
-   The ideal scenario would be for you to call all available functions in a chain. However, even testing a few functions in one request would still be valuable.
-   Check the performance, if possible

Logging:

-   If you mix logging with function calls, log a message after each function call to document the progress (e.g., a status report).
-   **CRUCIAL** Use "search_logs" before new log to avoid duplicates, change the content of the message, if necessary.

Your goal is to ensure that the system can handle multiple function calls without encountering any issues and what our boundaries are. Good luck!

## Tag geenerator Instructions

You are tasked with suggesting tags based on the properties and categories provided by the Shopware store. These tags will be used by assistants for logging data and searching through logged information.

Tag Requirements:

Rules:

-   To avoid duplicate tag recommendations, call "tags_for_logs" with the "aliases" property set to true before making any recommendations. Adjust your response based on the returned tags and their aliases.
-   When tags are provided, adhere to the given format for existing tags.
    -   Aliases group similar terms under a primary tag to simplify maintenance. Aliases are intended to represent similar concepts, not exact duplicates.
    -   Each alias and tag name should be unique across the entire tags object; no alias or tag name should appear more than once in any part of the tags object.
    -   If you encounter any issues or inconsistencies with the existing tags, please report them.

Relevance:

-   Suggest 10-15 tags that are directly related to the shop’s properties and categories.
    -   Call "get_product_properties" to check, what's the shop is all about and adjust your response.
-   The tags should be human-readable and reflect the general context of the store.
-   Additionally, suggest a few tags that may not directly match the shop’s properties but are still relevant to the overall e-commerce context and would be useful for logging purposes, eg. "meta_data" or "technical".

Assistant Notifications:

-   This section is crucial.
-   Here, you can include any suggestions you may have or want to share. Suggestions can include:
    -   Improving categories, manufacturers, properties, or group names that are prefixed or suffixed with irrelevant data.
    -   Suggestions for improving a poorly designed overall category structure.
    -   Suggestions for improving poorly designed groups of properties and options.
    -   General recommendations for improving the design of properties and options.
    -   Help identify and suggest fixes for potential bad design choices that are clearly problematic.

Tag Format:

-   Tags should be in plain text, without special characters.
-   All tags should be in lowercase.
-   Use underscores to separate words in multi-word tags, with a maximum of three words per tag.
-   Assistant Recommendations:
    -   Provide specific recommendations for tags that would be optimal from the assistant’s perspective for performance, efficient logging and searchability.

Response Format:

-   Return the results as a valid JSON object without surrounding characters, using the format below:
    -   "without surrounding characters" is crucial for this response.

{
"task": "tags",
"assistantNotifications": [
"...",
],
"tags": {
"tag_one": ["tag_aliases"],
"tag_two": ["tag_aliases"],
"tag_three": ["tag_aliases"]
}
}

Log Messages
Description:
Generate realistic log messages to test system functionality. Simulate customer inquiries and ensure accurate tagging and prioritization.

Tools Performance
Description:
Evaluate system performance by testing multiple backend tools in one go. Check for seamless function execution and identify performance limits.

Product Properties
They should check the response, an assistant will receive, when it uses "get_product_properties". Some shops have extended properties and categories, what could result in lot of token costs. They can use our "Content Checker" tool to see, what assistants will receive.

Log Messages
This is just a general log test, but in the background, we see it more like a performance test since we request dozens of assistant logs in one go.




There is 1 file attached, that contains logs with questions from user interactions. Grab the first 2 logs, the message field is the user question, that needs to get answered. The data to answer the user questions can be found using the search_logs() tool, using the query "FEL_FULL_FAQ" and no tag.

1. **Input File:**
- The JSON file contains log entries from user interactions (each entry includes a "message" field with the user’s question).

2. **Task:**
- For each log entry from the JSON file, treat the "message" field as the user question that needs an answer.
  - The question can be short, use a fuzzy matching algorithm.
- Use the `search_logs()` tool with the query "FEL_FULL_FAQ" (with no tag) to retrieve the FAQ data.
  - The FAQ data contains all informations needed to answer the questions.
  - Call: search_logs(query: "FEL_FULL_FAQ")
- **Important:** Do not simply use the questions provided in the FAQ; instead, search for the relevant answer by interpreting the log question as separate prompt. If a log question is short or vague, reformulate it into a more natural question to improve match quality. The more vague the question, the more information should match.

3. **Language:**
- First, determine the language used in the FAQ file.
- Tailor the search query to match that language when processing log questions.

4. **Matching and Flexibility:**
- Always use a fuzzy matching algorithm.
- The retrieval system should be flexible; even a vaguely matched answer (if relevant) is acceptable.
- The FAQ content may be irregularly formatted – focus on extracting any relevant answer rather than an exact match.
- A question can have multiple answers, put all the information considered related to a question together, if the question is short.

5. **Output:**
- Use the log() tool to write the answer.
  - Set the message as "message" argument.
  - Set the answer as "answer" argument.
  - Set "babu_answered" as tag.
  - To avoid duplicate errors, write "UPDATE: " before the message.
  - example: log(message: "MESSAGE", answer: "ANSWER", tag: "babu_answered")
- If log successful, response a short report.




- Return the final result as a JSON object in the exact same format as the input logs, with the answer field filled in from the FAQ data.
  - No additional before or after the JSON object.
  - No marker like backticks.






Sometimes, it feels like you can read my mind, so i tend do assume it's right :D

The function is to dynamically adjust the default instructions of the assistant, where the responseType is getting set. Because json_object requires  an JSON object example, that the "auto" type don't need, the schema type set's even a structured data object.


```php
const { Mixin } = Shopware;

const FEL_OPENAI_RESPONSE_FORMAT = {
    auto: "auto",
    json: "json",
    object: {
        type: 'json_object'
    },
    schema: {
        type: "json_schema",
        json_schema: {
            name: "OutputRules",
            schema: {
                name: "structured_response",
                description: "Format used for consistent assistant replies including status and optional metadata.",
                type: "object",
                required: ["output", "request_type", "request_status"],
                additionalProperties: false,
                properties: {
                    output: {
                        type: "string",
                        description: "Main assistant response content. Can include HTML or multiline text."
                    },
                    request_type: {
                        type: "string",
                        enum: [
                            "information_gathering",
                            "required_action",
                            "product_search",
                            "product_recommendation"
                        ]
                    },
                    request_status: {
                        type: "string",
                        enum: [
                            "ok",
                            "in_progress",
                            "failed"
                        ]
                    },
                    information_gap: {
                        type: "array",
                        description: "If the Assistant encounter any information gap or similar issues, put it here.",
                        items: { type: "string" }
                    },
                    error_occurred: {
                        type: "array",
                        items: { type: "string" }
                    }
                }
            }
        }
    },
};

Mixin.register('fel-assistant-manager-input-helper-mixin', {

    data() {
        return {
            isCreated: false,
            canCreate: false,
            canUpdate: false,
            isUpdated: false,
            disableInputElements: false,
            trackContentChange: null,
            trackContentChangeLast: null,
            instructionIssues: [],
            responseFormatDefault: 'auto',
            toSubmit: {
                'fel--sw--assistant_name': null,
                'fel--sw--assistant_description': null,
                'fel--sw--assistant_model': null,
                'fel--sw--assistant_instruction': null,
                'fel--sw--assistant_temperature': null,
                'fel--sw--assistant_tools': [],
                'fel--sw--assistant_files': [],
                'fel--sw--assistant_attached_files': [],
                'fel--sw--assistant_vector_store': [],
                'fel--sw--assistant_response_format': null,
            },
            toSubmitRequired: [
                'fel--sw--assistant_name',
                'fel--sw--assistant_model',
                'fel--sw--assistant_instruction',
                'fel--sw--assistant_temperature',
                'fel--sw--assistant_response_format',
            ],
        };
    },

    computed: {
        felApiResponseFormats() { return FEL_OPENAI_RESPONSE_FORMAT; },
    },

    methods: {

        async createOrUpdateAssistant(assistantId = null) {
            this.checkRequirements();

            if (this.canCreate) {
                this.disableInputElements = true;
                let useResponseFormatKey = this.toSubmit['fel--sw--assistant_response_format'];

                const submit = {
                    name:         this.toSubmit['fel--sw--assistant_name'],
                    description:  this.toSubmit['fel--sw--assistant_description'] || ' ',
                    model:        this.toSubmit['fel--sw--assistant_model'],
                    instructions: this.toSubmit['fel--sw--assistant_instruction'],
                    temperature:  this.toSubmit['fel--sw--assistant_temperature'],
                    response_format: useResponseFormatKey,
                    tools: [],
                    tool_resources: {
                        file_search: {
                            vector_store_ids: this.toSubmit['fel--sw--assistant_vector_store'] || []
                        },
                    },
                };
                // 'truncation_strategy' => ['type' => 'off'],

                let isBackendAssistant;
                if (this?.backendAssistantId?.helper && submit.name === this.backendServiceAssistant.helper) {
                    assistantId = this.backendAssistantId.helper;
                    isBackendAssistant = true;
                } else {
                    isBackendAssistant = false;
                }

                if (this.toSubmit['fel--sw--assistant_vector_store'].length) {
                    submit.tools.push({ type: 'file_search' });
                }

                // For code interpreter: pass file IDs to attach.
                if (this.toSubmit['fel--sw--assistant_attached_files'].length) {
                    submit.tools.push({ type: 'code_interpreter' });
                    submit.tool_resources.code_interpreter = {
                        file_ids: this.toSubmit['fel--sw--assistant_attached_files']
                    };
                } else {
                    if (this.assistant?.tool_resources?.code_interpreter?.file_ids?.length) {
                        // OpenAI Bug, works but causes a network error, when files get removed from assistant
                        submit.tool_resources.code_interpreter = { file_ids: [] };
                    }
                }

                const getFnList = this.availableFunctionsObject.list || {};
                submit.tools = submit.tools.concat(
                    this.toSubmit['fel--sw--assistant_tools']
                        .filter(tool => getFnList[tool])
                        .map(tool => ({
                            type: 'function',
                            function: getFnList[tool]
                        }))
                );

                if (this.felApiResponseFormats?.[useResponseFormatKey]) {
                    submit.response_format = this.felApiResponseFormats[useResponseFormatKey];
                } else {
                    submit.response_format = this.responseFormatDefault;
                }

                try {
                    this.setManagerIsLoading('assistant', true);
                    this.canCreate = false;
                    this.canUpdate = false;

                    const url = `${this.getApiEndpoint('assistants')}${assistantId ? `/${assistantId}` : ''}`;
                    const response = await this.httpClientPost(url, submit, { getName: 'assistant' });

                    if (response?.data?.id) {
                        this.isUpdated = true;
                        this.assistant = response.data;
                        this.isCreated = this.assistant.id;
                        this.catchSuccess(assistantId ? 'updateSuccessful' : 'created');
                        this.setAssistantFormInitHash(true);
                    } else if (response?.data) {
                        this.catchErrors(JSON.stringify(response.data), null, false);
                    }
                } catch (error) {
                    this.catchErrors(error, 'failedToCreateOrUpdateAssistant');
                } finally {
                    this.disableInputElements = false;
                    this.setManagerIsLoading('assistant', false);

                    if (isBackendAssistant) {
                        this.checkIfBackendAssistant(true);
                    }
                }
            }
        },

        async setResponseType() {
            await this.toSubmit;

            const useResponseFormat = this.toSubmit['fel--sw--assistant_response_format'];
            const instructions = this.toSubmit['fel--sw--assistant_instruction'];
            const formats = this.getResponseFormats();
            const setFormat = formats?.[useResponseFormat];
            let replaceFormat, replaceAlterFormat;

            if (useResponseFormat === 'schema') {
                replaceFormat = formats.object;
                replaceAlterFormat = formats.auto;
            } else if (useResponseFormat === 'object') {
                replaceFormat = formats.schema;
                replaceAlterFormat = formats.auto;
            } else if (useResponseFormat === 'auto') {
                replaceFormat = formats.object;
                replaceAlterFormat = formats.schema;
            }

            if (useResponseFormat && instructions) {
                if (!instructions.toLowerCase().includes(setFormat.toLowerCase())) {
                    if (instructions.toLowerCase().includes(replaceFormat.toLowerCase())) {
                        const escaped = replaceFormat.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        this.toSubmit['fel--sw--assistant_instruction'] = instructions.replace(new RegExp(escaped, 'gi'), setFormat);
                    }
                    else if (instructions.toLowerCase().includes(replaceAlterFormat.toLowerCase())) {
                        const escaped = replaceAlterFormat.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        this.toSubmit['fel--sw--assistant_instruction'] = instructions.replace(new RegExp(escaped, 'gi'), setFormat);
                    }
                    else {
                        this.catchErrors(this.$t('fel-assistant-manager.create.responseFormatMissingInstruction'), null, false);
                    }
                }
                this.checkIfCanUpdate();
            }
        },

        checkForInstructionIssues() {
            const instruction = this.toSubmit['fel--sw--assistant_instruction'] || '';
            const tools = this.toSubmit['fel--sw--assistant_tools'] || [];
            let instructionIssues = [];

            const checkGroups = [
                ['get_meta_information'],
                ['search_logs'],
                ['log'],
                ['tags_for_logs'],
                ['fetch_url'],
                ['product_search'],
                ['get_product_properties'],
                ['get_product_details'],
                ['get_order_status'],
                ['get_date_time'],
            ];

            const conflictingPairs = [
                ['get_meta_information', 'search_logs'],
                ['get_product_properties', 'get_categories'],
                ['get_product_properties', 'get_manufacturer'],
            ];

            const toolNoteConditions = [
                {
                    tools: ['search_logs'],
                    key: 'searchLogsAggressiveUsage',
                },{
                    tools: ['log'],
                    key: 'logFunctionNote',
                },{
                    tools: ['get_product_properties', 'get_categories'],
                    key: 'productPropertyNote',
                },{
                    tools: ['get_product_properties', 'get_manufacturer'],
                    key: 'productPropertyNote',
                }
            ];

            try {
                // Issues in instructions
                for (const group of checkGroups) {
                    const hasTool = group.some(tool => tools.includes(tool));

                    if (!hasTool) {
                        for (const tool of group) {
                            const regex = new RegExp(`\\b${tool.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}\\(\\)`, 'gi');
                            const matches = instruction.match(regex);
                            const count = matches ? matches.length : 0;

                            if (count > 0) {
                                instructionIssues.push({
                                    message: this.$t('fel-assistant-manager.instructionIssues.issues', { tool, count })
                                });
                            }
                        }
                    }
                }

                // Conflicting tools
                for (const [toolA, toolB] of conflictingPairs) {
                    if (tools.includes(toolA) && tools.includes(toolB)) {
                        instructionIssues.push({
                            message: this.$t('fel-assistant-manager.instructionIssues.conflictingTools', { toolA, toolB })
                        });
                    }
                }

                // Additional notes
                const addedNoteKeys = new Set();
                for (const note of toolNoteConditions) {
                    const allPresent = note.tools.every(tool => tools.includes(tool));

                    if (allPresent && !addedNoteKeys.has(note.key)) {
                        instructionIssues.push({
                            message: this.$t(`fel-assistant-manager.instructionIssues.${note.key}`)
                        });
                        addedNoteKeys.add(note.key);
                    }
                }
            } catch (error) {
                this.catchErrors(error);
            }

            this.instructionIssues = instructionIssues;
        },

        onAssistantInput(event, formElName) {
            const el = event?.target;
            let elName = el?.name,
                elValue = el?.value;

            if (typeof el === 'undefined' && typeof formElName === 'string' && event != null) {
                elName = formElName;
                elValue = event;
            }

            if ((elName && typeof elValue !== 'undefined') || el?.options) {
                this.canUpdate = true;

                if (elName === 'fel--sw--assistant_tools') {
                    const getCheckedFunctions = [...this.$el.querySelectorAll(`[name="${elName}"]:checked`)];
                    elValue = getCheckedFunctions.map(fn => fn.value);
                }
                else if (el?.options) {
                    if (elValue === '') {
                        elValue = [];
                    } else {
                        const getSelectedOptions = el?.options ? [...el.options].filter(option => option.selected) : [];

                        if (getSelectedOptions.length <= 20) {
                            elValue = getSelectedOptions.map(option => option.value);
                        }
                    }
                }
                else if (elName === 'fel--sw--assistant_temperature') {
                    elValue = parseFloat(elValue);
                }
                else if (elName === 'fel--sw--assistant_description') {
                    this.assistant.description = elValue;
                }

                this.toSubmit[elName] = elValue;
                this.checkRequirements();
                this.adjustIntructionsHeight();
                this.checkForInstructionIssues();

                if (this.toSubmit['fel--sw--assistant_attached_files'].length > this.maxFilesPerAssistant) {
                    this.canUpdate = false;
                }

                if (this.trackContentChange === this.felMd5(JSON.stringify(this.toSubmit))) {
                    this.canUpdate = false;
                }
            }
        },

        adjustIntructionsHeight() {
            this.$nextTick(() => this.setTextareaRowsSize());
        },

        checkIfRetrievalIsInTools(searchIn) {
            return searchIn.some(tool => tool.type === 'retrieval');
        },

        resetSelect(querySel) {
            const getActiveOptions = this.$el.querySelectorAll(`${querySel} option:checked`);
            getActiveOptions.forEach(option => {
                option.selected = false;
            });

            const cleanQuerySel = querySel.replace(/[#.]/g, '');

            this.toSubmit[cleanQuerySel] = [];
            this.canUpdate = true;
        },

        checkIfBackendAssistant(isBackendAssistant=false) {
            let setTools = [];

            if (this.tags.total) {
                setTools = this.availableFunctionsObject.logDefaults;
            }

            if (this.defaultInstructionModel === 'backendAssistant' || isBackendAssistant) {
                if (!this.detailPage) {
                    this.assistant.model = this.defaultModel;
                }
                setTools = this.detailPage === true
                    ? this.toSubmit['fel--sw--assistant_tools']
                    : [ ...setTools, ...this.availableFunctionsObject.backendDefaults ];
                this.toSubmit['fel--sw--assistant_name'] = this.backendServiceAssistant.helper;
                this.toSubmit['fel--sw--assistant_description'] = this.$t('fel-assistant-manager.backendAssistant.helper.description');
                this.toSubmit['fel--sw--assistant_model'] = this.assistant.model;
                this.toSubmit['fel--sw--assistant_tools'] = setTools;
                this.toSubmit['fel--sw--assistant_vector_store'] = [];
                this.disableInputElements = true;
                this.setAssistantFormInitHash(true);
            } else {
                setTools = this.detailPage === true
                    ? this.toSubmit['fel--sw--assistant_tools']
                    : [ ...setTools, ...this.availableFunctionsObject.defaults ];
                this.toSubmit['fel--sw--assistant_name'] = '';
                this.toSubmit['fel--sw--assistant_description'] = '';
                this.toSubmit['fel--sw--assistant_tools'] = setTools;
                this.disableInputElements = false;
            }

            this.adjustIntructionsHeight();
            this.checkRequirements();
            this.checkForInstructionIssues();
        },

        createSubmitHash() {
            return this.felMd5(JSON.stringify(this.toSubmit));
        },

        getSubmitHash() {
            return this.trackContentChange;
        },

        checkIfCanUpdate() {
            this.canUpdate = this.getSubmitHash() !== this.createSubmitHash();
        },

        setAssistantFormInitHash(override = false) {
            if (!this.trackContentChange || override) {
                this.trackContentChange = this.createSubmitHash();
            }
        },

        checkRequirements() {
            this.canCreate = this.toSubmitRequired.every(key => {
                const value = this.toSubmit[key];

                if (typeof value === 'string') {
                    return value.trim() !== '';
                } else if (typeof value === 'number') {
                    return !isNaN(value);
                }

                return !!value;
            });

            this.canUpdate = this.canCreate;
        },

    },
});

```













hallo
Kann ich mehrere Gutscheine in einer Bestellung einlösen?
Welche Zahlungsmethoden stehen zur Verfügung?
Wie kann ich defekte Produkte reklamieren?
Was passiert, wenn ein Produkt ausverkauft ist?
Wie sieht's mit meiner bestellung aus?
Die Nummer ist 10000 und PLZ 44649?
Wie hoch sind die Versandkosten?
Wie lange bleiben Pakete in der Abhol-Box?
Kann ich mehrere Versandoptionen in einer Bestellung kombinieren?
Kann ich Produkte vor Ort testen?
Was kostet ein Lebensmitteldruck?
Wie sehe ich, ob meine Bestellung eingegangen ist?
Bieten Sie auch Kurse für Fortgeschrittene an?
Muss ich doppelt Versandkosten zahlen, wenn ich zweimal bestelle?
Kann ich per TWINT bezahlen?
Gibt es eine Obergrenze bei Poinz-Punkten pro Einkauf?
Wie lange ist ein Gutschein gültig?
Kann ich meinen Gutschein in Bar auszahlen lassen?
Was bedeutet Vorkasse?
Was mache ich, wenn ich keine Bestellbestätigung erhalten habe?
Gibt es ein Ladengeschäft? Wo befindet es sich?
Wie sicher ist die Zahlung per Kreditkarte?
Wie kann ich meine Bestellung nachverfolgen?
Wie wird die Qualität der Produkte sichergestellt?
Wie sind die Öffnungszeiten des Ladens?
Wie lange dauert die Bearbeitung einer Bestellung?
Werden sensible Daten verschlüsselt übertragen?
Wie kann ich Punkte mit der Poinz-App sammeln?
Wie finde ich Artikel im Online-Shop?
Kann ich im Laden mit Karte bezahlen?
Wie kann ich meine Kontodaten ändern?
Wie schnell erfolgt die Lieferung?
Gibt es Rabatte für Erstbesteller?
Kann ich Lebensmitteldrucke per Express bestellen?
Wie löse ich einen Gutschein ein?
Wann bekomme ich den Code für die Abhol-Box?
Kann ich die Zahlungsmethode nachträglich ändern?
Kann ich eine Abholzeit für meine Bestellung festlegen?
Können Lebensmittel zurückgegeben werden?
Kann ich auf Rechnung bestellen?
Kann ich per E-Mail oder Telefon bestellen?
Wurde Ihre Frage nicht beantwortet?
Wann kann ich Sie telefonisch erreichen?
Wofür gibt es Gutschriften?
Muss ich mich registrieren, um eine Bestellung aufzugeben?
Wie kann ich den Kundenservice erreichen?
Kann ich den Abholcode erneut anfordern?
Welche Kurse bieten Sie an?
Wie kann ich die Lieferzeit verkürzen?
Kann ich eine Bestellung nach der Zahlung ändern?
Was ist die Abhol-Box?
Kann ich einen Gutschein im Laden kaufen?
Bieten Sie Geschenkverpackungen an?
Kann ich im Laden auf Rechnung kaufen?
Kann ich mehrere Bestellungen zu einer Lieferung zusammenfassen?
Welche Zahlungsmethoden werden akzeptiert?
Kann ich auch telefonisch beraten werden?
Wann erhalte ich meine Gutschrift?
Was passiert, wenn ich die Zahlungsfrist der Vorkasse verpasse?
Sind alle Produkte aus dem Onlineshop auch im Laden verfügbar?
Wann haben Sie Betriebsferien?
Kann ich auch telefonisch bezahlen?
Wo finde ich die AGBs?
Was mache ich, wenn der Gutscheinwert höher ist als mein Einkauf?
Wie lange dauert die Abholung von Lebensmitteldrucken?
Kann ich Produkte direkt nach dem Druck abholen?
Wie funktioniert der Versand per Express?
Wie wird der Restbetrag eines Gutscheins erstattet?
Gibt es Rabatte für Firmenkunden?
Gibt es Parkplätze vor dem Geschäft?
Gibt es besondere Angebote für Stammkunden?
Was passiert, wenn mein Paket verloren geht?
Welche Rückgabebedingungen gelten für Kurse?
Welche Gebühren fallen bei Rücksendungen an?
Kann ich Lebensmitteldrucke auch im Laden bestellen?
Gibt es Kurse für Kinder?
Kann ich Artikel in der Bestellung austauschen?
Kann ich mein Paket an eine andere Adresse liefern lassen?
Welcher Logistikpartner wird für den Versand genutzt?
Wie funktioniert die Bestellung von Lebensmitteldrucken?
Kann ich eine Bestellung priorisieren lassen?
Gibt es Rabatte für größere Bestellungen?
Kann ich ein Paket an eine Packstation liefern lassen?
Wo finden die Kurse statt?
Kann ich eine Bestellung nachträglich ändern oder stornieren?
Wie werden Gutschriften erstattet?
Bieten Sie auch Kurse für Anfänger an?
Kann ich Produkte aus verschiedenen Kategorien kombinieren?
Kann ich eine Bestellung direkt nach dem Kauf stornieren?
Kann ich den Status meiner Lieferung verfolgen?
Wie sehe ich, ob ein Produkt in gewünschter Menge verfügbar ist?
Kann ich den Gutscheinwert aufteilen?
Wie wird mit meinen persönlichen Daten umgegangen?
Kann ich Produkte reservieren?
Was ist die maximale Anzahl an Produkten pro Bestellung?
Was passiert, wenn ich die Rechnung nicht rechtzeitig bezahle?
Kann ich ein Konto löschen?
Wie kann ich mehrere Adressen in meinem Konto speichern?
Wie hoch sind die Gebühren für Zahlungsverzug?
Gibt es eine Mindestbestellmenge?
Kann ich mit Kreditkarte bezahlen?
Kann ich eine Bestellung vor Ort abholen?
Wie erkenne ich Produkte, die nicht auf Lager sind?
Ich habe mein Passwort vergessen. Was kann ich tun?
Kann ich aus dem Ausland bestellen?
Wie kann ich bezahlen?
Was passiert, wenn ich bei der Zustellung nicht zu Hause bin?
Bieten Sie auch Geschenkkarten an?
Wie kann ich meine Daten einsehen, ändern oder löschen?
Wie viele Punkte brauche ich für einen Rabatt mit Poinz?
Werden Rabatte automatisch angewendet?
Können Poinz-Punkte verfallen?
Kann ich Lebensmitteldrucke bestellen?
Welche Produkte sind am häufigsten ausverkauft?
Kann ich Produkte zurückgeben?
