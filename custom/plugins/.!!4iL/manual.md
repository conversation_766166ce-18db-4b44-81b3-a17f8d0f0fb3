FelAIProductAdvisor - Benutzerhandbuch
======================================

Inhaltsverzeichnis
------------------

1.  Einführung
    
2.  Installation und Voraussetzungen
    
3.  Erstellung und Konfiguration von Produktberatern
    
4.  Integration in Erlebniswelten
    
5.  Anpassung der Berater
    
6.  Fehlerbehebung
    
7.  Häufig gestellte Fragen (FAQ)
    

1\. Einführung
--------------

FelAIProductAdvisor ist ein spezialisiertes KI-Produktberater-Plugin für Shopware 6, das auf OpenAI's Assistants API basiert und das kostengünstige GPT-4o-mini Modell verwendet. Das Plugin ermöglicht die Erstellung mehrerer spezialisierter Produktberater, die jeweils auf bestimmte Produktkategorien oder -bereiche zugeschnitten sind und über CMS-Elemente in Erlebniswelten integriert werden können.

### Abhängigkeiten

FelAIProductAdvisor benötigt das FelOAIAssistantsManager Basisplugin, das die grundlegende OpenAI-Integration und Store API-Funktionalität bereitstellt.

2\. Installation und Voraussetzungen
------------------------------------

### Voraussetzungen

*   Shopware 6.6 oder höher
    
*   PHP 8.0 oder höher
    
*   FelOAIAssistantsManager Basisplugin (muss zuerst installiert werden)
    
*   OpenAI API-Schlüssel mit Zugriff auf die Assistants API
    

### Installationsschritte

1.  Installieren Sie zuerst das FelOAIAssistantsManager Basisplugin über den Shopware Store oder durch manuelles Hochladen.
    
2.  Konfigurieren Sie das Basisplugin mit Ihrem OpenAI API-Schlüssel.
    
3.  Installieren Sie das FelAIProductAdvisor Plugin über den Shopware Store oder durch manuelles Hochladen.
    
4.  Aktivieren Sie beide Plugins in der Shopware Administration unter "Erweiterungen > Meine Erweiterungen".
    
5.  Führen Sie einen Cache-Clear durch: bin/console cache:clear
    

3\. Erstellung und Konfiguration von Produktberatern
----------------------------------------------------

### Erstellung eines OpenAI Assistants

1.  Navigieren Sie in der Shopware Administration zu "Erweiterungen > FelOAIAssistantsManager > Assistants".
    
2.  Klicken Sie auf "Neuen Assistant erstellen".
    
3.  Geben Sie einen Namen und eine Beschreibung ein, die den Zweck des Beraters widerspiegeln (z.B. "Weinberater", "Technikexperte").
    
4.  Wählen Sie das zu verwendende OpenAI-Modell (empfohlen: gpt-4o-mini für das beste Preis-Leistungs-Verhältnis).
    
5.  Definieren Sie die Systemanweisungen, die die Persönlichkeit und das Fachwissen des Beraters festlegen.
    
6.  Aktivieren Sie die benötigten Store API-Funktionen:
    
    *   get\_categories
        
    *   get\_manufacturer
        
    *   get\_product\_properties
        
    *   product\_search
        
    *   get\_product\_details
        
7.  Speichern Sie den Assistant.
    

### Konfiguration der Berater-Richtlinien

1.  Navigieren Sie zu "Erweiterungen > FelAIProductAdvisor > Berater-Richtlinien".
    
2.  Klicken Sie auf "Neue Richtlinie erstellen".
    
3.  Geben Sie einen Namen für die Richtlinie ein (z.B. "Weinberatung", "Technikberatung").
    
4.  Definieren Sie spezifische Richtlinien für den Berater, wie:
    
    *   Fachspezifische Terminologie
        
    *   Beratungsansatz (z.B. Fragen zum Geschmack bei Weinen, technische Details bei Elektronik)
        
    *   Produktspezifische Empfehlungskriterien
        
5.  Speichern Sie die Richtlinie.
    

4\. Integration in Erlebniswelten
---------------------------------

### Hinzufügen des Produktberater-CMS-Elements

1.  Navigieren Sie zu "Inhalte > Erlebniswelten".
    
2.  Öffnen Sie eine bestehende Erlebniswelt oder erstellen Sie eine neue.
    
3.  Ziehen Sie das "FelAI Produktberater" CMS-Element in den gewünschten Bereich der Erlebniswelt.
    
4.  Konfigurieren Sie das Element:
    
    *   Wählen Sie den zuvor erstellten OpenAI Assistant aus
        
    *   Wählen Sie die passende Berater-Richtlinie
        
    *   Passen Sie das Erscheinungsbild an (Farben, Position, Größe)
        
    *   Definieren Sie eine Willkommensnachricht
        
5.  Speichern Sie die Erlebniswelt.
    

### Platzierung in verschiedenen Kategorien

1.  Sie können verschiedene Erlebniswelten mit unterschiedlichen Produktberatern für verschiedene Kategorien erstellen.
    
2.  Weisen Sie die Erlebniswelten den entsprechenden Kategorieseiten zu.
    
3.  Alternativ können Sie den Berater auf Produktdetailseiten innerhalb bestimmter Kategorien platzieren.
    

5\. Anpassung der Berater
-------------------------

### Anpassung des Erscheinungsbilds

1.  Im CMS-Element können Sie folgende Aspekte anpassen:
    
    *   Farben und Design des Chat-Fensters
        
    *   Position und Größe
        
    *   Icon und Titel des Beraters
        
    *   Animation und Verhalten
        

### Anpassung der Berater-Persönlichkeit

1.  Über die OpenAI Assistant-Konfiguration können Sie:
    
    *   Den Ton und Stil des Beraters definieren
        
    *   Spezifisches Fachwissen hinzufügen
        
    *   Die Antwortlänge und -detailtiefe steuern
        
    *   Spezifische Produktempfehlungsstrategien festlegen
        

### Kategoriespezifische Anpassungen

1.  Für jeden Berater können Sie festlegen:
    
    *   Auf welche Kategorien er sich konzentrieren soll
        
    *   Welche Produkteigenschaften besonders hervorgehoben werden sollen
        
    *   Welche Arten von Fragen er beantworten soll
        
    *   Spezifische Empfehlungsalgorithmen basierend auf Produkteigenschaften
        

6\. Fehlerbehebung
------------------

### Häufige Probleme und Lösungen

1.  **Berater erscheint nicht in der Erlebniswelt**:
    
    *   Überprüfen Sie, ob das CMS-Element korrekt konfiguriert ist
        
    *   Stellen Sie sicher, dass der ausgewählte Assistant existiert und aktiv ist
        
    *   Prüfen Sie, ob JavaScript-Fehler in der Browserkonsole auftreten
        
2.  **Berater liefert falsche oder unspezifische Antworten**:
    
    *   Überprüfen Sie die Systemanweisungen des Assistants
        
    *   Stellen Sie sicher, dass die Berater-Richtlinien spezifisch genug sind
        
    *   Prüfen Sie, ob die richtigen Store API-Funktionen aktiviert sind
        
3.  **Berater kann keine Produktinformationen abrufen**:
    
    *   Überprüfen Sie, ob die Produkte korrekt kategorisiert sind
        
    *   Stellen Sie sicher, dass Produkteigenschaften vollständig ausgefüllt sind
        
    *   Prüfen Sie die Logs unter "FelOAIAssistantsManager > Logs"
        

### Logging und Debugging

1.  Aktivieren Sie das erweiterte Logging in den Plugin-Einstellungen.
    
2.  Überprüfen Sie die Logs unter "FelOAIAssistantsManager > Logs".
    
3.  Für technisches Debugging können Sie die Browser-Entwicklertools verwenden, um API-Anfragen zu überwachen.
    

7\. Häufig gestellte Fragen (FAQ)
---------------------------------

### Allgemeine Fragen

**F: Wie viele verschiedene Produktberater kann ich erstellen?**A: Sie können beliebig viele Produktberater erstellen, je nach Ihren Anforderungen und Kategorien.

**F: Wie hoch sind die Kosten für die OpenAI API?**A: Die Kosten hängen vom verwendeten Modell und der Nutzung ab. Mit GPT-4o-mini betragen die Kosten etwa $0.15 pro 1M Token für Input und $0.60 pro 1M Token für Output, was es zu einer kostengünstigen Option macht.

**F: Können Berater auf mehrere Kategorien spezialisiert sein?**A: Ja, Sie können einen Berater für mehrere verwandte Kategorien konfigurieren oder separate Berater für jede Kategorie erstellen.

### Technische Fragen

**F: Kann ich die Berater-Antworten trainieren oder verbessern?**A: Ja, Sie können die Systemanweisungen und Richtlinien basierend auf den Chatverläufen kontinuierlich verbessern.

**F: Ist das Plugin mit Multi-Shop-Setups kompatibel?**A: Ja, das Plugin funktioniert mit Multi-Shop-Setups. Sie können für jeden Sales Channel unterschiedliche Berater einrichten.

**F: Wie kann ich die Performance der Berater optimieren?**A: Stellen Sie sicher, dass Ihre Produktdaten gut strukturiert sind, verwenden Sie präzise Systemanweisungen und wählen Sie das richtige Modell für Ihre Anforderungen.

Anwendungsbeispiele
-------------------

### Weinberater

*   **Platzierung**: In der Weinkategorie
    
*   **Spezialisierung**: Weintypen, Rebsorten, Geschmacksprofile, Speisenpaarungen
    
*   **Beispielfragen**: "Welcher Rotwein passt zu Rinderfilet?", "Ich suche einen trockenen Weißwein unter 15€"
    

### Technikexperte

*   **Platzierung**: In der Elektronikkategorie
    
*   **Spezialisierung**: Technische Spezifikationen, Vergleiche, Nutzungsszenarien
    
*   **Beispielfragen**: "Welcher Laptop eignet sich für Videobearbeitung?", "Was ist der Unterschied zwischen diesen beiden Kameras?"
    

### Modeberater

*   **Platzierung**: Im Bekleidungsbereich
    
*   **Spezialisierung**: Größenberatung, Styling-Tipps, Materialien, Pflegehinweise
    
*   **Beispielfragen**: "Welche Jeans passt zu meiner Figur?", "Wie kombiniere ich dieses Kleid?"
    

Support und Kontakt
-------------------

Bei Fragen oder Problemen wenden Sie sich bitte an:

*   Support-E-Mail: <EMAIL>
    
*   Dokumentation: [docs.fel-plugins.com/product-advisor](https://docs.fel-plugins.com/product-advisor)
    
*   Updates und News: [fel-plugins.com/blog](https://fel-plugins.com/blog)
    

Wir hoffen, dass FelAIProductAdvisor Ihren Shop bereichert und Ihren Kunden ein personalisiertes, kategoriespezifisches Beratungserlebnis bietet!




# ################ Shopware Store Beschreibung #################


# FelAIProductAdvisor - Spezialisierte KI-Produktberater für Shopware 6

**Der erste KI-gestützte Produktberater für Shopware 6, der spezialisierte Beratung für bestimmte Produktkategorien bietet**

FelAIProductAdvisor revolutioniert die Produktberatung in Ihrem Shopware 6 Shop durch die Integration von OpenAI's Assistants API mit dem kostengünstigen GPT-4o-mini Modell. Im Gegensatz zu herkömmlichen Chatbots ermöglicht dieses Plugin die Erstellung mehrerer spezialisierter Produktberater, die jeweils auf bestimmte Produktkategorien oder -bereiche zugeschnitten sind.

## Hauptfunktionen

- **Mehrere spezialisierte Berater**: Erstellen Sie verschiedene Produktberater für unterschiedliche Kategorien oder Produktlinien
- **CMS-Element Integration**: Einfache Einbindung in Erlebniswelten über ein dediziertes CMS-Element
- **Individuelle Konfiguration**: Weisen Sie jedem Berater einen eigenen OpenAI Assistant mit spezifischen Richtlinien zu
- **Kategoriespezifische Beratung**: Bieten Sie maßgeschneiderte Beratung für bestimmte Produktbereiche
- **Dynamische Produktinformationen**: Der Berater greift auf aktuelle Produktdaten, Eigenschaften und Kategorien zu

## Technische Highlights

- Basiert auf OpenAI's neuester Assistants API
- Verwendet das kostengünstige GPT-4o-mini Modell (nur $0.15 pro 1M Token für Input und $0.60 pro 1M Token für Output)
- Nahtlose Integration mit dem FelOAIAssistantsManager Basisplugin
- Vollständige Nutzung der Shopware 6 Store API für Produktdaten
- Flexibles CMS-Element für einfache Integration in Erlebniswelten
- Optimiert für spezialisierte Produktberatung

## Anwendungsbeispiele

- **Weinberater** in der Weinkategorie, der Kunden bei der Auswahl des perfekten Weins berät
- **Technikexperte** in der Elektronikkategorie, der technische Fragen zu Geräten beantwortet
- **Modeberater** im Bekleidungsbereich, der Styling-Tipps und Größenberatung bietet
- **Kosmetikexperte** für Beautyprodukte, der auf Hauttypen und Inhaltsstoffe eingeht
- **Sportausrüstungsberater** für Sportartikel, der basierend auf Aktivitäten und Niveau berät

## Vorteile für Ihren Shop

- **Erhöhte Konversionsraten**: Durch spezialisierte, fachkundige Beratung direkt am Point-of-Interest
- **Verbesserte Kundenerfahrung**: Kunden erhalten maßgeschneiderte Beratung für genau die Produkte, die sie interessieren
- **Reduzierte Kaufabbrüche**: Fragen werden direkt beantwortet, ohne dass Kunden den Shop verlassen müssen
- **Differenzierung vom Wettbewerb**: Bieten Sie ein einzigartiges Einkaufserlebnis mit spezialisierten KI-Beratern
- **Flexibler Einsatz**: Platzieren Sie verschiedene Berater genau dort, wo sie am meisten benötigt werden

Heben Sie Ihr Shopware 6 Einkaufserlebnis auf die nächste Stufe mit FelAIProductAdvisor - spezialisierten KI-Produktberatern, die Ihren Kunden genau dort helfen, wo sie Beratung benötigen.