<div class="fel-chat-product-listing-wrapper">
    {% set felConfig = config('FelAIProductAdvisor.config') %}
    {% set categoryListingMax = 6 %}
    {% set setBreadcrumbLength = 2 %}
    {% set isProductSearch = 'product_search' === isSearch %}
    {% set descriptionLength = isProductSearch ? 74 : 300 %}
    {% set productsTotal = products|length %}
    {% set isSingleProduct = 1 == productsTotal ? ' fel-is-single-product' : '' %}
    {% set isSearchClass = isSearch ? ' fel-is-search-result-listing' : '' %}
    {% set propertyParams = productsResponse.propertyData.setParameter %}

    <style>
        /* Produktlisten-Styling */
        .fel-chat-product-list {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .fel-chat-product-list-item {
            margin-bottom: 15px;
        }

        .fel-item-wrapper {
            display: flex;
            border: 1px solid #e9e9e9;
            border-radius: 5px;
            overflow: hidden;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .fel-item-thumbnail {
            flex: 0 0 120px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            margin-bottom: 0 !important;
        }

        .fel-item-thumbnail img {
            max-width: 100%;
            max-height: 100px;
            object-fit: contain;
        }

        .fel-item-content {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .fel-item-header {
            margin-bottom: 8px !important;
        }

        .fel-item-header h2 {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .fel-item-header h2 a {
            color: var(--sw-color-brand-primary, #0d6efd);
            text-decoration: none;
        }

        .fel-subtitle {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .fel-price {
            font-weight: 600;
            color: var(--sw-color-price, #e74c3c);
        }

        .fel-manufacturer {
            color: #6c757d;
        }

        .fel-item-body {
            flex-grow: 1;
        }

        .fel-description, .fel-long-description {
            font-size: 14px;
            color: #495057;
            margin-bottom: 12px;
        }

        .fel-product-properties {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .fel-product-properties-item {
            display: inline-block;
            padding: 2px 6px;
            background-color: rgba(var(--fel-chatbot-theme-color-rgb, var(--bs-primary-rgb)), 0.1);
            border-radius: 3px;
            font-size: 12px;
            color: var(--fel-chatbot-theme-color, var(--bs-primary));
            border: 1px solid rgba(var(--fel-chatbot-theme-color-rgb, var(--bs-primary-rgb)), 0.2);
        }

        .fel-item-footer {
            margin-top: auto;
        }

        .fel-product-details-btn {
            font-size: 14px;
            padding: 5px 10px;
        }

        /* Kategorie-Listen-Styling */
        .fel-chat-link-category-ul-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .fel-chat-category-list-header {
            width: 100%;
            margin-bottom: 8px;
        }

        .fel-categories-list-header {
            font-weight: bold;
            font-size: 16px;
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            .fel-item-wrapper {
                flex-direction: column;
            }

            .fel-item-thumbnail {
                flex: 0 0 auto;
                height: 150px;
            }
        }
    </style>

    <h3 class="fel-product-listing-title">
        {% if productsResponse.args.categoryId %}
            {% set categoryName = '' %}
            {% for category in productsResponse.categoryMap %}
                {% if category.id == productsResponse.args.categoryId %}
                    {% set categoryName = category.name %}
                {% endif %}
            {% endfor %}
            Produkte in der Kategorie {{ categoryName|default('Kleidung') }}
        {% elseif productsResponse.args.term %}
            Suchergebnisse für "{{ productsResponse.args.term }}"
        {% else %}
            Produktergebnisse
        {% endif %}
    </h3>

    <ul class="fel-chat-product-list{{ isSearchClass }}">
        {% for product in products %}
            {% set setQueryProperties = product.parentId ? {} : propertyParams.properties %}
            {% set felSeoUrl = url('frontend.detail.page', {productId: product.id, properties: setQueryProperties}) %}
            {% if felSeoUrl %}
                {% set isLastProduct = loop.index == productsTotal %}
                {% set isLastProductClass = true == isLastProduct ? ' fel-product-list-last-product' : '' %}
                {% set felHighlightProduct = (product.isNew || product.markAsTopseller) ? ' fel-highlight' : '' %}
                {% set translatedName = product.translated.name ?? product.name %}
                {% set translatedDescription = (product.translated.description ?? product.description)|striptags|trim|slice(0, descriptionLength) %}
                {% set felSetTruncateEllipsis = false %}
                {% if translatedDescription|length >= descriptionLength %}
                    {% set translatedDescription = translatedDescription|split(' ')|slice(0, -1)|join(' ') %}
                    {% set felSetTruncateEllipsis = true %}
                {% endif %}
                {% set translatedDescription = translatedDescription|trim(',') %}
                {% set felBuyable = product.available and product.childCount <= 0 and product.calculatedMaxPurchase > 0 %}

                <li class="fel-chat-product-list-item{{ isLastProductClass ~ isSingleProduct ~ felHighlightProduct }}"
                    data-ref-id="{{ product.parentId ?? product.id }}"
                    data-is-available="{{ felBuyable ? 'true' : 'false' }}">
                    <div class="fel-item-wrapper">
                        <div class="fel-item-thumbnail">
                            {% if product.cover.media.thumbnails[0].url is not empty %}
                                <a href="{{ felSeoUrl }}" class="fel-product-image-link">
                                    <img src="{{ product.cover.media.thumbnails[0].url }}" alt="{{ translatedName }}" class="fel-product-image" />
                                </a>
                            {% else %}
                                <div class="fel-product-no-image">
                                    <span>{{ 'fel-gpt-assistant.chat.productListing.noImageAssigned'|trans|sw_sanitize }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <div class="fel-item-content">
                            <div class="fel-item-header">
                                <h2 title="{{ 'detail.productNumberLabel'|trans|sw_sanitize }} {{ product.productNumber }}">
                                    <a href="{{ felSeoUrl }}">{{ translatedName }}</a>
                                </h2>
                                <div class="fel-subtitle">
                                    {% set getCheapestPrice = product.calculatedCheapestPrice.totalPrice ?? product.calculatedPrice.totalPrice ?? null %}
                                    {% if getCheapestPrice %}
                                        <span class="fel-price" title="{{ 'listing.filterPriceDisplayName'|trans|sw_sanitize }}">
                                            {{ getCheapestPrice|currency }}
                                        </span>
                                    {% endif %}
                                    {% if product.manufacturer %}
                                        <span class="fel-manufacturer" title="{{ 'listing.filterManufacturerDisplayName'|trans|sw_sanitize }}">
                                            {{ product.manufacturer.translated.name ?? product.manufacturer.name }}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="fel-item-body">
                                <div class="{{ isProductSearch ? 'fel-description' : 'fel-long-description' }}">
                                    {{ translatedDescription }}
                                    {% if felSetTruncateEllipsis %}<span class="fel-ellipsis">…</span>{% endif %}
                                </div>

                                {% if product.options or product.properties %}
                                    <div class="fel-product-properties-container">
                                        <div class="fel-product-properties">
                                            {% for property in product.options|sort((a, b) => a.group.name <=> b.group.name) %}
                                                {% if property.group.filterable and property.group.visibleOnProductDetailPage %}
                                                    <span class="fel-product-properties-item" title="{{ property.group.translated.name }}">
                                                        {{ property.translated.name }}
                                                    </span>
                                                {% endif %}
                                            {% endfor %}

                                            {% for property in product.properties|sort((a, b) => a.group.name <=> b.group.name) %}
                                                {% if property.group.filterable and property.group.visibleOnProductDetailPage %}
                                                    <span class="fel-product-properties-item" title="{{ property.group.translated.name }}">
                                                        {{ property.translated.name }}
                                                    </span>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="fel-item-footer">
                                <a href="{{ felSeoUrl }}" class="btn btn-primary fel-product-details-btn">
                                    {{ 'listing.boxProductDetails'|trans|sw_sanitize }}
                                </a>
                            </div>
                        </div>
                    </div>
                </li>
            {% endif %}
        {% endfor %}
    </ul>
    {% if propertyParams.search and isProductSearch %}
        <div class="fel-chat-link-search-page mt-2 mb-2">
            <a class="btn d-block fel-show-all-products" href="{{ path('frontend.search.page', propertyParams) }}">
                {{ 'header.searchAllResults'|trans|sw_sanitize }}
            </a>
        </div>
    {% endif %}
    {% if productsResponse.categoryMap is not empty %}
        <div class="fel-chat-link-category-list-wrapper mt-2 mb-1">
            <ul class="fel-chat-link-category-ul-list">
                <li class="fel-chat-category-list-header" title="{{ productsResponse.categoryMap|length }}">
                    <span class="btn disabled fel-categories-list-header">
                        {{ 'fel-gpt-assistant.chat.categories'|trans|sw_sanitize }}
                    </span>
                </li>
                {% set categoryLevel = felConfig.openAiFilterCategoryLevel %}
                {% for categoryData in productsResponse.categoryMap|slice(0, categoryListingMax) %}
                    {% set breadcrumbLength = categoryData.breadcrumb|length %}
                    {% set breadcrumbStart = breadcrumbLength - setBreadcrumbLength %}
                    {% set visibleBreadcrumbs = categoryData.breadcrumb|slice(breadcrumbStart, -1) %}
                    {% if visibleBreadcrumbs|length < 1 %}
                        {% set visibleBreadcrumbs = categoryData.breadcrumb|slice(0, -1) %}
                    {% endif %}
                    <li>
                        <a href="{{ path('frontend.navigation.page', propertyParams|merge({navigationId: categoryData.id})) }}"
                            class="btn fel-category-button{% if breadcrumbStart %} fel-breadcrumb-trancate{% endif %}"
                            title="{{ categoryData.breadcrumb|join(" » ") }}">
                            <span class="fel-dir-rtl">
                                {% if visibleBreadcrumbs|length and setBreadcrumbLength > 1 %}
                                    <em>{{ visibleBreadcrumbs|join(' » ') }} »</em>
                                {% endif %}
                                <b>{{ categoryData.name }}</b>
                            </span>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
</div>
