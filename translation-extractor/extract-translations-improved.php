#!/usr/bin/php
<?php error_reporting(E_ALL);

class TranslationExtractor
{
    private $sourceFiles = [];
    private $translationFiles = [];
    private $translationData = [];
    private $config = [
        'extensions' => ['twig', 'js', 'html.twig'],
        'translationFunctions' => ['\$t', 'this\.\$t'],
        'translationFilePattern' => '/[a-zA-Z]{2}-[a-zA-Z]{2}\.json$/', // e.g., en-GB.json, de-DE.json
        'ignore' => [
            'prefix' => [],
            'removePrefix' => []
        ],
        'dynamicParts' => [],
        'dynamicPatterns' => []
    ];
    private $unusedTranslations = [];
    private $comparisonSummary = [];

    public function __construct($srcDir, $config = [])
    {
        if (!is_dir($srcDir)) {
            die("Error: Invalid directory provided: $srcDir\n");
        }

        $this->srcDir = $srcDir;
        $this->config = array_replace_recursive($this->config, $config);

        // Separate source files from translation files
        $this->findFiles($srcDir);

        if (empty($this->sourceFiles)) {
            die("Error: No source files found in: $srcDir\n");
        }

        if (empty($this->translationFiles)) {
            die("Error: No translation files found in: $srcDir\n");
        }

        $this->translationData = $this->loadTranslationData();
        $this->extractTranslations(); // Just run the extraction, we don't need to store the result
        $this->analyzeTranslations();
        $this->printSummary();
    }

    // Find all source and translation files
    private function findFiles($dir)
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $pathname = $file->getPathname();

                // Skip hidden files and directories
                if (strpos($pathname, '/.') !== false) {
                    continue;
                }

                // Check if it's a translation file
                if (preg_match($this->config['translationFilePattern'], $pathname)) {
                    $this->translationFiles[] = $pathname;
                    // Skip translation files from being considered as source files
                    continue;
                }

                // Check if it's a source file with allowed extension
                $extension = pathinfo($pathname, PATHINFO_EXTENSION);

                // Handle .html.twig files correctly
                if ($extension === 'twig' && strpos($pathname, '.html.twig') !== false) {
                    $extension = 'html.twig';
                }

                if (in_array($extension, $this->config['extensions'])) {
                    $this->sourceFiles[] = $pathname;
                }
            }
        }

        echo "Found " . count($this->sourceFiles) . " source files and " . count($this->translationFiles) . " translation files.\n";
    }

    // Extract translations from source files
    private function extractTranslations()
    {
        // Build regex patterns for all translation functions
        $functionPatterns = [];
        foreach ($this->config['translationFunctions'] as $func) {
            // Match both single and double quotes in JS files
            $functionPatterns[] = $func . '\s*\(\s*[\'"]([^\'"]+)[\'"]';

            // Match Twig template syntax: {{ $t('key') }}
            $functionPatterns[] = '{{\s*' . $func . '\s*\(\s*[\'"]([^\'"]+)[\'"]';
        }

        // Add patterns for helper functions
        $functionPatterns[] = 'getContentEditorTranslation\s*\(\s*[\'"]?([^\'"`,\)]+)[\'"]?\s*,\s*[\'"]([^\'"]+)[\'"]';
        $functionPatterns[] = 'felTranslate\s*\(\s*[\'"]([^\'"]+)[\'"]';

        // Add pattern for custom trans method
        $functionPatterns[] = 'trans\s*\(\s*[\'"]([^\'"]+)[\'"]';

        $pattern = '/' . implode('|', $functionPatterns) . '/';

        echo "Scanning source files for translation keys...\n";
        $count = 0;
        $dynamicCount = 0;
        $dynamicTransMethods = [];

        foreach ($this->sourceFiles as $file) {
            $content = file_get_contents($file);

            // Look for custom trans method definition
            if (pathinfo($file, PATHINFO_EXTENSION) === 'php' &&
                strpos($content, 'private function trans(') !== false) {

                // Extract the default tree parameter
                if (preg_match('/tree\s*=\s*[\'"]([^\'"]+)[\'"]/', $content, $treeMatch)) {
                    $defaultTree = $treeMatch[1];
                    $dynamicTransMethods[$file] = $defaultTree;
                    echo "Found custom trans method with default tree '$defaultTree' in file: $file\n";

                    // Look for calls to this method
                    if (preg_match_all('/\$this->trans\s*\(\s*[\'"]([^\'"]+)[\'"]/', $content, $transMatches)) {
                        foreach ($transMatches[1] as $transKey) {
                            $fullKey = "fel-assistant-manager.$defaultTree.$transKey";
                            echo "Found dynamic translation key: $fullKey in file: $file\n";
                            $dynamicCount++;
                        }
                    }

                    // Look for calls with explicit tree parameter
                    if (preg_match_all('/\$this->trans\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*[^,]+\s*,\s*[\'"]([^\'"]+)[\'"]/', $content, $explicitMatches, PREG_SET_ORDER)) {
                        foreach ($explicitMatches as $match) {
                            $transKey = $match[1];
                            $tree = $match[2];
                            $fullKey = "fel-assistant-manager.$tree.$transKey";
                            echo "Found dynamic translation key with explicit tree: $fullKey in file: $file\n";
                            $dynamicCount++;
                        }
                    }
                }
            }

            // Check for standard translation patterns
            preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

            foreach ($matches as $match) {
                // The last captured group will be the translation key
                $key = end($match);
                $count++;

                // Debug output for specific key
                if ($key === 'fel-assistant-manager.catchErrors.invalidSalesChannelDomain') {
                    echo "Found key in file: $file\n";
                }
            }

            // Check for dynamic patterns
            foreach ($this->config['dynamicPatterns'] as $dynamicPattern) {
                preg_match_all($dynamicPattern, $content, $dynamicMatches, PREG_SET_ORDER);

                foreach ($dynamicMatches as $match) {
                    $dynamicCount++;
                }
            }

            // Check for helper functions with dynamic keys
            if (strpos($content, 'getContentEditorTranslation(') !== false ||
                strpos($content, 'felTranslate(') !== false) {

                // Look for patterns like: getContentEditorTranslation(key, 'name')
                if (preg_match_all('/getContentEditorTranslation\s*\(\s*([^\'",\)]+)\s*,/', $content, $helperMatches)) {
                    foreach ($helperMatches[1] as $varName) {
                        $dynamicCount++;
                        echo "Found dynamic helper function call with variable '$varName' in file: $file\n";
                    }
                }

                // Look for patterns like: felTranslate(`${prefix}.${key}`)
                if (preg_match_all('/felTranslate\s*\(\s*`([^`]+)`/', $content, $templateMatches)) {
                    foreach ($templateMatches[1] as $template) {
                        $dynamicCount++;
                        echo "Found template literal in helper function: '$template' in file: $file\n";
                    }
                }
            }
        }

        echo "Found $count direct translation key usages and approximately $dynamicCount dynamic usages in source files.\n";
    }

    // Load translation data from JSON files
    private function loadTranslationData()
    {
        $translationData = [];
        $duplicateKeys = [];
        $languageFiles = [];

        // Group translation files by language
        foreach ($this->translationFiles as $file) {
            $basename = basename($file);
            $langCode = preg_replace('/^.*?([a-z]{2}-[A-Z]{2})\.json$/', '$1', $basename);
            if (!isset($languageFiles[$langCode])) {
                $languageFiles[$langCode] = [];
            }
            $languageFiles[$langCode][] = $file;
        }

        // Process each language group separately
        foreach ($languageFiles as $langCode => $files) {
            $langTranslationData = [];

            foreach ($files as $file) {
                $content = file_get_contents($file);
                $json = json_decode($content, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    $flattenedKeys = $this->flattenTranslationKeys($json);
                    foreach ($flattenedKeys as $key) {
                        if (isset($langTranslationData[$key])) {
                            // Track duplicate keys within the same language
                            if (!isset($duplicateKeys[$key])) {
                                $duplicateKeys[$key] = [$langTranslationData[$key]];
                            }
                            $duplicateKeys[$key][] = $file;
                        }
                        $langTranslationData[$key] = $file;
                        $translationData[$key] = $file; // Add to global translation data
                    }
                } else {
                    echo "Warning: Failed to parse JSON file: $file\n";
                }
            }
        }

        // Report duplicate keys (only within the same language)
        if (!empty($duplicateKeys)) {
            echo "\nFound " . count($duplicateKeys) . " duplicate translation keys within the same language:\n";
            foreach ($duplicateKeys as $key => $files) {
                echo "  - '$key' found in:\n";
                foreach ($files as $file) {
                    echo "      - " . basename($file) . "\n";
                }
            }
            echo "\n";
        }

        return $translationData;
    }

    // Analyze translations to find unused ones
    private function analyzeTranslations()
    {
        $allTranslationKeys = array_keys($this->translationData);

        // Special keys that are known to be used but might not be detected
        $specialKeys = [
            'fel-assistant-manager.catchErrors.invalidSalesChannelDomain',
            'fel-assistant-manager.exception.missingMessageForCreateThread',
            // Add more special keys here
        ];

        // Common prefixes that indicate translations used in specific contexts
        $contextPrefixes = [
            'administration' => [
                'fel-assistant-manager.general.',
                'fel-assistant-manager.contentEditor.',
                'fel-assistant-manager.filter.',
                'fel-assistant-manager.log.',
                'fel-assistant-manager.chat.',
                'fel-assistant-manager.chatlog.',
                'fel-assistant-manager.tags-tips.'
            ],
            'storefront' => [
                'fel-assistant-manager.storefront.',
                'fel-assistant-manager.exception.',
                'fel-assistant-manager.catchErrors.',
                'fel-assistant-manager.catchSuccess.',
                'fel-assistant-manager.catchInfo.',
                'fel-assistant-manager.catchWarning.'
            ]
        ];

        foreach ($allTranslationKeys as $key) {
            $originalKey = $key;

            // Special case for known problematic keys
            if (in_array($originalKey, $specialKeys)) {
                echo "Special handling for key: $originalKey\n";
                // Skip this key as we know it's used
                continue;
            }

            // Apply prefix removal if configured
            $key = $this->removePrefixIfConfigured($key);

            // Track in summary
            $this->incrementComparisonSummary($originalKey);

            // Skip ignored translations
            if ($this->shouldIgnoreTranslation($originalKey)) {
                continue;
            }

            // Check if translation is used
            if (!$this->isTranslationUsed($key)) {
                // Check if it's a dynamic translation
                if ($this->isDynamicTranslationUsed($key)) {
                    continue;
                }

                // Check if it's part of a dynamic pattern
                if ($this->isPartOfDynamicPattern($originalKey)) {
                    continue;
                }

                // Check if it's a context-specific translation
                $isContextSpecific = false;
                foreach ($contextPrefixes as $context => $prefixes) {
                    foreach ($prefixes as $prefix) {
                        if (strpos($originalKey, $prefix) === 0) {
                            // This is a context-specific translation
                            $isContextSpecific = true;
                            echo "Note: Key '$originalKey' is specific to the $context context.\n";
                            break 2;
                        }
                    }
                }

                // If it's a context-specific translation, we might want to keep it
                // even if it's not detected as used in the current scan
                if ($isContextSpecific) {
                    // For now, we'll still mark it as unused but with a note
                    // You can comment out the next two lines if you want to skip these
                    $this->comparisonSummary[$originalKey]['unused']++;
                    $this->unusedTranslations[] = $originalKey;
                    continue;
                }

                $this->comparisonSummary[$originalKey]['unused']++;
                $this->unusedTranslations[] = $originalKey;
            }
        }
    }

    // Check if a translation key is part of a dynamic pattern
    private function isPartOfDynamicPattern($key)
    {
        // Check if the key is part of a known dynamic prefix
        foreach (array_keys($this->config['dynamicParts']) as $prefix) {
            if (strpos($key, $prefix) === 0) {
                // Extract the base part and suffix
                $basePart = $prefix;
                $suffix = substr($key, strlen($prefix));
                $suffixParts = explode('.', $suffix);

                // Check if the prefix is used in a dynamic pattern
                foreach ($this->sourceFiles as $file) {
                    $content = file_get_contents($file);

                    // Check for dynamic patterns
                    foreach ($this->config['dynamicPatterns'] as $pattern) {
                        if (preg_match($pattern, $content, $matches)) {
                            $prefixPart = $matches[1] ?? '';

                            if (strpos($key, $prefixPart) === 0) {
                                echo "Found dynamic pattern for key: $key in file: $file\n";
                                return true;
                            }
                        }
                    }

                    // Check for helper functions
                    foreach ($this->config['helperFunctions'] as $function) {
                        // Check for function calls with the base part
                        if (strpos($content, $function . '(') !== false &&
                            (strpos($content, $basePart) !== false ||
                             strpos($content, substr($basePart, 0, -1)) !== false)) {

                            echo "Found helper function '$function' with base part '$basePart' for key: $key in file: $file\n";
                            return true;
                        }

                        // Check for function calls with the suffix parts
                        foreach ($suffixParts as $part) {
                            if (!empty($part) && strpos($content, $function . '(') !== false &&
                                (strpos($content, "'$part'") !== false ||
                                 strpos($content, "\"$part\"") !== false)) {

                                echo "Found helper function '$function' with suffix part '$part' for key: $key in file: $file\n";
                                return true;
                            }
                        }
                    }

                    // Check for simple string presence of the prefix
                    if (strpos($content, $prefix) !== false) {
                        echo "Found prefix '$prefix' for key: $key in file: $file\n";
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // Check if a translation is used in source files
    private function isTranslationUsed($key)
    {
        // Escape special regex characters in the key
        $escapedKey = preg_quote($key, '/');

        // Look for the key in single quotes, double quotes, and backticks with various patterns
        $patterns = [
            '/[\'"]' . $escapedKey . '[\'"]/', // Exact match with single or double quotes
            '/`' . $escapedKey . '`/', // Exact match with backticks
            '/[\'"]' . str_replace('.', '\\.', $escapedKey) . '[\'"]/', // With escaped dots
            '/`' . str_replace('.', '\\.', $escapedKey) . '`/', // With escaped dots in backticks
            '/[\'"]' . str_replace('.', '\\.', $escapedKey) . '\\./', // With trailing dot
            '/{{\s*\$t\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // Twig template syntax with quotes
            '/{{\s*\$t\s*\(\s*`' . $escapedKey . '`/', // Twig template syntax with backticks
            '/this\.\$t\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // this.$t syntax with quotes
            '/this\.\$t\s*\(\s*`' . $escapedKey . '`/', // this.$t syntax with backticks
            '/\$t\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // $t syntax with quotes
            '/\$t\s*\(\s*`' . $escapedKey . '`/', // $t syntax with backticks
            '/getContentEditorTranslation\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // getContentEditorTranslation with quotes
            '/getContentEditorTranslation\s*\(\s*`' . $escapedKey . '`/', // getContentEditorTranslation with backticks
            '/felTranslate\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // felTranslate with quotes
            '/felTranslate\s*\(\s*`' . $escapedKey . '`/', // felTranslate with backticks
            '/trans\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // PHP trans function with quotes
            '/trans\s*\(\s*`' . $escapedKey . '`/', // PHP trans function with backticks
            '/->trans\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // PHP $this->trans function with quotes
            '/->trans\s*\(\s*`' . $escapedKey . '`/', // PHP $this->trans function with backticks
            '/\$translator->trans\s*\(\s*[\'"]' . $escapedKey . '[\'"]/', // PHP $translator->trans function with quotes
            '/\$translator->trans\s*\(\s*`' . $escapedKey . '`/', // PHP $translator->trans function with backticks
            '/label:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js label property with quotes
            '/label:\s*`' . $escapedKey . '`/', // Vue.js label property with backticks
            '/title:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js title property with quotes
            '/title:\s*`' . $escapedKey . '`/', // Vue.js title property with backticks
            '/message:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js message property with quotes
            '/message:\s*`' . $escapedKey . '`/', // Vue.js message property with backticks
            '/name:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js name property with quotes
            '/name:\s*`' . $escapedKey . '`/', // Vue.js name property with backticks
            '/text:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js text property with quotes
            '/text:\s*`' . $escapedKey . '`/', // Vue.js text property with backticks
            '/placeholder:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js placeholder property with quotes
            '/placeholder:\s*`' . $escapedKey . '`/', // Vue.js placeholder property with backticks
            '/tooltip:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js tooltip property with quotes
            '/tooltip:\s*`' . $escapedKey . '`/', // Vue.js tooltip property with backticks
            '/hint:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js hint property with quotes
            '/hint:\s*`' . $escapedKey . '`/', // Vue.js hint property with backticks
            '/description:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js description property with quotes
            '/description:\s*`' . $escapedKey . '`/', // Vue.js description property with backticks
            '/aria-label:\s*[\'"]' . $escapedKey . '[\'"]/', // Vue.js aria-label property with quotes
            '/aria-label:\s*`' . $escapedKey . '`/', // Vue.js aria-label property with backticks
            '/v-tooltip="[^"]*[\'"]' . $escapedKey . '[\'"][^"]*"/', // Vue.js v-tooltip directive with quotes
            '/v-tooltip="[^"]*`' . $escapedKey . '`[^"]*"/' // Vue.js v-tooltip directive with backticks
        ];

        // Check for special patterns for contentEditor.editable keys
        if (strpos($key, 'fel-assistant-manager.contentEditor.editable.') === 0) {
            $keyParts = explode('.', $key);
            $lastPart = end($keyParts);

            // Add patterns for getContentEditorTranslation(key, 'name') and getContentEditorTranslation(key, 'description')
            if ($lastPart === 'name' || $lastPart === 'description') {
                $baseKey = implode('.', array_slice($keyParts, 0, -1));
                $escapedBaseKey = preg_quote($baseKey, '/');
                $patterns[] = '/getContentEditorTranslation\s*\(\s*[\'"]' . $escapedBaseKey . '[\'"],\s*[\'"]' . $lastPart . '[\'"]/' ;
                $patterns[] = '/getContentEditorTranslation\s*\(\s*([^,]+),\s*[\'"]' . $lastPart . '[\'"]/' ;
            }
        }

        foreach ($this->sourceFiles as $file) {
            $content = file_get_contents($file);

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $content)) {
                    // Debug output for specific key
                    if ($key === 'fel-assistant-manager.catchErrors.invalidSalesChannelDomain') {
                        echo "Found key '$key' in file: $file using pattern: $pattern\n";
                    }
                    return true;
                }
            }

            // Additional check for specific keys
            if (($key === 'fel-assistant-manager.catchErrors.invalidSalesChannelDomain' ||
                 $key === 'fel-assistant-manager.exception.missingMessageForCreateThread') &&
                strpos($content, $key) !== false) {
                echo "Found key '$key' in file: $file using simple string search\n";
                return true;
            }

            // Special check for exception-related translations in PHP files
            if (strpos($key, 'fel-assistant-manager.exception.') === 0 &&
                pathinfo($file, PATHINFO_EXTENSION) === 'php') {

                // Extract the exception name
                $exceptionName = substr($key, strlen('fel-assistant-manager.exception.'));

                // Check for exception class or message
                if (strpos($content, 'Exception') !== false &&
                    (strpos($content, $exceptionName) !== false ||
                     strpos($content, lcfirst($exceptionName)) !== false ||
                     strpos($content, ucfirst($exceptionName)) !== false)) {

                    echo "Found exception-related key '$key' in PHP file: $file\n";
                    return true;
                }

                // Check for dynamic translation method
                if (strpos($content, 'trans(') !== false &&
                    strpos($content, 'tree = \'exception\'') !== false) {

                    // Check if the key part after the prefix is used as a parameter
                    if (strpos($content, "'$exceptionName'") !== false ||
                        strpos($content, "\"$exceptionName\"") !== false) {

                        echo "Found dynamic exception translation for key: $key in file: $file\n";
                        return true;
                    }
                }
            }

            // Check for any dynamic translations using the custom trans method
            if (strpos($key, 'fel-assistant-manager.') === 0 &&
                pathinfo($file, PATHINFO_EXTENSION) === 'php') {

                // Extract the tree and key parts
                $parts = explode('.', $key);
                if (count($parts) >= 3) {
                    $tree = $parts[1];
                    $subKey = $parts[2];

                    // Check for the custom trans method with this tree
                    if (strpos($content, "tree = '$tree'") !== false ||
                        strpos($content, "tree = \"$tree\"") !== false) {

                        // Check if the subKey is used as a parameter
                        if (strpos($content, "'$subKey'") !== false ||
                            strpos($content, "\"$subKey\"") !== false) {

                            echo "Found dynamic translation with tree '$tree' for key: $key in file: $file\n";
                            return true;
                        }
                    }
                }
            }

            // Special check for contentEditor.editable keys
            if (strpos($key, 'fel-assistant-manager.contentEditor.editable.') === 0) {
                $keyParts = explode('.', $key);
                $componentName = $keyParts[count($keyParts) - 2] ?? '';

                // Check if the component name is used with getContentEditorTranslation
                if (!empty($componentName) &&
                    (strpos($content, "'$componentName'") !== false ||
                     strpos($content, "\"$componentName\"") !== false) &&
                    strpos($content, 'getContentEditorTranslation') !== false) {

                    echo "Found component name '$componentName' for key: $key in file: $file\n";
                    return true;
                }
            }

            // Special check for Vue.js components using translation keys directly
            if (pathinfo($file, PATHINFO_EXTENSION) === 'js' ||
                pathinfo($file, PATHINFO_EXTENSION) === 'twig' ||
                strpos($file, '.html.twig') !== false) {

                // Check for common Vue.js patterns
                $vuePatterns = [
                    '/return\s*\[\s*{[^}]*[\'"`]' . $escapedKey . '[\'"`][^}]*}\s*\]/', // Array of objects with translation keys
                    '/{\s*[^}]*[\'"`]' . $escapedKey . '[\'"`][^}]*}/', // Object with translation key
                    '/\[\s*[^]]*[\'"`]' . $escapedKey . '[\'"`][^]]*\]/' // Array with translation key
                ];

                foreach ($vuePatterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        echo "Found Vue.js pattern for key: $key in file: $file\n";
                        return true;
                    }
                }

                // Check for the key as part of a computed property or method
                if (strpos($content, 'computed:') !== false ||
                    strpos($content, 'methods:') !== false) {

                    // Simple string search for the key in the file
                    if (strpos($content, $key) !== false) {
                        echo "Found key '$key' in Vue.js component file: $file\n";
                        return true;
                    }
                }
            }

            // Special check for unique translation keys with simple string search
            // Only do this for keys that are unique enough (long and specific)
            if (strlen($key) > 30 && strpos($key, 'fel-assistant-manager.') === 0) {
                // Simple string search for the key in the file
                if (strpos($content, $key) !== false) {
                    echo "Found unique key '$key' using simple string search in file: $file\n";
                    return true;
                }

                // Check for the key without the prefix
                $keyWithoutPrefix = substr($key, strlen('fel-assistant-manager.'));
                if (strpos($content, $keyWithoutPrefix) !== false) {
                    echo "Found unique key part '$keyWithoutPrefix' using simple string search in file: $file\n";
                    return true;
                }
            }
        }

        return false;
    }

    // Check if a dynamic translation is used
    private function isDynamicTranslationUsed($key)
    {
        foreach (array_keys($this->config['dynamicParts']) as $prefix) {
            if (strpos($key, $prefix) === 0) {
                // Extract the dynamic part
                $dynamicKey = str_replace($prefix, '', $key);
                $basePart = explode('.', $dynamicKey)[0];

                // Check if the base part is used
                if ($basePart && $this->isTranslationUsed($basePart)) {
                    return true;
                }
            }
        }

        return false;
    }

    // Remove prefix if configured
    private function removePrefixIfConfigured($key)
    {
        foreach ($this->config['ignore']['removePrefix'] as $prefix) {
            if (strpos($key, $prefix) === 0) {
                return substr($key, strlen($prefix));
            }
        }

        return $key;
    }

    // Check if a translation should be ignored
    private function shouldIgnoreTranslation($key)
    {
        foreach ($this->config['ignore']['prefix'] as $prefix) {
            if (strpos($key, $prefix) === 0) {
                return true;
            }
        }

        return false;
    }

    // Flatten nested translation keys into dot notation
    private function flattenTranslationKeys($array, $prefix = '')
    {
        $keys = [];

        foreach ($array as $key => $value) {
            if (is_numeric($key)) {
                continue; // Skip numeric keys
            }

            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;

            if (is_array($value)) {
                $keys = array_merge($keys, $this->flattenTranslationKeys($value, $newKey));
            } else {
                $keys[] = $newKey;
            }
        }

        return $keys;
    }

    // Track translation usage in summary
    private function incrementComparisonSummary($key)
    {
        $parts = explode('.', $key);
        $prefix = '';

        foreach ($parts as $index => $part) {
            $prefix = $index === 0 ? $part : $prefix . '.' . $part;

            if (!isset($this->comparisonSummary[$prefix])) {
                $this->comparisonSummary[$prefix] = [
                    'found' => 0,
                    'unused' => 0
                ];
            }

            $this->comparisonSummary[$prefix]['found']++;
        }
    }

    // Print summary of analysis
    private function printSummary()
    {
        // Common prefixes that indicate translations used in specific contexts
        $contextPrefixes = [
            'administration' => [
                'fel-assistant-manager.general.',
                'fel-assistant-manager.contentEditor.',
                'fel-assistant-manager.filter.',
                'fel-assistant-manager.log.',
                'fel-assistant-manager.chat.',
                'fel-assistant-manager.chatlog.',
                'fel-assistant-manager.tags-tips.'
            ],
            'storefront' => [
                'fel-assistant-manager.storefront.',
                'fel-assistant-manager.exception.',
                'fel-assistant-manager.catchErrors.',
                'fel-assistant-manager.catchSuccess.',
                'fel-assistant-manager.catchInfo.',
                'fel-assistant-manager.catchWarning.'
            ]
        ];

        // Group unused translations by parent key
        $parentSummary = [];
        $contextUnused = [
            'administration' => [],
            'storefront' => [],
            'unknown' => []
        ];

        foreach ($this->unusedTranslations as $unused) {
            $parts = explode('.', $unused);
            $parentKey = implode('.', array_slice($parts, 0, -1));

            if (!isset($parentSummary[$parentKey])) {
                $parentSummary[$parentKey] = 0;
            }

            $parentSummary[$parentKey]++;

            // Categorize by context
            $contextFound = false;
            foreach ($contextPrefixes as $context => $prefixes) {
                foreach ($prefixes as $prefix) {
                    if (strpos($unused, $prefix) === 0) {
                        $contextUnused[$context][] = $unused;
                        $contextFound = true;
                        break 2;
                    }
                }
            }

            if (!$contextFound) {
                $contextUnused['unknown'][] = $unused;
            }
        }

        // Sort by count (highest first)
        arsort($parentSummary);

        echo "\nUnused Translations by Parent Key:\n";
        echo json_encode($parentSummary, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        echo "\nUnused Translations by Context:\n";
        foreach ($contextUnused as $context => $keys) {
            if (!empty($keys)) {
                echo "  $context: " . count($keys) . " keys\n";
            }
        }

        echo "\nUnused Administration Translations (" . count($contextUnused['administration']) . " total):\n";
        sort($contextUnused['administration']); // Sort alphabetically
        echo json_encode($contextUnused['administration'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        echo "\nUnused Storefront Translations (" . count($contextUnused['storefront']) . " total):\n";
        sort($contextUnused['storefront']); // Sort alphabetically
        echo json_encode($contextUnused['storefront'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        echo "\nUnused Unknown Context Translations (" . count($contextUnused['unknown']) . " total):\n";
        sort($contextUnused['unknown']); // Sort alphabetically
        echo json_encode($contextUnused['unknown'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        echo "\nUnused Translations List (" . count($this->unusedTranslations) . " total):\n";
        sort($this->unusedTranslations); // Sort alphabetically
        echo json_encode($this->unusedTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        // Summary of all translations
        $allSummary = [];
        foreach ($this->comparisonSummary as $key => $summary) {
            if ($summary['found'] > 1) { // Only include non-leaf keys
                $allSummary[$key] = $summary['found'];
            }
        }

        echo "\nAll Translations Summary:\n";
        echo json_encode($allSummary, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        // Statistics
        $totalTranslations = count($this->translationData);
        $unusedCount = count($this->unusedTranslations);
        $usedCount = $totalTranslations - $unusedCount;
        $usagePercentage = $totalTranslations > 0 ? round(($usedCount / $totalTranslations) * 100, 2) : 0;

        echo "\nStatistics:\n";
        echo "Total Translations: $totalTranslations\n";
        echo "Used Translations: $usedCount\n";
        echo "Unused Translations: $unusedCount\n";
        echo "Usage Percentage: $usagePercentage%\n";

        // Context-specific statistics
        echo "\nContext-Specific Statistics:\n";
        foreach ($contextUnused as $context => $keys) {
            $contextPercentage = $totalTranslations > 0 ? round((count($keys) / $totalTranslations) * 100, 2) : 0;
            echo "  $context Unused: " . count($keys) . " (" . $contextPercentage . "%)\n";
        }
    }
}

// Main execution
if ($argc < 2) {
    exit("Usage: ./extract-translations-improved.php [src_directory]\n");
}

$srcDir = $argv[1];

// If the path is to the administration directory, go up to scan the entire plugin
if (strpos($srcDir, 'administration') !== false) {
    $pluginDir = dirname(dirname(dirname($srcDir)));
    if (is_dir($pluginDir) && basename(dirname($pluginDir)) === 'plugins') {
        echo "Detected administration directory. Scanning entire plugin at: $pluginDir\n";
        $srcDir = $pluginDir;
    }
}

$config = [
    'extensions' => ['twig', 'js', 'html.twig', 'json', 'php'],
    'translationFunctions' => ['\$t', 'this\.\$t', 'getContentEditorTranslation', 'felTranslate', 'trans'],
    'translationFilePattern' => '/[a-zA-Z]{2}-[a-zA-Z]{2}\.json$/',
    'ignore' => [
        'prefix' => [
            // Add prefixes to completely ignore
        ],
        'removePrefix' => [
            'fel-assistant-manager.catchErrors.',
            'fel-assistant-manager.catchSuccess.',
            'fel-assistant-manager.catchInfo.',
            'fel-assistant-manager.catchWarning.',
            'fel-assistant-manager.storageManager.cacheName.',
            'fel-assistant-manager.general.aiToolsTranslation.',
            'fel-assistant-manager.filter.ui.',
        ]
    ],
    'dynamicParts' => [
        'fel-assistant-manager.tags-tips.' => [],
        'fel-assistant-manager.contentEditor.editable.' => [],
        'fel-assistant-manager.general.aiToolsTranslation.' => [],
        'fel-assistant-manager.contentEditor.editable.openAiBlacklistCategory.' => []
    ],
    // Patterns for dynamic translation keys in templates
    'dynamicPatterns' => [
        // Template literal with variable interpolation
        '/\$t\(`([^`]+)\$\{([^}]+)\}([^`]*)`\)/',
        // Dynamic key access
        '/\$t\(`([^`]+)\[\'([^\']+)\'\]([^`]*)`\)/',
        '/\$t\(`([^`]+)\["([^"]+)"\]([^`]*)`\)/',
        // Dynamic key with variable
        '/\$t\(`([^`]+)\[\'?\$\{([^}]+)\}\'?\]([^`]*)`\)/',
        '/\$t\(`([^`]+)\["?\$\{([^}]+)\}"?\]([^`]*)`\)/',
        // Helper function with key and type
        '/getContentEditorTranslation\s*\(\s*[\'"`]?([^\'"`,\)]+)[\'"`]?\s*,\s*[\'"`]([^\'"]+)[\'"`]/',
        // Helper function with dynamic key and type
        '/getContentEditorTranslation\s*\(\s*([^,\)]+)\s*,\s*[\'"`]([^\'"]+)[\'"`]/',
        // felTranslate function with quotes
        '/felTranslate\s*\(\s*[\'"]([^\'"]+)[\'"]/',
        // felTranslate function with backticks
        '/felTranslate\s*\(\s*`([^`]+)`/',
        // this.$t with template literals
        '/this\.\$t\s*\(`([^`]+)`\)/',
        // $t with template literals
        '/\$t\s*\(`([^`]+)`\)/'
    ],
    // Function patterns to search for
    'helperFunctions' => [
        'getContentEditorTranslation',
        'felTranslate'
    ]
];

new TranslationExtractor($srcDir, $config);
