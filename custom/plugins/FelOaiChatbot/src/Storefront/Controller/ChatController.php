<?php declare(strict_types=1);

namespace FelOaiChatbot\Storefront\Controller;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Contracts\Translation\TranslatorInterface;
use Exception;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ChatController extends StorefrontController
{
    /**
     * @var SystemConfigService $configService Retrieves system configuration settings
     * @var TranslatorInterface $translator Translator service
     * @var OpenAIChatService $openAIChatService Handles the interaction with OpenAI Assistant API
     */
    public function __construct(
        protected readonly SystemConfigService $configService,
        protected readonly TranslatorInterface $translator,
        protected readonly OpenAIChatService $openAIChatService
    ) {
    }

    /**
     * @param array<string, mixed> $response Standard JSON response helper
     */
    private function felJson(bool $status, array $response, int $code = 200): JsonResponse
    {
        return $this->json([
            'success' => $status,
            'data' => $response,
        ], $code, ['x-robots-tag' => ['noindex', 'nofollow']]);
    }

    /**
     * Create or resume a thread - unified endpoint for thread management
     */
    #[Route(
        path: '/fel/chat/thread',
        name: 'frontend.fel.chat.thread',
        methods: ['POST'],
        defaults: ['XmlHttpRequest' => true]
    )]
    public function handleThread(RequestDataBag $dataBag, SalesChannelContext $salesContext): Response
    {
        $pluginConfig = $this->configService->get('FelOaiChatbot.config');

        $config = [
            'salesChannelContext'  => $salesContext,
            'loadCustomConfig'     => 'FelOaiChatbot',
            'felOpenAiApiKey'      => $pluginConfig['felOpenAiApiKey'] ?? null,
            'felOpenAiAssistantId' => $pluginConfig['felOpenAiAssistantId'] ?? null,
            'inputMaxLength'       => $pluginConfig['openAiUserInputMaxLengthsss'] ?? 400,
        ];


        /**
         * IGNORE
         *
         * TOTAL_DEV mode for faster DEV
         *
         */
        $json = file_get_contents('/var/www/site/shopware.loc/development_mirror/custom/plugins/FelCustomGptAgent/.notes/_z_inc.php');

        if ($json ) {
            sleep(2);
            return $this->json(json_decode($json, true));
        }
        /**
         * TOTAL_DEV mode end
         */



        try {
            if ($dataBag->get('message')) {
                if (!is_string($dataBag->get('message'))) {
                    throw new Exception($this->translator->trans('fel-oai-chat.error.input.is.not.string'));
                }
                if (is_string($dataBag->get('message')) AND $config['inputMaxLength']  < strlen($dataBag->get('message'))) {
                    throw new Exception($this->translator->trans('fel-oai-chat.error.input.max.length', ['%max%' => $config['inputMaxLength'] ]));
                }
            }

            $response = $this->openAIChatService->autoHandleRequest($dataBag, $config);

            // Handle product search rendering if needed
            if ($searchResponse = ($response['requiredActions']['product_search'][0] ?? null)) {
                $rendered = $this->renderView('@Storefront/storefront/component/product-listing.html.twig', [
                    'isSearch' => $products['exec'] ?? null,
                    'productsResponse' => $searchResponse,
                    'products' => $response['requiredActions']['used_products'] ?? [],
                ]);
                $response['renderedProducts'] = $rendered;
            }

            return $this->felJson(true, $response);
        } catch(Exception $e) {
            return $this->felJson(false, [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                '$config' => $config,
            ], 400);
        }
    }

    /**
     * Delete a thread
     */
    #[Route(
        path: '/fel/chat/delete-thread',
        name: 'frontend.fel.chat.delete_thread',
        methods: ['POST'],
        defaults: ['XmlHttpRequest' => true]
    )]
    public function deleteThread(RequestDataBag $dataBag, SalesChannelContext $salesContext): Response
    {
        $config = [
            'salesChannelContext'  => $salesContext,
            'loadCustomConfig'     => 'FelOaiChatbot',
            'felOpenAiApiKey'      => $this->configService->get('FelOaiChatbot.config.felOpenAiApiKey'),
            'felOpenAiAssistantId' => $this->configService->get('FelOaiChatbot.config.felOpenAiAssistantId'),
        ];

        try {
            $dataBag->set('delete_thread', true);
            $response = $this->openAIChatService->autoHandleRequest($dataBag, $config);

            return $this->felJson(true, $response);

        } catch(Exception $e) {
            return $this->felJson(false, [
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ], 400);
        }
    }
}
