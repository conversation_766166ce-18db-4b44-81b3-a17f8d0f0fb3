<?php declare(strict_types=1);

namespace FelOAIAssistantsManager\Service\Utils;

use FelOAIAssistantsManager\Core\Content\AssistantLog\AssistantLogCollection;
use FelOAIAssistantsManager\Core\Content\AssistantLog\AssistantLogEntity; // type
use FelOAIAssistantsManager\Core\Content\AssistantTag\AssistantTagCollection;
use FelOAIAssistantsManager\Core\Content\AssistantTag\AssistantTagEntity; // type
use FelOAIAssistantsManager\Core\Content\AssistantTagUsage\AssistantTagUsageCollection;
use FelOAIAssistantsManager\Core\Content\AssistantTagUsage\AssistantTagUsageEntity;
use FelOAIAssistantsManager\Service\FelConfigService;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Aggregation\Bucket\TermsAggregation;
use Shopware\Core\Framework\DataAbstractionLayer\Search\AggregationResult\Bucket\TermsResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\ContainsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsAnyFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\MultiFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Uuid\Uuid;
use Exception;
use const JSON_ERROR_NONE;
use function array_column;
use function array_filter;
use function array_map;
use function array_reduce;
use function array_slice;
use function array_values;
use function ceil;
use function explode;
use function implode;
use function in_array;
use function is_array;
use function json_decode;
use function json_last_error;
use function sprintf;
use function str_contains;
use function str_starts_with;
use function strlen;
use function strtolower;
use function substr;
use function trim;
use function usort;

class AssistantLogHelper
{
    private ?Context $currentContext = null;

    private bool $countUsageEnabled = false;

    private bool $logSearchQueries = false;

    private bool $isLogWriteAttempt = false;

    public function __construct(
        private readonly FelConfigService $felConfigService,
        /** @var EntityRepository<AssistantLogCollection> */
        private readonly EntityRepository $logRepository,
        /** @var EntityRepository<AssistantTagCollection> */
        private readonly EntityRepository $tagRepository,
        /** @var EntityRepository<AssistantTagUsageCollection> */
        private readonly EntityRepository $tagUsageRepository
    ) {
        $this->countUsageEnabled = (bool) $this->felConfigService->getConfig('felOpenAiEnableLogUsageCounter');
        $this->logSearchQueries = (bool) $this->felConfigService->getConfig('felOpenAiEnableLogSearchQueries');
    }

    public function getCountUsageEnabled(): bool
    {
        return $this->countUsageEnabled;
    }

    public function getLogSearchQueries(): bool
    {
        return $this->logSearchQueries;
    }

    public function setCountUsageEnabled(bool $countUsageEnabled): void
    {
        $this->countUsageEnabled = $countUsageEnabled;
    }

    public function getCurrentContext(): Context
    {
        if (!$this->currentContext) {
            $this->currentContext = Context::createDefaultContext();
        }

        return $this->currentContext;
    }

    public function setCurrentContext(Context $currentContext): self
    {
        $this->currentContext = $currentContext;

        return $this;
    }

    /**
     * @param array<string, mixed> $options internal use only
     * @return array<string, mixed>
     */
    public function getTagsForLogs(?bool $addAliases, array $options = []): array
    {
        $context = $this->getCurrentContext();

        $criteria = (new Criteria())
            ->setLimit($options['limit'] ?? 100)
            ->addFilter(new EqualsFilter('isActive', true))
            ->addSorting(
                new FieldSorting(
                    $options['sort']['col'] ?? 'tagName',
                    $options['sort']['dir'] ?? FieldSorting::ASCENDING,
                )
            );

        /** @var array<string, AssistantTagEntity> */
        $tags = $this->tagRepository->search($criteria, $context)->getElements();

        if ($addAliases) {
            $returnTags = array_reduce($tags, function ($carry, $tag) {
                $carry[$tag->getTagName()] = array_values(array_filter(explode(',', (string) $tag->getTagAlias())));

                return $carry;
            }, []);
            $metaData = ['notification' => [
                'assistant' => 'The response includes tag names and their aliases. Aliases group related terms under a primary tag.',
            ]];
        } else {
            $returnTags = array_column($tags, 'tagName');
        }

        return ['tags' => $returnTags, '_meta_data' => $metaData ?? []];
    }

    /**
     * @param array<string, mixed> $tags
     * @return array<int, mixed>
     */
    public function getFormattedLogsByTags(string $assistantId, array $tags, ?string $query = null, bool $includeMeta = true): array
    {
        /** @var AssistantLogCollection */
        $logs = $this->searchLogsByTags($assistantId, $tags, $query);

        if ($logs->count() === 0 && !empty($tags) && !$query) {
            $logs = $this->searchLogsByTags($assistantId, [], implode(' ', $tags), true);
        }

        $scoredLogEntries = [];
        $queryTerms = $query ? array_filter(explode(' ', strtolower($query))) : [];

        if ($logs->count() > 0 && !empty($queryTerms)) {
            $tempLogData = [];
            foreach ($logs as $log) {
                $score = 0;

                // Haystack for scored search
                $logContent = strtolower(
                    $log->getMessage() . ', ' .
                    $this->decodeAnswerAsString($log->getAnswer() ?? '') . ', ' .
                    ($log->getSearchTerms() ?? '')
                );

                foreach ($queryTerms as $term) {
                    if (str_contains($logContent, $term)) {
                        ++$score;
                    }
                }

                if (str_contains($logContent, strtolower((string) $query))) {
                    $score += count($queryTerms); // Add a bonus
                }

                $tempLogData[] = ['score' => $score, 'log' => $log];
            }

            usort($tempLogData, fn ($a, $b) => $b['score'] <=> $a['score']);

            $scoredLogEntries = array_map(fn ($item) => [
                'tag' => $item['log']->getTag() ? $item['log']->getTag()->getTagName() : $item['log']->getTagId(),
                'message' => $item['log']->getMessage(),
                'answer' => $this->decodeAnswer($item['log']->getAnswer()),
                '_score' => $item['score'],
            ], $tempLogData);
        } else {
            $scoredLogEntries = $logs->map(fn (AssistantLogEntity $log) => [
                'tag' => $log->getTag() ? $log->getTag()->getTagName() : $log->getTagId(),
                'message' => $log->getMessage(),
                'answer' => $this->decodeAnswer($log->getAnswer()),
            ]);
            $scoredLogEntries = array_values($scoredLogEntries);
        }

        $tagsInResult = array_values(array_column($scoredLogEntries, 'tag', 'tag'));

        $r = [];
        if ($includeMeta) {
            $r[] = ['_meta_data' => [
                'messages_total' => $logs->count(),
                'tags_has_content' => sprintf('%s / %s', count($tagsInResult), count($tags)),
                'tags_with_content' => implode(', ', $tagsInResult),
                'scoring_applied' => !empty($queryTerms)
                    ? 'Results scored and sorted by query term relevance.'
                    : 'No query provided, results not scored.',
            ]];
        }

        $limit = 50;
        $scoredLogEntries = array_slice($scoredLogEntries, 0, $limit);

        return [...$r, ...$scoredLogEntries];
    }

    /**
     * @param array<string, mixed> $args
     * @return array<int, mixed>
     */
    public function getUnansweredLogs(array $args): array
    {
        $context = $this->getCurrentContext();
        $limit = $args['limit'] ?? 5;
        $page = $args['page'] ?? 1;
        $offset = ($page - 1) * $limit;

        $criteria = (new Criteria())
            ->setTotalCountMode(1)
            ->setLimit($limit > 20 ? 5 : $limit)
            ->setOffset($offset)
            ->addAssociation('tag')
            ->addFilter(new EqualsFilter('isPrivate', false))
            ->addFilter(
                new MultiFilter(MultiFilter::CONNECTION_OR, [
                    new EqualsFilter('answer', ''),
                    new EqualsFilter('answer', null),
                ])
            );

        $search = $this->logRepository->search($criteria, $context);

        $totalLogs = $search->getTotal();
        $totalPages = (int) ceil($totalLogs / $limit);

        $r = [['_meta_data' => [
            'total' => $totalLogs,
            'page' => $page,
            'limit' => $limit,
            'pages' => $totalPages,
        ]]];

        if ($page > $totalPages || $page < 1) {
            $r[0]['_meta_data']['error'] = ['STATUS' => '404 - PAGE NOT FOUND: ' . $page];
        }

        if ($search->getTotal()) {
            /** @var AssistantLogEntity $value */
            foreach ($search->getElements() as $value) {
                $r[] = [
                    'message' => $value->getMessage(),
                    'answer' => $value->getAnswer(),
                    'tagName' => $value->getTag() ? $value->getTag()->getTagName() : null,
                    'tagAliases' => $value->getTagAliases(),
                    'searchTerms' => $value->getSearchTerms(),
                ];
            }
        } else {
            $r[] = 'No unresolved logs found';
        }

        return $r;
    }

    /**
     * @return array<string, mixed>
     */
    public function setLog(
        string $message,
        string $tag,
        string $priority,
        string $assistantId,
        ?string $salesChannelId,
        ?string $note,
        ?string $tagAliases,
        ?string $searchTerms,
        ?string $answer,
        bool $created = false
    ): array {
        $response = [
            'logged' => false,
            'tagName' => $tag,
            'loggedData' => null,
            'error' => null,
        ];

        try {
            $context = $this->getCurrentContext();
            $tagEntity = $this->searchTagByNameAndAlias($tag, $context)->first();

            if (!$tagEntity instanceof AssistantTagEntity) {
                if (!$created) {
                    $this->createTag($tag, $assistantId, $context, $salesChannelId);

                    return $this->setLog($message, $tag, $priority, $assistantId, $salesChannelId, $note, $tagAliases, $searchTerms, $answer, true);
                }
                throw new Exception('Tag doesn\'t exist and couldn\'t be created: ' . $tag . '. Try with an existing tag again.');
            }

            $this->isLogWriteAttempt = true;
            $this->incrementTagWriteAttempts($tagEntity->getId(), $assistantId, $context);

            if ($this->searchLogsByTagAndMessage($tag, $message, $context)->count()) {
                throw new Exception('The combination of tag and message already exists (Duplicate).');
            }

            if (!in_array($priority, AssistantLogEntity::FEL_ALLOWED_PRIORITIES, true)) {
                $priority = 'medium';
            }

            if (!$note) {
                $note = $tagEntity->getTagName() !== $tag ? 'UsedAlias: ' . $tag : null;
            }

            $this->logRepository->create([[
                'id' => Uuid::randomHex(),
                'tagId' => $tagEntity->getId(),
                'message' => $message,
                'priority' => $priority,
                'assistantId' => $assistantId,
                'salesChannelId' => $salesChannelId,
                'note' => $note,
                'tagAliases' => $tagAliases,
                'searchTerms' => $searchTerms,
                'answer' => $answer,
            ]], $context);

            $response['logged'] = true;
            $response['loggedData'] = ['tagName' => $tagEntity->getTagName(), 'message' => $message];
        } catch (Exception $e) {
            $response['error'] = 'Failed to create assistant log entry: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Usage counter helper for tracking anything, using the tool name and assistantId as identifier.
     *
     * expects: $method,              $assistantId,   $append
     * example: "search_logs_terms", "ASSISTANT_ID", "SEARCH_TERM | Tag_1 | TAG_2"
     *
     * Formats: "search_logs_terms___ASSISTANT_ID::SEARCH_TERM | Tag_1 | TAG_2"
     *
     * - First call triggers storing the formatted term
     * - Repeated equal calls increment the counter
     * - Long strings are automatically truncated to 190 characters
     *
     * @param string $method The tracking method (e.g., 'search_logs_terms')
     * @param string|null $assistantId The assistant ID
     * @param string|null $append Additional data to append (e.g., search terms, tags, aliases, etc.)
     */
    public function countUsageHandler(string $method, ?string $assistantId = null, ?string $append = null): void
    {
        if ($append) {
            $assistantId .= '::' . $append;
        }

        $finalStr = $method . '___' . $assistantId;

        if (strlen($finalStr) > 190) {
            $finalStr = substr($finalStr . '...', 0, 190);
        }

        $this->incrementAny($finalStr, $method);
    }

    // Increment any using the assistantId as identifier. AssistantId is expected a string.
    public function incrementAny(string $assistantIdAsKey, ?string $toolName = null): void
    {
        if ($toolName && in_array($toolName, ['search_logs_terms', 'product_search_terms'], true) && !$this->getLogSearchQueries()) {
            return;
        }

        if (!$this->getCountUsageEnabled()) {
            return;
        }
        if ($this->isLogWriteAttempt) {
            return;
        }

        $context = $this->getCurrentContext();
        $existingUsage = $this->getFirstExistingTagEntity($context, $assistantIdAsKey);

        if ($existingUsage instanceof AssistantTagUsageEntity) {
            $existingUsage->incrementUsageCount();
            $setTotal = $existingUsage->getUsageCount();
        }

        try {
            $this->tagUsageRepository->upsert([[
                'id' => Uuid::fromStringToHex($assistantIdAsKey),
                'assistantId' => $assistantIdAsKey,
                'usageCount' => $setTotal ?? 1,
                'updatedAt' => isset($setTotal) ? new \DateTime() : null,
            ]], $context);
        } catch (Exception $e) {
        }
    }

    /**
     * @param array<string, mixed> $tags
     */
    private function searchLogsByTags(string $assistantId, array $tags, ?string $query = null, bool $retried = false): AssistantLogCollection
    {
        $context = $this->getCurrentContext();
        $tags = array_filter($tags);

        $criteria = (new Criteria())
            ->setTotalCountMode(1)
            ->setLimit(100)
            ->addAssociation('tag')
            ->addFilter(new EqualsFilter('isPrivate', false));

        if ($query !== 'FEL_FULL_FAQ') {
            $criteria->addFilter(new NotFilter(MultiFilter::CONNECTION_AND, [
                new EqualsFilter('message', 'FEL_FULL_FAQ'),
            ]));
        }

        if (!empty($tags)) {
            $tagFilters = [new EqualsAnyFilter('tag.tagName', $tags)];
            foreach ($tags as $tag) {
                $tag = (string) trim($tag);
                if (!empty($tag)) {
                    $tagFilters[] = new ContainsFilter('tag.tagAlias', $tag);
                    $tagFilters[] = new ContainsFilter('tagAliases', $tag);
                }
            }
            $criteria->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, $tagFilters));
        }

        if ($query) {
            $queryFilters = [];
            $spitQuery = explode(' ', $query);

            if (!$retried) {
                if (!empty($tags)) {
                    // Log query with tags: "SEARCH QUERY | TAG | TAG_2"
                    $query = sprintf('%s | %s', $query, implode(' | ', $tags));
                }
                $this->countUsageHandler('search_logs_terms', $assistantId, $query);
            }

            $validTerms = [];
            foreach ($spitQuery as $term) {
                $term = trim((string) $term);
                if (!empty($term)) {
                    $validTerms[] = $term;
                    $queryFilters[] = new MultiFilter(MultiFilter::CONNECTION_OR, [
                        new ContainsFilter('message', $term),
                        new ContainsFilter('answer', $term),
                        new ContainsFilter('searchTerms', $term),
                    ]);
                }
            }

            if (!empty($queryFilters)) {
                $setAndOr = $retried ? MultiFilter::CONNECTION_OR : MultiFilter::CONNECTION_AND;
                $criteria->addFilter(new MultiFilter($setAndOr, $queryFilters));
            }
        }

        if ($this->getCountUsageEnabled()) {
            $criteria->addAggregation(new TermsAggregation('usedTags', 'tagId', 50));
        }

        $searchLogs = $this->logRepository->search($criteria, $context);

        if ($this->getCountUsageEnabled()) {
            /** @var TermsResult|null $usedTagsAggregation */
            $usedTagsAggregation = $searchLogs->getAggregations()->get('usedTags');

            /** @var TermsResult|null $usedTagsAggregation */
            if ($usedTagsAggregation && $usedTagsAggregation->getBuckets()) {
                foreach ($usedTagsAggregation->getBuckets() as $bucket) {
                    $tagId = $bucket->getKey();
                    if ($tagId) {
                        $this->incrementTagUsage($tagId, $assistantId, $context);
                    }
                }
            }
        }

        return $searchLogs->getEntities();
    }

    /**
     * @return string|array<string, mixed>
     */
    private function decodeAnswer(?string $answer): string|array
    {
        if (!$answer || !str_starts_with(trim((string) $answer), '{')) {
            return $answer ?? '';
        }

        $decodedAnswer = json_decode($answer, true);

        return json_last_error() === JSON_ERROR_NONE ? $decodedAnswer : $answer;
    }

    private function decodeAnswerAsString(?string $answer): string
    {
        if (!$answer) {
            return '';
        }

        if (!str_starts_with(trim((string) $answer), '{')) {
            return $answer;
        }

        $decoded = json_decode($answer, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $this->flattenArrayToString($decoded);
        }

        return $answer;
    }

    /**
     * @param array<int, mixed> $data
     *
     * @return string Recursively flattens an array into a string.
     */
    private function flattenArrayToString(array $data): string
    {
        $result = [];
        foreach ($data as $value) {
            if (is_array($value)) {
                $result[] = $this->flattenArrayToString($value);
            } else {
                $result[] = (string) $value;
            }
        }

        return implode(' ', $result);
    }

    /**
     * @return EntitySearchResult<AssistantLogCollection>
     */
    private function searchLogsByTagAndMessage(string $tag, string $message, Context $context): EntitySearchResult
    {
        return $this->logRepository->search(
            (new Criteria())
                ->setLimit(1)
                ->addFilter(new EqualsFilter('message', $message))
                ->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, [
                    new EqualsFilter('tag.tagName', $tag),
                    new ContainsFilter('tag.tagAlias', $tag),
                ])),
            $context
        );
    }

    /**
     * @return EntitySearchResult<AssistantTagCollection>
     */
    private function searchTagByNameAndAlias(string $tag, Context $context): EntitySearchResult
    {
        return $this->tagRepository->search(
            (new Criteria())
                ->addFilter(new MultiFilter(MultiFilter::CONNECTION_OR, [
                    new EqualsFilter('tagName', $tag),
                    new ContainsFilter('tagAlias', $tag),
                ])),
            $context
        );
    }

    private function getFirstExistingTagEntity(Context $context, string $assistantId, ?string $tagId = null): ?AssistantTagUsageEntity
    {
        $criteria = (new Criteria())
            ->addFilter(new EqualsFilter('assistantId', $assistantId));

        if ($tagId) {
            $criteria->addFilter(new EqualsFilter('tagId', $tagId));
        }

        /** @var ?AssistantTagUsageEntity */
        return $this->tagUsageRepository->search($criteria, $context)->first();
    }

    private function createTag(string $tag, ?string $assistantId, Context $context, ?string $salesChannelId): void
    {
        try {
            $this->tagRepository->create([[
                'id' => Uuid::randomHex(),
                'tagName' => $tag,
                'createdBy' => $assistantId ?? 'DefaultAssistant',
                'salesChannelId' => $salesChannelId,
            ]], $context);
        } catch (Exception $e) {
        }
    }

    // count each tag usage
    private function incrementTagUsage(string $tagId, string $assistantId, Context $context): void
    {
        if (!$this->getCountUsageEnabled()) {
            return;
        }
        if ($this->isLogWriteAttempt) {
            return;
        }

        $existingUsage = $this->getFirstExistingTagEntity($context, $assistantId, $tagId);

        if ($existingUsage instanceof AssistantTagUsageEntity) {
            $existingUsage->incrementUsageCount();
            $setTotal = $existingUsage->getUsageCount();
        }

        try {
            $this->tagUsageRepository->upsert([[
                'id' => Uuid::fromStringToHex($assistantId . $tagId),
                'tagId' => $tagId,
                'assistantId' => $assistantId,
                'usageCount' => $setTotal ?? 1,
            ]], $context);
        } catch (Exception $e) { /** ignore */ }
    }

    // count each write attempt
    private function incrementTagWriteAttempts(?string $tagId, string $assistantId, Context $context): void
    {
        if (!$this->getCountUsageEnabled()) {
            return;
        }

        $existingUsage = $this->getFirstExistingTagEntity($context, $assistantId, $tagId);

        if ($existingUsage instanceof AssistantTagUsageEntity) {
            $existingUsage->incrementWriteAttempts();
            $setTotal = $existingUsage->getWriteAttempts();
        }

        try {
            $this->tagUsageRepository->upsert([[
                'id' => Uuid::fromStringToHex($assistantId . $tagId),
                'tagId' => $tagId,
                'assistantId' => $assistantId,
                'writeAttempts' => $setTotal ?? 1,
            ]], $context);
        } catch (Exception $e) {
        }
    }
}
