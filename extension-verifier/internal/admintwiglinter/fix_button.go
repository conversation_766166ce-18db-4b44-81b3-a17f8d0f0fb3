package admintwiglinter

import (
	"fmt"
	"strings"

	"github.com/shopware/extension-verifier/internal/html"
	"github.com/shyim/go-version"
)

type ButtonFixer struct{}

func init() {
	AddFixer(ButtonFixer{})
}

func (b <PERSON>tonFixer) Check(nodes []html.Node) []CheckError {
	// ...existing code...
	var errors []CheckError
	html.TraverseNode(nodes, func(node *html.ElementNode) {
		if node.Tag == "sw-button" {
			errors = append(errors, CheckError{
				Message:    "sw-button is removed, use mt-button instead. Please review conversion for variant and router-link.",
				Severity:   "error",
				Identifier: "sw-button",
				Line:       node.Line,
			})
		}
	})
	return errors
}

func (b ButtonFixer) Supports(v *version.Version) bool {
	// ...existing code...
	return shopware67Constraint.Check(v)
}

func (b <PERSON>tonFixer) Fix(nodes []html.Node) error {
	html.TraverseNode(nodes, func(node *html.ElementNode) {
		if node.Tag == "sw-button" {
			node.Tag = "mt-button"
			var newAttrs html.NodeList
			// Flags to determine additional properties.
			addGhost := false

			for _, attrNode := range node.Attributes {
				// Check if the attribute is an html.Attribute
				if attr, ok := attrNode.(html.Attribute); ok {
					switch attr.Key {
					case "variant":
						lower := strings.ToLower(attr.Value)
						switch lower {
						case "ghost":
							// Remove variant and set ghost.
							addGhost = true
						case "danger":
							// Change value to critical.
							attr.Value = "critical"
							newAttrs = append(newAttrs, attr)
						case "ghost-danger":
							// Set critical and also ghost.
							attr.Value = "critical"
							newAttrs = append(newAttrs, attr)
							addGhost = true
						case "contrast", "context":
							// Remove attribute
						default:
							newAttrs = append(newAttrs, attr)
						}
					case "router-link":
						// Replace with @click event.
						val := attr.Value
						newAttrs = append(newAttrs, html.Attribute{
							Key:   "@click",
							Value: fmt.Sprintf("this.$router.push('%s')", val),
						})
					default:
						newAttrs = append(newAttrs, attr)
					}
				} else {
					// If it's not an html.Attribute (e.g., TwigIfNode), preserve it as is
					newAttrs = append(newAttrs, attrNode)
				}
			}

			if addGhost {
				newAttrs = append(newAttrs, html.Attribute{
					Key: "ghost",
				})
			}
			node.Attributes = newAttrs
		}
	})
	return nil
}
