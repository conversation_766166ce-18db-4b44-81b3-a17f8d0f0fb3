<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\Routing\RouterInterface as Router;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelEntity;

class OpenAIRequiredActionAdapter
{
    protected array $felConfig = [];
    private ?string $cmsAssistantId = null;
    private ?string $salesChannelId = null;
    private ?string $accessKey = null;
    private array $notifications = [];
    private array $storeApiError = [];
    private ?string $storefrontUrl = null;
    private array $swApiHeader = [];
    private float $requestStarted;

    public function __construct(
        private SystemConfigService $configService,
        private HttpClientInterface $httpClient,
        private Router $router,
        private LoggerInterface $logger,
        private EntityRepository $salesChannelRepository
    ) {
        $this->requestStarted = microtime(true);
    }
    
    /**
     * Set Shopware configurations for Store API access
     */
    public function setSwConfigs(
        string $storefrontUrl,
        string $accessKey,
        ?string $currency = null,
        ?string $salesChannelId = null
    ): self {
        $this->storefrontUrl = $storefrontUrl;
        $this->setSwAccessKey($accessKey);
        
        $this->logger->debug('OpenAIRequiredActionAdapter: Set Store API configs', [
            'storefrontUrl' => $storefrontUrl,
            'accessKey' => $accessKey ? 'set' : 'not set',
            'salesChannelId' => $salesChannelId
        ]);
        
        return $this;
    }
    
    /**
     * Set the Store API access key
     */
    private function setSwAccessKey(string $accessKey): self {
        $this->swApiHeader = [
            'accept' => 'application/json',
            'content-type' => 'application/json',
            'sw-access-key' => $accessKey,
        ];
        
        return $this;
    }

    /**
     * @param array<string, mixed> $toolCalls
     * @param array<string, mixed> $post
     * @return array<string, mixed>
     */
    public function requiredAction(array $toolCalls = [], array $post = []): array
    {
        $this->storeApiError = [];
        $this->notifications = [];
        $this->requestStarted = microtime(true);

        $args = [];
        $method = '';

        if (isset($toolCalls[0]['function']['name'])) {
            $method = $toolCalls[0]['function']['name'];
            $args = json_decode($toolCalls[0]['function']['arguments'] ?? '{}', true) ?: [];
        }

        $this->logger->debug('OpenAIRequiredActionAdapter: Executing function', [
            'method' => $method,
            'args' => $args
        ]);

        $result = match ($method) {
            'get_meta_information' => $this->get_meta_information(),
            'get_product_properties' => $this->get_product_properties($args),
            'get_categories' => $this->get_categories($args),
            'get_manufacturer' => $this->get_manufacturer($args),
            'search_products' => $this->search_products($args),
            default => json_encode(['exec' => $method, 'response' => 'Method not implemented']),
        };

        return [
            $toolCalls[0]['id'] => [
                'tool_call_id' => $toolCalls[0]['id'],
                'output' => $result
            ]
        ];
    }

    /**
     * @return array<string, mixed>
     */
    public function getStoreApiError(): array
    {
        return $this->storeApiError;
    }

    /**
     * Get meta information
     *
     * @return string
     */
    private function get_meta_information(): string
    {
        // This would need to be adapted to use the configuration from the adapter
        $metaInfo = $this->configService->get('FelAIProductAdvisor.config.openAiStaticInformationField');
        return json_encode(['exec' => __FUNCTION__, 'response' => $metaInfo]);
    }
    
    /**
     * Get categories
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_categories(array $args = []): array
    {
        $getCategories = $this->get_product_properties($args)['categories'] ?? null;
        
        return ['output' => ['categories' => $getCategories ?: 'No categories found']];
    }
    
    /**
     * Get manufacturers
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function get_manufacturer(array $args = []): array
    {
        $getManufacturer = $this->get_product_properties($args)['properties']['Manufacturer'] ?? null;
        
        return ['output' => ['manufacturer' => $getManufacturer ?: 'No manufacturer found']];
    }
    
    /**
     * Search products
     *
     * @param array<string, mixed> $args
     * @return array<string, mixed>
     */
    private function search_products(array $args = []): array
    {
        $this->logger->debug('OpenAIRequiredActionAdapter: Searching products', [
            'args' => $args
        ]);
        
        try {
            // Get the access key from the OpenAIConfigAdapter
            $accessKey = $this->configService->get('FelOAIAssistantsManager.config.storeApiAccessKey');
            
            if (!$accessKey) {
                // Try to get it from the system config
                $accessKey = $this->configService->get('core.basicInformation.apiAccessKey');
            }
            
            if (!$accessKey) {
                // Try to get it from the sales channel context via the system config
                $accessKey = $this->configService->get('core.basicInformation.accessKey');
            }
            
            if (!$accessKey) {
                // Hard-coded fallback for development
                $accessKey = 'SWSCMVWFJZET2NQRXF5UYWWJWQ';
            }
            
            // Prepare search parameters
            $searchParams = [
                'includes' => [
                    'product' => ['id', 'name', 'description', 'translated', 'available', 'markAsTopseller'],
                    'product_manufacturer' => ['id', 'name', 'translated']
                ],
                'limit' => $args['limit'] ?? 10,
                'page' => $args['page'] ?? 1
            ];
            
            // Add category filter if provided
            if (!empty($args['categories'])) {
                $searchParams['filter'][] = [
                    'type' => 'equalsAny',
                    'field' => 'product.categoryTree',
                    'value' => $args['categories']
                ];
            }
            
            // Add manufacturer filter if provided
            if (!empty($args['manufacturer'])) {
                $searchParams['filter'][] = [
                    'type' => 'equalsAny',
                    'field' => 'product.manufacturerId',
                    'value' => $args['manufacturer']
                ];
            }
            
            // Add search term if provided
            if (!empty($args['query'])) {
                $searchParams['search'] = $args['query'];
            }
            
            // Add price range if provided
            if (!empty($args['price_min']) || !empty($args['price_max'])) {
                $priceFilter = [
                    'type' => 'range',
                    'field' => 'product.price',
                    'parameters' => []
                ];
                
                if (!empty($args['price_min'])) {
                    $priceFilter['parameters']['gte'] = (float)$args['price_min'];
                }
                
                if (!empty($args['price_max'])) {
                    $priceFilter['parameters']['lte'] = (float)$args['price_max'];
                }
                
                $searchParams['filter'][] = $priceFilter;
            }
            
            // Execute search
            $result = $this->storeApiClient('POST', '/product', $searchParams);
            
            if (empty($result['elements'])) {
                return ['output' => ['products' => 'No products found matching your criteria']];
            }
            
            // Process results
            $products = [];
            foreach ($result['elements'] as $product) {
                $products[] = [
                    'id' => $product['id'],
                    'name' => $product['translated']['name'] ?? $product['name'],
                    'manufacturer' => $product['manufacturer']['translated']['name'] ?? $product['manufacturer']['name'] ?? 'Unknown',
                    'description' => substr(strip_tags($product['translated']['description'] ?? $product['description'] ?? ''), 0, 200),
                    'availability' => $product['available'] ? 'In stock' : 'Out of stock',
                    'url' => $this->router->generate('frontend.detail.page', ['productId' => $product['id']]),
                ];
            }
            
            return [
                'output' => [
                    '_meta_data' => [
                        'total' => $result['total'] ?? count($products),
                        'page' => $args['page'] ?? 1,
                        'limit' => $args['limit'] ?? 10,
                        'pages' => ceil(($result['total'] ?? count($products)) / ($args['limit'] ?? 10))
                    ],
                    'products' => $products
                ]
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Error searching products', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return ['output' => ['error' => 'Failed to search products: ' . $e->getMessage()]];
        }
    }

    /**
     * @param array<string, mixed> $args
     * @param bool $fullMap
     * @return array<string, mixed>
     */
    private function get_product_properties(array $args = [], bool $fullMap = false): array
    {
        $this->logger->debug('OpenAIRequiredActionAdapter: Getting product properties', [
            'args' => $args
        ]);
        
        $startTime = microtime(true);
        
        try {
            $response = [
                '_meta_data' => [],
                'properties' => [],
                'categories' => []
            ];
            
            // Fetch categories
            $filter[] = [
                'type' => 'multi',
                'operator' => 'and',
                'queries' => [
                    ['type' => 'equals', 'field' => 'type', 'value' => 'page'],
                    ['type' => 'equals', 'field' => 'active', 'value' => true],
                    ['type' => 'equals', 'field' => 'visible', 'value' => true],
                    ['type' => 'equals', 'field' => 'displayNestedProducts', 'value' => true],
                    [
                        'type' => 'range',
                        'field' => 'level',
                        'parameters' => ['gte' => 1, 'lte' => 15],
                    ],
                ],
            ];
            
            $categoryParams = [
                'total-count-mode' => 'none',
                'buildTree' => false,
                'depth' => 15,
                'filter' => $filter,
                'sort' => [['field' => 'level', 'order' => 'ASC']],
                'includes' => ['category' => ['id', 'name', 'breadcrumb', 'translated', 'level']],
            ];
            
            $fetchCategories = $this->storeApiClient('POST', '/category', $categoryParams)['elements'] ?? [];
            
            // Process categories
            if ($fetchCategories) {
                foreach ($fetchCategories as $i => $category) {
                    if ($breadcrumb = ($category['translated']['breadcrumb'] ?? $category['breadcrumb'] ?? null)) {
                        $name = $category['translated']['name'] ?? $category['name'];
                        
                        if ($fullMap) {
                            $i = $category['id'];
                        }
                        
                        $response['categories'][$i] = [
                            'name' => $name,
                            'parent' => array_slice($breadcrumb, 0, -1),
                            'url' => '/navigation/' . $category['id'],
                            'id' => $category['id'],
                        ];
                    }
                }
                if (!$fullMap) {
                    $response['categories'] = array_values($response['categories']);
                }
            }
            
            // Fetch product properties
            $productParams = [
                'total-count-mode' => 'exact',
                'limit' => 1,
                'includes' => [
                    'product' => ['id'],
                    'product_manufacturer' => ['id', 'name', 'translated'],
                    'property_group' => ['id', 'name', 'translated', 'position', 'displayType', 'filterable', 'visibleOnProductDetailPage'],
                ],
                'aggregations' => [
                    'fel_manufacturer' => [
                        'type' => 'entity',
                        'field' => 'manufacturerId',
                        'definition' => 'product_manufacturer',
                    ],
                    'fel_properties' => [
                        'type' => 'entity',
                        'field' => 'properties.id',
                        'definition' => 'property_group_option',
                    ],
                ],
            ];
            
            $fetch = $this->storeApiClient('POST', '/product', $productParams);
            
            // Process manufacturers
            if ($manufacturers = ($fetch['aggregations']['fel_manufacturer']['entities'] ?? [])) {
                $response['properties']['Manufacturer'] = [];
                $idx = 0;
                
                foreach ($manufacturers as $manufacturer) {
                    if ($name = ($manufacturer['translated']['name'] ?? $manufacturer['name'] ?? null)) {
                        $response['properties']['Manufacturer'][$idx++] = $name;
                    }
                }
            }
            
            // Process property groups
            if ($propertyOptions = ($fetch['aggregations']['fel_properties']['entities'] ?? [])) {
                $groupMap = [];
                
                foreach ($propertyOptions as $entity) {
                    if ($entity['groupId'] ?? null) {
                        $groupMap[$entity['groupId']][] = $entity;
                    }
                }
                
                foreach ($groupMap as $groupId => $options) {
                    $groupEntity = $options[0]['group'] ?? null;
                    
                    if ($groupEntity) {
                        $groupName = $groupEntity['translated']['name'] ?? $groupEntity['name'] ?? null;
                        
                        if ($groupName) {
                            $response['properties'][$groupName] = [];
                            $idx = 0;
                            
                            foreach ($options as $option) {
                                $name = $option['translated']['name'] ?? $option['name'] ?? null;
                                
                                if ($name && $name !== '-' && !in_array($name, $response['properties'][$groupName])) {
                                    $response['properties'][$groupName][$idx++] = $name;
                                }
                            }
                        }
                    }
                }
            }
            
            $response['_meta_data'] = [
                'count' => [
                    'properties' => count($response['properties'], COUNT_RECURSIVE) - count($response['properties']),
                    'categories' => count($response['categories'])
                ],
                'notification' => [],
                'request_took' => number_format(microtime(true) - $startTime, 4, '.') . ' sec'
            ];
            
            $this->logger->debug('OpenAIRequiredActionAdapter: Product properties response', [
                'result' => $response
            ]);
            
            return $response;
        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Error fetching product properties', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Return dummy data as a last resort
            return [
                '_meta_data' => [
                    'count' => [
                        'properties' => 10,
                        'categories' => 3
                    ],
                    'notification' => [],
                    'request_took' => number_format(microtime(true) - $startTime, 4, '.') . ' sec'
                ],
                'properties' => [
                    'Manufacturer' => ['Exo Terra', 'Zoo Med', 'Lucky Reptile'],
                    'Tierart' => ['Gecko', 'Schlange', 'Schildkröte', 'Leguan'],
                    'Futtertyp' => ['Lebendfutter', 'Trockenfutter', 'Frostfutter']
                ],
                'categories' => [
                    ['name' => 'Reptilien', 'parent' => [], 'url' => '#', 'id' => 'dummy-1'],
                    ['name' => 'Futter', 'parent' => ['Reptilien'], 'url' => '#', 'id' => 'dummy-2'],
                    ['name' => 'Zubehör', 'parent' => ['Reptilien'], 'url' => '#', 'id' => 'dummy-3']
                ]
            ];
        }
    }

    /**
     * @param string $method
     * @param string $path
     * @param array<string, mixed> $content
     * @param array<string, mixed>|null $header
     * @return array<string, mixed>
     */
    private function storeApiClient(string $method, string $path, array $content = [], ?array $header = []): array
    {
        try {
            // Use the swApiHeader if available, otherwise create a new one
            if (empty($this->swApiHeader)) {
                // Get the access key from the OpenAIConfigAdapter
                $accessKey = $this->configService->get('FelOAIAssistantsManager.config.storeApiAccessKey');
                
                if (!$accessKey) {
                    // Try to get it from the system config
                    $accessKey = $this->configService->get('core.basicInformation.apiAccessKey');
                }
                
                if (!$accessKey) {
                    // Try to get it from the sales channel context via the system config
                    $accessKey = $this->configService->get('core.basicInformation.accessKey');
                }
                
                if (!$accessKey) {
                    // Hard-coded fallback for development
                    $accessKey = 'SWSCMVWFJZET2NQRXF5UYWWJWQ';
                }
                
                $this->logger->debug('OpenAIRequiredActionAdapter: Store API access key', [
                    'accessKey' => $accessKey ? 'set' : 'not set'
                ]);
                
                // Set the swApiHeader
                $this->setSwAccessKey($accessKey);
            }
            
            $headers = $this->swApiHeader;
            
            if ($header) {
                $headers = array_merge($headers, $header);
            }
            
            // Use storefrontUrl if available, otherwise get it from the config
            if (empty($this->storefrontUrl)) {
                $this->storefrontUrl = $this->configService->get('core.basicInformation.shopUrl');
                if (!$this->storefrontUrl) {
                    $this->storefrontUrl = 'http://localhost';
                }
            }
            
            $this->logger->debug('OpenAIRequiredActionAdapter: Making Store API request', [
                'method' => $method,
                'url' => $this->storefrontUrl . '/store-api/' . ltrim($path, '/'),
                'headers' => array_keys($headers),
                'content' => $content
            ]);
            
            $response = $this->httpClient->request($method, $this->storefrontUrl . '/store-api/' . ltrim($path, '/'), [
                'headers' => $headers,
                'json' => $content
            ]);
            
            $result = $response->toArray();
            
            $this->logger->debug('OpenAIRequiredActionAdapter: Store API response', [
                'status' => $response->getStatusCode(),
                'result' => $result
            ]);
            
            return $result;
        } catch (\Exception $e) {
            $this->logger->error('OpenAIRequiredActionAdapter: Store-API request failed', [
                'exception' => $e->getMessage(),
                'path' => $path,
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->storeApiError['apiException'][] = $e->getMessage();
            
            return [];
        }
    }
}
