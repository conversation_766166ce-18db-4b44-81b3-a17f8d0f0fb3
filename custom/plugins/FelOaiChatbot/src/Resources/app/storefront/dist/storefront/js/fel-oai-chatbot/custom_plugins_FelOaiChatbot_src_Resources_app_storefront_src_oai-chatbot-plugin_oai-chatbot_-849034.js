"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["custom_plugins_FelOaiChatbot_src_Resources_app_storefront_src_oai-chatbot-plugin_oai-chatbot_-849034"],{857:t=>{var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==s},s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function a(t,e){return!1!==e.clone&&e.isMergeableObject(t)?n(Array.isArray(t)?[]:{},t,e):t}function i(t,e,s){return t.concat(e).map(function(t){return a(t,s)})}function o(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function r(t,e){try{return e in t}catch(t){return!1}}function n(t,s,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=a;var c,h,d=Array.isArray(s);return d!==Array.isArray(t)?a(s,l):d?l.arrayMerge(t,s,l):(h={},(c=l).isMergeableObject(t)&&o(t).forEach(function(e){h[e]=a(t[e],c)}),o(s).forEach(function(e){(!r(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(r(t,e)&&c.isMergeableObject(s[e])?h[e]=(function(t,e){if(!e.customMerge)return n;var s=e.customMerge(t);return"function"==typeof s?s:n})(e,c)(t[e],s[e],c):h[e]=a(s[e],c))}),h)}n.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,s){return n(t,s,e)},{})},t.exports=n},987:(t,e,s)=>{s.r(e),s.d(e,{default:()=>_});class a{static debounce(t,e){let s,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){for(var i=arguments.length,o=Array(i),r=0;r<i;r++)o[r]=arguments[r];a&&!s&&setTimeout(t.bind(t,...o),0),clearTimeout(s),s=setTimeout(t.bind(t,...o),e)}}}var i=s(49),o=s(266);class r{static isSupported(){return"undefined"!==document.cookie}static setItem(t,e,s){if(null==t)throw Error("You must specify a key to set a cookie");let a=new Date;a.setTime(a.getTime()+864e5*s);let i="";"https:"===location.protocol&&(i="secure"),document.cookie="".concat(t,"=").concat(e,";expires=").concat(a.toUTCString(),";path=/;sameSite=lax;").concat(i)}static getItem(t){if(!t)return!1;let e=t+"=",s=document.cookie.split(";");for(let t=0;t<s.length;t++){let a=s[t];for(;" "===a.charAt(0);)a=a.substring(1);if(0===a.indexOf(e))return a.substring(e.length,a.length)}return!1}static removeItem(t){document.cookie="".concat(t,"= ; expires = Thu, 01 Jan 1970 00:00:00 GMT;path=/")}static key(){return""}static clear(){}}class n{setItem(t,e){return this._storage[t]=e}getItem(t){return Object.prototype.hasOwnProperty.call(this._storage,t)?this._storage[t]:null}removeItem(t){return delete this._storage[t]}key(t){return Object.values(this._storage)[t]||null}clear(){return this._storage={}}constructor(){this._storage={}}}class l{_chooseStorage(){return l._isSupported(window.localStorage)?this._storage=window.localStorage:l._isSupported(window.sessionStorage)?this._storage=window.sessionStorage:r.isSupported()?this._storage=r:this._storage=new n}static _isSupported(t){try{let e="__storage_test";return t.setItem(e,"1"),t.removeItem(e),!0}catch(t){return!1}}_validateStorage(){if("function"!=typeof this._storage.setItem)throw Error('The storage must have a "setItem" function');if("function"!=typeof this._storage.getItem)throw Error('The storage must have a "getItem" function');if("function"!=typeof this._storage.removeItem)throw Error('The storage must have a "removeItem" function');if("function"!=typeof this._storage.key)throw Error('The storage must have a "key" function');if("function"!=typeof this._storage.clear)throw Error('The storage must have a "clear" function')}getStorage(){return this._storage}constructor(){this._storage=null,this._chooseStorage(),this._validateStorage()}}let c=Object.freeze(new l).getStorage();var h=s(857);s(140);class d{publish(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=new CustomEvent(t,{detail:e,cancelable:s});return this.el.dispatchEvent(a),a}subscribe(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=this,i=t.split("."),o=s.scope?e.bind(s.scope):e;if(s.once&&!0===s.once){let e=o;o=function(s){a.unsubscribe(t),e(s)}}return this.el.addEventListener(i[0],o),this.listeners.push({splitEventName:i,opts:s,cb:o}),!0}unsubscribe(t){let e=t.split(".");return this.listeners=this.listeners.reduce((t,s)=>([...s.splitEventName].sort().toString()===e.sort().toString()?this.el.removeEventListener(s.splitEventName[0],s.cb):t.push(s),t),[]),!0}reset(){return this.listeners.forEach(t=>{this.el.removeEventListener(t.splitEventName[0],t.cb)}),this.listeners=[],!0}get el(){return this._el}set el(t){this._el=t}get listeners(){return this._listeners}set listeners(t){this._listeners=t}constructor(t=document){this._el=t,t.$emitter=this,this._listeners=[]}}class u{static isTouchDevice(){return"ontouchstart"in document.documentElement}static isIOSDevice(){return u.isIPhoneDevice()||u.isIPadDevice()}static isNativeWindowsBrowser(){return u.isIEBrowser()||u.isEdgeBrowser()}static isIPhoneDevice(){return!!navigator.userAgent.match(/iPhone/i)}static isIPadDevice(){return!!navigator.userAgent.match(/iPad/i)}static isIEBrowser(){return -1!==navigator.userAgent.toLowerCase().indexOf("msie")||!!navigator.userAgent.match(/Trident.*rv:\d+\./)}static isEdgeBrowser(){return!!navigator.userAgent.match(/Edge\/\d+/i)}static getList(){return{"is-touch":u.isTouchDevice(),"is-ios":u.isIOSDevice(),"is-native-windows":u.isNativeWindowsBrowser(),"is-iphone":u.isIPhoneDevice(),"is-ipad":u.isIPadDevice(),"is-ie":u.isIEBrowser(),"is-edge":u.isEdgeBrowser()}}}let g="offcanvas";class p{open(t,e,s,a,i,o,r){this._removeExistingOffCanvas();let n=this._createOffCanvas(s,o,r,a);this.setContent(t,a,i),this._openOffcanvas(n,e)}setContent(t,e){let s=this.getOffCanvas();s[0]&&(s[0].innerHTML=t,this._registerEvents(e))}setAdditionalClassName(t){this.getOffCanvas()[0].classList.add(t)}getOffCanvas(){return document.querySelectorAll(".".concat(g))}close(t){let e=this.getOffCanvas();o.Z.iterate(e,t=>{bootstrap.Offcanvas.getInstance(t).hide()}),setTimeout(()=>{this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:e})},t)}goBackInHistory(){window.history.back()}exists(){return this.getOffCanvas().length>0}_openOffcanvas(t,e){p.bsOffcanvas.show(),window.history.pushState("offcanvas-open",""),"function"==typeof e&&e()}_registerEvents(t){let e=u.isTouchDevice()?"touchend":"click",s=this.getOffCanvas();o.Z.iterate(s,e=>{let a=()=>{setTimeout(()=>{e.remove(),this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:s})},t),e.removeEventListener("hide.bs.offcanvas",a)};e.addEventListener("hide.bs.offcanvas",a)}),window.addEventListener("popstate",this.close.bind(this,t),{once:!0});let a=document.querySelectorAll(".".concat("js-offcanvas-close"));o.Z.iterate(a,s=>s.addEventListener(e,this.close.bind(this,t)))}_removeExistingOffCanvas(){p.bsOffcanvas=null;let t=this.getOffCanvas();return o.Z.iterate(t,t=>t.remove())}_getPositionClass(t){return"left"===t?"offcanvas-start":"right"===t?"offcanvas-end":"offcanvas-".concat(t)}_createOffCanvas(t,e,s,a){let i=document.createElement("div");if(i.classList.add(g),i.classList.add(this._getPositionClass(t)),!0===e&&i.classList.add("is-fullwidth"),s){let t=typeof s;if("string"===t)i.classList.add(s);else if(Array.isArray(s))s.forEach(t=>{i.classList.add(t)});else throw Error('The type "'.concat(t,'" is not supported. Please pass an array or a string.'))}return document.body.appendChild(i),p.bsOffcanvas=new bootstrap.Offcanvas(i,{backdrop:!1!==a||"static"}),i}constructor(){this.$emitter=new d}}let f=Object.freeze(new p);class v{static open(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"left",a=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:350,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"";f.open(t,e,s,a,i,o,r)}static setContent(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:350;f.setContent(t,e,s)}static setAdditionalClassName(t){f.setAdditionalClassName(t)}static close(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:350;f.close(t)}static exists(){return f.exists()}static getOffCanvas(){return f.getOffCanvas()}static REMOVE_OFF_CANVAS_DELAY(){return 350}}class m{get(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/json",a=this._createPreparedRequest("GET",t,s);return this._sendRequest(a,null,e)}post(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("POST",t,a);return this._sendRequest(i,e,s)}delete(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("DELETE",t,a);return this._sendRequest(i,e,s)}patch(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("PATCH",t,a);return this._sendRequest(i,e,s)}abort(){if(this._request)return this._request.abort()}setErrorHandlingInternal(t){this._errorHandlingInternal=t}_registerOnLoaded(t,e){e&&(!0===this._errorHandlingInternal?(t.addEventListener("load",()=>{e(t.responseText,t)}),t.addEventListener("abort",()=>{console.warn("the request to ".concat(t.responseURL," was aborted"))}),t.addEventListener("error",()=>{console.warn("the request to ".concat(t.responseURL," failed with status ").concat(t.status))}),t.addEventListener("timeout",()=>{console.warn("the request to ".concat(t.responseURL," timed out"))})):t.addEventListener("loadend",()=>{e(t.responseText,t)}))}_sendRequest(t,e,s){return this._registerOnLoaded(t,s),t.send(e),t}_getContentType(t,e){return t instanceof FormData&&(e=!1),e}_createPreparedRequest(t,e,s){return this._request=new XMLHttpRequest,this._request.open(t,e),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),s&&this._request.setRequestHeader("Content-type",s),this._request}constructor(){this._request=null,this._errorHandlingInternal=!1}}let b="loader",E={BEFORE:"before",INNER:"inner"};class C{create(){if(!this.exists()){if(this.position===E.INNER){this.parent.innerHTML=C.getTemplate();return}this.parent.insertAdjacentHTML(this._getPosition(),C.getTemplate())}}remove(){let t=this.parent.querySelectorAll(".".concat(b));o.Z.iterate(t,t=>t.remove())}exists(){return this.parent.querySelectorAll(".".concat(b)).length>0}_getPosition(){return this.position===E.BEFORE?"afterbegin":"beforeend"}static getTemplate(){return'<div class="'.concat(b,'" role="status">\n                    <span class="').concat("visually-hidden",'">Loading...</span>\n                </div>')}static SELECTOR_CLASS(){return b}constructor(t,e=E.BEFORE){this.parent=t instanceof Element?t:document.body.querySelector(t),this.position=e}}let w=null;class L extends v{static open(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"left",i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:v.REMOVE_OFF_CANVAS_DELAY(),r=arguments.length>6&&void 0!==arguments[6]&&arguments[6],n=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"";if(!t)throw Error("A url must be given!");f._removeExistingOffCanvas();let l=f._createOffCanvas(a,r,n,i);this.setContent(t,e,s,i,o),f._openOffcanvas(l)}static setContent(t,e,s,a,i){let o=new m;super.setContent('<div class="offcanvas-body">'.concat(C.getTemplate(),"</div>"),a,i),w&&w.abort();let r=t=>{super.setContent(t,a,i),"function"==typeof s&&s(t)};w=e?o.post(t,e,L.executeCallback.bind(this,r)):o.get(t,L.executeCallback.bind(this,r))}static executeCallback(t,e){"function"==typeof t&&t(e),window.PluginManager.initializePlugins()}}let I="element-loader-backdrop";class y extends C{static create(t){t.classList.add("has-element-loader"),y.exists(t)||(y.appendLoader(t),setTimeout(()=>{let e=t.querySelector(".".concat(I));e&&e.classList.add("element-loader-backdrop-open")},1))}static remove(t){t.classList.remove("has-element-loader");let e=t.querySelector(".".concat(I));e&&e.remove()}static exists(t){return t.querySelectorAll(".".concat(I)).length>0}static getTemplate(){return'\n        <div class="'.concat(I,'">\n            <div class="loader" role="status">\n                <span class="').concat("visually-hidden",'">Loading...</span>\n            </div>\n        </div>\n        ')}static appendLoader(t){t.insertAdjacentHTML("beforeend",y.getTemplate())}}let T="CookieConfiguration_Update";window.router["frontend.cookie.offcanvas"];class _ extends window.PluginBaseClass{init(){window.Debouncer=a,window.DomAccess=i.Z,window.Iterator=o.Z,window.CookieStorageHelper=r,window.Storage=c,window.COOKIE_CONFIGURATION_UPDATE=T,window.HttpClient=m,window.FelOaiChatbotPlugin=this,window.$emitter=this.$emitter}registerEvents(){this.handleEvent=t=>{if(this.isLoading&&!t.target.classList.contains("fel-ui-btn"))return t.preventDefault(),!1;switch(t.type){case"submit":return this.chatEventCallback(t,t.target);case"change":return this.changeEventCallback(t,t.target);case"click":return this.clickEventCallback(t,t.target)}},this.setEventHandler("add"),this.setFromLastVisit(),this.swEventHelper(),this.highlightActiveProductsInChat()}setFromLastVisit(){if(this.setLocalStorageIsAllowed(),this.setLastChatZoom(),"active"===this.storageGet("".concat(this.options.storage.chat,"_chatOpen"))){var t=this.assistantEl.querySelector(".fel-toggle-chatbot-button.toggle-open");t&&t.dispatchEvent(new MouseEvent("click",{bubbles:!0}))}}swEventHelper(){document.$emitter.subscribe(T,t=>this.cookieCallback(t));let t=window.PluginManager.getPluginInstances("ScrollUp");t.length&&this.fixToggleChatScrollUpBtn(t)}fixToggleChatScrollUpBtn(t){var e;let s=(e=t[0].el)===null||void 0===e?void 0:e.firstElementChild;if(s){let e=window.getComputedStyle(s),a=this.felParseInt(e.right),i=a+this.felParseInt(e.width)+3,o=s.classList.contains("is-visible")?i:a,r=this.assistantEl;r.querySelector(".fel-toggle-chatbot-button.toggle-open").style.height=e.height,r.style.bottom=r.classList.contains("active")?null:e.bottom,r.dataset.setBottom=e.bottom,!1===this.options.layout.scrollUpIsLeft&&(r.classList.contains("active")||(r.style.right=o+"px",r.dataset.setRight=o+"px"),t[0].$emitter.subscribe("toggleVisibility",()=>{let t=s.classList.contains("is-visible")?i:a;r.style.right=r.classList.contains("active")?null:t+"px",r.dataset.setRight=t+"px"}))}}checkIfLocalStorageIsAllowed(){return this.cookieAccepted?this.cookieAccepted:"allowed"===r.getItem(this.options.selector.cookieDefaultName)}setLocalStorageIsAllowed(){let t=this.checkIfLocalStorageIsAllowed();t?(this.assistantEl.classList.add("fel-localstorage-is-allowed"),this.assistantEl.classList.remove("fel-localstorage-is-disabled"),this.assistantEl.classList.contains("active")&&this.storageSet("".concat(this.options.storage.chat,"_chatOpen"),"active"),this.assistantEl.classList.contains("fel-zoom-chat")&&this.storageSet("".concat(this.options.storage.chat,"_chatZoom"),"active")):(this.assistantEl.classList.add("fel-localstorage-is-disabled"),this.assistantEl.classList.remove("fel-localstorage-is-allowed"),this.clearLocalStorage()),this.setCheckedConsentLocalStorageTemplate(t)}cookieCallback(t){var e;let s=this.options.selector.cookieDefaultName;(null==t?void 0:t.detail)&&void 0!==((e=t.detail)===null||void 0===e?void 0:e[s])&&(this.cookieAccepted=t.detail[s]),this.setLocalStorageIsAllowed()}setCheckedConsentLocalStorageTemplate(t){let e=this.template.greeting.querySelector('[name="fel-allow-localstorage"]'),s=this.assistantEl.querySelector('[name="fel-allow-localstorage"]');e&&(e.checked=!!t||null),s&&(s.checked=!!t||null)}toggleLocalStorage(t,e){return this.cookieAccepted=e.checked,r.setItem(this.options.selector.cookieDefaultName,!!this.cookieAccepted&&"allowed"),this.setLocalStorageIsAllowed(),e.blur()}chatEventCallback(t,e){try{return this._chatEventCallback(t,e)}catch(t){return this.putToMessages("chatbot",e.message||"error")}}checkIfMessageThrown(t){return!!t.error&&void 0!==t.error.exception&&t.error.exception}_chatEventCallback(t,e){if("fel-chatbot-form"===e.id){if(t.preventDefault(),this.getChatbotUserInput()){let t={threadId:this.getChatbotThreadId().value,runId:this.getChatbotRunId().value,userInput:this.getChatbotUserInput().value};""===t.runId?(this.isLoadingResponse=!1,this.clientPost("".concat(this.controllerUrl,"/create-thread"),t,e=>{if(e){if(this.checkIfMessageThrown(e))return this.putToMessages("chatbot",this.checkIfMessageThrown(e),"",""," fel-system-exception");if(void 0!==e.id)return this.$emitter&&this.$emitter.publish("felAssistantThreadCreated"),this.threadId=e.id,this.putToMessages("user",t.userInput,this.threadId,e.datetime),this.getChatbotUserInput().value="",this.getChatbotRunId().value=e.runId,this.getChatbotThreadId().value=e.threadId,this.assistantEl.classList.add("contains-thread"),this.chatbotForm.dispatchEvent(new Event("submit",{bubbles:!0,cancelable:!0}))}})):this.threadId&&(this.isLoadingResponse=!0,this.getChatbotRunId().value="",this.scrollThreadIntoView(!0),this.clientPost("".concat(this.controllerUrl,"/run"),t,t=>{if(t){if(this.checkIfMessageThrown(t))this.putToMessages("chatbot",this.checkIfMessageThrown(t),"",""," fel-system-exception");else if(void 0!==t.id){if(void 0!==t.threadMessages){let e=t.threadMessages,s=null;if(void 0!==e[0]&&void 0!==e[0].role&&"assistant"===e[0].role){let a=!0;this.foreach(e,(e,i)=>{if("user"===i.role&&(a=!1),a){if(void 0!==t.uiActionRequired.product_search)void 0!==i.originalValue&&(i=this.replaceAiProductList(i,t.uiActionRequired.product_search)),0===t.uiActionRequired.product_search.total&&(s=t.uiActionRequired.product_search);else if(!i.value.includes("<p")&&!i.value.includes("<ul")){let t=document.createElement("div");t.classList.add("fel-text-plain","text-break"),t.insertAdjacentHTML("beforeend",i.value.trim()),i.value=t.outerHTML}if(this.putToMessages("chatbot",i.value,t.id,t.datetime),s){let e=[];this.foreach(s.args,(t,s)=>{if("_truncate"!==t)try{["object","array","iterator"].includes(typeof s)&&(s=s.join(", ")),e.push("".concat(t,": ").concat(JSON.stringify(s)))}catch(t){}});let a=document.createElement("p"),i=document.createElement("h2");a.classList.add("fel-text-plain","text-break"),a.innerHTML=e.join("\n"),i.innerHTML=this.options.translation.usedArguments,this.putToMessages("chatbot",i.outerHTML+a.outerHTML,"",t.datetime," fel-search-failed-props")}}})}else this.putToMessages("chatbot",this.options.translation.errorClearChat,"",t.datetime)}this.getChatbotUserInput().focus(),this.chatToStorage(t.threadId),void 0!==t.uiActionRequired&&t.uiActionRequired&&this.uiActionRequired(t.uiActionRequired)}}this.$emitter&&this.$emitter.publish("FelOaiChatbotMessageResponse")}))}return!1}}replaceAiProductList(t,e){let s=document.createElement("div"),a=document.createElement("div");if(s.insertAdjacentHTML("beforeend",t.originalValue),a.insertAdjacentHTML("beforeend",t.value),s.querySelector("ul")){let n=s.querySelectorAll("ul");if(n.length){var i,o;if(1===n.length&&(null==e?void 0:(o=e.items)===null||void 0===o?void 0:(i=o.products)===null||void 0===i?void 0:i.length)){for(var r=0;r<e.items.products.length;r++)if(t.originalValue.includes(e.items.products[r].id)){s.querySelector("ul").replaceWith(a),t.value=s.innerHTML;break}}else t.value=t.originalValue}}return t}uiActionRequired(t){void 0!==t.redirectTo&&t.redirectTo&&t.redirectTo!==location.href&&(location.href=t.redirectTo)}deleteChatThread(){var t,e=(t=this.getChatbotThreadId())===null||void 0===t?void 0:t.value;e?(this.addIsLoading(),this._client.post("".concat(this.controllerUrl,"/delete-thread"),JSON.stringify({threadId:e}),t=>{if(this.removeIsLoading(),t){try{t=JSON.parse(t)}catch(t){}this.assistantEl.classList.remove("contains-thread"),this.getChatbotRunId().value="",this.getChatbotThreadId().value="",this.chatClearStorage(),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML,this.checkIfLocalStorageIsAllowed()&&this.setCheckedConsentLocalStorageTemplate(!0)),this.getChatbotUserInput().focus()}})):(this.chatClearStorage(),this.assistantEl.classList.remove("contains-thread"),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML,this.checkIfLocalStorageIsAllowed()&&this.setCheckedConsentLocalStorageTemplate(!1)))}clientPost(t,e,s){this.addIsLoading(),this._client.post(t,JSON.stringify(e),t=>{if(this.removeIsLoading(),t)try{let e=JSON.parse(t);e&&(t=e)}catch(t){var e=document.createElement("div");return e.appendChild(document.createTextNode(t)),s({error:{exception:e.innerHTML.substring(0,200)}})}return s(t)})}_scrollIntoView(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,s="object"==typeof t?t:this.domGet(this.assistantEl,t,!1);s&&this.debounce(()=>{s.scrollIntoView({behavior:this.options.behavior,block:"start"})},e)}scrollThreadIntoView(t){if(!t&&!this.options.compactView){let t=this.getActiveProductsInChat();if(t.length)return this._scrollIntoView(t[0],150)}let e=this.domGetAll(this.assistantEl,".user-message-item",!1);e.length&&this._scrollIntoView(e[e.length-1],200)}addIsLoading(){let t="fel-chat-loader-temp-id",e=this.template.loading;if(this.isLoading=!0,this.assistantEl.classList.add("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=!0,this.getChatbotUserInput().disabled=!0),this.removeGreetingContainer(),this.isLoadingResponse){var s=e.cloneNode(!0),a=this.domGet(s,".fel-chatbot-loader-info",!1);s.id=t,a&&a.dataset.secondRun&&(this.domGet(s,".fel-chatbot-loader-info").innerHTML=a.dataset.secondRun),e=s}this.putToMessages("loading",e.outerHTML,t)}removeIsLoading(){this.isLoading=!1,this.assistantEl.classList.remove("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=null,this.getChatbotUserInput().disabled=null);let t=this.domGetAll(this.assistantEl,".fel-chatbot-load-container",!1);if(t.length)for(let e=0;e<t.length;e++)t[e].classList.add("position-absolute","w-100"),this.debounce(()=>{t[e].remove()},150)}removeGreetingContainer(){var t=this.domGet(this.assistantEl,this.options.selector.greetingContainer,!1);t&&(t.remove(),this.assistantEl.classList.remove("fel-chat-initial"))}getChatSubmitButton(){return this.domGet(this.assistantEl,this.options.selector.submitChatButton,!1)}getChatbotMessagesEl(){return this.domGet(this.assistantEl,this.options.selector.chatMessages,!1)}getChatbotUserInput(){return this.domGet(this.chatbotForm,this.options.selector.chatUserInput,!1)}getChatbotThreadId(){return this.domGet(this.chatbotForm,this.options.selector.chatThreadId,!1)}getChatbotRunId(){return this.domGet(this.chatbotForm,this.options.selector.chatRunId,!1)}addToChatSubmit(t,e){let s=e.dataset.prompt;s&&(this.getChatbotUserInput().value=s,this.getChatSubmitButton().dispatchEvent(new MouseEvent("click",{bubbles:!0,cancelable:!0})))}toggleChatbot(t,e){let s=e.dataset.toggleClass;if(s){this.assistantEl.classList.toggle(s);let t="".concat(this.options.storage.chat,"_chatOpen");this.assistantEl.classList.contains("active")?(this.assistantEl.style.right=null,this.assistantEl.style.bottom=null,this.storageSet(t,"active"),this.scrollThreadIntoView(),this.getChatbotUserInput()&&this.getChatbotUserInput().focus(),this._addBackdrop()):(this.assistantEl.style.right=this.assistantEl.dataset.setRight,this.assistantEl.style.bottom=this.assistantEl.dataset.setBottom,this.storageSet(t,"not-active"),this._removeBackdrop())}}chatToggleProperties(t){t.target.parentNode.classList.toggle("fel-show-properties")}chatToggleSearchArgs(t){let e=t.target,s=this.domGet(this.assistantEl,e.dataset.targetEl,!1);s&&(e.dataset.toggleClass.split(",").map(t=>s.classList.toggle(t)),e.dataset.toggleSelf.split(",").map(t=>e.classList.toggle(t)))}chatWorkerFixProperties(t){let e=t.target,s=e.dataset.shortPrompt,a=e.dataset.sharedProp,i=e.dataset.targetId,o=e.dataset.targetSelector;if(s&&a){let t=this.domGetAll(this.assistantEl,"".concat(i," ").concat(o),!1);e.classList.contains("active")?e.classList.remove("active"):e.classList.toggle("active");for(var r=0;r<t.length;r++)e!==t[r]&&(t[r].disabled=!!e.classList.contains("active")||null);let s=this.domGet(this.assistantEl,this.options.selector.chatUserInput,!1),a=(()=>{let t={shortPrompts:[],shortPromptsEnd:null},e=this.domGetAll(this.assistantEl,"".concat(i," [data-shared-prop].active"),!1);if(e.length)for(var s=0;s<e.length;s++)t.shortPromptsEnd||(t.shortPromptsEnd=e[s].dataset.shortPromptEnd),t.shortPrompts.push(e[s].dataset.shortPrompt);return t})();if(s){let t=s.getAttribute("maxlength")||50;if(s.value="",a.shortPrompts.length){let e="".concat(a.shortPrompts.join(", ")," ").concat(a.shortPromptsEnd);e.length>t&&(e=e.slice(0,t)),s.value=e}}}}_addBackdrop(){this.options.compactView&&this.putHtml(this.assistantEl,"afterend",'\n				<div class="modal-backdrop fel-modal-backdrop fel-fade-in show" onclick="this.remove()"></div>\n			')}_removeBackdrop(){let t=document.body.querySelector(".fel-modal-backdrop");t&&t.remove()}toggleChatZoom(t,e){if(e.classList.toggle("fel-active"),this.assistantEl){let t="".concat(this.options.storage.chat,"_chatZoom");e.classList.contains("fel-active")?(this.assistantEl.classList.add("fel-zoom-chat"),this.storageSet(t,"active")):(this.assistantEl.classList.remove("fel-zoom-chat"),this.storageSet(t,"not-active")),this.getChatbotUserInput()&&this.getChatbotUserInput().focus()}}setLastChatZoom(){if("active"===this.storageGet("".concat(this.options.storage.chat,"_chatZoom"))){let t=this.assistantEl.querySelector(".fel-zoom-button");t&&(t.classList.add("fel-active"),this.assistantEl.classList.add("fel-zoom-chat"))}}removeChatMessage(t){var e,s;if((s=t.target)===null||void 0===s?void 0:(e=s.offsetParent)===null||void 0===e?void 0:e.offsetParent){let e=t.target.offsetParent.offsetParent;e&&(e.previousElementSibling.classList.contains("user-message-item")&&e.previousElementSibling.remove(),e.remove(),this.chatToStorage(this.getChatbotThreadId().value),""==this.storageGet(this.options.storage.chat).trim()&&this.deleteChatThread(),this.scrollThreadIntoView())}}clickEventCallback(t,e){if(!e.classList.contains("fel-btn"))return;let s=e.dataset.callback;if(e.dataset.preventDefault&&t.preventDefault(),e.dataset.stopPropagation&&t.stopPropagation(),e.dataset.blur&&e.blur(),s){if("function"==typeof this[s])return this[s](...arguments);if("function"==typeof window[s])return window[s](...arguments)}}changeEventCallback(t,e){if(e.classList.contains("fel-checkbox")){t.preventDefault();let s=e.dataset.callback;"function"==typeof this[s]&&this[s](t,e)}return!1}putToMessages(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o="";s&&(s=s.slice(4),o=' id="f-'.concat(s,'"')),a&&(a=' data-datetime="'.concat(a,'"'));let r="chatbot"===t?'<div class="fel-delete-message">\n				<span title="'.concat(this.options.translation.removeChatMessage,'"\n					class="btn fel-btn" data-callback="removeChatMessage">X</span>\n				</div>'):"",n=["loading","greeting","append"].includes(t)?e:'<div class="'.concat(this.options.selector.chatMessageEl," ").concat(t,'-message-item fel-fade-in d-flex"').concat(o,'>\n				<div class="').concat(this.options.selector.chatMessage," ").concat(t,"-message").concat(i,'">\n					<div class="fel-inner"').concat(a,">").concat(r," ").concat(e,"</div>\n				</div>\n			</div>");this.putHtml(this.getChatbotMessagesEl(),"beforeend",n)}domGet(){return i.Z.querySelector(...arguments)}domGetAll(){return i.Z.querySelectorAll(...arguments)}storageGet(){return c.getItem(...arguments)}storageSet(){this.checkIfLocalStorageIsAllowed()&&c.setItem(...arguments)}storageRemove(){c.removeItem(...arguments)}chatClearStorage(){this.storageRemove(this.options.storage.chat),this.storageRemove("".concat(this.options.storage.chat,"_threadId"))}chatToStorage(t){let e=this.getChatbotMessagesEl();if(e){let s=this.domGetAll(e,this.options.selector.loadingContainer,!1);s.length&&this.foreach(s,(t,e)=>{e.remove()}),this.storageSet(this.options.storage.chat,e.innerHTML),this.storageSet("".concat(this.options.storage.chat,"_threadId"),t)}}clearLocalStorage(){this.storageRemove("".concat(this.options.storage.chat)),this.storageRemove("".concat(this.options.storage.chat,"_threadId")),this.storageRemove("".concat(this.options.storage.chat,"_chatOpen")),this.storageRemove("".concat(this.options.storage.chat,"_chatZoom"))}foreach(t,e){return o.Z.iterate(t,function(t,s){e(s,t)})}objectMerge(){for(var t=arguments.length,e=Array(t),s=0;s<t;s++)e[s]=arguments[s];let a=(t,e)=>(Object.entries(e).forEach(e=>{let[s,i]=e;t[s]=i&&"object"==typeof i?a(t[s]||{},i):i}),t);return e.reduce(a,{})}putHtml(t,e,s){t.insertAdjacentHTML(e,s)}debounce(){return"function"==typeof arguments[0]?setTimeout(()=>{arguments[0].apply()},arguments[1]||0):void 0}felParseInt(t){return parseInt(t,10)}loadTemplates(){this.template={greeting:"",loading:"",messageItem:""};let t=document.getElementById("fel-chatbot-template");if(t){if(this.template.greeting=t.content.querySelector(this.options.selector.greetingContainer),this.template.greeting){let t=this.storageGet(this.options.storage.chat);this.putToMessages("greeting",t||this.template.greeting.outerHTML),t&&(this.assistantEl.classList.add("contains-thread"),this.getChatbotThreadId().value=this.storageGet(this.options.storage.chat+"_threadId"))}this.template.loading=t.content.querySelector(this.options.selector.loadingContainer)}}getActiveProductsInChat(){return this.options.page.refId?this.assistantEl.querySelectorAll('.fel-chat-product-list li[data-ref-id="'.concat(this.options.page.refId,'"]')):[]}embedStyle(t){let e="fel-embed-highlight-active-products",s=document.querySelector("#".concat(e));return s||((s=document.createElement("style")).id=e,document.head.append(s)),s.textContent=t}highlightActiveProductsInChat(){if(this.options.page.refId){let t='.fel-chat-product-list li[data-ref-id="'.concat(this.options.page.refId,'"]');this.domGet(this.assistantEl,t,!1)&&this.embedStyle("\n					#fel-chatbot.active.fel-zoom-chat ".concat(t," .fel-item-wrapper {\n						box-shadow: var(--fel-hl-active-products-box-shadow);\n					}\n					#fel-chatbot.active:not(.fel-zoom-chat) ").concat(t," img {\n						box-shadow: var(--fel-hl-active-products-box-shadow);\n					}\n				"))}}setPluginVars(){this._client=new m,this.cookieAccepted=!1,this.eventsRegistered=!1,this.isLoading=!1,this.isLoadingResponse=!1,this.threadId=null,this.options.eventHandlerMap=this.chatbotForm?[[this.chatbotForm,"submit.felBtn",!1],[this.assistantEl,"click.felBtn",!1],[this.assistantEl,"change.felBtn",!1]]:[]}setPluginOptions(){if(void 0===i.Z)return!1;this.assistantEl=this.domGet(document.body,"[data-assistant-gpt-plugin]",!1),this.chatbotForm=this.domGet(this.assistantEl,"form#fel-chatbot-form",!1),this.controllerUrl=this.chatbotForm.action,this.chatMessages=this.domGet(this.assistantEl,this.options.selector.chatMessages,!1);let t=this.assistantEl.dataset.options;if(t&&"string"==typeof t)try{if(t=JSON.parse(t))return this.options=this.objectMerge(this.options,t),!0}catch(t){}return!1}removeEvents(){this.setEventHandler("remove")}resetEvents(){this.removeEvents(),this.registerEvents()}setEventHandler(t){var e={add:"addEventListener",remove:"removeEventListener"};if("remove"===t&&!this.eventsRegistered)return!1;"add"===t&&this.eventsRegistered&&this.removeEvents(),this.foreach(this.options.eventHandlerMap,(s,a)=>{if(void 0!==a[1]){var i=a[1].split(".");a[0][e[t]](i[0],this,a[2]||!0)}}),this.eventsRegistered="add"===t}}_.options={behavior:"smooth",compactView:640>window.innerWidth,selector:{chatMessages:"#fel-oai-chatbot-messages",greetingContainer:".fel-chatbot-greeting-container",loadingContainer:".fel-chatbot-load-container",submitChatButton:".fel-submit-chat-btn",chatThreadId:'[name="fel-chatbot-thread-id"]',chatRunId:'[name="fel-chatbot-run-id"]',chatUserInput:'[name="fel-chatbot-user-input"]',chatMessageEl:"chat-message-item",chatMessage:"chat-message",cookieDefaultName:"fel-chatbot-localstorage-accepted"},layout:{scrollUpIsLeft:!1},page:{refId:null},storage:{chat:"felChatStorage"}}},49:(t,e,s)=>{s.d(e,{Z:()=>i});var a=s(140);class i{static isNode(t){return"object"==typeof t&&null!==t&&(t===document||t===window||t instanceof Node)}static hasAttribute(t,e){if(!i.isNode(t))throw Error("The element must be a valid HTML Node!");return"function"==typeof t.hasAttribute&&t.hasAttribute(e)}static getAttribute(t,e){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!1===i.hasAttribute(t,e))throw Error('The required property "'.concat(e,'" does not exist!'));if("function"!=typeof t.getAttribute){if(s)throw Error("This node doesn't support the getAttribute function!");return}return t.getAttribute(e)}static getDataAttribute(t,e){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=e.replace(/^data(|-)/,""),r=a.Z.toLowerCamelCase(o,"-");if(!i.isNode(t)){if(s)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===t.dataset){if(s)throw Error("This node doesn't support the dataset attribute!");return}let n=t.dataset[r];if(void 0===n){if(s)throw Error('The required data attribute "'.concat(e,'" does not exist on ').concat(t,"!"));return n}return a.Z.parsePrimitive(n)}static querySelector(t,e){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!i.isNode(t))throw Error("The parent node is not a valid HTML Node!");let a=t.querySelector(e)||!1;if(s&&!1===a)throw Error('The required element "'.concat(e,'" does not exist in parent node!'));return a}static querySelectorAll(t,e){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(s&&!i.isNode(t))throw Error("The parent node is not a valid HTML Node!");let a=t.querySelectorAll(e);if(0===a.length&&(a=!1),s&&!1===a)throw Error('At least one item of "'.concat(e,'" must exist in parent node!'));return a}}},266:(t,e,s)=>{s.d(e,{Z:()=>a});class a{static iterate(t,e){if(t instanceof Map||Array.isArray(t))return t.forEach(e);if(t instanceof FormData){for(var s of t.entries())e(s[1],s[0]);return}if(t instanceof NodeList)return t.forEach(e);if(t instanceof HTMLCollection)return Array.from(t).forEach(e);if(t instanceof Object)return Object.keys(t).forEach(s=>{e(t[s],s)});throw Error("The element type ".concat(typeof t," is not iterable!"))}}},140:(t,e,s)=>{s.d(e,{Z:()=>a});class a{static ucFirst(t){return t.charAt(0).toUpperCase()+t.slice(1)}static lcFirst(t){return t.charAt(0).toLowerCase()+t.slice(1)}static toDashCase(t){return t.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(t,e){let s=a.toUpperCamelCase(t,e);return a.lcFirst(s)}static toUpperCamelCase(t,e){return e?t.split(e).map(t=>a.ucFirst(t.toLowerCase())).join(""):a.ucFirst(t.toLowerCase())}static parsePrimitive(t){try{return/^\d+(.|,)\d+$/.test(t)&&(t=t.replace(",",".")),JSON.parse(t)}catch(e){return t.toString()}}}}}]);