const { Component, Mixin } = Shopware;

Mixin.register('fel-plugin-config-mixin', {

    inject: [
        'systemConfigApiService',
    ],

    data() {
        return {
            configName: 'FelAIProductAdvisor.config',
            pluginConfig: {},
            canRun: true,
            detailPage: false,
        };
    },

    created() {
        this.httpClient = this.systemConfigApiService.client;
    },

    methods: {

        setConfigValue(configName, setValue) {
            if ('assistantId' === configName) {
                configName = `${this.configName}.openAiAssistantId`;
            } else if ('chatbotName' === configName) {
                configName = `${this.configName}.openAiChatMetaChatbotName`;
            }
            this.systemConfigApiService.saveValues({[configName]: setValue});
        },

        httpClientGet() {
            return this.httpClientExec('get', ...arguments);
        },

        httpClientDelete() {
            return this.httpClientExec('delete', ...arguments);
        },

        httpClientPost(url, post, callback) {
            return this.httpClientExec('post', url, callback, post);
        },

        async httpClientExec(method, url, callback, submitBody) {
            if (!this.canRun) {
                return false;
            }

            this.isLoading = true;

            var setArgs = [url];
            if (submitBody) {
                setArgs.push(submitBody);
            }
            setArgs.push({headers: this.pluginConfig.apiHeader});

            return await this.httpClient[method](...setArgs)
                .then((response) => {return callback(response)})
                .catch((error) => {
                    console.error(error);
                    return callback(error);
                })
                .finally(() => {
                    setTimeout(() => {this.isLoading = false;}, 300);
                });
        },

        async fetchPluginConfig() {
            return await this.systemConfigApiService.getValues(this.configName);
        },

        async getPluginConfig() {
            return await this.fetchPluginConfig().then((pluginConfig) => {
                var r = {
                    setLoadSkeleton: Math.round(this.$el.querySelector('.sw-page__content').offsetHeight / 64),
                    apiKey: pluginConfig[`${this.configName}.openAiApiKey`],
                    chatbotName: pluginConfig[`${this.configName}.openAiChatMetaChatbotName`],
                    assistantId: pluginConfig[`${this.configName}.openAiAssistantId`],
                    openAiBaseUrl: pluginConfig[`${this.configName}.openAiBaseUrl`],
                    openAiVersion: pluginConfig[`${this.configName}.openAiVersion`],
                    openAiBetaVersion: pluginConfig[`${this.configName}.openAiBetaVersion`],
                    pluginConfig: pluginConfig,
                }

                r.apiUrl = `${r.openAiBaseUrl}/${r.openAiVersion}`;

                r.apiHeader = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${r.apiKey}`,
                    'OpenAI-Beta': r.openAiBetaVersion,
                };

                if (false === r.apiKey || '' === r.apiKey) {
                    this.canRun = false;
                }

                this.pluginConfig = r;

                return r;
            });
        },

    },

});
