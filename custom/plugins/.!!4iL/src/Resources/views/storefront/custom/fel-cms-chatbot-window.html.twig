<div class="cms_chatbot_wrapper">
	{% set chatConfig = config('FelAIProductAdvisor.config', context.salesChannel.id || null) %}
	{% set chatEnabled = chatConfig.enablePlugin %}
	{% set felPluginOptions = {
        page: {
            refId: page.product.parentId ?? page.product.id ?? null,
            categoryPath: page.header.navigation.active.get('breadcrumb')|keys|join('|'),
        },
        translation: {
            removeChatMessage: 'fel-gpt-assistant.chat.deleteChatMessage'|trans,
            errorClearChat: 'fel-gpt-assistant.chat.errorClearChat'|trans
        },
        config: {
            maxProductsInChat: chatConfig.maxProductsInChat ?? 4
        }
    } %}

    {# {{ chatConfig.openAiAssistantId }} #}



    {% set cmsAssistantId = element.config.cmsAssistantId.value %}
    {% set cmsCategoryId = element.config.cmsCategoryId.value %}
    {# {% set cmsAssistantId = element.config.assistantId.value %} #}



	{% if chatConfig.felOpenAiApiKey is empty or cmsAssistantId is empty %}
		{% set chatEnabled = false %}
	{% elseif chatConfig.authenticatedUserOnly %}
		{% if context.customer is empty %}
			{% set chatEnabled = false %}
		{% else %}
			{% if chatConfig.authenticatedUserOnlyGroup and context.customer.groupId not in chatConfig.authenticatedUserOnlyGroup %}
				{% set chatEnabled = false %}
			{% endif %}
		{% endif %}
	{% endif %}
	{% if chatEnabled %}
		{% set chatbotAvatarUrl = asset("bundles/felaiproductadvisor/chatbot-avatar-female-sm.png", 'asset') %}
		{% set chatbotLoaderFile = asset("bundles/felaiproductadvisor/fel-loader-default.svg", 'asset') %}
		{% set fel_chatbot_avatar %}
		<img src="{{ chatbotAvatarUrl }}" alt="{{ 'fel-gpt-assistant.chat.avatarTitle'|trans }}"/>
		{% endset %}


		{% block fel_chatbot_template %}
			<template
				id="fel-chatbot-template">
				{# greetings container #}
				<div class="fel-chatbot-greeting-container fel-fade-in pb-3 h-100 d-flex justify-content-center align-items-center">
					<div>
						{% if element.data and element.data.media and element.data.media.url %}
							<div class="fel-chatbot-avatar text-center">
								<img src="{{ element.data.media.url }}" alt="Product Advisor Avatar"/>
							</div>
						{% endif %}
						<div class="fel-chatbot-greeting-text text-center pt-1 pb-2">
							{% if element.config.cmsAdvisorName and element.config.cmsAdvisorName.value %}
								<p class="m-0">
									<b>{{ element.config.cmsAdvisorName.value }}</b>
								</p>
							{% endif %}

							{% if element.config.cmsGreetingMessage and element.config.cmsGreetingMessage.value %}
								<p>{{ element.config.cmsGreetingMessage.value }}</p>
							{% endif %}
						</div>
					</div>
					<div class="fel-chatbot-greeting">
						<div class="fel-chatbot-config-wrapper py-3 bg-white">
							<div class="fel-cookie-consent">
								<div class="px-3">
									{% set felLocalStorageConsent = app.request.cookies.get('fel-chatbot-localstorage-accepted') %}
									<div class="form-text form-check">
										<input type="checkbox" name="fel-allow-localstorage" id="fel-allow-localstorage" class="form-check-input fel-checkbox" data-callback="toggleLocalStorage" {% if 'allowed' == felLocalStorageConsent %} checked {% endif %}>
										<label for="fel-allow-localstorage" class="form-check-label align-text-top ps-1">
											{{ 'fel-gpt-assistant.cookie.description'|trans }}
										</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				{# loading indicator container #}
				<div class="fel-chatbot-load-container fel-fade-in">
					<div class="fel-chatbot-loader text-center py-3 fel-loading-icon-default">
							<svg viewBox="3.0692 2.4854 72.8769 16.0141" width="72.8769" height="16.0141" xmlns="http://www.w3.org/2000/svg">
								<circle class="fel-loader-circle" cx="10" cy="10" r="3">
									<animate attributeName="r" repeatCount="indefinite" dur="0.7s" calcMode="spline" keyTimes="0;1" values="3;0" keySplines="0 0.5 0.5 1" begin="0s"/>
								</circle>
								<circle class="fel-loader-circle" cx="20" cy="10" r="3">
									<animate attributeName="r" repeatCount="indefinite" dur="0.7s" calcMode="spline" keyTimes="0;1" values="3;0" keySplines="0 0.5 0.5 1" begin="0.1s"/>
								</circle>
								<circle class="fel-loader-circle" cx="30" cy="10" r="3">
									<animate attributeName="r" repeatCount="indefinite" dur="0.7s" calcMode="spline" keyTimes="0;1" values="3;0" keySplines="0 0.5 0.5 1" begin="0.2s"/>
								</circle>
								<circle class="fel-loader-circle" cx="40" cy="10" r="3">
									<animate attributeName="r" repeatCount="indefinite" dur="0.7s" calcMode="spline" keyTimes="0;1" values="3;0" keySplines="0 0.5 0.5 1" begin="0.3s"/>
								</circle>
							</svg>

					</div>
					<div class="fel-chatbot-loader-info text-center" data-second-run="{{ 'fel-gpt-assistant.chat.wait.a.second.more'|trans }}">
						{{ 'fel-gpt-assistant.chat.wait.a.second'|trans }}
					</div>
				</div>
			</template>
		{% endblock %}

		<div id="fel-chatbot-advisor"
		     class="fel-cms-chatbot fel-chat-initial fel-zoom-intial active"
		     data-options="{{ felPluginOptions|json_encode }}"
		     data-assistant-gpt-plugin
		     data-theme-color="{{ themeColor }}">
			<div class="fel-chatbot-container" style="max-height:{{ element.config.cmsProductAdvisorChatWindowHeight.value }}px;{% if element.config.cmsBorderRadius.value == true %}border-radius: 16px{% endif %};overflow: hidden;box-shadow: 1px 1px 13px #777777;">
				{% block fel_chatbot_options_buttons %}
					<div class="fel-chatbot-header" style="background:{{ element.config.cmsChatThemeColor.value }}">
						<div class="fel-chatbot-options d-flex justify-content-between">
							<div class="align-items-center fel-header-chatbot-info d-flex">
								{% if element.data and element.data.media and element.data.media.url %}
									<div class="fel-header-avatar me-2">
										<img src="{{ element.data.media.url }}" alt="{{ 'fel-gpt-assistant.chat.avatarTitle'|trans }}" class="fel-header-avatar-img"/>
									</div>
								{% endif %}
								{% if element.config.cmsAdvisorName and element.config.cmsAdvisorName.value %}
									<div class="fel-header-advisor-name text-white">
										{{ element.config.cmsAdvisorName.value }}
									</div>
								{% endif %}
							</div>
							<div class="d-flex align-items-center">
								<div class="btn-group">
									<button type="button" class="fel-delete-thread-button fel-btn btn btn-small btn-primary" style="background:{{ element.config.cmsChatThemeColor.value }}; border-color:{{ element.config.cmsChatThemeColor.value }};" data-callback="deleteChatThread" title="{{ 'fel-gpt-assistant.chat.delete'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.delete'|trans }}">
										{% sw_icon 'arrow-360-right' style {'class': 'help pe-none'} %}
									</button>
									<!-- Zoom und Toggle Buttons entfernt -->
								</div>
							</div>
						</div>
					</div>
				{% endblock %}

				{% block fel_chatbot_messages_container %}
					<div id="fel-chatbot-messages" style="min-height: 360px;"></div>
					<hr class="fel-chat-divider m-0"/>
				{% endblock %}

				{% block fel_chatbot_form_user %}
					<form id="fel-chatbot-form" class="bg-light" action="{{ seoUrl('frontend.fel.chatbox.message') }}" method="post">
						<div class="fel-chatbot-inputs w-100">
							<input type="hidden" name="fel-chatbot-assistant-id" value="{{ cmsAssistantId }}">
							<input type="hidden" name="fel-chatbot-run-id" value="">
							<input type="hidden" name="fel-chatbot-thread-id" value="">
							<input type="hidden" name="fel-chatbot-category-id" value="{{ cmsCategoryId }}">
							<input type="hidden" name="hiddenMessage" value="{{ element.config.cmsProductAdvisorFor.value|default('Allgemeiner Produktberater') }}">
							{% set systemPrefix = "[SYSTEM: " %}
							{% if element.config.cmsAdvisorName and element.config.cmsAdvisorName.value %}
								{% set systemPrefix = systemPrefix ~ "Du bist " ~ element.config.cmsAdvisorName.value ~ ". " %}
							{% endif %}
							{% if element.config.cmsProductAdvisorFor and element.config.cmsProductAdvisorFor.value %}
								{% set systemPrefix = systemPrefix ~ "Deine Aufgabe ist: " ~ element.config.cmsProductAdvisorFor.value ~ ". " %}
							{% endif %}
							{% if element.config.cmsCategoryId and element.config.cmsCategoryId.value %}
								{% set systemPrefix = systemPrefix ~ "Du berätst zu Produkten in der Kategorie " ~ element.config.cmsCategoryId.value ~ ". " %}
							{% endif %}
							{% set systemPrefix = systemPrefix ~ "Wenn du dem Benutzer Auswahlmöglichkeiten anbietest, formatiere diese als klickbare Buttons mit dem Format [BUTTON:Buttontext]. Beispiel: [BUTTON:Gecko]. Verwende dieses Format nur für kurze, klickbare Optionen." %}
							{% set systemPrefix = systemPrefix ~ "] " %}
							<input type="hidden" name="system-prefix" value="{{ systemPrefix }}">
							<input type="text" class="form-control input bg-white p-3 border-0" name="fel-chatbot-user-input" value="" placeholder="{{ 'fel-gpt-assistant.chat.placeholder'|trans }}" required="true" minlength="2" maxlength="{{ chatConfig.openAiUserInputMaxLength }}" autocomplete="off" data-system-prefix="{{ systemPrefix }}">
						</div>
						<div class="fel-submit-btn bg-white">
							<button type="submit" class="fel-submit-chat-btn btn bg-white h-100 px-3" title="{{ 'fel-gpt-assistant.chat.submit'|trans }}" aria-label="{{ 'fel-gpt-assistant.chat.submit'|trans }}">
								{% sw_icon 'paperplane' style {'class': 'forward pe-none'} %}
							</button>
						</div>
					</form>
				{% endblock %}
			</div>
		</div>
	{% endif %}

{#
<pre>
	{{ dump( page.header.navigation.active.name) }}
	{{ dump( page.header.navigation.active.breadcrumb) }}
	{{ dump( element.config ) }}
</pre> #}


</div>
