{"fel-gpt-assistant": {"chatbot": "<PERSON><PERSON><PERSON>", "chat": {"avatarTitle": "<PERSON><PERSON><PERSON>", "box": "Chatbox", "categories": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON> au<PERSON>", "delete": "<PERSON><PERSON> l<PERSON>", "deleteChatMessage": "Frage und Antwort löschen", "errorClearChat": "Ein Fehler ist aufgetreten, bitte löschen Sie den Chat", "initial.greeting": "Wie kann ich Ihnen helfen?", "onlineStatus": "online", "open": "Assistent starten", "placeholder": "Nachricht eingeben", "submit": "<PERSON><PERSON><PERSON><PERSON>", "toggleChatbox": "Chatbox umschalten", "toggleZoomChat": "Chatbox auf größer oder kleiner umstellen", "wait.a.second": "<PERSON><PERSON><PERSON> Si<PERSON> mir einen Moment", "wait.a.second.more": "<PERSON><PERSON><PERSON> Si<PERSON> mir noch einen Moment", "whats.your.purpose": "Wie könntest du mir helfen?", "productListing": {"noImageAssigned": "Bild nicht gefunden"}}, "cookie": {"name": "Lokaler Speicher", "description": "Aktivieren Sie diese Option, damit der Chatbot das Gespräch lokal in Ihrem Browser speichert und den Chat seitenübergreifend aufrechterhält.", "privacy": "Es werden keine persönlichen Daten mit der KI geteilt. Nur Ihre Eingaben im Chat werden an die KI übermittelt."}, "error": {"chat": {"queryFailedTruncatedTo": "Keine Produkte für den Suchbegriff „%from%“ gefunden, geändert zu „%to%“  und nochmal versucht."}, "input": {"max.length": "Eingabelänge überschritten, max: %max%", "is.not.string": "Die Eingabe liegt nicht im Textformat vor. <PERSON><PERSON><PERSON>, dass Ihre Eingabe eine Textzeichenfolge ist"}}}}