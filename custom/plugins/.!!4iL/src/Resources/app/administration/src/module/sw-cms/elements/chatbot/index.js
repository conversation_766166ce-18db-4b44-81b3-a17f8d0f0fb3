// <plugin root>/src/Resources/app/administration/src/module/sw-cms/elements/chatbot/index.js
import './component';
import './config';
import './preview';

Shopware.Service('cmsService').registerCmsElement({
    name: 'chatbot',
    label: 'sw-cms.elements.chatbot.label',
    component: 'sw-cms-el-chatbot',
    configComponent: 'sw-cms-el-config-chatbot',
    previewComponent: 'sw-cms-el-preview-chatbot',
    defaultConfig: {
        cmsAssistantId: {
            source: 'static',
            value: ''
        },
        cmsAdvisorTitle: {
            source: 'static',
            value: ''
        },
        cmsAdvisorName: {
            source: 'static',
            value: ''
        },
        cmsCategoryId: {  // New field default configuration
            source: 'static',
            value: null,
            required: false,
            entity: {
                name: 'category',
            },
        },
        cmsGreetingMessage: {  // Text editor
            source: 'static',
            value: 'Hello! How can I assist you today?'
        },
        cmsChatThemeColor: {  // Colorpicker
            source: 'static',
            value: '#6ed59f'
        },
        cmsProductAdvisorFor: {  // Text field
            source: 'static',
            value: ''
        },
        cmsProductAdvisorChatWindowHeight: {  // Number field
            source: 'static',
            value: ''
        },
        cmsBorderRadius: {  // Checkbox
            source: 'static',
            value: false
        },
        cmsProductAdvisorAvatar: {  // Image upload
            source: 'static',
            value: null,
            required: false,
            entity: {
                name: 'media',
            },
        }
    }
});
