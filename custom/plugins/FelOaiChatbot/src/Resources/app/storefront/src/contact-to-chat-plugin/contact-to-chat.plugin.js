import DomAccess from 'src/helper/dom-access.helper';
import Iterator from 'src/helper/iterator.helper';

export default class FelContactToChatbotPlugin extends window.PluginBaseClass {
    static options = {
        selector: {
			felBtn: '.fel-ext-btn',
			defaultCommentField: 'textarea[name="comment"]',
			loadingContainer:  '.fel-chatbot-load-container',
			supportButton: '.fel-contact-to-chat-button-container',
			supportResponse: '.fel-contact-to-chat-response-container',
            chatbotInputEl: '[name="fel-chatbot-user-input"]',
            chatLastResponse: '#fel-oai-chatbot .chatbot-message-item:last-of-type .fel-inner',
            pseudoModalEl: '.js-pseudo-modal',
		},
    };

    init() {
        window.DomAccess = DomAccess;
        window.Iterator = Iterator;

		window.FelContactToChatbotPlugin = this;
		window.$emitterContactToChatbot = this.$emitter;

        // this.clickHandlerRegistered = false;
        // this._loadTemplates();
        // this._registerAll();
	}

    _registerAll() {
        this.handleEvent = (event) => {
			switch (event.type) {
				case 'input': return this.toggleBtnCommentToAi(event, event.target);
				case 'click': return this.clickEventCallback(event, event.target);
			}
		};
        this._checkEmbeddedForms();
        this._subscribeAjaxModal();
    }

    _registerClickHandler() {
        if (!this.clickHandlerRegistered) {
            document.body.addEventListener('click', this, false);
            this.clickHandlerRegistered = true;
        }
    }

    _unregisterClickHandler() {
        if (this.clickHandlerRegistered) {
            document.body.removeEventListener('click', this, false);
            this.clickHandlerRegistered = false;
        }
    }

    _registerCommentListener(commentField) {
        commentField.removeEventListener('input', this, false);
        commentField.addEventListener('input', this, false);
    }

    removeChatMessage(event) {
        let el = event.target;
        let elParent = el.parentNode.parentNode;
        if (elParent.classList.contains(this.options.selector.supportResponse.replace('.', ''))) {
            let getResponseEl = this.domGet(elParent, '.fel-inner', false);
            getResponseEl.innerHTML = '';
            getResponseEl.classList.remove('fel-contains-response');
            elParent.classList.add('fel-is-hidden');
        }
    }

    addToChatSubmit(event) {
        let trigger = event.target;
        let getChatInput = this.domGet(this.el, this.options.selector.chatbotInputEl, false);
        if (getChatInput && trigger.dataset.felResponseQuerySelector) {
            let getCommentField = this.domGet(document.body, trigger.dataset.felResponseQuerySelector, false);

            if (getCommentField) {
                trigger.disabled = true;
                let commentWrapper = getCommentField.parentNode;
                let getResponseWrapper = this.domGet(commentWrapper, this.options.selector.supportResponse, false);
                let getResponseEl = this.domGet(getResponseWrapper, '.fel-inner', false);

                if (getResponseWrapper && getResponseEl) {
                    let FelAssistantPlugin = window.PluginManager.getPluginInstances('FelOaiChatbotPlugin');

                    if (FelAssistantPlugin.length) {
                        getResponseWrapper.classList.remove('fel-is-hidden');
                        getResponseEl.classList.add('fel-contains-response');
                        this.putHtml(getResponseEl, 'afterbegin', this.template.loading.outerHTML);
                        getChatInput.value = getCommentField.value.substring(0, getChatInput.maxLength || 100);
                        getCommentField.disabled = true;
                        getChatInput.form.dispatchEvent(new Event('submit', {bubbles: true, cancelable: true}));

                        FelAssistantPlugin[0].$emitter.subscribe('FelOaiChatbotMessageResponse', () => {
                            getCommentField.disabled = null;
                            let getChatResponse = this.domGet(this.el, this.options.selector.chatLastResponse, false);
                            let getDeleteBtn = getChatResponse.querySelector('.fel-delete-message');
                            if (getDeleteBtn) {
                                getDeleteBtn.remove();
                            }
                            if (getChatResponse) {
                                getResponseEl.innerHTML = getChatResponse.innerHTML;
                                FelAssistantPlugin[0].$emitter.unsubscribe('FelOaiChatbotMessageResponse');
                            }
                        });
                    }
                }
            }
        }
    }

	toggleBtnCommentToAi(event, el) {
		if ('undefined' !== typeof el.value) {
			let getBtn = this.domGet(el.parentNode, this.options.selector.felBtn, false);
			if (getBtn) {
				getBtn.disabled = el.value ? null : true ;
			}
		}
	}

    clickEventCallback(event, el) {
		if (!el.classList.contains('fel-ext-btn')) {
			return;
		}

		let callback = el.dataset.callback;
		if (el.dataset.preventDefault) event.preventDefault();
		if (el.dataset.stopPropagation) event.stopPropagation();
		if (el.dataset.blur) el.blur();

		if (callback) {
			if (typeof this[callback] === 'function') {
				return this[callback](...arguments);
			} else if (typeof window[callback] === 'function') {
				return window[callback](...arguments);
			}
		}
	}

    _subscribeAjaxModal() {
		let AjaxModalPlugin = window.PluginManager.getPluginInstances('AjaxModal');
        if (AjaxModalPlugin.length) {
            this._registerClickHandler();
            for (var i = 0; i < AjaxModalPlugin.length; i++) {
                AjaxModalPlugin[i].$emitter.subscribe('ajaxModalOpen', () => this.onModalOpen());
            }
        }
    }

    onModalOpen(cnt) {
        setTimeout(() => {return this._onModalOpen(cnt || 0)}, !cnt ? 0 : 150);
    }

    _onModalOpen(retries) {
        let modalQuerySelector = `${this.options.selector.pseudoModalEl} ${this.options.selector.defaultCommentField}`;
        let commentField = this.domGet(document.body, modalQuerySelector, false);
        if (!commentField && 5 > retries) {
            return this.onModalOpen(++retries);
        }
        if (commentField) {
            this._commentFieldHandler(commentField, 'modal', modalQuerySelector);
        }
    }

    _checkEmbeddedForms() {
        let getCommentFields = this.domGetAll(document.body, this.options.selector.defaultCommentField, false);
        if (getCommentFields.length) {
            this._registerClickHandler();
            this.foreach(getCommentFields, (key, commentField) => {
                let setFormUniqueClass = `fel-embedded-form-${key}`;
                commentField.form.classList.add(setFormUniqueClass);
                this._commentFieldHandler(commentField, 'embedded', `.${setFormUniqueClass} ${this.options.selector.defaultCommentField}`);
            });
        }
    }

    _commentFieldHandler(commentField, formIn, formQuerySelector) {
        let addSupportTrigger = this.template.supportButton.cloneNode(true);
        let supportTriggerBtn = this.domGet(addSupportTrigger, 'button', false);
        supportTriggerBtn.dataset.felFormIn = formIn;
        supportTriggerBtn.dataset.felResponseQuerySelector = formQuerySelector;
        if ('' === commentField.value) {
            supportTriggerBtn.disabled = true;
        }
        this.putElement(commentField, 'beforebegin', addSupportTrigger);
        this.putElement(commentField, 'afterend', this.template.supportResponse.cloneNode(true));
        this._registerCommentListener(commentField);
    }

	domGet() {
		return DomAccess.querySelector(...arguments);
	}

	domGetAll() {
		return DomAccess.querySelectorAll(...arguments);
	}

	foreach(toIterate, callback) {
		return Iterator.iterate(toIterate, function(value, key) {
			callback(key, value);
		});
	}

    // beforebegin afterbegin beforeend afterend
    putHtml(el, toPosition, html) {
        el.insertAdjacentHTML(toPosition, html);
    }

    putElement(el, toPosition, addElement) {
        el.insertAdjacentElement(toPosition, addElement);
    }

	_loadTemplates() {
		let templates = document.getElementById('fel-oai-chatbot-template');
		let extensionTemplates = document.getElementById('fel-contact-to-chat-template');
		this.template = {loading: ''};
		if (templates) {
			this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
		}
		if (extensionTemplates) {
			this.template.supportButton = extensionTemplates.content.querySelector(this.options.selector.supportButton);
			this.template.supportResponse = extensionTemplates.content.querySelector(this.options.selector.supportResponse);
		}
	}

}
