{% block fel_assistant_manager_search_log %}
<sw-page class="fel-assistant-manager-search-log fel-assistant-manager-log fel-assistant-manager">
    <template #smart-bar-actions>
        {# <mt-button
            @click="$router.push({ name: 'fel.assistant.manager.chatLogList', query: { page, limit } })">
            {{ $t('fel-assistant-manager.log.backToPage', {'page': page}) }}
        </mt-button> #}
    </template>
    <template #content>
    	<template v-if="isLoading">
 			<div class="fel-full-height-skeleton-bar">
			    <mt-skeleton-bar v-for="idx in 6" :key="idx" />
            </div>
		</template>
		<template v-else>
 			<div class="fel-assistant-manager-app-container">
                <div class="fel-box fel-p-3 fel-border-1 fel-border-radius-2">
                    <div class="fel-mb-3">
                        <h2 class="fel-mb-2">{{ $t('fel-assistant-manager.search.title') }}</h2>
                        <p class="fel-mb-2">{{ $t('fel-assistant-manager.search.description') }}</p>

                        <!-- Filter input -->
                        <div class="fel-mb-3">
                            <mt-text-field
                                v-model="filterTerm"
                                :placeholder="$t('fel-assistant-manager.search.filterPlaceholder')"
                                :label="$t('fel-assistant-manager.search.searchFilter')"
                                @input="filterTerm = $event">
                            </mt-text-field>
                        </div>

                        <!-- Tab navigation -->
                        <div class="fel-mb-3">
                            <mt-tabs
                                position="top"
                                :small="true"
                                :alignRight="false"
                                :defaultItem="activeTab"
                                @new-item-active="onNewItemActive"
                                :items="[
                                    {
                                        'label': $t('fel-assistant-manager.search.logsTab') || 'Search Logs',
                                        'name': 'search_logs',
                                        'itemActive': true
                                    },
                                    {
                                        'label': $t('fel-assistant-manager.search.productSearchTab') || 'Product Search',
                                        'name': 'product_search'
                                    }
                                ]">
                            </mt-tabs>
                        </div>
                    </div>

                    <sw-pagination
                        class="fel-grid-pagination fel-flex-center-center"
                        :total="total"
                        :limit="limit"
                        :page="page"
                        :steps="[5, 10, 25, 50, 100]"
                        @page-change="onPageChange"
                    ></sw-pagination>

                    {{ total }} / {{ limit }} / {{ page }}

                    <div v-if="activeTab === 'search_logs'" class="fel-search-logs-content">
                            <mt-card
                                v-for="item in filteredSearchLogsTerms"
                                :key="item.id"
                                class="fel-mb-2">
                            <template #title>
                                <span class="fel-search-query">{{ item.parsedQuery.query }}</span>
                            </template>

                            <template #default>
                            </template>
                        </mt-card>
                    </div>




                    <!-- Search Logs Tab Content -->
                    <div v-if="activeTab === 'search_logs'" class="fel-search-logs-content fel-is-hidden">
                        <div v-if="filteredSearchLogsTerms.length === 0" class="fel-empty-state">
                            <p>{{ $t('fel-assistant-manager.search.noSearchLogsFound') }}</p>
                        </div>

                        <div v-else class="fel-search-logs-list">
                            <mt-card
                                v-for="item in filteredSearchLogsTerms"
                                :key="item.id"
                                class="fel-mb-2">
                                <template #title>
                                    <div class="fel-d-flex fel-justify-content-between fel-align-items-center">
                                        <span class="fel-search-query">{{ item.parsedQuery.query }}</span>
                                        <span class="fel-badge fel-badge-info">{{ $t('fel-assistant-manager.search.usedCount', {'count': item.usageCount}) || `Used ${item.usageCount} times` }}</span>
                                    </div>
                                </template>

                                <template #default>
                                    <div class="fel-search-details">
                                        <div class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchAssistant') }}:</strong>
                                            {{ item.assistantId }}
                                        </div>

                                        <div v-if="item.parsedQuery.tags && item.parsedQuery.tags.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchTags') }}:</strong>
                                            <span
                                                v-for="tag in item.parsedQuery.tags"
                                                :key="tag"
                                                class="fel-tag fel-mr-1">
                                                {{ tag }}
                                            </span>
                                        </div>

                                        <div class="fel-search-meta fel-text-muted">
                                            <small>
                                                {{ $t('fel-assistant-manager.search.lastUsed') }}:
                                                {{ new Date(item.updatedAt).toLocaleString() }}
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </mt-card>
                        </div>
                    </div>

                    <!-- Product Search Tab Content -->
                    <div v-if="activeTab === 'product_search'" class="fel-product-search-content">
                        <div v-if="filteredProductSearchTerms.length === 0" class="fel-empty-state">
                            <p>{{ $t('fel-assistant-manager.search.noProductSearchFound') }}</p>
                        </div>

                        <div v-else class="fel-product-search-list">
                            <mt-card
                                v-for="item in filteredProductSearchTerms"
                                :key="item.id"
                                class="fel-mb-2">
                                <template #title>
                                    <div class="fel-d-flex fel-justify-content-between fel-align-items-center">
                                        <span class="fel-search-query">{{ item.parsedQuery.query }}</span>
                                        <span class="fel-badge fel-badge-info">{{ $t('fel-assistant-manager.search.usedCount', {'count': item.usageCount}) || `Used ${item.usageCount} times` }}</span>
                                    </div>
                                </template>

                                <template #default>
                                    <div class="fel-search-details">
                                        <div class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchAssistant') }}:</strong>
                                            {{ item.assistantId }}
                                        </div>

                                        <div v-if="item.parsedQuery.categories && item.parsedQuery.categories.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchCategories') }}:</strong>
                                            <span
                                                v-for="category in item.parsedQuery.categories"
                                                :key="category"
                                                class="fel-tag fel-tag-category fel-mr-1">
                                                {{ category }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.properties && item.parsedQuery.properties.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchProperties') }}:</strong>
                                            <span
                                                v-for="property in item.parsedQuery.properties"
                                                :key="property"
                                                class="fel-tag fel-tag-property fel-mr-1">
                                                {{ property }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.manufacturer && item.parsedQuery.manufacturer.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchManufacturer') }}:</strong>
                                            <span
                                                v-for="manu in item.parsedQuery.manufacturer"
                                                :key="manu"
                                                class="fel-tag fel-tag-manufacturer fel-mr-1">
                                                {{ manu }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.price_min !== null || item.parsedQuery.price_max !== null" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.price_min') || 'Price Range' }}:</strong>
                                            {{ item.parsedQuery.price_min || '0' }} - {{ item.parsedQuery.price_max || '∞' }}
                                        </div>

                                        <div v-if="item.parsedQuery.sort" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.order') || 'Sorting' }}:</strong>
                                            {{ item.parsedQuery.sort }}
                                        </div>

                                        <div v-if="item.parsedQuery.limit !== null || item.parsedQuery.page !== null" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.limit') }}:</strong>
                                            {{ `Limit: ${item.parsedQuery.limit || 'default'}, Page: ${item.parsedQuery.page || '1'}` }}
                                        </div>

                                        <div class="fel-search-meta fel-text-muted">
                                            <small>
                                                {{ $t('fel-assistant-manager.search.lastUsed') }}:
                                                {{ new Date(item.updatedAt).toLocaleString() }}
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </mt-card>
                        </div>
                    </div>



                    <sw-pagination
                        class="fel-grid-pagination fel-flex-center-center"
                        :total="total"
                        :limit="limit"
                        :page="page"
                        :steps="[5, 10, 25, 50, 100]"
                        @page-change="onPageChange"
                    ></sw-pagination>

                </div>
            </div>
		</template>
    </template>
</sw-page>
{% endblock %}
