{"fel-gpt-assistant": {"trainer": {"name": "5 Elements Trainer", "descriptions": ["<PERSON><PERSON>, das sich neu in der Entwicklung befindet, ist da<PERSON><PERSON> au<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> zu generieren, die weitestgehend den erforderlichen Formaten entsprechen und echte Daten aus Ihrem Online-Shop verwenden. Es bindet zufällig Präpositionen wie „<b>from</b>“, „<b>with</b>“, „<b>in</b>“ und Phrasen wie „<b>budget from</b>“ in die generierten Benutzereingaben, um natürliche Sprachmuster zu simulieren. Diese Elemente werden ohne spezifischen Kontext einfach vor und zwischen die Hauptwörter geschmissen, was bedeute<PERSON>, dass die generierten Benutzereingaben abschließend bereinigt werden müssen. Während technische Daten problemlos verarbeitet werden können, erfordern die simulierten menschlichen Eingaben leider eine Nachbearbeitung.", "Wir haben das Plugin und die Standard-Anweisungen für das Modell „gpt-3.5-turbo“ optimiert, um dessen Kosteneffizienz im Rahmen der Assistants-API zu nutzen, welches aufgrund seiner Komplexität schon von Haus aus kostspieliger ist als ähnliche OpenAI-Dienste. Die Nutzung von GPT-3.5 kostet mittlerweile praktisch nichts mehr, und jeder mit einem OpenAI-Konto kann GPT-3.5 nutzen, während GPT-4 nur für „Plus“-Nutzer verfügbar und die Nutzung mit der API zudem ziemlich kostspielig ist.", "Wir empfehlen die Erstellung von 5 bis 10 Standardbeispielen basierend auf Anfragen, die <PERSON><PERSON> von Ihren Kunden erwarten, um der KI eine klare Richtung zu geben. Angesichts des umfangreichen Wissens von 3.5 sind in der Regel nicht viele weitere Beispiele notwendig. Sie können jedoch bei Bedarf weitere Beispiele hinzufügen, falls die KI unerwartete Reaktionen zeigt. Sollte die KI auf bestimmte Fragen Kauderwelsch generieren, können Sie die Anweisungen direkt anpassen, um die KI auf die gewünschten Ergebnisse zu lenken. Das Standardbeispiel enthält ein Chat-Beispiel für „Contact details“. Ohne dieses Beispiel erfindet die KI Kontaktdaten, wenn es die allererste Benutzer-Frage an den Chatbot ist. Die hier generierten Beispieldaten werden Lokal in Ihrem Browser gespeichert, nicht auf dem <PERSON>. Wenn Sie die Cookies in ihrem Browser löschen, verschwinden die generierten Beispiele wieder."], "startTrainer": "Trainer starten", "fewShotExamplesNote": {"title": "<PERSON>ich<PERSON><PERSON> Hinweis zur Verwendung von Few-Shot-Beispielen:", "descriptions": ["Die mitgelieferten Standardbeispiele im Plugin arbeiten tadellos mit den Demodaten aus Shopware 6. Der Chatbot setzt alle gewünschten Eigenschaften präzise und nahtlos um – und das sogar schneller als GPT-4. <PERSON><PERSON><PERSON> könnte das System direkt nach der Installation perfekt für Sie funktionieren, was den Bedarf an zusätzlichem Training erheblich reduziert. Während spezielles Training vor allem die Suchfunktionen optimiert, funktionieren alle anderen Features bereits sehr gut.", "<PERSON>te <PERSON>ten <PERSON>, dass Few-Shot-Beispiele nicht wie vorgesehen funktionieren, wenn sie einfach als Dateianhänge an den Assistenten hochgeladen werden. Um Few-Shot-Learning-Techniken effektiv zu nutzen, müssen diese Beispiele direkt in die Anweisungen integriert werden, die Sie dem Assistenten zur Verfügung stellen.", "<PERSON><PERSON><PERSON>, dass <PERSON>e Few-Shot-Beispiele am Ende der Anweisungen hinzufügen, die Sie dem Assistenten bereitstellen. Diese Platzierung stellt sicher, dass der Assistent die Beispiele korrekt interpretieren und nutzen kann, wodurch er seine Antworten effektiv anhand der in diesen Beispielen dargestellten Szenarien anpassen kann."]}, "ui": {"assistantResponse": "Assistenten-Antwort", "info": "Info", "introduction": "Einführung", "description": "Beschreibung", "manufacturer": "<PERSON><PERSON><PERSON>", "maxPrice": "Höchstpreis", "minPrice": "Mindestpreis", "produktName": "Produktname", "properties": "Eigenschaften", "start": "Start", "sorting": "Sortierung", "total": "Gesamt", "userQuery": "Benutzeranfrage", "defaultExamples": "Standard Beispiele", "defaultExamplesAlreadyCapable": "Die Standardbeispiele nutzen die in Shopware 6 bereitgestellten Demodaten effektiv. Der Chatbot setzt alle in den folgenden Beispielen dargestellten Szenarien präzise und nahtlos um. Sie könnten direkt aus der Box heraus perfekt für Sie funktionieren, sodass kein zusätzliches Training erforderlich ist. Ein Training ist hauptsächlich für die Optimierung der Suchfunktionen notwendig; alle anderen Funktionen funktionieren bereits gut bis sehr gut.", "replaceDefaultWithCustom": "Ersetze Standard Beispiele mit den generierten", "defaultReplaced": "Die Standardanweisungen wurden ersetzt. Bitte überprüfen Sie die neuen Anweisungen.", "gptModelsComparison": "Diese <PERSON> zeigt einen Vergleich zwischen gpt-4-turbo und gpt-3.5-turbo.", "gptModelsComparisonHeader": {"model": "<PERSON><PERSON>", "requests": "API Anfragen", "tokens": "Token", "price": "Pre<PERSON>"}, "storage": {"adjustBeforeReplace": "Letzte Anpassungen vor dem ersetzen?", "capture": "Erfassen", "captured": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "Gelöscht", "Localstorage": "Lokalspeicher", "gotExamples": "Beispiele im Lokalspeicher", "localStorageNote": "Der Lokalspeicher ist ein Browser-<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> e<PERSON>m Cookie.", "lastAdjustmentsNote": "Nehmen Sie hier die letzten Anpassungen vor dem Erfassen vor.", "loadExamples": "Beispiele aus dem Lokalspeicher laden", "deleteExamples": "Beispiele löschen", "usageNotification": "Bitte überprüfen Sie das Ergebnis auf Unstimmigkeiten. Es ist entscheidend, dass die IDs der Eigenschaften und Hersteller, die in den Benutzeranfragen bereitgestellt werden, genau in den Antworten des Assistenten widergespiegelt werden. Dies stellt sicher, dass die Chat-Szenarien realistisch sind und dass das Tool die Absichten des Benutzers korrekt in die strukturierten Anfragen übersetzt, die die KI leiten. Achten Sie besonders auf die „Eigenschaften“ unter den Chats, um zu überprüfen, dass die vom Assistenten verwendeten IDs genau denen entsprechen, die vom Benutzer angegeben wurden. Die Konsistenz dieser Details ist entscheidend für eine optimale KI-Leistung und eine genaue Datenverarbeitung.", "howToUseNotification": "Wenn das Ergebnis korrekt erscheint, ersetzen Sie die Standardbeispiele in den Anweisungen durch Ihre eigenen und überprüfen Sie, ob sie wie erwartet funktionieren. <PERSON>te beachten Si<PERSON>, dass Änderungen einige Minuten dauern könne<PERSON>, um wirksam zu werden. Das erste vom Tool generierte Chat-Beispiel wird automatisch in Ihre Beispielsammlung aufgenommen, kann jedoch manuell entfernt werden, falls nicht benötigt."}, "availableSortings": {"name-asc": "Name ascending", "name-desc": "Name descending", "price-asc": "Price ascending", "price-desc": "Price descending", "topseller": "Topseller"}}, "step": {"1": {"Instructions": ["<PERSON><PERSON> ist entscheidend, die KI genau zu lehren, wie sie Begriffe und Eigenschaften in Benutzeranfragen interpretieren und verarbeiten muss, um Produkte in Ihrem Shop korrekt zu lokalisieren. Klare und präzise Anweisungen verhindern, dass die KI irrelevante oder falsche Informationen generiert, bekannt als „Halluzinationen“.", "Wenn Sie auf Inkonsistenzen in den Antworten der KI stoßen, könne<PERSON> Si<PERSON> diese durch gezielte Beispiele angehen. Gelegentlich kann es notwendig sein, in den Anweisungen genau zu spezifizieren, wie <PERSON> erwarten, dass die KI sich verhält. Indem Sie die Probleme klar erklären, he<PERSON><PERSON>, die KI so zu führen, dass sie wie beabsichtigt reagiert.", "Dieses Tool generiert anfängliche Beispielabfragen, indem es zufällig Präpositionen wie „<b>from</b>“, „<b>with</b>“, „<b>in</b>“ und Phrasen wie „<b>budget from</b>“ ein<PERSON><PERSON><PERSON>, um natürliche Sprachmuster zu simulieren. Diese Elemente werden ohne spezifischen Kontext platziert, um die strukturelle Integrität einer natürlich formulierten Abfrage zu wahren. Stellen Si<PERSON> sicher, dass Präpositionen und Phrasen logisch mit dem Rest der Abfrage übereinstimmen. Passen Sie die Abfragen so an, dass sie in Bezug auf Grammatik und Kontext logisch sind. Korrigieren Sie jegliche ungeschickte oder falsche Sprachverwendung, die durch den zufälligen Einfügeprozess entstanden ist. Vergewissern Sie sich, dass alle Teile der Abfrage relevant und notwendig sind. Entfernen oder ersetzen Sie alle Teile, die nicht zu einer klaren und effektiven Abfrage beitragen.", "Pluralisieren Sie die Benutzereingaben und singularisieren Sie die Anfrage für den Assistenten. Zum Beispiel sollte die Benutzereingabe „T-Shirts“ für den Assistenten zu „T-Shirt“ umgewandelt werden.", "Nehmen Sie die korrekturen als letzten Schritt vor, nach erfolgter Auswahl aller gewünschten Felder.", "Beginnen Sie mit der einfachsten Anfrage, bei der der Benutzer nach irgendwas spezifischem sucht.", "<b>Ich suche nach einem [Produkt].</b>", "Die<PERSON> stellt sicher, dass <b>[Produkt]</b> kor<PERSON>t in die „Beispiel“-Antwort des Assistenten einbezogen wird, die eine simulierte Antwort ist, die darauf ausgelegt ist, die Benutzeranfrage zu spiegeln. Diese präzise Ausrichtung ist entscheidend, um eine optimale KI-Leistung zu gewährleisten."]}}}}}