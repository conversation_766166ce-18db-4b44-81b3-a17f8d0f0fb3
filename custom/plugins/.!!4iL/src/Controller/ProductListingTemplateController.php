<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Controller;

use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\Framework\Routing\Annotation\Since;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @RouteScope(scopes={"storefront"})
 */
class ProductListingTemplateController extends StorefrontController
{
    /**
     * @Since("*******")
     * @Route("/fel-product-listing-template", name="frontend.fel.product.listing.template", methods={"GET"})
     */
    public function getProductListingTemplate(Request $request): Response
    {
        return $this->renderStorefront('@FelAIProductAdvisor/storefront/component/enhanced-product-listing.html.twig', [
            'products' => [],
            'productsResponse' => [
                'categoryMap' => [],
                'args' => [],
                'propertyData' => [
                    'setParameter' => []
                ],
                'parsedArguments' => [
                    'toFix' => []
                ],
                'items' => [
                    '_meta_data' => [
                        'notification' => [
                            'not_found' => [
                                'properties' => [],
                                'categories' => []
                            ]
                        ]
                    ]
                ]
            ],
            'isSearch' => 'product_search'
        ]);
    }
}
