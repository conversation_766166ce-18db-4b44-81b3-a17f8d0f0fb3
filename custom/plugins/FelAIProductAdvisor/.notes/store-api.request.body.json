{"limit": 1, "includes": {"calculated_price": ["id", "totalPrice"], "delivery_time": ["id", "max", "min", "name", "translated.name", "unit"], "media": ["alt", "fileExtension", "fileName", "fileSize", "fileSize", "fileSize", "id", "metaData", "thumbnails", "title", "translated", "url"], "media_thumbnail": ["alt", "height", "id", "mediaId", "width"], "product": ["active", "available", "availableStock", "calculatedMaxPurchase", "calculatedPrice.totalPrice", "canonicalProduct", "canonicalProductId", "categories", "categoriesRo", "categoryIds", "categoryTree", "children", "configuratorSettings", "cover", "coverId", "customFields", "deliveryTime", "deliveryTimeId", "description", "displayGroup", "downloads", "ean", "id", "isCloseout", "isNew", "keywords", "mainCategories", "manufacturer", "manufacturerId", "manufacturerNumber", "markAs<PERSON><PERSON>eller", "maxPurchase", "media", "metaDescription", "metaTitle", "minPurchase", "name", "optionIds", "options", "packUnit", "packUnitPlural", "parent", "parentId", "productNumber", "productReviews", "properties", "propertyIds", "purchaseSteps", "purchaseUnit", "ratingAverage", "referenceUnit", "releaseDate", "restockTime", "sales", "seoCategory", "seoUrls", "shippingFree", "stock", "streamIds", "streams", "tax", "taxId", "translated", "unit", "unitId"]}, "associations": {"categories": {"associations": []}, "manufacturer": {"associations": []}, "options": {"associations": {"group": {"associations": []}}}, "properties": {"associations": {"group": {"associations": []}}}, "productReviews": {"associations": []}}, "aggregations": [{"name": "fel_options", "type": "terms", "definition": "product", "field": "product.parentId", "aggregation": {"name": "options", "type": "entity", "definition": "property_group_option", "field": "options.id"}}, {"name": "fel_properties", "type": "terms", "definition": "product", "field": "product.parentId", "aggregation": {"name": "options", "type": "entity", "definition": "property_group_option", "field": "properties.id"}}, {"name": "fel_parent_product_ids", "type": "filter", "filter": [{"type": "equals", "field": "product.parentId", "value": null}], "aggregation": {"name": "fel_parent_product_ids", "type": "terms", "field": "product.id"}}]}