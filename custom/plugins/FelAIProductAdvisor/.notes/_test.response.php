<?php

use Shopware\Core\Framework\Context;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Fel\AIProductAdvisor\Service\OpenAI\OpenAIService;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Fel\AIProductAdvisor\Storefront\Controller\ChatbotController;

if ('order' === ($_GET['order'] ?? null)) {
    unset($_GET['order']);
}

/** $getProductProperties */
include __DIR__ . '/_test.response.helper.php';


// // /fel/chatbot/message?devmode=true&template=true
// if (isset($_GET['devmode'])) {
//     return require realpath(__DIR__ . '/../../..') . '/.notes/_test.response.php';
// }


$zoomChat = ' -fel-zoom-chat';
$query = $_GET['query'] ?? null;
$id = $_GET['id'] ?? null;
$useFunction = $_GET['use_function'] ?? null;

$availableFunctions = [
    'get_categories',
    'get_chatbot_name',
    'get_countries',
    'get_delivery_times',
    'get_manufacturer',
    'get_meta_information',
    'get_payment_methods',
    'get_product_details',
    'get_product_properties',
    'product_search',
];

$availableOrder = [
    'name-asc',
    'name-desc',
    'price-asc',
    'price-desc',
    'topseller',
];

$keepParameter = [
    'devmode' => true,
    'template' => true,
    'categories' => $_GET['categories'] ?? [],
    'manufacturer' => $_GET['manufacturer'] ?? [],
    'properties' => $_GET['properties'] ?? [],
    'query' => $_GET['query'] ?? null,
    'use_function' => $_GET['use_function'] ?? null,
    'price_min' => $_GET['price_min'] ?? null,
    'price_max' => $_GET['price_max'] ?? null,
    'order' => $_GET['order'] ?? null,
    'id' => $_GET['id'] ?? null,
    'filter-open' => $_GET['filter-open'] ?? null,
    'auto-submit' => $_GET['auto-submit'] ?? null,
    'full-output' => $_GET['full-output'] ?? null,
];

$setArguments = [
    'query'        => $query,
    'categories'   => is_string($keepParameter['categories']) ? explode('|', $keepParameter['categories']) : $keepParameter['categories'],
    'properties'   => is_string($keepParameter['properties']) ? explode('|', $keepParameter['properties']) : $keepParameter['properties'],
    'manufacturer' => is_string($keepParameter['manufacturer']) ? explode('|', $keepParameter['manufacturer']) : $keepParameter['manufacturer'],
    'price_min'    => $keepParameter['price_min'] ? intval($keepParameter['price_min']) : null,
    'price_max'    => $keepParameter['price_max'] ? intval($keepParameter['price_max']) : null,
    'order'        => $keepParameter['order'] ? $keepParameter['order'] : null,
    'id'           => $keepParameter['id'] ? $keepParameter['id'] : null,
];

$ignoreQueryParams = [
    'properties',
    'manufacturer',
    'categories',
    'price_min',
    'price_max',
    'order',
    'id',
    'filter-open',
    'auto-submit',
    'full-output',
];

$sleep = [
    'create' => 0,
    'delete' => 0,
    'default' => 0,
];

$useFunction = in_array($useFunction, $availableFunctions) ? $useFunction : 'get_categories';

if (!($_COOKIE['fel-chatbot-localstorage-accepted'] ?? null)) {
    setcookie('fel-chatbot-localstorage-accepted', 'allowed');
}

$setArguments =  str_replace('"', '\\"', json_encode($setArguments, 0));

$j = json_decode('{
    "id": "chatblock-1828764327' . time() . '",
    "datetime": "' . date(DATE_RFC850) . '",
    "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
    "threadMessages": [],
    "actionRequired": {
        "required_action": {
            "submit_tool_outputs": {
                "tool_calls": [
                    {
                        "id": "call_QQY6ieyxlKF2khiBxWxO1xx",
                        "type": "function",
                        "function": {
                            "name": "' . $useFunction . '",
                            "arguments": "' . $setArguments . '"
                        }
                    }
                ]
            }
        }
    }
}', true);


if ('create-thread' === $fn) {
    sleep($sleep['create']);
} else if ('delete-thread' === $fn) {
    sleep($sleep['delete']);
} else {
    sleep($sleep['default']);
}

if ('create-thread' !== $fn AND ($j['actionRequired'] ?? null)) {
    $actionRequired = $this->openAiService->requiredAction($j['actionRequired'], $request, $salesContext, $post);
    $j['actionExecuted'] = $actionRequired;

    $finalOut = $actionRequired;
    $finalOut = filterToolsubmitResponse($actionRequired);
    $finalOutCopy = $finalOut;

    if ($finalOut['output'][0]['output'] ?? null) {
        $getOutput = $finalOut['output'][0]['output'];

        if (is_string($getOutput)) {
            if (str_starts_with(trim($getOutput), '{') OR str_starts_with(trim($getOutput), '[')) {
                $finalOut['output'][0]['output'] = json_encode(json_decode($finalOut['output'][0]['output']), 480);
            }
        }
        if (!$finalOut['output'][0]['output']) {
            $finalOut['output'][0]['output'] = [$finalOutCopy];
        }
    }
}

$actionExecuted = $j['actionExecuted'][0]['output'] ?? null;

$j = $actionExecuted;
$jSearch = json_decode($j, true);
$isSearchResult = false;

if ('product_search' === ($jSearch['exec'] ?? null) OR 'product_details' === ($jSearch['exec'] ?? null)) {
    $isSearchResult = true;
    if (isset($_GET['template'])) {
        $parsed = $this->renderView('@FelAIProductAdvisor/storefront/component/product-listing.html.twig', [
            'isSearch' => $jSearch['exec'],
            'productsResponse' => $jSearch,
            'products' => $jSearch['fetch']['elements'] ?? [],
        ]);
    }
}

if (is_string($j) AND (substr($j, 0, 1) === '{' OR substr($j, 0, 1) === '[')) {
    if ($isSearchResult AND !$keepParameter['full-output']) {
        $j = json_encode([
            'total' => $jSearch['total'] ?? 0,
            'totalAll' => $jSearch['totalAll'] ?? 0,
            'notification' => $jSearch['_notification'] ?? $jSearch['notification'] ?? 'no notification array',
            'getFilter' => $jSearch['getFilter'],
            'items' => $jSearch['items'],
            'propertyData' => $jSearch['propertyData'],
        ], 480);
    } else {
        $j = json_encode($jSearch, 480);
    }
}

$j = print_r($j, true);

$parsed ??= null;
$hideChat = $parsed ? null : ' hidden';
$sLink = ($isSearchResult AND !$parsed) ? '<a href="?devmode=true&template=true">fetch</a>' : null;

$renderHeader = $this->renderView('@Storefront/storefront/layout/meta.html.twig', [
    'page' => ['metaInformation' => ['metaTitle' => $_SERVER['LOCAL_MACHINE_TITLE'] ?? null]],
]);

$getProductCategories = $getProductProperties['categories'] ?? [];
$getProductCategories = print_r($getProductCategories, true);
$addContents = [
    'available_functions' => null,
];

$fns = [];
foreach($availableFunctions as $fn) {
    $fns[] = sprintf($fn === $useFunction ? '<li><a>%1$s</a></li>' : '<li><a href="?%2$s">%1$s</a></li>'
        , $fn
        , http_build_query(array_merge($keepParameter, ['use_function' => $fn]))
    );
}
$addContents['available_functions'] = implode('', $fns);

// print $_SERVER['LOCAL_MACHINE_TITLE_HTML'];
// print '<pre>';
// print_r($getProductProperties['categories']);
// print '</pre>';
// dump($j);
// exit;






// if ('assistant' === ($data['role'] ?? null)) {
//     $contentValue = $data['content'][0]['text']['value'];
//     $replaceSourceInfo = $this->getMessageSourceList($contentValue);
//     if ($getCitatitonsSrc = ($data['content'][0]['text']['annotations'][0]['text'] ?? null)) {
//         $replaceSourceInfo[] = $getCitatitonsSrc;
//     }
//     $data['content'][0]['text']['value'] = str_replace($replaceSourceInfo, '', $contentValue);
// }

// $getMessages = json_decode('{
//     "object": "list",
//     "data": [
//         {
//             "id": "msg_zMgNcghMP8CM5vvd2jGcjbzX",
//             "object": "thread.message",
//             "created_at": 1718034985,
//             "assistant_id": "asst_3Lj19LSeFfxfil0qa5TfyyNr",
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": "run_Z4nwZdwUSyLg3X1pFtug9JKt",
//             "role": "assistant",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "<div class=\"fel-oai-response\">\n    <p>Hier sind einige Vorschläge für Futter für Bartagamen:</p>\n    <ul>\n        <li><a href=\"/detail/018ec6cb651372d4a2e249b7a992dbeb\"><b>Zwergbartagame Fire Red Pogona henrylawsoni</b></a></li>\n        <li><a href=\"/detail/018ec6cb6a187083983a454c4ca1c66f\"><b>Bartagamen – Biologie, Pflege, Zucht, Erkrankungen und Zuchtformen</b></a></li>\n        <li><a href=\"/detail/018ec6cb6ac971a9b9d7d4f437dd516e\"><b>Flumi Futterpflanze</b></a></li>\n        <li><a href=\"/detail/018ec6cb6af07259b712c6255a903013\"><b>Art für Art Die Bartagame</b></a></li>\n        <li><a href=\"/detail/018ec6cb6af273a781315cba8a923aa8\"><b>Lucky Reptile Herb Garden Löwenzahn Reptilienfutter</b></a></li>\n        <li><a href=\"/detail/018ec6cb6b41711b9c4d9ea3cc81fb18\"><b>Repashy Superfoods Beardie Buffet</b></a></li>\n        <li><a href=\"/detail/018ec6cb6d3872e6aa725cc0f5468519\"><b>Herp Diner Superworms + Calcium</b></a></li>\n        <li><a href=\"/detail/018ec6cb6d85728ebafc0c490f62e2c4\"><b>Bartagame Cawley Super Red</b></a></li>\n    </ul>\n</div>",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_jXRxp5Xgmh4r4b9vAPJoDHGu",
//             "object": "thread.message",
//             "created_at": 1718034976,
//             "assistant_id": "asst_3Lj19LSeFfxfil0qa5TfyyNr",
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": "run_Z4nwZdwUSyLg3X1pFtug9JKt",
//             "role": "assistant",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "<div class=\"fel-oai-response\">\n    <p>Kein Problem! Lassen Sie mich Ihnen einige Empfehlungen für Bartagamenfutter geben. Bitte einen Moment Geduld.</p>\n</div>",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_iAtEujtS8cgdkSzkdNaE6lMn",
//             "object": "thread.message",
//             "created_at": 1718034975,
//             "assistant_id": null,
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": null,
//             "role": "user",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "ich hab keine ahnung, ich bin nur ein mensch",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_adw5tGhFL1ZoehTNBbn7K3Wm",
//             "object": "thread.message",
//             "created_at": 1718034950,
//             "assistant_id": "asst_3Lj19LSeFfxfil0qa5TfyyNr",
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": "run_HbR8ZxrPw05jXFuna2ytozO2",
//             "role": "assistant",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "<div class=\"fel-oai-response\">\n    <p>Selbstverständlich kann ich Ihnen bei der Suche nach Futter für Ihre Bartagame helfen. Geben Sie mir bitte mehr Informationen, wie zum Beispiel die Art des Futters oder spezifische Anforderungen.</p>\n</div>",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_xNqY8P1qeb7NzoV8tHAJ821K",
//             "object": "thread.message",
//             "created_at": 1718034948,
//             "assistant_id": null,
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": null,
//             "role": "user",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "ich brauche futter für meine bartagame, aber ich weiss nicht, wo ich suchen soll? Kannst du mir helfen?",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_LqmuB4RTJ833QSkwysWB9WoG",
//             "object": "thread.message",
//             "created_at": 1718034929,
//             "assistant_id": "asst_3Lj19LSeFfxfil0qa5TfyyNr",
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": "run_VhgFuab6AmwVBBjmg7etMMP6",
//             "role": "assistant",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "<div class=\"fel-oai-response\">\n    <p>Guten Tag! Wie kann ich Ihnen behilflich sein?</p>\n</div>",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         },
//         {
//             "id": "msg_iVfSpjQuxrxwapYjLxOMwMXX",
//             "object": "thread.message",
//             "created_at": 1718034928,
//             "assistant_id": null,
//             "thread_id": "thread_qnhncU1EI648HgbXbfPwD6vS",
//             "run_id": null,
//             "role": "user",
//             "content": [
//                 {
//                     "type": "text",
//                     "text": {
//                         "value": "hallo",
//                         "annotations": []
//                     }
//                 }
//             ],
//             "attachments": [],
//             "metadata": []
//         }
//     ],
//     "first_id": "msg_W3qvBWxs2g0hABMSey0DoVGG",
//     "last_id": "msg_k6effcLtEtpjlL0cCkbBYOCh",
//     "has_more": true
// }', true)['data'] ?? [];

// print $_SERVER['LOCAL_MACHINE_TITLE_HTML'];
// print '<pre>';

// // usort($getMessages, fn($a, $b) => $a['created_at'] <=> $b['created_at']);
// $newMessages = [];
// $break = false;
// foreach($getMessages as $i => $message) {
//     $role = $message['role'] ?? null;
//     if ('assistant' === $role AND !$break) {
//         $newMessages[$message['created_at'] . $i] = [
//             'value'       => $message['content'][0]['text']['value'] ?? null,
//             'id'          => $message['id'],
//             'role'        => $role,
//             'datetime'    => date(DATE_RFC850, $message['created_at'] ?? time()),
//             'annotations' => $message['content'][0]['text']['annotations'] ?? null,
//         ];
//     }
//     if ('user' === $role) {
//         $break = true;
//         // break;
//     }
// }

// ksort($newMessages, SORT_REGULAR);

// print htmlspecialchars(print_r($newMessages, true));
// print '</pre>';
// exit;


















if ($isSearchResult AND ($getProductProperties['properties'] ?? null)) {
    $parsedProps = is_string($keepParameter['properties']) ? explode('|', $keepParameter['properties']) : $keepParameter['properties'];
    $fullMap = array_merge($getProductProperties['properties'], $setManus ?? []);
    $parsedCategories = is_string($keepParameter['categories']) ? explode('|', $keepParameter['categories']) : $keepParameter['categories'];
    $setFilterOpen = $keepParameter['filter-open'] ? 'checked' : null;
    $setAutoSubmit = $keepParameter['auto-submit'] ? 'checked' : null;
    $setFullOutput = $keepParameter['full-output'] ? 'checked' : null;
    $autoSubmitAttr = $setAutoSubmit ? ' onchange="autoSubmit(this)"' : null;

    $keepQueryParams = [];
    foreach($keepParameter as $name => $params) {
        if (!in_array($name, $ignoreQueryParams)) {
            $params = is_array($params) ? implode('', $params) : $params;
            $keepQueryParams[] = sprintf('<input type="hidden" name="%1$s" value="%2$s">', $name, $params);
        }
    }
    $keepQueryParams = implode('', $keepQueryParams);

    $outProps = [];
    foreach($fullMap as $propGroup => $props) {
        $propsContainsActive[$propGroup] = false;
        foreach($props as $i => $prop) {
            $isActive = in_array($prop, $parsedProps) OR array_key_exists($prop, $parsedProps);
            if ($isActive AND !$propsContainsActive[$propGroup]) {
                $propsContainsActive[$propGroup] = true;
            }
            $outProps[$propGroup][] = sprintf('
                <label>
                    <input type="checkbox" name="properties[]" value="%1$s" %5$s %2$s> <span>%1$s</span>
                </label>'
                , $prop
                , $isActive ? ' checked' : null
                , 'manufacturer' === $propGroup
                , print_r($parsedProps, true)
                , $autoSubmitAttr
            );
        }

        $outProps[$propGroup] = sprintf('
            <div class="prop-group-container order-%3$s" style="">
                <div class="my-2 mt-1">%1$s</div>
                <div class="d-flex check-options">%2$s</div>
            </div>'
            , ucfirst($propGroup)
            , implode('', $outProps[$propGroup])
            , $propsContainsActive[$propGroup] ? 1 : 2
        );
    }

    $outProps = implode('', $outProps);

    $categoryList = [];
    $c = 0;
    foreach($getProductProperties['categories'] as $category) {
        $isActive = in_array($category['name'], $parsedCategories);
        if ($isActive) {
            ++$c;
        }
        $categoryList[] = sprintf('
            <label class="order-%6$s" style="order:%6$s">
                <input type="checkbox" name="categories[]" value="%1$s" %5$s %4$s>
                <span>%1$s</span>
                <div class="fel-tooltip"><a href="%3$s" target="_blank">%2$s %1$s</a></div>
            </label>'
            , $category['name']
            , $category['parent'] ? implode(' » ', $category['parent']) : null
            , $category['url']
            , $isActive ? 'checked' : null
            , $autoSubmitAttr
            , $isActive ? $c : 10
        );
    }
    $categoryList = sprintf('
        <div class="prop-group-container categories-container">
            <div class="my-2">Categories</div>
            <div class="d-flex check-options check-options-categories">%1$s</div>
        </div>', implode('', $categoryList)
    );

    $setOrder = ['<select class="form-control p-1" name="order" ' . $autoSubmitAttr . '><option>order</option>'];
    foreach($availableOrder as $order) {
        $setOrder[] = sprintf('<option value="%1$s" %2$s>%1$s</option>'
            , $order
            , $order === $keepParameter['order'] ? 'selected' : null
        );
    }
    $setOrder = implode('', $setOrder) . '</select>';

    $setPropsForm = $isSearchResult ? <<<EOT
        <div class="px-3">
            <form action="" method="get" class="mt-2 mb-2 set-props-form">
                <label role="button">
                    autosubmit
                    <input type="checkbox" name="auto-submit" value="true" class="toggle-form" onchange="this.form.submit()" {$setAutoSubmit}>
                </label>
                <label for="full-output" role="button" class="ms-2 pe-1">
                    full-output
                    <input type="checkbox" id="full-output" name="full-output" value="true" class="toggle-form me-2 p-0 w-auto" {$autoSubmitAttr} {$setFullOutput}>
                </label>
                <label for="filter-open" role="button" class="ms-2 pe-1">filter</label>
                <input type="checkbox" id="filter-open" name="filter-open" value="true" class="toggle-form me-2 p-0 w-auto" $setFilterOpen>
                <a class="ms-3" href="{$chatbotPath}?devmode=1&template=1&use_function=product_search">reset</a>
                <div class="form-inner-wrapper">
                    {$keepQueryParams}
                    <div class="mb-2 inut-fields d-flex">
                        <div class="me-2 w-100"><input class="form-control p-1" type="text" name="query" value="{$query}" placeholder="query"></div>
                        <div class="me-2 w-100"><input class="form-control p-1" type="text" name="id" value="{$id}" placeholder="id"></div>
                        <div class="me-2 w-25"><input class="form-control p-1" type="number" name="price_min" value="{$keepParameter['price_min']}" placeholder="price_min"></div>
                        <div class="me-2 w-25"><input class="form-control p-1" type="number" name="price_max" value="{$keepParameter['price_max']}" placeholder="price_max"></div>
                        <div class="me-2 w-25">{$setOrder}</div>
                        <button class="btn-btn-dark border-light">set</button>
                    </div>
                    <div class="prop-set-container">
                        <div class="category-set-container mb-0 w-100">
                            {$categoryList}
                        </div>
                        {$outProps}
                    </div>
                </div>
            </form>
        </div>
    EOT : null ;
}

$setPropsForm ??= null;

return new Response(<<<EOT
<!DOCTYPE html><html itemscope="itemscope" itemtype="https://schema.org/WebPage">{$renderHeader}<body>{$sLink}
<script>
    window.onload = function() {
        var getOpener = document.querySelector('.fel-toggle-chatbot-button.toggle-open');
        if (getOpener) {
            // document.querySelector('.fel-chat-link-category-ul-list').scrollIntoView();
        }
    }
</script>
<div class="fel-test-app">
    <ul class="mb-0 py-2">{$addContents['available_functions']}</ul>

    {$setPropsForm}

    <div id="fel-chatbot"
        class="fel-chat-initial fel-zoom-intial contains-thread fel-localstorage-is-allowed {$zoomChat}"
        data-options="{&quot;page&quot;:{&quot;productId&quot;:null,&quot;parentId&quot;:null,&quot;requestedProductId&quot;:null,&quot;categoryId&quot;:&quot;2fb7998f223646d4ae8427c029c96757&quot;,&quot;categoryPath&quot;:&quot;2fb7998f223646d4ae8427c029c96757&quot;,&quot;breadcrumbs&quot;:{&quot;2fb7998f223646d4ae8427c029c96757&quot;:&quot;Catalogue #1&quot;}},&quot;translation&quot;:{&quot;removeChatMessage&quot;:&quot;Remove question and answer&quot;,&quot;errorClearChat&quot;:&quot;An error occurred, please delete the chat&quot;}}"
        data-set-bottom="24px" data-set-right="24px"
        data-assistant-gpt-plugin
        {$hideChat}>
        <div class="fel-chatbot-container">
            <div class="fel-chatbot-header">
                <div class="fel-chatbot-options d-flex justify-content-between">
                    <div class="align-items-center fel-header-chatbot-info">
                        <div class="fel-chatbot-avatar-container">
                            <div class="fel-chatbot-avatar text-center">
                                <img src="/bundles/felaiproductadvisor/chatbot-avatar-female-sm.png?1717493075" alt="Chatbot Avatar">
                            </div>
                        </div>
                        <div class="px-3 text-light">
                                <p class="m-0"><b>Nummer 5.0</b></p>
                                <p class="d-flex align-items-center fel-online-status-wrapper m-0">
                                <span class="online-status-dot text-success me-1"></span>
                                <span>online</span>
                            </p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center pt-2">
                        <div class="btn-group">
                            <button type="button" class="fel-delete-thread-button fel-btn btn btn-small btn-primary" data-callback="deleteChatThread" title="Clear Chat" aria-label="Clear Chat"><span class="icon icon-arrow-360-right icon-help pe-none"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24"><defs><path d="M17.5858 6H12.5C8.3579 6 5 9.3579 5 13.5S8.3579 21 12.5 21s7.5-3.3579 7.5-7.5c0-.5523.4477-1 1-1s1 .4477 1 1c0 5.2467-4.2533 9.5-9.5 9.5S3 18.7467 3 13.5 7.2533 4 12.5 4h5.0858l-2.293-2.2929c-.3904-.3905-.3904-1.0237 0-1.4142.3906-.3905 1.0238-.3905 1.4143 0l4 4c.3905.3905.3905 1.0237 0 1.4142l-4 4c-.3905.3905-1.0237.3905-1.4142 0-.3905-.3905-.3905-1.0237 0-1.4142L17.5858 6z" id="icons-default-arrow-360-right"></path></defs><use xlink:href="#icons-default-arrow-360-right" fill="#758CA3" fill-rule="evenodd"></use></svg></span></button>
                            <button type="button" class="fel-zoom-button fel-btn fel-ui-btn btn btn-small btn-primary" data-callback="toggleChatZoom" title="Toggle Chatbox size" aria-label="Toggle Chatbox size"><span class="icon icon-code icon-editor-expand pe-none"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24"><defs><path d="M13.0299 3.7575c.134-.5358.6768-.8616 1.2126-.7276.5358.134.8616.6768.7276 1.2126l-4 16c-.134.5358-.6768.8616-1.2126.7276-.5358-.134-.8616-.6768-.7276-1.2126l4-16zM6.707 7.707 2.4142 12l4.293 4.2929c.3904.3905.3904 1.0237 0 1.4142-.3906.3905-1.0238.3905-1.4143 0l-5-5c-.3905-.3905-.3905-1.0237 0-1.4142l5-5c.3905-.3905 1.0237-.3905 1.4142 0 .3905.3905.3905 1.0237 0 1.4142zm10.586 8.586L21.5858 12l-4.293-4.2929c-.3904-.3905-.3904-1.0237 0-1.4142.3906-.3905 1.0238-.3905 1.4143 0l5 5c.3905.3905.3905 1.0237 0 1.4142l-5 5c-.3905.3905-1.0237.3905-1.4142 0-.3905-.3905-.3905-1.0237 0-1.4142z" id="icons-default-code"></path></defs><use xlink:href="#icons-default-code" fill="#758CA3" fill-rule="evenodd"></use></svg></span></button>
                            <button type="button" class="fel-toggle-chatbot-button fel-btn btn fel-ui-btn btn-small btn-primary toggle-close" title="Hide Chat" aria-label="Hide Chat" data-callback="toggleChatbot" data-toggle-class="active"><span class="px-1 pe-none fel-pe-none" style="font-size: 1.1rem;">X</span></button>
                            <button type="button" class="fel-toggle-chatbot-button fel-btn fel-ui-btn btn btn-small btn-primary toggle-open" title="Open Chat" aria-label="Open Chat" data-callback="toggleChatbot" data-toggle-class="active">Open Chat</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="fel-chatbot-messages" class="px-3">
                {$parsed}
            </div>
            <hr class="fel-chat-divider m-0">
            <form id="fel-chatbot-form" class="bg-light m-0" action="" method="get" hidden>
                <div class="fel-chatbot-inputs w-100">
                    <input type="hidden" name="fel-chatbot-run-id">
                    <input type="hidden" name="fel-chatbot-thread-id">
                    <input type="hidden" name="fel-chatbot-user-input">
                </div>
            </form>
            <form id="fel-chatbot-form" class="bg-light m-0" action="{$chatbotPath}" method="get">
                <div class="fel-chatbot-inputs w-100">
                    <input type="hidden" name="devmode" value="true">
                    <input type="hidden" name="template" value="true">
                    <input type="hidden" name="use_function" value="{$useFunction}">
                    <input
                        type="text"
                        class="form-control input bg-white p-3 border-0"
                        name="query"
                        value="{$query}"
                        placeholder="Enter your Message">
                </div>
                <div class="fel-submit-btn bg-white">
                    <button type="submit" class="fel-submit-chat-btn btn bg-white py-2 px-3" title="Submit" aria-label="Submit"><span class="icon icon-paperplane icon-forward pe-none"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" viewBox="0 0 24 24"><defs><path d="m6.7208 13-2.031 6.093L19.664 12 4.6897 4.907 6.7207 11H12c.5523 0 1 .4477 1 1s-.4477 1-1 1H6.7208zm-1.7749-1L2.0513 3.3162c-.2785-.8355.5809-1.597 1.3768-1.22l19 9c.7625.3613.7625 1.4463 0 1.8075l-19 9c-.796.377-1.6553-.3844-1.3768-1.22L4.946 12z" id="icons-default-paperplane"></path></defs><use xlink:href="#icons-default-paperplane" fill="#758CA3" fill-rule="evenodd"></use></svg></span>                            </button>
                </div>
            </form>

        </div>
    </div>
    <pre style="margin: 10px 0 0 15px;max-width: calc(100% - 440px);">{$j}</pre>
</div>

<style>
    body {
        margin: 0 !important;
        padding: 0 !important;
        background: #fff !important;
    }
    .fel-test-app {
        min-height: 100vh;
        background: #fff;
        transition: all .1s ease-in-out;
        label {
            transition: all .1s ease-in-out;
        }
    }
    body.is-loading .fel-test-app {
        background: #f5f5f5;
        opacity: .9;
        pointer-events: none;
        label {
            opacity: .5;
        }
    }
    [type="checkbox"] {
        position: relative;
        top: 2px;
    }
    .prop-set-container {
        display: flex;
        flex-wrap: wrap;
    }
    .category-set-container {
        order: 0;
    }
    .contains-checked,
    .contains-no-checked {
        flex: 100% 1;
    }
    .contains-checked {
        order: 1;
    }
    .contains-no-checked {
        order: 2;
    }
    #fel-chatbot.active:not(.fel-zoom-chat) {
        height: calc(100% - 40px) !important;
    }
    .fel-chatbot-options > .d-flex {
        height: 52px;
        .btn-group {
            height: 100%;
        }
    }
    .fel-chat-product-list-item {
        background: #fff;
    }
    pre {
        padding: 1rem;
        box-shadow: 0 0 8px #bbb;
    }
    .toggle-form {
        padding: 1rem;
        width: 20px;
    }
    .toggle-form:checked + a + .form-inner-wrapper {
        margin-top: 10px;
        padding: 1rem;
        box-shadow: 0 0 8px #aaa;
        display: block;
    }
    .prop-set-container {
        max-height: 40vh;
        overflow: auto;
        /* overscroll-behavior: contain; */
    }
    .set-props-form {
        max-width: calc(100% - 410px);

        .form-inner-wrapper {
            display: none;
        }
        .prop-group-container.categories-container {
            .check-options.check-options-categories {
                padding-bottom: 10px !important;
                overflow-x: hidden;
            }
        }
        .prop-group-container {
            flex: 100%;

            &.order-1 {
                padding: 0 0 0 1rem;
                border-left: 4px solid #e5e5e5;
            }
        }
        .category-set-container {
            margin-bottom: -2px !important;
        }
        .check-options {
            margin: 0 0 0;
            padding: 2px;
            flex-wrap: wrap;
            max-height: 200px;
            overflow: auto;

            &.check-options-categories {
                label {
                    flex: 8% 1;
                }
            }
            label {
                margin: 0 10px 10px 0;
                padding: 0;
                min-width: fit-content;
                display: flex;
                flex: 5% 0;
                align-items: center;
                justify-content: center;
                position: relative;
                font-size: 90%;
                cursor: pointer;

                [type="checkbox"] {
                    display: none;
                }
                span {
                    padding: 4px 12px 3px;
                    width: 100%;
                    min-width: 58px;
                    display: block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-align: center;
                    background: #eee;
                    box-shadow: 0 0 3px #999;
                }
                input:checked + span {
                    color: #e52427;
                    background: #fff;
                    box-shadow: 0 0 3px #e5242788;
                }
                &:hover .fel-tooltip {
                    z-index: 55555;
                    top: 100%;
                    opacity: 1;
                    visibility: visible;
                    transition: opacity 0.1s ease-in 1s;
                }
                .fel-tooltip {
                    margin-top: 0;
                    padding: 0 0 0;
                    width: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: -1;
                    opacity: 0;
                    visibility: hidden;
                    white-space: nowrap;
                    overflow: hidden;
                    box-shadow: 0 0 4px #aaa;
                    transition: opacity 0s ease 0s;

                    a {
                        padding: 2px 8px 1px;
                        min-width: 100%;
                        display: block;
                        background: #fff;
                        font-size: 11px;
                    }
                }
                &:last-of-type .fel-tooltip {
                    left: unset;
                    right: 0;
                }
            }
        }
        input:checked + span {
            color: red;
        }
    }
</style>
<script>
    (()=> {
        if (typeof sessionStorage !== 'undefined') {
            const felScrollPos = sessionStorage.getItem('felScrollpos');
            if (felScrollPos) {
                const felScrollY = felScrollPos;
                if (parseInt(felScrollY)) {
                    document.documentElement.style.scrollBehavior = 'initial';
                    window.scrollTo(0, parseInt(felScrollY));
                    sessionStorage.removeItem('felScrollpos');
                    document.documentElement.style.scrollBehavior = null;
                }
            }
            window.addEventListener('beforeunload', ()=> {
                if (window.scrollY) {
                    sessionStorage.setItem('felScrollpos', `\${window.scrollY}`);
                }
            });
        }
    })();

    function autoSubmit(el) {
        document.body.classList.add('is-loading');
        el.form.submit()
    }
</script>
</body>
</html>
EOT
, 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
