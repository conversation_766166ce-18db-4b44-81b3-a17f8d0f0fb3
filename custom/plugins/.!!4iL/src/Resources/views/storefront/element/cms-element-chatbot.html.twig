{% block element_chatbot %}
    {% set cmsAssistantId = element.config.cmsAssistantId.value %}
    {% set themeColor = element.config.cmsChatThemeColor.value %}

    <div class="cms-element-chatbot" 
         data-cms-assistant-gpt-plugin 
         data-cms-assistant-id="{{ cmsAssistantId }}"
         data-theme-color="{{ themeColor }}">
        <h2>{{ element.config.cmsAdvisorTitle.value }}</h2>
        <div id="chatbot-window" data-options="{{ felPluginOptions|json_encode }}"></div>
        {% sw_include '@Storefront/storefront/custom/fel-cms-chatbot-window.html.twig' with {
            'cmsAssistantId': cmsAssistantId,
            'themeColor': themeColor
        } %}
    </div>
    <template data-cms-assistant-gpt-plugin></template>
{% endblock %}
