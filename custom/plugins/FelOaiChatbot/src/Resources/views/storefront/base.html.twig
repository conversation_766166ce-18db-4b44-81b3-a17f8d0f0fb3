{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_header_inner %}
	{{ parent() }}
    {# /** TEMPORARY DEV HACK | IGNORE */ #}
    <script>document.title += ` local-dev-many-title`;{% sw_include '@Storefront/storefront/dev.js' %}</script>
    <style>
        /* #fel-oai-chatbot {
            opacity: 0;
        } */

        /* .fel-loading-msg-placeholder::after {
            display: inline-block;
            animation: dotty steps(1,end) 1s infinite;
            content: ' ';
        }
        @keyframes dotty {
            0%   { content: ' '; }
            25%  { content: ' .'; }
            60%  { content: ' ..'; }
            100%  { content: ' ...'; }
        } */
        /* 2.8571428571428568s */
        .fel-loading-msg-placeholder::after {
            content: '…';
            animation: fade 1.2s ease-in-out infinite;
        }
        @keyframes fade {
            0%, 100% { opacity: 0.2; }
            50% { opacity: 1; }
        }
    </style>
{% endblock %}


{% set felChatbotConfig = config('FelOaiChatbot.config') %}
{% set chatEnabled = felChatbotConfig.enablePlugin %}
{% set felPluginOptions = {
    layout: {
        scrollUpIsLeft: felChatbotConfig.felScrollUpIsLeft
    },
    page: {
        refId: page.product.parentId ?? page.product.id ?? null,
        categoryPath: page.header.navigation.active.get('breadcrumb')|keys|join('|'),
    },
    translation: {
        removeChatMessage: 'fel-oai-chat.chat.deleteChatMessage'|trans,
        waitASecond: 'fel-oai-chat.chat.wait.a.second'|trans,
    },
    controllerPath: {
        thread: path('frontend.fel.chat.thread'),
        delete: path('frontend.fel.chat.delete_thread')
    }
} %}

{% block base_footer %}

    {% if felChatbotConfig.felOpenAiApiKey is empty or felChatbotConfig.felOpenAiAssistantId is empty %}
        {% set chatEnabled = false %}
    {% elseif felChatbotConfig.authenticatedUserOnly %}
        {% if context.customer is empty %}
            {% set chatEnabled = false %}
        {% else %}
            {% if felChatbotConfig.authenticatedUserOnlyGroup and context.customer.groupId not in felChatbotConfig.authenticatedUserOnlyGroup %}
                {% set chatEnabled = false %}
            {% endif %}
        {% endif %}
    {% endif %}

    {% if chatEnabled %}
        {% set getCustomAvatarId = felChatbotConfig.chatbotCustomAvatar %}
        {% set chatbotAvatarUrl = asset("bundles/feloaichatbot/chatbot-avatar-#{felChatbotConfig.chatbotAvatar}-sm.png", 'asset') %}
        {% set chatbotLoaderFile = asset("bundles/feloaichatbot/fel-loader-#{felChatbotConfig.chatbotLoadingIcon}.svg", 'asset') %}

        {% if getCustomAvatarId %}
            {% set mediaCollection = searchMedia([getCustomAvatarId], context.context) %}
            {% set getMediaFile = mediaCollection.get(getCustomAvatarId) %}
            {% if getMediaFile.url %}
                {% set chatbotAvatarUrl = getMediaFile.url %}
            {% endif %}
        {% endif %}
        {% set fel_chatbot_avatar %}
            <img src="{{ chatbotAvatarUrl }}" alt="{{ 'fel-oai-chat.chat.avatarTitle'|trans }}" />
        {% endset %}

        {% block fel_chatbot_template %}
            <template id="fel-oai-chatbot-template">
                {# greetings container #}
                <div class="fel-chatbot-greeting-container fel-chat-fade-in h-100 d-flex justify-content-center align-items-center">
                    <div class="fel-chatbot-greeting">
                        <div class="fel-chatbot-avatar text-center">
                            {{ fel_chatbot_avatar }}
                        </div>
                        <div class="fel-chatbot-greeting-text text-center pt-1 pb-2">
                            <button type="button"
                                class="btn btn-link fel-btn"
                                title="{{ 'fel-oai-chat.chat.submit'|trans }}"
                                data-callback="addToChatSubmit"
                                data-prompt="{{ 'fel-oai-chat.chat.whats.your.purpose'|trans }}"
                                aria-label="{{ 'fel-oai-chat.chat.initial.greeting'|trans }}">
                                {{ 'fel-oai-chat.chat.initial.greeting'|trans }}
                            </button>
                        </div>
                        <div class="fel-chatbot-config-wrapper py-3 bg-white">
                            <div class="fel-cookie-consent">
                                <div class="px-3">
                                    <div class="form-text form-check">
                                        <input type="checkbox"
                                            name="fel-allow-localstorage"
                                            id="fel-allow-localstorage"
                                            class="form-check-input fel-checkbox"
                                            data-blur="true"
                                            data-callback="toggleLocalStorage">
                                        <label for="fel-allow-localstorage" class="form-check-label align-text-top ps-1">
                                            {{ 'fel-oai-chat.cookie.description'|trans }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {# loading indicator container #}
                <div class="fel-chatbot-load-container fel-chat-fade-in">
                    <div class="fel-chatbot-loader py-3 fel-loading-icon-{{ felChatbotConfig.chatbotLoader }}"
                        style="background-image: url({{ chatbotLoaderFile }});"></div>
                </div>
            </template>
        {% endblock %}

        {% if felChatbotConfig.enableContactFormAssistant %}
            {% block fel_chatbot_contact_to_ai %}
                <template id="fel-contact-to-chat-template" data-oai-contact-to-chatbot-plugin>
                    <div class="fel-contact-to-chat-button-container mb-2">
                        <button type="button" class="btn btn-warning fel-ext-btn fel-ask-chat-first fel-chat-fade-in" data-callback="addToChatSubmit">
                            {{ 'fel-oai-chat.contactToChat.askChatFirst'|trans }}
                        </button>
                        {% if 'fel-oai-chat.contactToChat.customMessage'|trans is not empty %}
                            <small class="fel-contact-to-chat-custom-message">{{ 'fel-oai-chat.contactToChat.customMessage'|trans }}</small>
                        {% endif %}
                    </div>
                    <div class="fel-contact-to-chat-response-container fel-initial fel-is-hidden">
                        <div class="fel-contact-to-chat-remove-container only-close">
                            <button type="button"
                                class="btn fel-ext-btn fel-remove-response btn-close close"
                                title="{{ 'fel-oai-chat.contactToChat.remove'|trans }}"
                                aria-label="{{ 'fel-oai-chat.contactToChat.remove'|trans }}"
                                data-callback="removeChatMessage"
                                data-blur="true"></button>
                        </div>
                        <div class="fel-inner"></div>
                    </div>
                </template>
            {% endblock %}
        {% endif %}

        <div id="fel-oai-chatbot"
            class="fel-chat-initial fel-zoom-intial fel-chat-fade-in"
            data-options="{{ felPluginOptions|json_encode }}"
            data-oai-chatbot-plugin>
            <div class="fel-chatbot-container">
                {% block fel_chatbot_options_buttons %}
                    <div class="fel-chatbot-header">
                        <div class="fel-chatbot-options d-flex justify-content-between">
                            <div class="align-items-center fel-header-chatbot-info">
                                <div class="fel-chatbot-avatar-container">
                                    <div class="fel-chatbot-avatar text-center">
                                        {{ fel_chatbot_avatar }}
                                    </div>
                                </div>
                                <div class="ps-3 pe-1 text-light fel-chatbot-name-wrapper">
                                    {% if felChatbotConfig.openAiChatbotName %}
                                        <p class="m-0"><b>{{ felChatbotConfig.openAiChatbotName }}</b></p>
                                    {% endif %}
                                    <p class="d-flex align-items-center fel-online-status-wrapper m-0">
                                        <span class="online-status-dot text-success me-1"></span>
                                        <span>{{ 'fel-oai-chat.chat.onlineStatus'|trans }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="btn-group">
                                    <button type="button"
                                        class="fel-delete-thread-button fel-btn btn btn-small btn-primary"
                                        data-callback="deleteChatThread"
                                        title="{{ 'fel-oai-chat.chat.delete'|trans }}"
                                        aria-label="{{ 'fel-oai-chat.chat.delete'|trans }}">
                                        {% sw_icon 'arrow-360-right' style {'class': 'help pe-none'} %}
                                    </button>
                                    <button type="button"
                                        class="fel-zoom-button fel-btn fel-ui-btn btn btn-small btn-primary"
                                        data-callback="toggleChatZoom"
                                        title="{{ 'fel-oai-chat.chat.toggleZoomChat'|trans }}"
                                        aria-label="{{ 'fel-oai-chat.chat.toggleZoomChat'|trans }}">
                                        {% sw_icon 'code' style {'class': 'editor-expand pe-none'} %}
                                    </button>
                                    <button type="button"
                                        class="fel-toggle-chatbot-button fel-btn btn fel-ui-btn btn-small btn-primary toggle-close"
                                        title="{{ 'fel-oai-chat.chat.close'|trans }}"
                                        aria-label="{{ 'fel-oai-chat.chat.close'|trans }}"
                                        data-callback="toggleChatbot"
                                        data-toggle-class="active">
                                        {% sw_icon 'x' style {'class': 'x pe-none'} %}
                                    </button>
                                    <button type="button"
                                        class="fel-toggle-chatbot-button fel-btn fel-ui-btn btn btn-small btn-primary toggle-open"
                                        title="{{ 'fel-oai-chat.chat.open'|trans }}"
                                        aria-label="{{ 'fel-oai-chat.chat.open'|trans }}"
                                        data-callback="toggleChatbot"
                                        data-toggle-class="active">
                                        {{ 'fel-oai-chat.chat.open'|trans }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endblock %}
                {% block fel_chatbot_messages_container %}
                    <div id="fel-oai-chatbot-messages"></div>
                    <hr class="fel-chat-divider m-0" />
                {% endblock %}
                {% block fel_chatbot_form_user %}
                    <form id="fel-oai-chatbot-form"
                        class="bg-light"
                        action="{{ felPluginOptions.controllerPath.thread }}"
                        method="post">
                        <div class="fel-chatbot-inputs w-100">
                            <input type="hidden" name="fel-chatbot-run-id" value="">
                            <input type="hidden" name="fel-chatbot-thread-id" value="">
                            <input type="text"
                                class="form-control input bg-white p-3 border-0"
                                name="fel-chatbot-user-input"
                                value=""
                                placeholder="{{ 'fel-oai-chat.chat.placeholder'|trans }}"
                                required="true"
                                minlength="2"
                                maxlength="{{ felChatbotConfig.openAiUserInputMaxLength }}"
                                autocomplete="off">
                        </div>
                        <div class="fel-submit-btn bg-white">
                            <button type="submit"
                                class="fel-submit-chat-btn btn bg-white h-100 px-3"
                                title="{{ 'fel-oai-chat.chat.submit'|trans }}"
                                aria-label="{{ 'fel-oai-chat.chat.submit'|trans }}">
                                {% sw_icon 'paperplane' style {'class': 'forward pe-none'} %}
                            </button>
                        </div>
                    </form>
                {% endblock %}
            </div>
        </div>
    {% endif %}

    {{ parent() }}
{% endblock %}
