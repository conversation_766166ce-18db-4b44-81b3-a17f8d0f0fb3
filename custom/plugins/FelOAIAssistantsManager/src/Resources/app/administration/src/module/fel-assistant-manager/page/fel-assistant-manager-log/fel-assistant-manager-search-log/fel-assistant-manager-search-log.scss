/** Specific styles for the search log component **/
.fel-assistant-manager-search-log {
    .fel-pagination {
        // .sw-pagination__per-page {
        //     margin-right: 0;
        //     position: relative;
        // }
        #perPage {
            // min-width: 70px;
            appearance: unset;
        }
    }
    .fel-search-query {
        font-weight: bold;
        font-size: 1em;
        color: var(--color-darkgray-400);
    }
    .fel-tag {
        display: inline-block;
        margin: 2px;
        padding: 4px 10px;
        border-radius: 8px;
        background-color: var(--color-gray-200);
        color: var(--color-darkgray-400);
    }
    .fel-tag-category {
        background-color: var(--color-gray-300) ;
        color: var(--color-gray-800);
    }
    .fel-tag-property {
        background-color: var(--color-gray-400);
        color: var(--color-gray-800);
    }
    .fel-tag-manufacturer {
        background-color: var(--color-gray-400);
        color: var(--color-gray-800);
    }
    .fel-empty-state {
        text-align: center;
        padding: 2rem;
        color: var(--color-gray-800);
    }
    // .fel-search-meta {
    //     margin-top: 1rem;
    //     border-top: 1px solid var(--color-gray-300);
    //     padding-top: 0.5rem;
    // }
    .fel-badge {
        padding: 4px 10px;
        border-radius: 8px;
        font-size: var(--font-size-xxs);
        font-weight: normal;
    }
    .fel-badge-info {
        background-color: var(--color-gray-100);
        color: var(--color-gray-800);
        border: 1px solid var(--color-gray-300);
        font-weight: bold;
        font-size: var(--font-size-xxs);
        line-height: 1;
    }
    // .fel-search-details {
    //     padding: 0.5rem 0;
    // }
    .fel-search-logs-list {
        .mt-card__header {
            padding: 16px 22px;

            .mt-card__avatar,
            .mt-card__titles-right-slot {
                position: absolute;
            }
            .mt-card__titles {
                display: block;
                width: 100%;
            }
        }
    }
}