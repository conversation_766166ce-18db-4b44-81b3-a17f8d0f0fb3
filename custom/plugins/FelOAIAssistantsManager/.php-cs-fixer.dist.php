<?php declare(strict_types=1);

use PhpCsFixer\Config;
use PhpCsFixer\Finder;

// Create finder for PHP files
$finder = Finder::create()
    ->exclude('vendor')
    ->name('/\.php$/')
    ->in(__DIR__);

$config = new Config();
$config
    ->setRiskyAllowed(true)
    ->setUsingCache(false)
    ->setRules([
        '@Symfony' => true,
        '@Symfony:risky' => true,
        'blank_line_after_opening_tag' => false,
        // Disable class attributes separation to allow blank lines between properties
        'class_attributes_separation' => false,
        'concat_space' => ['spacing' => 'one'],
        'declare_strict_types' => true,
        'fopen_flags' => false,
        'general_phpdoc_annotation_remove' => ['annotations' => ['copyright', 'category']],
        'linebreak_after_opening_tag' => false,
        'method_argument_space' => ['on_multiline' => 'ensure_fully_multiline'],
        // Disable rules that affect use statements and imports
        'native_function_invocation' => false,
        'global_namespace_import' => false,
        'ordered_imports' => [
            'sort_algorithm' => 'none',
            'imports_order' => null,
        ],
        'no_unused_imports' => false,
        'single_import_per_statement' => true,
        'group_import' => false,
        'blank_line_between_import_groups' => false,
        'no_superfluous_phpdoc_tags' => ['allow_unused_params' => true, 'allow_mixed' => true],
        'no_useless_else' => true,
        'no_useless_return' => true,
        // Disable ordered class elements to allow custom organization
        'ordered_class_elements' => false,
        // Allow extra blank lines for better code organization
        'no_extra_blank_lines' => [
            'tokens' => [
                'throw',
                'case',
                'default',
            ],
        ],
        // Disable all PHPDoc formatting rules to preserve manual formatting
        'phpdoc_align' => false,
        'phpdoc_annotation_without_dot' => false,
        'phpdoc_line_span' => false,
        'phpdoc_order' => false,
        'phpdoc_summary' => false,
        'phpdoc_to_comment' => false,
        'phpdoc_trim' => false,
        'phpdoc_types_order' => false,
        'phpdoc_var_without_name' => false,
        'phpdoc_separation' => false,
        'phpdoc_indent' => false,
        'php_unit_dedicate_assert' => ['target' => 'newest'],
        'php_unit_dedicate_assert_internal_type' => true,
        'php_unit_mock' => true,
        'php_unit_test_case_static_method_calls' => ['call_type' => 'static'],
        'self_accessor' => false,
        'single_line_throw' => false,
        'single_quote' => ['strings_containing_single_quote_chars' => true],
        'strict_comparison' => true,
        'strict_param' => true,
        'trailing_comma_in_multiline' => ['after_heredoc' => true, 'elements' => ['array_destructuring', 'arrays', 'match']],
        'void_return' => true,
        'yoda_style' => [
            'equal' => false,
            'identical' => false,
            'less_and_greater' => false,
        ],
    ])
    ->setFinder($finder);

return $config;
