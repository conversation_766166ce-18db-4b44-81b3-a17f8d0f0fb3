// <plugin root>/src/Resources/app/administration/src/module/sw-cms/elements/chatbot/component/index.js
import template from './sw-cms-el-chatbot.html.twig';
import './sw-cms-el-chatbot.scss';

Shopware.Component.register('sw-cms-el-chatbot', {
    template,

    mixins: [
        'cms-element'
    ],

    data() {
        return {
            messages: [],
            userMessage: ''
        };
    },

    computed: {
        cmsAssistantId() {
            return this.element.config.cmsAssistantId.value;
        }
    },

    methods: {
        sendMessage() {
            if (this.userMessage.trim() !== '') {
                this.messages.push({ sender: 'user', text: this.userMessage });
                this.userMessage = '';
                // Here you would add the logic to send the message to the AI assistant
                // and handle the response.
            }
        }
    }
});
