# Vue templates

```js
var path = {target: {offset: { meta: true }}};
// var path = {};

if (path?.target?.offset?.meta !== undefined) {
    console.log(path);
}
```

```template
<style>
    @media (prefers-reduced-motion: reduce) {
        .form-range::-moz-range-thumb {
            transition: none;
        }
    }
</style>

<!--
    link via button
-->
<mt-button
	class="fel-mr-1"
    variant="critical"
    ghost="true"
    v-if="pluginConfig"
    v-html="Go to example"
    @click="this.$router.push({ name: 'fel.gpt.assistant.index', params: {} })" />

<!--
    link via link
-->
<router-link
    class="mt-button mt-button--critical"
    :to="{ name: 'fel.gpt.assistant.delete', params: {} }"
    v-if="pluginConfig.assistantId != assistant.id"
    v-html="$t('fel-gpt-assistant.delete.delete')" />

<!--
    help
-->
<sw-help-text
    :text="$t('fel-gpt-assistant.create.setInstructionsInfo')"
    :width="240"
    :showDelay="100"
    :hideDelay="100"
    tooltipPosition="left" />

<!--
    banner
-->
<mt-banner variant="critical">
    mt-banner
</mt-banner>

<!--
    tooltip
-->
<div v-tooltip="{message: 'Room for more information'}">
    Pro tip
</div>

```
