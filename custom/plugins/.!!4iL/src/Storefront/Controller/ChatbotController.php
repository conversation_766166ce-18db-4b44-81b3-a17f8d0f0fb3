<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\Storefront\Controller;

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIService;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Psr\Log\LoggerInterface;
use function is_string;
use function strlen;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ChatbotController extends StorefrontController
{
    public function __construct(
        private SystemConfigService $configService,
        private OpenAIService $openAiService,
        private LoggerInterface $logger
    ) {}

    #[Route(
        path: '/fel/chatbot/message/{fn?}',
        name: 'frontend.fel.chatbox.message',
        methods: ['POST'],
        defaults: ['XmlHttpRequest' => true]
    )]
    public function chatbot($fn, Request $request, Context $context, SalesChannelContext $salesContext): Response|JsonResponse
    {
        $post = $request->request->all();
        $user = $salesContext->getCustomer();

        // Fetch cmsAssistantId and cmsCategoryId from the request
        $cmsAssistantId = $post['assiId'] ?? $post['fel-chatbot-assistant-id'] ?? null;
        $cmsCategoryId = $post['fel-chatbot-category-id'] ?? null;

        $this->logger->debug('ChatbotController: Received cmsAssistantId and cmsCategoryId', [
            'cmsAssistantId' => $cmsAssistantId,
            'cmsCategoryId' => $cmsCategoryId,
        ]);

        $this->openAiService->setAiConfigSalesChannelContext($salesContext, $cmsAssistantId);
        $maxInputLength = $this->openAiService->getClientConfig()->getOpenAiUserInputMaxLength();

        // Extrahieren Sie die Systemnachricht aus der Benutzereingabe
        $userInput = $post['userInput'] ?? '';
        $systemPrefix = '';

        // Prüfen Sie, ob die Nachricht mit einer Systemnachricht beginnt
        if (strpos($userInput, '[SYSTEM') === 0) {
            $endPos = strpos($userInput, '] ');
            if ($endPos !== false) {
                $systemPrefix = substr($userInput, 0, $endPos + 2);
                $userInput = substr($userInput, $endPos + 2);
            }
        }

        // Überprüfen Sie nur die tatsächliche Benutzereingabe auf die maximale Länge
        if (is_string($userInput) && $maxInputLength < strlen($userInput)) {
            $r['error'] = ['exception' => $this->trans('fel-gpt-assistant.error.input.max.length', ['%max%' => $maxInputLength])];
        } else {
            // Fügen Sie die Systemnachricht wieder hinzu, wenn die Prüfung bestanden wurde
            if ($systemPrefix) {
                $userInput = $systemPrefix . $userInput;
            }

            // Verarbeiten Sie die Nachricht weiter...
        }

        if (!isset($r['error']) && $this->openAiService->isEnabled($user ? $user->getGroupId() : null)) {
            if ('delete-thread' === $fn) {
                if (!empty($post['threadId'])) {
                    $r = $this->openAiService->deleteThread($context, $post['threadId']);
                }
            } else if ('create-thread' === $fn) {
                // Add cmsCategoryId to the post data
                $post['cmsCategoryId'] = $cmsCategoryId;

                // Pass cmsCategoryId to handleCreateThread
                $r = $this->openAiService->handleCreateThread($post);
            } else {
                $r = $this->openAiService->handleRequest($post, $request, $salesContext, $context);
            }

            if ($aiResponse = ($r['threadMessages'][0] ?? null)) {
                if ('assistant' === ($aiResponse['role'] ?? null)) {
                    if ($products = $r['uiActionRequired']['product'] ?? $r['uiActionRequired']['product_search'] ?? null) {
                        // Parse the AI response if it's JSON
                        $aiResponseValue = $aiResponse['value'];
                        $decodedValue = json_decode($aiResponseValue, true);

                        // If the response is valid JSON and contains an output field
                        if ($decodedValue && isset($decodedValue['output'])) {
                            // Store the original response
                            $r['threadMessages'][0]['originalValue'] = $aiResponse['value'];

                            // Get used products from the response
                            $usedProducts = $this->openAiService->getUsedProducts($products['fetch']['elements'] ?? [], $decodedValue['output']);

                            // If we have products, use our template
                            if ($usedProducts) {
                                // Add debug information
                                $debugInfo = "<!-- DEBUG: Template wird verwendet - " . count($usedProducts) . " Produkte gefunden -->";

                                // Replace the output with our rendered template
                                $renderedTemplate = $this->renderView('@FelAIProductAdvisor/storefront/component/product-listing.html.twig', [
                                    'isSearch' => $products['exec'] ?? null,
                                    'productsResponse' => $products,
                                    'products' => $usedProducts,
                                ]);

                                // Add debug information to the rendered template
                                $decodedValue['output'] = $debugInfo . $renderedTemplate;

                                // Update the response with our modified output
                                $r['threadMessages'][0]['value'] = json_encode($decodedValue);

                                // Log that we're using the template
                                $this->logger->debug('FelAIProductAdvisor: Using template for product display', [
                                    'productCount' => count($usedProducts),
                                    'templateUsed' => '@FelAIProductAdvisor/storefront/component/product-listing.html.twig'
                                ]);
                            }
                        } else {
                            // If not JSON or no output field, use the original approach
                            $usedProducts = $this->openAiService->getUsedProducts($products['fetch']['elements'] ?? [], $aiResponseValue);
                            if ($usedProducts) {
                                // Add debug information
                                $debugInfo = "<!-- DEBUG: Template wird verwendet (nicht-JSON) - " . count($usedProducts) . " Produkte gefunden -->";

                                // Store the original response
                                $r['threadMessages'][0]['originalValue'] = $r['threadMessages'][0]['value'];

                                // Render the template with debug information
                                $renderedTemplate = $this->renderView('@FelAIProductAdvisor/storefront/component/product-listing.html.twig', [
                                    'isSearch' => $products['exec'] ?? null,
                                    'productsResponse' => $products,
                                    'products' => $usedProducts,
                                ]);

                                // Set the value with debug information
                                $r['threadMessages'][0]['value'] = $debugInfo . $renderedTemplate;

                                // Log that we're using the template
                                $this->logger->debug('FelAIProductAdvisor: Using template for product display (non-JSON response)', [
                                    'productCount' => count($usedProducts),
                                    'templateUsed' => '@FelAIProductAdvisor/storefront/component/product-listing.html.twig'
                                ]);
                            }
                        }
                    }
                }
            }

            if ($trackedError = ($r['trackError']['full_status']['last_error'] ?? null)) {
                if ($trackedError['code'] ?? null) {
                    if ('rate_limit_exceeded' === $trackedError['code']) {
                        $r['trackError']['full_status'] = $trackedError['message'];
                        $this->openAiService->disablePlugin($salesContext);
                    }
                }
            }
        }

        $r['addedExt'] = $cmsAssistantId;

        return new JsonResponse($r ?? [], 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
    }
}
