.is-fixed {
    margin: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    overflow: auto;
    .fel-oai-trainer-container {
        margin: 0 auto;
        padding: 1rem;
        width: 95%;
        max-width: 1240px;
        height: 95%;
        position: relative;
        z-index: 55;
        overflow: auto;
        overflow-y: scroll;
        background: #f5f5f5;
        box-shadow: 0 0 8px rgba(160, 120, 120, 0.8);
        textarea,
        .fel-textarea {
            min-height: 70px;
            max-height: 70vh;
        }
        .fel-oai-copy-and-pastable-examples {
            padding: .5rem;
            ul {
                margin-top: 2rem;
                margin-bottom: 2rem;
            }
        }
        .fel-oai-storage-container {
            &.fel-oai-storage-parsed {
                .fel-load-examples-from-storage {
                    display: none;
                }
            }
        }
    }
}
.fel-assistant {
    .fel-oai-trainer-container-wrapper {
        .fel-content-box {
            h3, h4, h5, h6 {
                margin: 0 0 1rem;
                font-size: var(--font-size-l);
            }
            p {
                margin: initial;
                font-size: var(--font-size-xs);
                + p {
                    margin-top: .7rem;
                }
            }
            hr {
                margin: .7rem 0;
            }
            table {
                width: auto;
                caption {
                    margin: 0 0 .7rem;
                    text-align: left;
                }
                th {
                    text-align: left;
                }
            }
        }
    }
}
.fel-oai-trainer-container-wrapper {
    padding: 0;
    line-height: 1.5;

    &:before {
        content: ' ';
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        opacity: .6;
        background: var(--lt-color-gray-900);
    }

    .fel-compare-gpts {
        margin: .8rem 0;
        font-size: .8rem;
        td {
            padding: .5rem 1rem .5rem 0;
        }
    }

    .fel-oai-starter-container {
        margin-top: .5rem;
    }

    .fel-oai-trainer-examples-default-examples {
        &:not(.fel-is-hidden) {
            margin: .7rem 0 0 0;
        }
    }
    .fel-trainer-footer {
        position: sticky;
        top: 100%;
    }
    pre,
    .fel-trainer-box {
        margin: .5rem 0 0;
        padding: 1.25rem;
        background: var(--color-white);
        box-shadow: 6px 6px 6px -6px var(--color-gray-200), 0px 2px 6px 1px var(--color-gray-200);
    }
    pre {
        overflow: auto;
        font-family: monospace,Inconsolata,Hack,SF Mono,Roboto Mono,Source Code Pro,Ubuntu Mono;
    }
    .fel-oai-trainer-available-properties-container {
        margin-bottom: .5rem;
        .fel-oai-trainer-option-list {
            max-height: 120px;
            overflow: auto;
            li {
                margin: .3rem;
                display: inline-flex;
            }
            &.fel-oai-trainer-option-list-manufacturer {
                padding-top: .3rem;
            }
        }
    }
    .fel-oai-final-content-container {
        margin: 0 0 .5rem;
        padding: 0;
    }
    .fel-oai-training-input-wrapper {
        margin: .5rem 0;
    }
    .active {
        color: var(--color-crimson-700);
        background: var(--color-white);
    }
    .sw-block-field__block {
        input {
            + {
                input,
                select {
                    border-left: 1px solid var(--color-gray-400);
                }
            }
        }
    }
    ul {
        list-style: none;
        font-size: var(--font-size-xs);

        &[disabled] {
            opacity: .5;
            pointer-events: none;
        }
        ul {
            margin: .5rem .5rem;
            line-height: 1.5;
        }
    }
}
