<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Psr\Log\LoggerInterface;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Exception;
use function array_merge;
use function ltrim;
use function sprintf;
use Fel\AIProductAdvisor\DataResolver\CmsAssistantIdResolver;


class OpenAIConfigService
{
    protected array $felConfig = [];
    private ?string $cmsAssistantId = null;


    public function __construct(
        private SystemConfigService $configService,
        private LoggerInterface $logger,
        private CmsAssistantIdResolver $cmsAssistantIdResolver
    ) {
        $this->felConfig = $this->configService->get('FelAIProductAdvisor.config');
        $this->cmsAssistantId = $this->cmsAssistantIdResolver->resolve();
        $this->logger->debug('OpenAIConfigService: cmsAssistantId set in constructor', ['cmsAssistantId' => $this->cmsAssistantId]);
    }

    public function setSalesChannelContext(SalesChannelContext $salesChannelContext): self
    {
        $this->felConfig = $this->configService->get('FelAIProductAdvisor.config', $salesChannelContext->getSalesChannelId());

        return $this;
    }

    public function setCmsAssistantId(string $cmsAssistantId): void
    {
        $this->logger->debug('OpenAIConfigService: Setting cmsAssistantId', ['cmsAssistantId' => $cmsAssistantId]);
        $this->cmsAssistantId = $cmsAssistantId;
    }

    public function getAssistantId(): ?string
    {
        $assistantId = $this->cmsAssistantId ?? $this->felConfig['openAiAssistantId'] ?? null;
        $this->logger->debug('OpenAIConfigService: Fetching assistantId', ['assistantId' => $assistantId]);
        return $assistantId;
    }


    public function getApiHeaders(array $add = []): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => "Bearer {$this->getApiKey()}",
        ];

        if ($this->getOpenAiBetaVersion()) {
            $headers['OpenAI-Beta'] = $this->getOpenAiBetaVersion();
        }

        return array_merge($headers, $add);
    }

    public function getUrl(string $append = null, string $v = null): string
    {
        $version = $v ?? $this->getApiVersion();

        return sprintf(
            '%s%s%s',
            $this->getApiBaseUrl(),
            $version ? "/{$version}/" : null,
            $append ? ltrim($append, '/') : null
        );
    }

    public function disablePlugin(?string $salesChannelId = null): void
    {
        $this->configService->set('FelAIProductAdvisor.config.enablePlugin', false, $salesChannelId);
    }

    public function enablePlugin(?string $salesChannelId = null): void
    {
        $this->configService->set('FelAIProductAdvisor.config.enablePlugin', true, $salesChannelId);
    }

    public function getOpenAiBetaVersion(): ?string
    {
        return $this->felConfig['openAiBetaVersion'] ?? null;
    }

    public function getIsEnabled(): bool
    {
        return $this->felConfig['enablePlugin'] ?? false;
    }



    private function getApiKey(): ?string
    {
        return $this->felConfig['felOpenAiApiKey'] ?? $this->felConfig['openAiApiKey'] ?? null;
    }

    public function getApiBaseUrl(): ?string
    {
        if (!($this->felConfig['openAiBaseUrl'] ?? null)) {
            throw new Exception('OpenAI API base URL is not set');
        }

        return $this->felConfig['openAiBaseUrl'] ?? null;
    }

    public function getApiVersion(): ?string
    {
        if (!($this->felConfig['openAiVersion'] ?? null)) {
            throw new Exception('OpenAI API version is not set');
        }

        return $this->felConfig['openAiVersion'] ?? null;
    }

    public function getLoopUntilCompleted(): int
    {
        return $this->felConfig['openAiLoopUntilCompleted'] ?? 50;
    }

    public function getLoopUntilCompletedSeconds(): int
    {
        return $this->felConfig['openAiLoopWaitSeconds'] ?? 2;
    }

    public function getAuthenticatedUserOnly(): bool
    {
        return $this->felConfig['authenticatedUserOnly'] ?? false;
    }

    public function getAuthenticatedUserOnlyGroup(): array
    {
        return $this->felConfig['authenticatedUserOnlyGroup'] ?? [];
    }



    public function getBlacklistManufacturer(): array
    {
        return $this->felConfig['openAiBlacklistManufacturer'] ?? [];
    }

    public function getBlacklistCategory(): array
    {
        return $this->felConfig['openAiBlacklistCategory'] ?? [];
    }

    public function getOpenAiFilterCategoryLevel(): int
    {
        return $this->felConfig['openAiFilterCategoryLevel'] ?? 0;
    }

    public function getOpenAiFilterAllowedProperties(): ?array
    {
        return $this->felConfig['openAiFilterAllowedProperties'] ?? null;
    }

    public function getOpenAiBlacklistPropertyOptions(): ?array
    {
        return $this->felConfig['openAiBlacklistPropertyOptions'] ?? null;
    }

    public function getOpenAiFilterMaxOptions(): int
    {
        return $this->felConfig['openAiFilterMaxOptions'] ?? 25;
    }

    public function getOpenAiFilterMaxDescriptionLength(): int
    {
        return $this->felConfig['openAiFilterMaxDescriptionLength'] ?? 500;
    }

    public function getOpenAiSearchLimit(): int
    {
        return $this->felConfig['openAiSearchLimit'] ?? 10;
    }

    public function getOpenAiUserInputMaxLength(): int
    {
        return $this->felConfig['openAiUserInputMaxLength'] ?? 400;
    }

    public function getEnableLogging(): bool
    {
        return $this->felConfig['enableLogging'] ?? false;
    }
}