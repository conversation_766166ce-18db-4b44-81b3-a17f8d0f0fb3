<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Fel\AIProductAdvisor\DataResolver\CmsAssistantIdResolver;
use FelOAIAssistantsManager\Service\FelConfigService;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Psr\Log\LoggerInterface;
use Exception;

class OpenAIConfigAdapter
{
    protected array $felConfig = [];
    private ?string $cmsAssistantId = null;
    private ?string $salesChannelId = null;
    private ?string $accessKey = null;

    public function __construct(
        private SystemConfigService $configService,
        private LoggerInterface $logger,
        private CmsAssistantIdResolver $cmsAssistantIdResolver,
        private ?FelConfigService $felConfigService = null
    ) {
        $this->felConfig = $this->configService->get('FelAIProductAdvisor.config');
        $this->cmsAssistantId = $this->cmsAssistantIdResolver->resolve();
        $this->logger->debug('OpenAIConfigAdapter: cmsAssistantId set in constructor', ['cmsAssistantId' => $this->cmsAssistantId]);
    }

    public function setSalesChannelContext(SalesChannelContext $salesChannelContext, ?string $cmsAssistantId = null): self
    {
        $this->salesChannelId = $salesChannelContext->getSalesChannelId();
        $this->felConfig = $this->configService->get('FelAIProductAdvisor.config', $this->salesChannelId);
        $this->accessKey = $salesChannelContext->getSalesChannel()->getAccessKey();

        $this->logger->debug('OpenAIConfigAdapter: Set access key from sales channel context', [
            'accessKey' => $this->accessKey ? 'set' : 'not set'
        ]);

        if ($cmsAssistantId) {
            $this->setCmsAssistantId($cmsAssistantId);
        }

        return $this;
    }

    public function setCmsAssistantId(string $cmsAssistantId): void
    {
        $this->logger->debug('OpenAIConfigAdapter: Setting cmsAssistantId', ['cmsAssistantId' => $cmsAssistantId]);
        $this->cmsAssistantId = $cmsAssistantId;
    }

    public function getAssistantId(): ?string
    {
        $assistantId = $this->cmsAssistantId ?? $this->felConfig['openAiAssistantId'] ?? null;
        $this->logger->debug('OpenAIConfigAdapter: Fetching assistantId', ['assistantId' => $assistantId]);
        return $assistantId;
    }

    public function getApiKey(): ?string
    {
        // Try to get the API key from the base plugin first
        if ($this->felConfigService) {
            $baseApiKey = $this->felConfigService->getConfig('felOpenAiApiKey', $this->salesChannelId);
            if ($baseApiKey) {
                $this->logger->debug('OpenAIConfigAdapter: Using API key from base plugin');
                return $baseApiKey;
            }
        }

        // Fall back to our own config
        return $this->felConfig['felOpenAiApiKey'] ?? $this->felConfig['openAiApiKey'] ?? null;
    }

    public function getApiBaseUrl(): ?string
    {
        // Try to get the base URL from the base plugin first
        if ($this->felConfigService) {
            $baseUrl = $this->felConfigService->getConfig('felOpenAiBaseUrl', $this->salesChannelId);
            if ($baseUrl) {
                $this->logger->debug('OpenAIConfigAdapter: Using base URL from base plugin');
                return $baseUrl;
            }
        }

        // Fall back to our own config
        if (!($this->felConfig['openAiBaseUrl'] ?? null)) {
            throw new Exception('OpenAI API base URL is not set');
        }

        return $this->felConfig['openAiBaseUrl'] ?? null;
    }

    public function getOpenAiBetaVersion(): ?string
    {
        // Try to get the beta version from the base plugin first
        if ($this->felConfigService) {
            $betaVersion = $this->felConfigService->getConfig('felOpenAiBetaVersion', $this->salesChannelId);
            if ($betaVersion) {
                $this->logger->debug('OpenAIConfigAdapter: Using beta version from base plugin');
                return $betaVersion;
            }
        }

        // Fall back to our own config
        return $this->felConfig['openAiBetaVersion'] ?? null;
    }

    public function getApiVersion(): ?string
    {
        if (!($this->felConfig['openAiVersion'] ?? null)) {
            throw new Exception('OpenAI API version is not set');
        }

        return $this->felConfig['openAiVersion'] ?? null;
    }

    public function getLoopUntilCompleted(): int
    {
        // Try to get the loop count from the base plugin first
        if ($this->felConfigService) {
            $loopCount = $this->felConfigService->getConfig('felOpenAiLoopUntilCompleted', $this->salesChannelId);
            if ($loopCount) {
                $this->logger->debug('OpenAIConfigAdapter: Using loop count from base plugin');
                return (int)$loopCount;
            }
        }

        // Fall back to our own config
        return $this->felConfig['openAiLoopUntilCompleted'] ?? 50;
    }

    public function getLoopUntilCompletedSeconds(): int
    {
        // Try to get the loop wait seconds from the base plugin first
        if ($this->felConfigService) {
            $loopWaitSeconds = $this->felConfigService->getConfig('felOpenAiLoopWaitSeconds', $this->salesChannelId);
            if ($loopWaitSeconds) {
                $this->logger->debug('OpenAIConfigAdapter: Using loop wait seconds from base plugin');
                return (int)$loopWaitSeconds;
            }
        }

        // Fall back to our own config
        return $this->felConfig['openAiLoopWaitSeconds'] ?? 2;
    }

    public function isEnabled($userInGroupId): bool
    {
        $isEnabled = $this->felConfig['enablePlugin'] ?? false;

        if ($isEnabled && $this->getAuthenticatedUserOnly()) {
            // not logged in
            if (!$userInGroupId) {
                $isEnabled = false;
            }
            if ($this->getAuthenticatedUserOnlyGroup()) {
                if (!in_array($userInGroupId, $this->getAuthenticatedUserOnlyGroup())) {
                    $isEnabled = false;
                }
            }
        }

        return $isEnabled;
    }

    public function disablePlugin(?string $salesChannelId = null): void
    {
        $this->configService->set('FelAIProductAdvisor.config.enablePlugin', false, $salesChannelId);
    }

    public function enablePlugin(?string $salesChannelId = null): void
    {
        $this->configService->set('FelAIProductAdvisor.config.enablePlugin', true, $salesChannelId);
    }

    public function getAuthenticatedUserOnly(): bool
    {
        return $this->felConfig['authenticatedUserOnly'] ?? false;
    }

    public function getAuthenticatedUserOnlyGroup(): array
    {
        return $this->felConfig['authenticatedUserOnlyGroup'] ?? [];
    }



    public function getOpenAiUserInputMaxLength(): int
    {
        return $this->felConfig['openAiUserInputMaxLength'] ?? 1000;
    }

    public function getBlacklistManufacturer(): array
    {
        return $this->felConfig['openAiBlacklistManufacturer'] ?? [];
    }

    public function getBlacklistCategory(): array
    {
        return $this->felConfig['openAiBlacklistCategory'] ?? [];
    }

    public function getOpenAiFilterCategoryLevel(): int
    {
        return $this->felConfig['openAiFilterCategoryLevel'] ?? 0;
    }

    public function getStorefrontUrl(): ?string
    {
        // Get the storefront URL from the system config
        $storefrontUrl = $this->configService->get('core.basicInformation.shopUrl');
        if (!$storefrontUrl) {
            $storefrontUrl = 'http://localhost';
        }

        return $storefrontUrl;
    }

    public function getAccessKey(): ?string
    {
        // If we have an access key from the sales channel context, use that
        if ($this->accessKey) {
            return $this->accessKey;
        }

        // Get the access key directly from the system config
        $accessKey = $this->configService->get('core.basicInformation.apiAccessKey');

        if (!$accessKey) {
            // Try to get it from the sales channel context via the system config
            $accessKey = $this->configService->get('core.basicInformation.accessKey');
        }

        if (!$accessKey) {
            // Hard-coded fallback for development
            $accessKey = 'SWSCMVWFJZET2NQRXF5UYWWJWQ';
        }

        $this->logger->debug('OpenAIConfigAdapter: Got access key', [
            'accessKey' => $accessKey ? 'set' : 'not set'
        ]);

        return $accessKey;
    }

    public function getSalesChannelId(): ?string
    {
        return $this->salesChannelId;
    }

    public function getCurrency(): ?string
    {
        // Return a default currency symbol
        return '€';
    }
}
