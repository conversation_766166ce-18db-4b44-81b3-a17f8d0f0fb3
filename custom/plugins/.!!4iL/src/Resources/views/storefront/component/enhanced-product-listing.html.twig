<div class="fel-simple-product-listing">
    {% set isProductSearch = 'product_search' === isSearch %}
    {% set descriptionLength = isProductSearch ? 100 : 300 %}
    {% set propertyParams = productsResponse.propertyData.setParameter %}

    <h3 class="fel-product-listing-title">
        {% if productsResponse.args.categoryId %}
            {% set categoryName = '' %}
            {% for category in productsResponse.categoryMap %}
                {% if category.id == productsResponse.args.categoryId %}
                    {% set categoryName = category.name %}
                {% endif %}
            {% endfor %}
            Produkte in der Kategorie {{ categoryName|default('Kleidung') }}
        {% elseif productsResponse.args.term %}
            Suchergebnisse für "{{ productsResponse.args.term }}"
        {% else %}
            Produktergebnisse
        {% endif %}
    </h3>

    <div class="fel-product-list">
        {% for product in products %}
            {% set setQueryProperties = product.parentId ? {} : propertyParams.properties %}
            {% set felSeoUrl = url('frontend.detail.page', {productId: product.id, properties: setQueryProperties}) %}
            {% if felSeoUrl %}
                {% set translatedName = product.translated.name ?? product.name %}
                {% set translatedDescription = (product.translated.description ?? product.description)|striptags|trim|slice(0, descriptionLength) %}
                {% if translatedDescription|length >= descriptionLength %}
                    {% set translatedDescription = translatedDescription ~ '...' %}
                {% endif %}

                <div class="fel-product-item">
                    <div class="fel-product-image-container">
                        {% if product.cover.media.thumbnails[0].url is not empty %}
                            <a href="{{ felSeoUrl }}" class="fel-product-image-link">
                                <img src="{{ product.cover.media.thumbnails[0].url }}" alt="{{ translatedName }}" class="fel-product-image" />
                            </a>
                        {% else %}
                            <div class="fel-product-no-image">
                                <span>Kein Bild verfügbar</span>
                            </div>
                        {% endif %}
                    </div>

                    <div class="fel-product-info">
                        <h4 class="fel-product-name">
                            <a href="{{ felSeoUrl }}">{{ translatedName }}</a>
                        </h4>

                        <div class="fel-product-price-manufacturer">
                            {% if product.calculatedPrice.totalPrice %}
                                <span class="fel-product-price">{{ product.calculatedPrice.totalPrice|currency }}</span>
                            {% endif %}

                            {% if product.manufacturer %}
                                <span class="fel-product-manufacturer">{{ product.manufacturer.translated.name ?? product.manufacturer.name }}</span>
                            {% endif %}
                        </div>

                        <div class="fel-product-description">
                            {{ translatedDescription }}
                        </div>

                        <div class="fel-product-actions">
                            <a href="{{ felSeoUrl }}" class="btn btn-primary fel-product-details-btn">
                                Details ansehen
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% endfor %}
    </div>

    {% if propertyParams.search and isProductSearch %}
        <div class="fel-search-all-results">
            <a class="btn btn-info" href="{{ path('frontend.search.page', propertyParams) }}">
                Alle Ergebnisse anzeigen
            </a>
        </div>
    {% endif %}
</div>

<style>
.fel-simple-product-listing {
    margin: 15px 0;
    font-family: var(--sw-font-family-base, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
}

.fel-product-listing-title {
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9e9e9;
}

.fel-product-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fel-product-item {
    display: flex;
    border: 1px solid #e9e9e9;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.fel-product-image-container {
    flex: 0 0 120px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.fel-product-image {
    max-width: 100%;
    max-height: 100px;
    object-fit: contain;
}

.fel-product-no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 12px;
    text-align: center;
}

.fel-product-info {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.fel-product-name {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.fel-product-name a {
    color: var(--sw-color-brand-primary, #0d6efd);
    text-decoration: none;
}

.fel-product-name a:hover {
    text-decoration: underline;
}

.fel-product-price-manufacturer {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.fel-product-price {
    font-weight: 600;
    color: var(--sw-color-price, #e74c3c);
}

.fel-product-manufacturer {
    font-size: 14px;
    color: #6c757d;
}

.fel-product-description {
    font-size: 14px;
    color: #495057;
    margin-bottom: 12px;
    flex-grow: 1;
}

.fel-product-actions {
    margin-top: auto;
}

.fel-product-details-btn {
    font-size: 14px;
    padding: 5px 10px;
}

.fel-search-all-results {
    margin-top: 15px;
    text-align: center;
}
</style>
