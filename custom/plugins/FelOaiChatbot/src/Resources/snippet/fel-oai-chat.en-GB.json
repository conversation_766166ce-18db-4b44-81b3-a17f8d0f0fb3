{"fel-oai-chat": {"chatbot": "<PERSON><PERSON><PERSON>", "contactToChat": {"askChatFirst": "Ask AI Live Support", "customMessage": "", "remove": "Remove"}, "chat": {"avatarTitle": "<PERSON><PERSON><PERSON>", "box": "Chatbox", "categories": "Categories", "category": "Category", "close": "<PERSON><PERSON>", "delete": "Clear Chat", "deleteChatMessage": "Remove question and answer", "errorOccurred": "An error occurred", "initial.greeting": "How can i help you?", "onlineStatus": "online", "open": "Open Chat", "placeholder": "Enter your Message", "submit": "Submit", "toggleChatbox": "Toggle Chatbox", "toggleZoomChat": "Toggle Chatbox size", "wait.a.second": "Give me a moment", "whats.your.purpose": "How could you help me?", "notifications": "Notifications", "productListing": {"noImageAssigned": "Image not found"}, "queryArguments": {"useFixedShortProperty": "Set '%new%' instead '%old%'", "useFixedPropertySearchAgain": "and search again", "id": "ID", "query": "Search term", "categories": "Categories", "properties": "Properties", "manufacturer": "Manufacturer", "price_min": "Minimum price", "price_max": "Maximum price", "notFilterable": "Not filterable", "notFoundCategories": "Categories not found", "notFoundProperties": "Properties not found", "order": "Order", "_truncate": "Truncated"}}, "cookie": {"name": "Local storage", "description": "Activate the option to allow the chatbot to save the conversation locally in your browser and maintain the chat across different pages.", "privacy": "No personal data is shared with the AI. Only your inputs within the chatbox are transmitted to the AI."}, "error": {"chat": {"queryFailedTruncatedTo": "No products found for search term '<b>%from%</b>', changed to '<b>%to%</b>' and tried again.", "usedArguments": "Searched for", "propsInDiffGroups": "Some properties are used in different filters, which may affect your search results."}, "input": {"max.length": "Input Length Exceeded, max: %max%", "is.not.string": "The input provided is not in text format. Please ensure your input is a text string and try again."}}}}