<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1714424857 extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1714424857;
    }

    public function update(Connection $connection): void
    {
        $sql = <<<SQL
            CREATE TABLE IF NOT EXISTS `fel_openai_assistants_log` (
                `id` BINARY(16) NOT NULL,
                `sales_channel_id` VARCHAR(64) COLLATE utf8mb4_unicode_ci NOT NULL,
                `assistant_id` VARCHAR(64) COLLATE utf8mb4_unicode_ci NOT NULL,
                `thread_id` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `thread_deleted` TINYINT(1) NOT NULL DEFAULT '0',
                `thread_messages` LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `additional_data` LONGTEXT COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `errors` LONGTEXT COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                `created_at` DATETIME(3) NOT NULL,
                `updated_at` DATETIME(3),
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        SQL;

        $connection->executeStatement($sql);
    }

    public function updateDestructive(Connection $connection): void
    {
    }

}
