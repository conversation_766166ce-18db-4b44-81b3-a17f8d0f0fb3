"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["custom_plugins_FelContactToAiSupport_src_Resources_app_storefront_src_contact-to-chat-plugin_-a01121"],{750:(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});class s{static ucFirst(e){return e.charAt(0).toUpperCase()+e.slice(1)}static lcFirst(e){return e.charAt(0).toLowerCase()+e.slice(1)}static toDashCase(e){return e.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(e,t){let o=s.toUpperCamelCase(e,t);return s.lcFirst(o)}static toUpperCamelCase(e,t){return t?e.split(t).map(e=>s.ucFirst(e.toLowerCase())).join(""):s.ucFirst(e.toLowerCase())}static parsePrimitive(e){try{return/^\d+(.|,)\d+$/.test(e)&&(e=e.replace(",",".")),JSON.parse(e)}catch(t){return e.toString()}}}class r{static isNode(e){return"object"==typeof e&&null!==e&&(e===document||e===window||e instanceof Node)}static hasAttribute(e,t){if(!r.isNode(e))throw Error("The element must be a valid HTML Node!");return"function"==typeof e.hasAttribute&&e.hasAttribute(t)}static getAttribute(e,t){let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(o&&!1===r.hasAttribute(e,t))throw Error('The required property "'.concat(t,'" does not exist!'));if("function"!=typeof e.getAttribute){if(o)throw Error("This node doesn't support the getAttribute function!");return}return e.getAttribute(t)}static getDataAttribute(e,t){let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],n=t.replace(/^data(|-)/,""),i=s.toLowerCamelCase(n,"-");if(!r.isNode(e)){if(o)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===e.dataset){if(o)throw Error("This node doesn't support the dataset attribute!");return}let a=e.dataset[i];if(void 0===a){if(o)throw Error('The required data attribute "'.concat(t,'" does not exist on ').concat(e,"!"));return a}return s.parsePrimitive(a)}static querySelector(e,t){let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(o&&!r.isNode(e))throw Error("The parent node is not a valid HTML Node!");let s=e.querySelector(t)||!1;if(o&&!1===s)throw Error('The required element "'.concat(t,'" does not exist in parent node!'));return s}static querySelectorAll(e,t){let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(o&&!r.isNode(e))throw Error("The parent node is not a valid HTML Node!");let s=e.querySelectorAll(t);if(0===s.length&&(s=!1),o&&!1===s)throw Error('At least one item of "'.concat(t,'" must exist in parent node!'));return s}}class n{static iterate(e,t){if(e instanceof Map||Array.isArray(e))return e.forEach(t);if(e instanceof FormData){for(var o of e.entries())t(o[1],o[0]);return}if(e instanceof NodeList)return e.forEach(t);if(e instanceof HTMLCollection)return Array.from(e).forEach(t);if(e instanceof Object)return Object.keys(e).forEach(o=>{t(e[o],o)});throw Error("The element type ".concat(typeof e," is not iterable!"))}}class i extends window.PluginBaseClass{init(){this.clickHandlerRegistered=!1,this._loadTemplates(),this._registerAll()}_registerAll(){this.handleEvent=e=>{switch(e.type){case"input":return this.toggleBtnCommentToAi(e,e.target);case"click":return this.clickEventCallback(e,e.target)}},this._checkEmbeddedForms(),this._subscribeAjaxModal()}_registerClickHandler(){this.clickHandlerRegistered||(document.body.addEventListener("click",this,!1),this.clickHandlerRegistered=!0)}_unregisterClickHandler(){this.clickHandlerRegistered&&(document.body.removeEventListener("click",this,!1),this.clickHandlerRegistered=!1)}_registerCommentListener(e){e.removeEventListener("input",this,!1),e.addEventListener("input",this,!1)}removeChatMessage(e){let t=e.target.parentNode.parentNode;if(t.classList.contains(this.options.selector.supportResponse.replace(".",""))){let e=this.domGet(t,".fel-inner",!1);e.innerHTML="",e.classList.remove("fel-contains-response"),t.classList.add("fel-is-hidden")}}addToChatSubmit(e){let t=e.target,o=this.domGet(this.el,this.options.selector.chatbotInputEl,!1);if(o&&t.dataset.felResponseQuerySelector){let e=this.domGet(document.body,t.dataset.felResponseQuerySelector,!1);if(e){t.disabled=!0;let s=e.parentNode,r=this.domGet(s,this.options.selector.supportResponse,!1),n=this.domGet(r,".fel-inner",!1);if(r&&n){let t=window.PluginManager.getPluginInstances("FelAssistantPlugin");t.length&&(r.classList.remove("fel-is-hidden"),n.classList.add("fel-contains-response"),this.putHtml(n,"afterbegin",this.template.loading.outerHTML),o.value=e.value.substring(0,o.maxLength||100),e.disabled=!0,o.form.dispatchEvent(new Event("submit",{bubbles:!0,cancelable:!0})),t[0].$emitter.subscribe("felAssistantMessageResponse",()=>{e.disabled=null;let o=this.domGet(this.el,this.options.selector.chatLastResponse,!1),s=o.querySelector(".fel-delete-message");s&&s.remove(),o&&(n.innerHTML=o.innerHTML,t[0].$emitter.unsubscribe("felAssistantMessageResponse"))}))}}}}toggleBtnCommentToAi(e,t){if(void 0!==t.value){let e=this.domGet(t.parentNode,this.options.selector.felBtn,!1);e&&(e.disabled=!t.value||null)}}clickEventCallback(e,t){if(!t.classList.contains("fel-ext-btn"))return;let o=t.dataset.callback;if(t.dataset.preventDefault&&e.preventDefault(),t.dataset.stopPropagation&&e.stopPropagation(),t.dataset.blur&&t.blur(),o){if("function"==typeof this[o])return this[o](...arguments);if("function"==typeof window[o])return window[o](...arguments)}}_subscribeAjaxModal(){let e=window.PluginManager.getPluginInstances("AjaxModal");if(e.length){this._registerClickHandler();for(var t=0;t<e.length;t++)e[t].$emitter.subscribe("ajaxModalOpen",()=>this.onModalOpen())}}onModalOpen(e){setTimeout(()=>this._onModalOpen(e||0),e?150:0)}_onModalOpen(e){let t="".concat(this.options.selector.pseudoModalEl," ").concat(this.options.selector.defaultCommentField),o=this.domGet(document.body,t,!1);if(!o&&5>e)return this.onModalOpen(++e);o&&this._commentFieldHandler(o,"modal",t)}_checkEmbeddedForms(){let e=this.domGetAll(document.body,this.options.selector.defaultCommentField,!1);e.length&&(this._registerClickHandler(),this.foreach(e,(e,t)=>{let o="fel-embedded-form-".concat(e);t.form.classList.add(o),this._commentFieldHandler(t,"embedded",".".concat(o," ").concat(this.options.selector.defaultCommentField))}))}_commentFieldHandler(e,t,o){let s=this.template.supportButton.cloneNode(!0),r=this.domGet(s,"button",!1);r.dataset.felFormIn=t,r.dataset.felResponseQuerySelector=o,""===e.value&&(r.disabled=!0),this.putElement(e,"beforebegin",s),this.putElement(e,"afterend",this.template.supportResponse.cloneNode(!0)),this._registerCommentListener(e)}domGet(){return r.querySelector(...arguments)}domGetAll(){return r.querySelectorAll(...arguments)}foreach(e,t){return n.iterate(e,function(e,o){t(o,e)})}putHtml(e,t,o){e.insertAdjacentHTML(t,o)}putElement(e,t,o){e.insertAdjacentElement(t,o)}_loadTemplates(){let e=document.getElementById("fel-chatbot-template"),t=document.getElementById("fel-contact-to-chat-template");this.template={loading:""},e&&(this.template.loading=e.content.querySelector(this.options.selector.loadingContainer)),t&&(this.template.supportButton=t.content.querySelector(this.options.selector.supportButton),this.template.supportResponse=t.content.querySelector(this.options.selector.supportResponse))}}i.options={selector:{felBtn:".fel-ext-btn",defaultCommentField:'textarea[name="comment"]',loadingContainer:".fel-chatbot-load-container",supportButton:".fel-contact-to-chat-button-container",supportResponse:".fel-contact-to-chat-response-container",chatbotInputEl:'[name="fel-chatbot-user-input"]',chatLastResponse:"#fel-chatbot .chatbot-message-item:last-of-type .fel-inner",pseudoModalEl:".js-pseudo-modal"}}}}]);