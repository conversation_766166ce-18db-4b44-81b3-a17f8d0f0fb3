import template from './fel-assistant-manager-notification-test.html.twig';

const { Component, Mixin } = Shopware;

Component.register('fel-assistant-manager-notification-test', {
    template,

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('fel-assistant-manager-api-request-mixin'),
    ],

    data() {
        return {
            isLoading: false,
        };
    },

    created() {
        this.runTests();
    },

    methods: {
        async runTests() {
            this.isLoading = true;

            // Test catchSuccess
            // Usage: catchSuccess(title, transReplace = {}, logConsole = false, message = null)
            this.catchSuccess('created');  // Simple success with just a title
            this.catchSuccess('importAssistantLogsFinished', { importedTotal: 5, total: 10 });  // With translation params for title
            this.catchSuccess('created', {}, true);  // With console logging
            this.catchSuccess('created', {}, false, 'This is a custom message');  // With custom message

            // Example with translation params for both title and message
            this.catchSuccess('updatedAliasesForSuccessfully', { name: 'Test' }, false, 'savedTagAndAliasesSuccessfully');

            // Test catchInfo
            // Usage: catchInfo(title, transReplace = {}, logConsole = false, message = null)
            this.catchInfo('filesAttachedSuccessfully');  // Simple info with just a title
            this.catchInfo('Storage State', {}, false, 'Storage size: 1024 bytes');  // With custom message

            // Example with translation params for both title and message
            this.catchInfo('filesAttachedSuccessfully', { count: 3 }, false, 'noFilesSelected');

            // Test catchWarning
            // Usage: catchWarning(title, transReplace = {}, logConsole = false, message = null)
            this.catchWarning('selectItemsToDelete');  // Simple warning with just a title
            this.catchWarning('attention', {}, true, 'This is a custom warning message');  // With custom message and logging

            // Example with translation params for both title and message
            this.catchWarning('warning', { type: 'Important' }, false, 'selectItemsToDelete');

            // Test catchErrors
            // Usage: catchErrors(error, title = null, logConsole = true, variant = 'error', transReplace = {})
            this.catchErrors('failedToDeleteVectorStore');  // Simple error with just a message
            this.catchErrors('failedToUpdateAliasesFor', 'error', true, 'error', { name: 'Test Tag' });  // With title and translation params

            // Test with error object
            const errorObj = new Error('Test error');
            this.catchErrors(errorObj, 'Custom Error Title');

            // Test catchAction
            await this.testCatchAction();

            this.isLoading = false;
        },

        async testCatchAction() {
            // Test confirm action
            console.log('Testing confirm action...');
            const confirmed = await this.catchAction('confirm');
            console.log('Confirm result:', confirmed);

            // Test choice action
            console.log('Testing choice action...');
            const choice = await this.catchAction('choice', 'choiceMessage');
            console.log('Choice result:', choice);

            // Test with custom options
            console.log('Testing custom options...');
            const customConfirm = await this.catchAction('confirm', 'confirmMessage', {
                title: 'choice',
                variant: 'warning'
            });
            console.log('Custom confirm result:', customConfirm);
        }
    }
});
