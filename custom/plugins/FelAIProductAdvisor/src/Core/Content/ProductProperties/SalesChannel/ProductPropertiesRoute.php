<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Core\Content\ProductProperties\SalesChannel;

use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Plugin\Exception\DecorationPatternException;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Psr\Log\LoggerInterface;
use Shopware\Core\Content\Category\CategoryEntity;
use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerEntity;
use Shopware\Core\Content\Property\PropertyGroupEntity;
use Shopware\Core\Content\Property\Aggregate\PropertyGroupOption\PropertyGroupOptionEntity;

#[Route(defaults: ['_routeScope' => ['store-api']])]
class ProductPropertiesRoute extends AbstractProductPropertiesRoute
{
    public function __construct(
        private readonly \Shopware\Core\Framework\DataAbstractionLayer\EntityRepository $categoryRepository,
        private readonly \Shopware\Core\Framework\DataAbstractionLayer\EntityRepository $productRepository,
        private readonly \Shopware\Core\Framework\DataAbstractionLayer\EntityRepository $propertyGroupRepository,
        private readonly \Shopware\Core\Framework\DataAbstractionLayer\EntityRepository $manufacturerRepository,
        private readonly LoggerInterface $logger
    ) {
    }

    public function getDecorated(): AbstractProductPropertiesRoute
    {
        throw new DecorationPatternException(self::class);
    }

    #[Route(path: '/store-api/fel/product-properties', name: 'store-api.fel.product-properties', methods: ['GET', 'POST'])]
    public function getProductProperties(Request $request, SalesChannelContext $context): ProductPropertiesRouteResponse
    {
        $startTime = microtime(true);
        $categories = [];
        $properties = [];

        dump($this); exit;


        try {
            // Get category IDs from request
            $content = json_decode($request->getContent(), true) ?? [];
            $categoryIds = $content['categories'] ?? [];

            $this->logger->debug('ProductPropertiesRoute: Getting product properties', [
                'categoryIds' => $categoryIds
            ]);

            // Fetch categories
            $categoryCriteria = new Criteria();
            $categoryCriteria->addAssociation('translations');
            $categoryCriteria->addSorting(new FieldSorting('level', 'ASC'));
            $categoryCriteria->setLimit(50);

            $categoryEntities = $this->categoryRepository->search($categoryCriteria, $context->getContext());

            foreach ($categoryEntities as $i => $category) {
                /** @var CategoryEntity $category */
                $breadcrumb = $category->getBreadcrumb();
                $name = $category->getTranslated()['name'] ?? $category->getName();

                if ($breadcrumb) {
                    $categories[$i] = [
                        'name' => $name,
                        'parent' => array_slice($breadcrumb, 0, -1),
                        'url' => '/navigation/' . $category->getId(),
                        'id' => $category->getId(),
                    ];
                }
            }

            // Fetch manufacturers
            $manufacturerCriteria = new Criteria();
            $manufacturerCriteria->addAssociation('translations');
            $manufacturerEntities = $this->manufacturerRepository->search($manufacturerCriteria, $context->getContext());

            if ($manufacturerEntities->count() > 0) {
                $properties['Manufacturer'] = [];

                foreach ($manufacturerEntities as $idx => $manufacturer) {
                    /** @var ProductManufacturerEntity $manufacturer */
                    $name = $manufacturer->getTranslated()['name'] ?? $manufacturer->getName();
                    if ($name) {
                        $properties['Manufacturer'][$idx] = $name;
                    }
                }
            }

            // Fetch property groups
            $propertyCriteria = new Criteria();
            $propertyCriteria->addAssociation('options');
            $propertyCriteria->addAssociation('translations');
            $propertyGroupEntities = $this->propertyGroupRepository->search($propertyCriteria, $context->getContext());

            foreach ($propertyGroupEntities as $group) {
                /** @var PropertyGroupEntity $group */
                $groupName = $group->getTranslated()['name'] ?? $group->getName();

                if ($groupName && $group->getOptions()->count() > 0) {
                    $properties[$groupName] = [];

                    foreach ($group->getOptions() as $optionIdx => $option) {
                        /** @var PropertyGroupOptionEntity $option */
                        $optionName = $option->getTranslated()['name'] ?? $option->getName();
                        if ($optionName) {
                            $properties[$groupName][$optionIdx] = $optionName;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('ProductPropertiesRoute: Error fetching product properties', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Add some dummy data in case of error
            $categories = [
                [
                    'name' => 'Reptilien',
                    'parent' => [],
                    'url' => '#',
                    'id' => 'dummy-1',
                ],
                [
                    'name' => 'Futter',
                    'parent' => ['Reptilien'],
                    'url' => '#',
                    'id' => 'dummy-2',
                ],
                [
                    'name' => 'Zubehör',
                    'parent' => ['Reptilien'],
                    'url' => '#',
                    'id' => 'dummy-3',
                ]
            ];

            $properties = [
                'Manufacturer' => ['Exo Terra', 'Zoo Med', 'Lucky Reptile'],
                'Tierart' => ['Gecko', 'Schlange', 'Schildkröte', 'Leguan'],
                'Futtertyp' => ['Lebendfutter', 'Trockenfutter', 'Frostfutter']
            ];
        }

        $endTime = microtime(true);
        $executionTime = number_format($endTime - $startTime, 4, '.') . ' sec';

        $result = [
            '_meta_data' => [
                'count' => [
                    'properties' => count($properties, COUNT_RECURSIVE) - count($properties),
                    'categories' => count($categories)
                ],
                'notification' => [],
                'request_took' => $executionTime
            ],
            'properties' => $properties,
            'categories' => array_values($categories)
        ];

        return new ProductPropertiesRouteResponse($result);
    }
}
