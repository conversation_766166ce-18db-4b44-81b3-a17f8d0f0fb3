<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Storefront\Controller\Api;

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigService;
use Shopware\Core\Framework\Validation\DataBag\RequestDataBag;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Exception;

#[Route(defaults: ['_routeScope' => ['administration']])]
class ApiTestController
{
    public function __construct(
        private HttpClientInterface $httpClient,
        private OpenAIConfigService $aiConfig
    ) {
    }

    #[Route(path: '/api/_action/fel-api-test/verify')]
    public function check(RequestDataBag $dataBag): JsonResponse
    {
        $r = ['success' => false];

        try {
            $headers = $this->aiConfig->getApiHeaders();
            $key = $dataBag->get('FelAIProductAdvisor.config.openAiApiKey');

            $headers['Authorization'] = "Bearer {$key}";
            $connected = $this->httpClient->request('GET', "{$this->aiConfig->getUrl()}assistants", [
                'headers' => $headers
            ]);

            if (isset($connected) AND $connected->getContent()) {
                $r['success'] = true;
                $r['assistantList'] = $connected->toArray();

                if ($assistantId = $dataBag->get('FelAIProductAdvisor.config.openAiAssistantId')) {
                    try {
                        $testAssistant = $this->httpClient->request('GET', "{$this->aiConfig->getUrl()}assistants/{$assistantId}", [
                            'headers' => $headers
                        ]);
                        if ($testAssistant) {
                            $r['assistantOk'] = $testAssistant->toArray();
                        }
                    } catch(Exception $e) {
                        $r['assistantFailed'] = true;
                        $getMessage = 'getMessage';
                        $r['error'] = $e->$getMessage();
                    }
                }
            }
        } catch(Exception $e) {
            $getMessage = 'getMessage';
            $r['error'] = $e->$getMessage();
        }

        return new JsonResponse($r, 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
    }
}
