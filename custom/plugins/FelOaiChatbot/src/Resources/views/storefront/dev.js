/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
"use strict";

/**
 * Modern FelOaiChatbot Plugin Implementation
 * Follows Shopware 6 plugin standards with modern JavaScript patterns
 */

window.onload = () => {
	// Get dependencies from window (available in Shopware)
	const {
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON><PERSON>,
		Iterator,
		CookieStorageHelper,
		Storage,
		COOKIE_CONFIGURATION_UPDATE,
		HttpClient
	} = window;

	class FelOaiChatbotPluginDEV {
		options = {
			behavior: 'smooth',
			compactView: 640 > window.innerWidth,
			selector: {
				chatMessages: '#fel-chatbot-messages',
				greetingContainer: '.fel-chatbot-greeting-container',
				loadingContainer: '.fel-chatbot-load-container',
				submitChatButton: '.fel-submit-chat-btn',
				chatThreadId: '[name="fel-chatbot-thread-id"]',
				chatRunId: '[name="fel-chatbot-run-id"]',
				chatUserInput: '[name="fel-chatbot-user-input"]',
				chatMessageEl: 'chat-message-item',
				chatMessage: 'chat-message',
				cookieDefaultName: 'fel-chatbot-localstorage-accepted',
			},
			layout: {
				scrollUpIsLeft: false,
			},
			page: {
				refId: null,
			},
			storage: {
				chat: 'felChatStorage',
			},
			eventMapping: {
				form: {
					element: '#fel-chatbot-form',
					events: [{ type: 'submit', handler: 'handleSubmit' }]
				},
				chat: {
					element: '[data-assistant-gpt-plugin]',
					events: [
						{ type: 'click', handler: 'handleClick' },
						{ type: 'change', handler: 'handleChange' }
					]
				}
			}
		};

		/**
		 * DEV NOTE: constructor() is in the final setupt not allowed and causes errors
		 * We only need it here.
		 */
		constructor() {this.init();}
		/**
		 * constructor() only for dev
		 */

		/**
		 * Initialize the plugin
		 * @public
		 */
		init() {
			if (!this.setPluginOptions()) return;

			this.setPluginVars();
			this.initializeEventSystem();
			this.initializeTemplates();
			this.initializeConsent();
			this.restoreLastState();
			this.setupShopwareIntegration();
		}

		/**
		 * Initialize plugin variables
		 * @private
		 */
		setPluginVars() {
			this._client = new HttpClient();
			this.cookieAccepted = false;
			this.eventsRegistered = false;
			this.isLoading = false;
			this.isLoadingResponse = false;
			this.threadId = null;
		}

		/**
		 * Setup plugin options and DOM elements
		 * @private
		 * @returns {boolean}
		 */
		setPluginOptions() {
			if ('undefined' === typeof DomAccess) return false;

			// Setup core elements
			this.assistantEl = this.domGet(document.body, '[data-assistant-gpt-plugin]', false);
			if (!this.assistantEl) return false;

			this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-chatbot-form', false);
			this.controllerUrl = '/fel/chat/thread'; // Updated to use new unified endpoint
			this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages, false);

			// Parse options from DOM
			const pluginOptions = this.assistantEl.dataset.options;
			if (!pluginOptions) return true;

			try {
				const parsed = JSON.parse(pluginOptions);
				if (parsed) {
					this.options = this.objectMerge(this.options, parsed);
				}
				return true;
			} catch (error) {
				return false;
			}
		}

		/**
		 * Initialize event system
		 * @private
		 */
		initializeEventSystem() {
			this.eventListeners = new Map();
			this.registerEvents();
		}

		/**
		 * Initialize template system
		 * @private
		 */
		initializeTemplates() {
			this.templateManager = {
				templates: {
					greeting: '',
					loading: '',
					messageItem: ''
				},
				load: () => this.loadTemplates(),
				put: (from, msg, id, datetime, addClass) =>
					this.putToMessages(from, msg, id, datetime, addClass)
			};
			this.templateManager.load();
		}

		/**
		 * Initialize consent system
		 * @private
		 */
		initializeConsent() {
			this.setLocalStorageConsent();
		}

		/**
		 * Restore last state from storage
		 * @private
		 */
		restoreLastState() {
			this.setLastChatZoom();
			this.restoreLastChatState();
		}

		/** Shopware Integration */
		setupShopwareIntegration() {
			// Subscribe to cookie configuration updates
			document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, (updated) => this.cookieCallback(updated));

			// Handle ScrollUp plugin integration
			const scrollUpPlugins = window.PluginManager.getPluginInstances('ScrollUp');
			if (scrollUpPlugins.length) {
				this.fixToggleChatScrollUpBtn(scrollUpPlugins);
			}

			// Highlight active products in chat
			this.highlightActiveProductsInChat();
		}

		fixToggleChatScrollUpBtn(scrollUpPlugins) {
			const scrollBtn = scrollUpPlugins[0].el?.firstElementChild;
			if (scrollBtn) {
				const computedStyle = window.getComputedStyle(scrollBtn);
				const right = this.felParseInt(computedStyle.right);
				const rightOpen = right + this.felParseInt(computedStyle.width) + 3;
				const rightClosed = right;
				const rightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
				const assistantEl = this.assistantEl;
				const toggleButton = assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');

				toggleButton.style.height = computedStyle.height;
				assistantEl.style.bottom = !assistantEl.classList.contains('active') ? computedStyle.bottom : null;
				assistantEl.dataset.setBottom = computedStyle.bottom;

				if (false === this.options.layout.scrollUpIsLeft) {
					if (!assistantEl.classList.contains('active')) {
						assistantEl.style.right = rightVal + 'px';
						assistantEl.dataset.setRight = rightVal + 'px';
					}
					scrollUpPlugins[0].$emitter.subscribe('toggleVisibility', () => {
						const newRightVal = scrollBtn.classList.contains('is-visible') ? rightOpen : rightClosed;
						assistantEl.style.right = assistantEl.classList.contains('active') ? null : newRightVal + 'px';
						assistantEl.dataset.setRight = newRightVal + 'px';
					});
				}
			}
		}

		cookieCallback(updatedCookies) {
			const cookieName = this.options.selector.cookieDefaultName;

			if (updatedCookies?.detail && undefined !== updatedCookies.detail?.[cookieName]) {
				this.cookieAccepted = updatedCookies.detail[cookieName];
			}

			this.setLocalStorageConsent();
		}

		felParseInt(str) {
			return parseInt(str, 10);
		}

		setLastChatZoom() {
			const getCurrent = this.storageGet(`${this.options.storage.chat}_chatZoom`);
			if ('active' === getCurrent) {
				const getZoomButton = this.assistantEl.querySelector('.fel-zoom-button');
				if (getZoomButton) {
					getZoomButton.classList.add('fel-active');
					this.assistantEl.classList.add('fel-zoom-chat');
				}
			}
		}

		restoreLastChatState() {
			if ('active' === this.storageGet(`${this.options.storage.chat}_chatOpen`)) {
				var getToggleButton = this.assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');
				if (getToggleButton) {
					getToggleButton.dispatchEvent(new MouseEvent('click', {bubbles: true}));
				}
			}
		}

		/**
		 * Click handler
		 */

		// Toggles the Chat interface in & out
		toggleChatbot(_, el) {
			const getToggleClass = el.dataset.toggleClass;
			if (getToggleClass) {
				this.assistantEl.classList.toggle(getToggleClass);
				const useStorageName = `${this.options.storage.chat}_chatOpen`;
				if (this.assistantEl.classList.contains('active')) {
					this.assistantEl.style.right = null;
					this.assistantEl.style.bottom = null;
					this.storageSet(useStorageName, 'active');
					this.scrollThreadIntoView();
					if (this.getChatbotUserInput()) {
						this.getChatbotUserInput().focus();
					}
					this._addBackdrop();
				} else {
					this.assistantEl.style.right = this.assistantEl.dataset.setRight;
					this.assistantEl.style.bottom = this.assistantEl.dataset.setBottom;
					this.storageSet(useStorageName, 'not-active');
					this._removeBackdrop();
				}
			}
		}

		toggleChatZoom(_, el) {
			el.classList.toggle('fel-active');
			if (this.assistantEl) {
				const useStorageName = `${this.options.storage.chat}_chatZoom`;
				if (el.classList.contains('fel-active')) {
					this.assistantEl.classList.add('fel-zoom-chat');
					this.storageSet(useStorageName, 'active');
				} else {
					this.assistantEl.classList.remove('fel-zoom-chat');
					this.storageSet(useStorageName, 'not-active');
				}
				if (this.getChatbotUserInput()) {
					this.getChatbotUserInput().focus();
				}
			}
		}

		// Data protection conform
		checkIfLocalStorageIsAllowed() {
			return this.cookieAccepted
				? this.cookieAccepted
				: ('allowed' || true) === CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
		}

		/**
		 * Storage helper
		 */

		storageGet() {
			return Storage.getItem(...arguments);
		}

		// setItem(keyName, keyValue)
		storageSet() {
			if (this.checkIfLocalStorageIsAllowed()) {
				Storage.setItem(...arguments);
			}
		}

		domGet() {
			return DomAccess.querySelector(...arguments);
		}

		domGetAll() {
			return DomAccess.querySelectorAll(...arguments);
		}

		// misc
		foreach(toIterate, callback) {
			return Iterator.iterate(toIterate, function(value, key) {
				callback(key, value);
			});
		}

		objectMerge(...objects) {
			const m = (t, s) => {
				Object.entries(s).forEach(([k, v]) => {
					t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
				});
				return t;
			}
			return objects.reduce(m, {});
		}

		// beforebegin afterbegin beforeend afterend
		putHtml(el, toPosition, html) {
			el.insertAdjacentHTML(toPosition, html);
		}

		/**
		 * @param {*} messageFrom ['user', 'chatbot']
		 * @param {*} message
		 */
		putToMessages(messageFrom, message, id='', datetime='', addClass='') {
			let setId = '';
			if (id) {
				id = id.slice(4);
				setId = ` id="f-${id}"`;
			}
			if (datetime) {
				datetime = ` data-datetime="${datetime}"`;
			}
			const setDeleteBtn = 'chatbot' === messageFrom
				? `<div class="fel-delete-message">
					<span title="${this.options.translation?.removeChatMessage || 'Remove'}"
						class="btn fel-btn" data-callback="removeChatMessage">X</span>
					</div>` : '' ;
			const template = ['loading', 'greeting', 'append'].includes(messageFrom)
				? message : `<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex"${setId}>
					<div class="${this.options.selector.chatMessage} ${messageFrom}-message${addClass}">
						<div class="fel-inner"${datetime}>${setDeleteBtn} ${message}</div>
					</div>
				</div>`;

			this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);
		}

		getChatbotMessagesEl() {
			return this.domGet(this.assistantEl, this.options.selector.chatMessages, false);
		}

		getChatbotUserInput() {
			return this.domGet(this.chatbotForm, this.options.selector.chatUserInput, false);
		}

		getChatbotThreadId() {
			return this.domGet(this.chatbotForm, this.options.selector.chatThreadId, false);
		}

		getChatbotRunId() {
			return this.domGet(this.chatbotForm, this.options.selector.chatRunId, false);
		}

		getChatSubmitButton() {
			return this.domGet(this.assistantEl, this.options.selector.submitChatButton, false);
		}

		_addBackdrop() {
			if (this.options.compactView) {
				this.putHtml(this.assistantEl, 'afterend', `
					<div class="modal-backdrop fel-modal-backdrop fel-fade-in show" onclick="this.remove()"></div>
				`);
			}
		}

		_removeBackdrop() {
			const getBackdrop = document.body.querySelector('.fel-modal-backdrop');
			if (getBackdrop) {
				getBackdrop.remove();
			}
		}

		scrollThreadIntoView(force) {
			if (!force && !this.options.compactView) {
				const getActiveProducts = this.getActiveProductsInChat();
				if (getActiveProducts.length) {
					return this._scrollIntoView(getActiveProducts[0], 150);
				}
			}
			const getLastUserMsgId = this.domGetAll(this.assistantEl, '.user-message-item', false);
			if (getLastUserMsgId.length) {
				this._scrollIntoView(getLastUserMsgId[getLastUserMsgId.length - 1], 200);
			}
		}

		_scrollIntoView(selector, timeout=50) {
			const getTarget = 'object' === typeof selector ? selector : this.domGet(this.assistantEl, selector, false);
			if (getTarget) {
				this.debounce(() => {getTarget.scrollIntoView({behavior: this.options.behavior, block: 'start'})}, timeout);
			}
		}

		getActiveProductsInChat() {
			if (this.options.page.refId) {
				return this.assistantEl.querySelectorAll(`.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`);
			}
			return [];
		}

		highlightActiveProductsInChat() {
			if (this.options.page.refId) {
				const activeProductsSelector = `.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`;
				if (this.domGet(this.assistantEl, activeProductsSelector, false)) {
					this.embedStyle(`
						#fel-chatbot.active.fel-zoom-chat ${activeProductsSelector} .fel-item-wrapper {
							box-shadow: var(--fel-hl-active-products-box-shadow);
						}
						#fel-chatbot.active:not(.fel-zoom-chat) ${activeProductsSelector} img {
							box-shadow: var(--fel-hl-active-products-box-shadow);
						}`);
				}
			}
		}

		embedStyle(styleString) {
			let highlightId = 'fel-embed-highlight-active-products';
			let getStyleEl = document.querySelector(`#${highlightId}`);
			if (!getStyleEl) {
				getStyleEl = document.createElement('style');
				getStyleEl.id = highlightId;
				document.head.append(getStyleEl);
			}
			return getStyleEl.textContent = styleString;
		}

		// debounce(callback, delay, immediate = false)
		debounce() {
			if (typeof Debouncer === 'function') {
				return Debouncer.debounce(...arguments).apply();
			} else if(typeof arguments[0] === 'function') {
				return setTimeout(() => {arguments[0].apply()}, arguments[1] || 0);
			}
		}

		addIsLoading() {
			const useTempLoaderId = 'fel-chat-loader-temp-id';
			let useTemplate = this.template.loading;
			this.isLoading = true;
			this.assistantEl.classList.add('fel-chatbot-is-loading');
			if (this.getChatSubmitButton()) {
				this.getChatSubmitButton().disabled = true;
				this.getChatbotUserInput().disabled = true;
			}
			this.removeGreetingContainer();
			if (this.isLoadingResponse) {
				var cloneLoader = useTemplate.cloneNode(true);
				var getInfoBox = this.domGet(cloneLoader, '.fel-chatbot-loader-info', false);
				cloneLoader.id = useTempLoaderId;
				if (getInfoBox && getInfoBox.dataset.secondRun) {
					this.domGet(cloneLoader, '.fel-chatbot-loader-info').innerHTML = getInfoBox.dataset.secondRun;
				}
				useTemplate = cloneLoader;
			}
			this.putToMessages('loading', useTemplate.outerHTML, useTempLoaderId);
		}

		removeIsLoading() {
			this.isLoading = false;
			this.assistantEl.classList.remove('fel-chatbot-is-loading');
			if (this.getChatSubmitButton()) {
				this.getChatSubmitButton().disabled = null;
				this.getChatbotUserInput().disabled = null;
			}
			let getLoader = this.domGetAll(this.assistantEl, '.fel-chatbot-load-container', false);
			if (getLoader.length) {
				for (let i = 0; i < getLoader.length; i++) {
					getLoader[i].classList.add('position-absolute', 'w-100');
					this.debounce(() => {getLoader[i].remove()}, 150);
				}
			}
		}

		removeGreetingContainer() {
			var checkGreetings = this.domGet(this.assistantEl, this.options.selector.greetingContainer, false);
			if (checkGreetings) {
				checkGreetings.remove();
				this.assistantEl.classList.remove('fel-chat-initial');
			}
		}

		setLocalStorageConsent() {
			// Initial state from cookie
			this.cookieAccepted = CookieStorageHelper.getItem(this.options.selector.cookieDefaultName) === 'allowed';

			// Update UI state
			this.updateConsentUI(this.cookieAccepted);

			// Subscribe to cookie configuration changes
			document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, this.handleConsentChange.bind(this));
		}

		handleConsentChange(event) {
			const cookieName = this.options.selector.cookieDefaultName;

			if (!event?.detail || typeof event.detail[cookieName] === 'undefined') {
				return;
			}

			this.cookieAccepted = event.detail[cookieName] === 'allowed';
			this.updateConsentUI(this.cookieAccepted);

			// Clear storage if consent is revoked
			if (!this.cookieAccepted) {
				this.clearLocalStorage();
			}
		}

		updateConsentUI(hasConsent) {
			// Update checkbox in greeting template
			const consentCheckbox = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');
			if (consentCheckbox) {
				consentCheckbox.checked = hasConsent;
			}

			// Update UI classes
			this.assistantEl.classList.toggle('fel-localstorage-is-allowed', hasConsent);
			this.assistantEl.classList.toggle('fel-localstorage-is-disabled', !hasConsent);
		}

		clearLocalStorage() {
			const storageKeys = [
				this.options.storage.chat,
				`${this.options.storage.chat}_threadId`,
				`${this.options.storage.chat}_chatOpen`,
				`${this.options.storage.chat}_chatZoom`
			];

			storageKeys.forEach(key => Storage.removeItem(key));
		}

		toggleLocalStorage(_, el) {
			const isChecked = el.checked;
			CookieStorageHelper.setItem(this.options.selector.cookieDefaultName, isChecked ? 'allowed' : 'denied');

			// Trigger consent change handler
			this.handleConsentChange({
				detail: {
					[this.options.selector.cookieDefaultName]: isChecked ? 'allowed' : 'denied'
				}
			});
		}

		/**
		 * Event Management
		 */
		handleSubmit(event) {
			try {
				return this._chatEventCallback(event, event.target);
			} catch (error) {
				return this.putToMessages('chatbot', error.message || 'error');
			}
		}

		_chatEventCallback(event, el) {
			if ('fel-chatbot-form' === el.id) {
				event.preventDefault();

				if (this.getChatbotUserInput()) {
					const userMessage = this.getChatbotUserInput().value.trim();
					if (!userMessage) return false;

					// Create conversation pair container
					const conversationId = this.generateConversationId();
					this.createConversationPair(conversationId, userMessage);

					// Prepare request data
					const requestData = {
						message: userMessage,
						threadId: this.getChatbotThreadId().value || null
					};

					// Clear input and show loading
					this.getChatbotUserInput().value = '';
					this.addLoadingToConversation(conversationId);

					// Send request to unified endpoint
					this.clientPost(this.controllerUrl, requestData, response => {
						this.removeLoadingFromConversation();

						if (response && response.success) {
							this.handleSuccessfulResponse(response.data, conversationId);
						} else {
							this.handleErrorResponse(response, conversationId);
						}
					});
				}
				return false;
			}
		}

		checkIfMessageThrown(response) {
			return (response.error && 'undefined' !== typeof response.error.exception) ? response.error.exception : false ;
		}

		/**
		 * Generate unique conversation ID
		 */
		generateConversationId() {
			return 'conv-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
		}

		/**
		 * Create a conversation pair container with user message
		 */
		createConversationPair(conversationId, userMessage) {
			const conversationHtml = `
				<div class="fel-conversation-pair" data-conversation-id="${conversationId}">

					<div class="fel-user-message chat-message-item user-message-item">
						<div class="chat-message user-message">
							<div class="fel-inner" data-datetime="${this.getCurrentTime()}">
								${this.escapeHtml(userMessage)}
							</div>
						</div>
					</div>

					<div class="chat-message-item chatbot-message-item">
						<div class="chat-message chatbot-message">
							<div class="fel-inner"></div>

							<div class="fel-message-actions" style="display: none;">
								<button class="fel-btn fel-btn-sm fel-delete-conversation"
									data-callback="removeConversationPair"
									data-conversation-id="${conversationId}"
									title="Remove conversation">×</button>
							</div>
						</div>
					</div>
				</div>
			`;

			this.putHtml(this.getChatbotMessagesEl(), 'beforeend', conversationHtml);
			this.removeGreetingContainer();
			this.scrollThreadIntoView(true);
		}

		/**
		 * Add loading indicator to conversation
		 */
		addLoadingToConversation(conversationId) {
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);
			if (conversation) {
				const assistantMessage = conversation.querySelector('.fel-assistant-message .fel-message-text');
				if (assistantMessage) {
					assistantMessage.innerHTML = `
						<div class="fel-loading-indicator">
							<div class="fel-loading-dots">
								<span></span><span></span><span></span>
							</div>
							<span class="fel-loading-text">Assistant is thinking...</span>
						</div>
					`;
				}
			}
			this.assistantEl.classList.add('fel-chatbot-is-loading');
		}

		/**
		 * Always remove loading indicator from conversation
		 */
		removeLoadingFromConversation() {
			const loadingIndicator = this.assistantEl.querySelector('.fel-loading-indicator');
			if (loadingIndicator) {
				loadingIndicator.remove();
			}
			this.assistantEl.classList.remove('fel-chatbot-is-loading');
		}

		/**
		 * Handle successful API response
		 */
		handleSuccessfulResponse(data, conversationId) {

			console.log(data)


			// Update thread information
			if (data.threadId) {
				this.getChatbotThreadId().value = data.threadId;
				this.threadId = data.threadId;
				this.assistantEl.classList.add('contains-thread');
			}

			// Process assistant messages
			if (data.threadMessages && Array.isArray(data.threadMessages)) {
				this.addAssistantResponseToConversation(conversationId, data.threadMessages);
			}

			// Handle product search results
			if (data.renderedProducts) {
				this.addProductsToConversation(conversationId, data.renderedProducts);
			}

			// Handle required actions
			if (data.requiredActions) {
				this.handleRequiredActions(data.requiredActions);
			}

			// Save to storage
			this.chatToStorage(data.threadId);

			// Focus input for next message
			this.getChatbotUserInput().focus();
		}

		/**
		 * Handle error response
		 */
		handleErrorResponse(response, conversationId) {
			const errorMessage = response?.error?.message || response?.exception || 'An error occurred';
			this.addAssistantResponseToConversation(conversationId, [{
				role: 'assistant',
				value: `<div class="fel-error-message">Error: ${this.escapeHtml(errorMessage)}</div>`
			}]);
		}

		/**
		 * Add assistant response to conversation
		 */
		addAssistantResponseToConversation(conversationId, messages) {

			console.log(conversationId)


			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);
			if (!conversation) return;

			const assistantMessage = conversation.querySelector('.fel-assistant-message .fel-message-text');
			const messageActions = conversation.querySelector('.fel-assistant-message .fel-message-actions');

			console.log(messages)
			console.log(assistantMessage)
			console.log(messageActions)

			if (assistantMessage) {
				// Process assistant messages (usually the first one is the latest)
				const latestMessage = messages[0];
				if (latestMessage && latestMessage.role === 'assistant') {
					assistantMessage.innerHTML = latestMessage.value || latestMessage.content || '';

					// Show message actions
					if (messageActions) {
						messageActions.style.display = 'block';
					}
				}
			}
		}

		/**
		 * Add products to conversation
		 */
		addProductsToConversation(conversationId, renderedProducts) {
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);
			if (!conversation) return;

			const assistantMessage = conversation.querySelector('.fel-assistant-message .fel-message-text');
			if (assistantMessage) {
				assistantMessage.innerHTML += renderedProducts;
			}
		}

		/**
		 * Remove conversation pair
		 */
		removeConversationPair(event) {
			const button = event.target;
			const conversationId = button.dataset.conversationId;
			const conversation = this.assistantEl.querySelector(`[data-conversation-id="${conversationId}"]`);

			if (conversation) {
				conversation.remove();
				this.chatToStorage(this.getChatbotThreadId().value);

				// Check if chat is empty
				const remainingConversations = this.assistantEl.querySelectorAll('.fel-conversation-pair');
				if (remainingConversations.length === 0) {
					this.deleteChatThread();
				}
			}
		}

		/**
		 * Utility methods
		 */
		escapeHtml(text) {
			const div = document.createElement('div');
			div.textContent = text;
			return div.innerHTML;
		}

		getCurrentTime() {
			return new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
		}

		/**
		 * Handle required actions from the API response
		 */
		handleRequiredActions(requiredActions) {
			// Handle redirects
			if (requiredActions.redirectTo) {
				if (requiredActions.redirectTo !== location.href) {
					location.href = requiredActions.redirectTo;
				}
			}

			// Handle other required actions as needed
			// This can be extended based on your specific requirements
		}

		// Legacy methods - kept for compatibility but not used in new conversation pair approach
		// These can be removed once we're sure the new approach works well

		clientPost(url, sendMessage, callback) {
			this.addIsLoading();
			this._client.post(url, JSON.stringify(sendMessage), response => {
				this.removeIsLoading();
				if (response) {
					try {
						const parse = JSON.parse(response);
						if (parse) {
							response = parse;
						}
					} catch (error) {
						var div = document.createElement('div');
						div.appendChild(document.createTextNode(error));
						return callback({error: {exception: div.innerHTML.substring(0, 200)}});
					}
				}
				return callback(response);
			});
		}

		handleChange(event) {
			const el = event.target;
			if (el.classList.contains('fel-checkbox')) {
				event.preventDefault();
				const callbackFn = el.dataset.callback;
				if ('function' === typeof this[callbackFn]) {
					this[callbackFn](event, el);
				}
			}
			return false;
		}

		// Additional click handlers from original plugin
		addToChatSubmit(_, el) {
			const getStrToSet = el.dataset.prompt;
			if (getStrToSet) {
				this.getChatbotUserInput().value = getStrToSet;
				this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
			}
		}

		chatToggleProperties(event) {
			event.target.parentNode.classList.toggle('fel-show-properties');
		}

		chatToggleSearchArgs(event) {
			const el = event.target;
			const targetEl = this.domGet(this.assistantEl, el.dataset.targetEl, false);
			if (targetEl) {
				el.dataset.toggleClass.split(',').map(v => targetEl.classList.toggle(v));
				el.dataset.toggleSelf.split(',').map(v => el.classList.toggle(v));
			}
		}

		chatWorkerFixProperties(event) {
			const el = event.target;
			const getStrToSet = el.dataset.shortPrompt;
			const sharedProp = el.dataset.sharedProp;
			const targetId = el.dataset.targetId;
			const targetSelector = el.dataset.targetSelector;
			const getActivePrompts = () => {
				const map = {shortPrompts: [], shortPromptsEnd: null};
				const getAll = this.domGetAll(this.assistantEl, `${targetId} [data-shared-prop].active`, false);
				if (getAll.length) {
					for (var i = 0; i < getAll.length; i++) {
						if (!map.shortPromptsEnd) {
							map.shortPromptsEnd = getAll[i].dataset.shortPromptEnd;
						}
						map.shortPrompts.push(getAll[i].dataset.shortPrompt);
					}
				}
				return map;
			};
			if (getStrToSet && sharedProp) {
				const getSharedEls = this.domGetAll(this.assistantEl, `${targetId} ${targetSelector}`, false);
				if (el.classList.contains('active')) {
					el.classList.remove('active');
				} else {
					el.classList.toggle('active');
				}
				for (var i = 0; i < getSharedEls.length; i++) {
					if (el !== getSharedEls[i]) {
						getSharedEls[i].disabled = el.classList.contains('active') ? true : null ;
					}
				}
				const chatbotUserInput = this.domGet(this.assistantEl, this.options.selector.chatUserInput, false);
				const activePrompts = getActivePrompts();
				if (chatbotUserInput) {
					const getMaxLength = chatbotUserInput.getAttribute('maxlength') || 50;
					chatbotUserInput.value = '';
					if (activePrompts.shortPrompts.length) {
						let newVal =`${activePrompts.shortPrompts.join(', ')} ${activePrompts.shortPromptsEnd}`;
						if (newVal.length > getMaxLength) {
							newVal = newVal.slice(0, getMaxLength);
						}
						chatbotUserInput.value = newVal;
					}
				}
			}
		}

		removeChatMessage(event) {
			if (event.target?.offsetParent?.offsetParent) {
				const targetParent = event.target.offsetParent.offsetParent;
				if (targetParent) {
					if (targetParent.previousElementSibling.classList.contains('user-message-item')) {
						targetParent.previousElementSibling.remove();
					}
					targetParent.remove();
					this.chatToStorage(this.getChatbotThreadId().value);
					const getStorage = this.storageGet(this.options.storage.chat);
					if ('' == getStorage.trim()) {
						this.deleteChatThread();
					}
					this.scrollThreadIntoView();
				}
			}
		}

		deleteChatThread() {
			var getThreadId = this.getChatbotThreadId()?.value;

			if (getThreadId) {
				this.addIsLoading();
				this._client.post('/fel/chat/delete-thread', JSON.stringify({threadId: getThreadId}), response => {
					this.removeIsLoading();
					if (response) {
						try {
							response = JSON.parse(response);
						} catch (error) {}
						this.resetChatState();
					}
				});
			} else {
				this.resetChatState();
			}
		}

		resetChatState() {
			this.assistantEl.classList.remove('contains-thread');
			this.getChatbotRunId().value = '';
			this.getChatbotThreadId().value = '';
			this.chatClearStorage();

			if (this.getChatbotMessagesEl()) {
				this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
				if (this.checkIfLocalStorageIsAllowed()) {
					this.setCheckedConsentLocalStorageTemplate(true);
				}
			}
			this.getChatbotUserInput().focus();
		}

		setCheckedConsentLocalStorageTemplate(localstorage) {
			const getGreetingTemplate = this.template.greeting.querySelector('[name="fel-allow-localstorage"]');
			const getOriginalEl = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');

			if (getGreetingTemplate) {
				getGreetingTemplate.checked = localstorage ? true : null;
			}
			if (getOriginalEl) {
				getOriginalEl.checked = localstorage ? true : null;
			}
		}

		// Storage management
		chatClearStorage() {
			this.storageRemove(this.options.storage.chat);
			this.storageRemove(`${this.options.storage.chat}_threadId`);
		}

		chatToStorage(threadId) {
			const getChat = this.getChatbotMessagesEl();
			if (getChat) {
				// Remove any loading indicators before saving
				const loadingIndicators = getChat.querySelectorAll('.fel-loading-indicator');
				loadingIndicators.forEach(indicator => indicator.remove());

				// Save chat content and thread ID
				this.storageSet(this.options.storage.chat, getChat.innerHTML);
				if (threadId) {
					this.storageSet(`${this.options.storage.chat}_threadId`, threadId);
				}
			}
		}

		storageRemove() {
			Storage.removeItem(...arguments);
		}

		handleClick(event) {
			const el = event.target.closest('.fel-btn');
			if (!el) return;

			const {
				callback,
				preventDefault,
				stopPropagation,
				blur
			} = el.dataset;

			if (preventDefault) event.preventDefault();
			if (stopPropagation) event.stopPropagation();
			if (blur) el.blur();

			if (!callback) return;

			// Try instance method first
			if (typeof this[callback] === 'function') {
				return this[callback](event, el);
			}

			// Fallback to window method
			if (typeof window[callback] === 'function') {
				return window[callback](event, el);
			}
		}

		loadTemplates() {
			this.template = {greeting: '', loading: '', messageItem: ''};
			const templates = document.getElementById('fel-chatbot-template');
			if (templates) {
				this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
				if (this.template.greeting) {
					const getChatFromStorage = this.storageGet(this.options.storage.chat);
					this.putToMessages('greeting', getChatFromStorage
						? getChatFromStorage
						: this.template.greeting.outerHTML
					);
					if (getChatFromStorage) {
						this.assistantEl.classList.add('contains-thread');
						this.getChatbotThreadId().value = this.storageGet(this.options.storage.chat + '_threadId');
					}
				}
				this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
			}
		}

		registerEvents() {
			Object.entries(this.options.eventMapping).forEach(([key, config]) => {
				const element = this.domGet(document.body, config.element, false);
				if (!element) {
					console.warn(`Element not found for ${key}:`, config.element);
					return;
				}

				this.eventListeners.set(key, {
					element,
					handlers: new Map()
				});

				// Ensure events is treated as an array
				const events = Array.isArray(config.events)
					? config.events
					: Object.values(config.events);

				events.forEach(eventConfig => {
					if (!eventConfig || !eventConfig.type || !eventConfig.handler) {
						console.warn(`Invalid event config for ${key}:`, eventConfig);
						return;
					}

					if (typeof this[eventConfig.handler] !== 'function') {
						console.warn(`Handler ${eventConfig.handler} not found for ${key}`);
						return;
					}

					const handler = this[eventConfig.handler].bind(this);
					element.addEventListener(eventConfig.type, handler);
					this.eventListeners.get(key).handlers.set(eventConfig.type, handler);
				});
			});
		}

		destroy() {
			this.eventListeners.forEach(({ element, handlers }) => {
				handlers.forEach((handler, type) => {
					element.removeEventListener(type, handler);
				});
			});
			this.eventListeners.clear();
		}
	}

    new FelOaiChatbotPluginDEV();
};
