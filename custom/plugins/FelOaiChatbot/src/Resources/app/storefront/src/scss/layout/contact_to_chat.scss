.fel-is-hidden {
    display: none;
}
.fel-contact-to-chat-button-container {
    .fel-fade-in {
        animation-duration: .3s;
    }
}
.fel-contact-to-chat-response-container {
    .fel-delete-message {
        display: none;
    }
    .fel-chatbot-load-container {
        .fel-chatbot-loader {
            padding: .6rem;
            background-position: 0 center;
        }
        .fel-chatbot-loader-info {
            display: none;
        }
        + div {
            margin-top: .6rem;
        }
    }
    .fel-text-plain {
        white-space: pre-line;
    }
    h2, h3, h4, h5, h6 {
        margin-bottom: 0;
        line-height: 1.3;
        font-size: 1.1rem;
        + p,
        + ul {
            margin-top: .5rem;
            padding-top: .5rem;
            border-top: 1px solid var(--bs-gray-300);
        }
    }
    ul {
        li {
            margin: .3rem 0;
        }
    }
    .only-close {
        margin: 0;
        float: right;
        .fel-remove-response {
            padding: 1rem;
        }
    }
    .fel-contains-response {
        margin: .5rem 0 0 0;
        padding: .8rem 1rem;
        background-color: var(--bs-gray-100);
        box-shadow: 0 0 4px var(--bs-gray-500);
        .fel-oai-response {
            > :last-child {
                margin-bottom: 0;
            }
        }
    }
}
