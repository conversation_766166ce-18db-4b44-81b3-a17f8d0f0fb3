<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService;
use FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction;
use FelOAIAssistantsManager\Service\FelConfigService;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['api']])]
class ServiceAvailabilityTest
{
    public function __construct(
        private LoggerInterface $logger,
        private ?OpenAIChatService $baseChatService,
        private ?OpenAIRequiredAction $baseRequiredAction,
        private ?FelConfigService $felConfigService
    ) {
    }

    #[Route(
        path: '/api/fel/ai-product-advisor/test-base-services',
        name: 'api.fel.ai-product-advisor.test-base-services',
        methods: ['GET']
    )]
    public function testBaseServices(): JsonResponse
    {
        $result = [
            'baseChatService' => $this->baseChatService !== null,
            'baseRequiredAction' => $this->baseRequiredAction !== null,
            'felConfigService' => $this->felConfigService !== null,
        ];

        $this->logger->info('ServiceAvailabilityTest: Testing base services', $result);

        return new JsonResponse($result);
    }
}
