import template from './sw-cms-el-config-chatbot.html.twig';
import './sw-cms-el-config-chatbot.scss';

const { Component, Mixin } = Shopware;

Component.register('sw-cms-el-config-chatbot', {
    template,

    inject: ['repositoryFactory'],

    mixins: [
        'cms-element',
        'notification',
        'fel-assistant-manager-plugin-config-mixin',
        'fel-assistant-manager-openai-mixin',
    ],

    data() {
        return {
            mediaModalIsOpen: false,
            initialFolderId: null,
            customAssistantList: null,
            assistantList: { data: [] },
            cmsCategoryId: null,
            categoryCollection: [],
            context: Shopware.Context.api,
            loadingCategory: true, // Loading state for categories
            previewSource: null // New data property for preview source
        };
    },

    computed: {
        mediaRepository() {
            return this.repositoryFactory.create('media');
        },
        categoryRepository() {
            return this.repositoryFactory.create('category');
        },
        uploadTag() {
            return `cms-element-media-config-${this.element.id}`;
        },
        cmsCategoryId: {
            get() {
                return this.element.config.cmsCategoryId?.value || null;
            },
            set(value) {
                this.element.config.cmsCategoryId.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsAssistantId: {
            get() {
                return this.element.config.cmsAssistantId?.value || '';
            },
            set(value) {
                this.element.config.cmsAssistantId.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsAdvisorTitle: {
            get() {
                return this.element.config.cmsAdvisorTitle?.value || '';
            },
            set(value) {
                this.element.config.cmsAdvisorTitle.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsAdvisorName: {
            get() {
                return this.element.config.cmsAdvisorName?.value || '';
            },
            set(value) {
                this.element.config.cmsAdvisorName.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsGreetingMessage: {
            get() {
                return this.element.config.cmsGreetingMessage?.value || '';
            },
            set(value) {
                this.element.config.cmsGreetingMessage.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsChatThemeColor: {
            get() {
                return this.element.config.cmsChatThemeColor?.value || '#6ed59f';
            },
            set(value) {
                this.element.config.cmsChatThemeColor.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsProductAdvisorFor: {
            get() {
                return this.element.config.cmsProductAdvisorFor?.value || '';
            },
            set(value) {
                this.element.config.cmsProductAdvisorFor.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsProductAdvisorChatWindowHeight: {
            get() {
                return this.element.config.cmsProductAdvisorChatWindowHeight?.value || '';
            },
            set(value) {
                this.element.config.cmsProductAdvisorChatWindowHeight.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsBorderRadius: {
            get() {
                return this.element.config.cmsBorderRadius?.value || false;
            },
            set(value) {
                this.element.config.cmsBorderRadius.value = value;
                this.$emit('element-update', this.element);
            }
        },
        cmsProductAdvisorAvatar: {
            get() {
                return this.element.config.cmsProductAdvisorAvatar?.value || null;
            },
            set(value) {
                this.element.config.cmsProductAdvisorAvatar.value = value;
                this.$emit('element-update', this.element);
            }
        }
    },

    created() {
        this.createdComponent();
    },

    methods: {
        async createdComponent() {
            this.initElementConfig('chatbot');

            // Initialize the preview source if we have a media ID
            if (this.element.config.cmsProductAdvisorAvatar && this.element.config.cmsProductAdvisorAvatar.value) {
                const mediaId = this.element.config.cmsProductAdvisorAvatar.value;
                try {
                    const media = await this.mediaRepository.get(mediaId, Shopware.Context.api);
                    if (media && media.url) {
                        this.previewSource = media.url;
                    }
                } catch (error) {
                    // Error loading media preview
                }
            }

            // Rest of your initialization code
            if (!this.element.config.cmsAdvisorName) {
                this.$set(this.element.config, 'cmsAdvisorName', { value: '' });
            }

            if (!this.element.config.cmsCategoryId) {
                this.$set(this.element.config, 'cmsCategoryId', { value: null });
            }

            await this.fetchPluginConfig();
            await this.fetchAssistants();
            await this.fetchCategories();
        },

        async fetchCategories() {
            this.loadingCategory = true; // Start loading categories
            const criteria = new Shopware.Data.Criteria(1, 100);
            criteria.addAssociation('media');
            this.categoryCollection = await this.categoryRepository.search(criteria, this.context);

            // Ensure the selected category is loaded when reopening
            if (this.element.config.cmsCategoryId && this.element.config.cmsCategoryId.value) {
                const selectedCategory = this.categoryCollection.find(category => category.id === this.element.config.cmsCategoryId.value);
                if (selectedCategory) {
                    this.cmsCategoryId = selectedCategory.id;  // Set the selected category
                }
            }
            this.loadingCategory = false; // End loading categories
        },

        onElementUpdate(value, field) {
            this.element.config[field].value = value;
            this.$emit('element-update', this.element);
        },

        onImageUpload({ targetId }) {
            this.mediaRepository.get(targetId).then(mediaEntity => {
                this.element.config.cmsProductAdvisorAvatar.value = mediaEntity.id;
                this.updateElementData(mediaEntity);
                this.$emit('element-update', this.element);
            });
        },

        onImageRemove() {
            this.element.config.cmsProductAdvisorAvatar.value = null;
            this.updateElementData();
            this.$emit('element-update', this.element);
        },

        onSelectionChanges(mediaEntity) {
            const media = mediaEntity[0];
            this.element.config.cmsProductAdvisorAvatar.value = media.id;
            // Set the preview source to the media URL
            this.previewSource = media.url;
            this.updateElementData(media);
            this.$emit('element-update', this.element);
        },

        updateElementData(media = null) {
            const mediaId = media === null ? null : media.id;
            if (!this.element.data) {
                this.$set(this.element, 'data', { mediaId, media });
            } else {
                this.$set(this.element, 'mediaId', mediaId);
                this.$set(this.element, 'media', media);
            }
        },

        onOpenMediaModal() {
            this.mediaModalIsOpen = true;
        },

        onCloseModal() {
            this.mediaModalIsOpen = false;
        },

        assistantMaker(event) {
            const selectedId = event.target.value;
            this.customAssistantList = selectedId;
            this.cmsAssistantId = selectedId;
        }
    }
});
