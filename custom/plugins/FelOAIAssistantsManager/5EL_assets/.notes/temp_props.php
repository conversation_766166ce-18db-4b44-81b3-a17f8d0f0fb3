<?php return array (
  '_meta_data' => 
  array (
    'count' => 
    array (
      'properties' => 20,
      'categories' => 10,
    ),
  ),
  'properties' => 
  array (
    'Size' => 
    array (
      0 => 'S',
      1 => 'L',
      2 => 'M',
      3 => 'XL',
    ),
    'Colour' => 
    array (
      0 => 'Red',
      1 => 'White',
      2 => 'Purple',
      3 => 'Green',
      4 => 'Black',
      5 => 'Blue',
      6 => 'Purple / Green',
    ),
    'Material' => 
    array (
      0 => 'Cotton',
      1 => 'Plastic',
      2 => 'Nylon',
      3 => 'Leather',
      4 => 'Stainless _ steel',
      5 => 'Polyester',
    ),
    'Target group' => 
    array (
      0 => 'Man',
      1 => 'Woman',
      2 => 'Children',
    ),
    'Manufacturer' => 
    array (
      0 => 'Shopware Fashion',
      1 => 'Shopware Freetime',
      2 => 'Shopware Food',
      3 => 'shopware AG',
    ),
  ),
  'categories' => 
  array (
    0 => 
    array (
      'name' => 'Clothing',
      'parent' => 
      array (
        0 => 'Catalogue #1',
      ),
      'url' => '/navigation/01904039ee1978d595b5d558f150d90f',
    ),
    1 => 
    array (
      'name' => 'Sports',
      'parent' => 
      array (
        0 => 'Catalogue #1',
      ),
      'url' => '/navigation/0190411c45507223b77e5f86a486baf6',
    ),
    2 => 
    array (
      'name' => 'Sub Katalog',
      'parent' => 
      array (
        0 => 'Catalogue #1',
      ),
      'url' => '/navigation/0190411c7487717da9623d8dc6167523',
    ),
    3 => 
    array (
      'name' => 'Men',
      'parent' => 
      array (
        0 => 'Catalogue #1',
        1 => 'Clothing',
      ),
      'url' => '/navigation/0190403a68437c378c32887f9da8af1d',
    ),
    4 => 
    array (
      'name' => 'Women',
      'parent' => 
      array (
        0 => 'Catalogue #1',
        1 => 'Clothing',
      ),
      'url' => '/navigation/0190403abc6f7adfaca331fd72ed15d8',
    ),
    5 => 
    array (
      'name' => 'Susi Kat',
      'parent' => 
      array (
        0 => 'Catalogue #1',
        1 => 'Sub Katalog',
      ),
      'url' => '/navigation/0190411d08b1717683d8a4172ded0520',
    ),
    6 => 
    array (
      'name' => 'Subuntu',
      'parent' => 
      array (
        0 => 'Catalogue #1',
        1 => 'Sub Katalog',
        2 => 'Susi Kat',
      ),
      'url' => '/navigation/0190411dd78e7070a8497ab9638069d8',
    ),
    7 => 
    array (
      'name' => 'Tops',
      'parent' => 
      array (
        0 => 'Catalogue #1',
        1 => 'Clothing',
        2 => 'Women',
      ),
      'url' => '/navigation/0190b7ed837b771282bad3e3f2a85d26',
    ),
    8 => 
    array (
      'name' => 'Fubu',
      'parent' => 
      array (
        0 => 'Susi Kat',
        1 => 'Subuntu',
      ),
      'url' => '/navigation/0190411e303174548326130192683a67',
    ),
    9 => 
    array (
      'name' => 'T-Shirts',
      'parent' => 
      array (
        0 => 'Women',
        1 => 'Tops',
      ),
      'url' => '/navigation/0190b7f80b417cfb907a86ed3dd7b40c',
    ),
  ),
);
