<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">

    <!-- OpenAI Authentication Configuration -->
    <card>
        <title>OpenAI Authentication Configuration</title>
        <title lang="de-DE">OpenAI Authentifizierungs Konfiguration</title>

        <input-field type="checkbox">
            <name>enablePlugin</name>
            <defaultValue>false</defaultValue>
            <label>Enable Plugin</label>
            <label lang="de-DE">Plugin aktivieren</label>
            <helpText>This option allows you to quickly enable/disable the plugin without changing system configurations.</helpText>
            <helpText lang="de-DE">Diese Option ermöglicht das schnelle Aktivieren/Deaktivieren des Plugins, ohne die Systemkonfigurationen zu ändern.</helpText>
        </input-field>

        <!-- New name, old 'openAiApiKey' -->
        <input-field type="password">
            <name>felOpenAiApiKey</name>
            <defaultValue></defaultValue>
            <label>Secret API Key from OpenAI</label>
            <label lang="de-DE">Ihr geheimer API-Schlüssel von OpenAI</label>
        </input-field>

        <!-- New button, requires OpenAI Assistant Manager -->
        <component name="fel-test-openai-api-key">
            <name>apiTest</name>
        </component>

    </card>

    <!-- OpenAI Assistant Configuration -->
    <card>
        <title>OpenAI Assistant Configuration</title>
        <title lang="de-DE">OpenAI Assistent Konfiguration</title>

        <!-- Name changed, old 'openAiAssistantId' -->
        <input-field type="text">
            <name>felOpenAiAssistantId</name>
            <defaultValue></defaultValue>
            <label>Assistant ID</label>
            <label lang="de-DE">Assistenten-ID</label>
            <helpText>Enter the assistant ID</helpText>
            <helpText lang="de-DE">Geben Sie die Assistenten-ID ein</helpText>
        </input-field>

        <!-- Name changed, old 'openAiChatMetaChatbotName' -->
        <input-field type="text">
            <name>openAiChatbotName</name>
            <defaultValue></defaultValue>
            <label>Give your chatbot a name</label>
            <label lang="de-DE">Geben sie ihrem Chatbot einen Namen</label>
            <helpText>The name entered here will be displayed as the chatbot name next to the avatar. With the help of the so-called “Instructions” you can give your chatbot a name and give it even more character traits.</helpText>
            <helpText lang="de-DE">Der hier eingegebene Name wird als Chatbot-Name neben dem Avatar angezeigt. Mithilfe der sogenannten „Instructions“ können Sie Ihrem Chatbot einen Namen geben und ihm sogar Charaktereigenschaften zuweisen.</helpText>
        </input-field>

    </card>

    <card>
        <title>5 Elements Tools</title>
        <title lang="de-DE">5 Elements Werkzeuge</title>

        <component name="fel-custom-tool-info">
            <name>customToolInfo</name>
        </component>
    </card>

    <!-- NEW - Text fields like Initial Chat Guidelines, FAQs, etc, ToDO: Translate -->
    <card>
        <title>Free text Fields for Different Purposes</title>
        <title lang="de-DE">Freitextfelder für verschiedene Zwecke</title>

        <!-- Initial Instructions -->
        <input-field type="textarea">
            <name>openAiInitAdditionalInstructions</name>
            <defaultValue></defaultValue>
            <label>Additional Instructions for the Assistant</label>
            <label lang="de-DE">Zusätzliche Anweisungen für den Assistenten</label>
            <helpText>Use this field to provide any extra instructions for the assistant that dont fit into the standard instructions. The assistant will consider this information at the start of every chat session. Make sure to enable the 'fetch_initial_chat_guidelines' function for this to take effect.</helpText>
            <helpText lang="de-DE">Nutzen Sie dieses Feld, um zusätzliche Anweisungen für den Assistenten bereitzustellen, die nicht in die standardmäßigen Anweisungen passen. Der Assistent wird diese Informationen zu Beginn jeder Chat-Sitzung berücksichtigen. Aktivieren Sie die Funktion 'fetch_initial_chat_guidelines', damit dies wirksam wird.</helpText>
        </input-field>

        <!-- FAQ -->
        <input-field type="textarea">
            <name>openAiFaqField</name>
            <defaultValue></defaultValue>
            <label>Frequently Asked Questions (FAQ) Data</label>
            <label lang="de-DE">Daten für häufig gestellte Fragen (FAQ)</label>
            <helpText>Enter your shop's FAQs here for the assistant to access and provide quick answers to customers. Ensure that the 'get_faqs' function is enabled for the assistant to use this data.</helpText>
            <helpText lang="de-DE">Geben Sie hier die häufig gestellten Fragen (FAQs) Ihres Shops ein, damit der Assistent schnelle Antworten für Kunden liefern kann. Stellen Sie sicher, dass die Funktion 'get_faqs' aktiviert ist, damit der Assistent diese Daten nutzen kann.</helpText>
        </input-field>

    </card>

    <!-- File alternative, meta information -->
    <card>
        <title>File alternative</title>
        <title lang="de-DE">Datei-Alternative</title>

        <!-- Name changed, old 'openAiStaticInformationField' -->
        <input-field type="textarea">
            <name>openAiMetaInformationField</name>
            <defaultValue></defaultValue>
            <label>Meta Information: Instead of assigning files to the assistant, the assistant can also dynamically load the following field.</label>
            <label lang="de-DE">Meta Informationen: Statt dem Assistenten Dateien zuzuweisen, kann der Assistent auch das folgende Feld dynamisch laden.</label>
            <helpText>Put any content, the used assistant should know, in this field. The assistant will request this data once when needed. Ensure that the 'get_meta_information' function is enabled for the assistant to use this data.</helpText>
            <helpText lang="de-DE">Schreiben Sie alles, was der Assistent wissen soll in dieses Feld. Der Assistent ladet diese Daten, wenn er die Daten benötigt. Stellen Sie sicher, dass die Funktion 'get_meta_information' aktiviert ist, damit der Assistent diese Daten nutzen kann.</helpText>
        </input-field>

    </card>

    <!-- OpenAI misc Configuration -->
    <card>
        <title>OpenAI misc Configuration</title>
        <title lang="de-DE">OpenAI sonstige Konfiguration</title>

        <input-field type="int">
            <name>openAiUserInputMaxLength</name>
            <step>1</step>
            <min>2</min>
            <defaultValue>400</defaultValue>
            <label>Set the maximum number of characters a user can enter into the chat interface.</label>
            <label lang="de-DE">Legen Sie die maximale Anzahl an Zeichen fest, die ein Benutzer in die Chat-Oberfläche eingeben kann.</label>
        </input-field>

        <input-field type="int">
            <name>openAiSearchLimit</name>
            <step>1</step>
            <min>1</min>
            <max>50</max>
            <defaultValue>8</defaultValue>
            <label>Set Limit for internal search results the assistant receives.</label>
            <label lang="de-DE">Legen Sie ein Limit für Suchergebnisse fest, die intern der KI zurück gegeben werden.</label>
        </input-field>

        <input-field type="int">
            <name>openAiFilterMaxDescriptionLength</name>
            <step>1</step>
            <min>50</min>
            <defaultValue>1500</defaultValue>
            <label>When an assistant calls up product details, limit the product description to a maximum of x characters.</label>
            <label lang="de-DE">Wenn ein Assistent Produktdetails aufruft, kürze die Produktbeschreibung auf maximal x Zeichen ein.</label>
            <helpText>The final response is plain text, with all HTML removed.</helpText>
            <helpText lang="de-DE">Die endgültige Antwort ist einfacher Text, aus dem sämtliches HTML entfernt wird.</helpText>
        </input-field>

        <!-- New - Description length in search results -->
        <input-field type="int">
            <name>openAiFilterMaxDescriptionLengthSearchResults</name>
            <step>1</step>
            <min>0</min>
            <defaultValue>500</defaultValue>
            <label>Limit the product description in search results to a maximum of x characters.</label>
            <label lang="de-DE">Kürze die Produktbeschreibung in Suchergebnissen auf maximal x Zeichen ein.</label>
        </input-field>

        <!-- New - Add Category IDs to response -->
        <input-field type="checkbox">
            <name>openAiReturnCategoriesWithIds</name>
            <defaultValue>false</defaultValue>
            <label>This option extends the category output to include the respective category IDs.</label>
            <label lang="de-DE">Diese Option erweitert die Kategorien-Ausgab um die jeweiligen Kategorie-IDs.</label>
        </input-field>

        <!-- New - Add property IDs to response -->
        <input-field type="checkbox">
            <name>swApiSetPropertyIdsAsKeys</name>
            <defaultValue>false</defaultValue>
            <label>This option extends the properties output to include the respective property IDs as keys.</label>
            <label lang="de-DE">Diese Option erweitert die Eigenschaften-Ausgab um die jeweiligen Eigenschaft-IDs.</label>
        </input-field>

    </card>

    <!-- Reduce and Optimize Content Generated for the AI -->
    <card>
        <title>Reduce and Optimize Content Generated for Assistants</title>
        <title lang="de-DE">Generierte Inhalte für die Assistenten reduzieren und optimieren</title>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistCategory</name>
            <entity>category</entity>
            <defaultValue>[]</defaultValue>
            <label>Select categories that should be excluded from the Content generated for the chatbot.</label>
            <label lang="de-DE">Wählen Sie Kategorien aus, die vom für den Chatbot generierten Inhalt ausgeschlossen werden sollen.</label>
            <helpText lang="en">By default, the AI retrieves categories through functions only for the Main-Navigation. However, many shops have extensive category lists that the AI can find challenging to process. By selectively excluding categories deemed irrelevant, you can significantly enhance the efficiency and performance of the chatbot.</helpText>
            <helpText lang="de-DE">Standardmäßig bekommt die KI über Funktionen nur Kategorien aus der Main-Navigation. Viele Shops verfügen jedoch über umfangreiche Kategorienlisten, die von der KI schwer bis gar nicht verarbeitet werden können. Durch das gezielte Ausschließen als irrelevant erachteter Kategorien können Sie die Effizienz und Leistungsfähigkeit des Chatbots deutlich verbessern.</helpText>
        </component>

        <input-field type="int">
            <name>openAiFilterCategoryLevel</name>
            <step>1</step>
            <min>0</min>
            <max>20</max>
            <defaultValue>0</defaultValue>
            <label>Set the starting level for category breadcrumbs (e.g., 1 for a structure like 'Catalogue #1').</label>
            <label lang="de-DE">Legen Sie die Startebene für die Brotkrumen-Navigation fest (z.B. 1 für eine Struktur wie 'Katalog Nr. 1').</label>
        </input-field>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistManufacturer</name>
            <entity>product_manufacturer</entity>
            <defaultValue>[]</defaultValue>
            <label>Select manufacturers that should be excluded from the Content generated for the chatbot.</label>
            <label lang="de-DE">Wählen Sie Hersteller aus, die vom für den Chatbot generierten Inhalt ausgeschlossen werden sollen.</label>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistPropertyGroups</name>
            <entity>property_group</entity>
            <defaultValue>[]</defaultValue>
            <label>Select property groups that should be excluded from the Content generated for the chatbot (including option groups).</label>
            <label lang="de-DE">Wählen Sie Eigenschaftsgruppen aus, die vom für den Chatbot generierten Inhalt ausgeschlossen werden sollen.</label>
            <helpText>Excluding certain property groups can help optimize the AI's performance by focusing only on the most relevant properties.</helpText>
            <helpText lang="de-DE">Das Ausschließen bestimmter Eigenschaftsgruppen kann dazu beitragen, die Leistung des Chatbot zu optimieren.</helpText>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>openAiWhitelistPropertyGroups</name>
            <entity>property_group</entity>
            <defaultValue>[]</defaultValue>
            <label>Select property groups that should be included. If none is selected, the chatbot will access all property groups.</label>
            <label lang="de-DE">Wählen Sie Eigenschaftsgruppen aus, die der Chatbot erhalten soll. Wenn keine ausgewählt sind, werden alle Eigenschaftsgruppen ausgelesen.</label>
            <helpText>If you neither include nor exclude groups, the plugin will return all options and properties assigned to active products (including 'Not filterable' options, because they are still searchable). This can quickly lead to hundreds or thousands of irrelevant data points for the chatbot, which can be costly and also affect the chatbot's performance.</helpText>
            <helpText lang="de-DE">Wenn Sie Gruppen weder ein- noch ausschließen, gibt das Plugin bei der Abfrage der Optionen („options“ und „properties“) alle zurück, die aktiven Produkten zugewiesen sind (auch „nicht filterbare“, weil diese dennoch auffindbar sind). Das kann schnell zu Hunderten bis Tausenden an für den Chatbot irrelevanten Daten führen, die zum einen die Kosten erhöhen und zum anderen die Leistung des Chatbots erheblich beeinträchtigen können.</helpText>
        </component>

        <component name="sw-entity-multi-id-select">
            <name>openAiBlacklistPropertyOptions</name>
            <entity>property_group_option</entity>
            <defaultValue>[]</defaultValue>
            <label>Select options and properties that should be excluded from the Content generated for the chatbot.</label>
            <label lang="de-DE">Wählen Sie Optionen und Eigenschaften aus, die vom für den Chatbot generierten Inhalt ausgeschlossen werden sollen.</label>
        </component>

        <input-field type="int">
            <name>openAiFilterMaxOptions</name>
            <step>1</step>
            <min>1</min>
            <max>250</max>
            <defaultValue>20</defaultValue>
            <label>Set the maximum number of properties per property group that the chatbot should process. This includes manufacturers.</label>
            <label lang="de-DE">Legen Sie die maximale Anzahl an Eigenschaften pro Eigenschaftsgruppe fest, die der Chatbot verarbeiten soll. Dies schließt Hersteller ein.</label>
        </input-field>

    </card>


    <!-- GPT Agent appearance -->
    <card>
        <title>GPT Agent appearance</title>
        <title lang="de-DE">GPT Agent aussehen konfigurieren</title>

        <input-field type="checkbox">
            <name>felScrollUpIsLeft</name>
            <defaultValue>false</defaultValue>
            <label>The 'scroll up'-button is on the left side</label>
            <label lang="de-DE">Der „Nach oben scrollen“-Button ist Links.</label>
        </input-field>

        <input-field type="single-select">
            <name>chatbotLoadingIcon</name>
            <defaultValue>default</defaultValue>
            <options>
                <option>
                    <id>default</id>
                    <name>Blue loading icon (default)</name>
                    <name lang="de-DE">Standard ladesymbol (Blau)</name>
                </option>
                <option>
                    <id>dark</id>
                    <name>Dark loading icon</name>
                    <name lang="de-DE">Dunkles Ladesymbol</name>
                </option>
                <option>
                    <id>golden</id>
                    <name>Yellow loading icon</name>
                    <name lang="de-DE">Gelbes Ladesymbol</name>
                </option>
                <option>
                    <id>red</id>
                    <name>Red loading icon</name>
                    <name lang="de-DE">Rotes Ladesymbol</name>
                </option>
            </options>
            <label>The loading icon to use</label>
            <label lang="de-DE">Das zu verwendende Ladesymbol</label>
        </input-field>

        <input-field type="single-select">
            <name>chatbotAvatar</name>
            <defaultValue>female</defaultValue>
            <options>
                <option>
                    <id>female</id>
                    <name>Female (default)</name>
                    <name lang="de-DE">Weiblich (Standard)</name>
                </option>
                <option>
                    <id>male</id>
                    <name>Male</name>
                    <name lang="de-DE">Männlich</name>
                </option>
            </options>
            <label>Chatbot Avatar</label>
            <label lang="de-DE">Chatbot Profilbild</label>
        </input-field>

        <component name="sw-media-field">
            <name>chatbotCustomAvatar</name>
            <entity>pluginMedia</entity>
            <label>Custom chatbot Avatar (240*240px, transparent)</label>
            <label lang="de-DE">Eigenes Chat Profilbild (240*240px, transparent)</label>
        </component>

    </card>

    <!-- Contact Form AI Assistant -->
    <card>
        <title>Contact Form AI Assistant</title>
        <title lang="de-DE">Kontaktformular KI-Assistent</title>

        <input-field type="checkbox">
            <name>enableContactFormAssistant</name>
            <defaultValue>false</defaultValue>
            <label>Enable AI Assistant for Contact Forms</label>
            <label lang="de-DE">KI-Assistent für Kontaktformulare aktivieren</label>
            <helpText>Show "Ask AI Assistant first?" button above contact forms to help customers get instant answers before filling out forms</helpText>
            <helpText lang="de-DE">Zeige "Erst KI-Assistent fragen?" Button über Kontaktformularen um Kunden sofortige Antworten zu geben bevor sie Formulare ausfüllen</helpText>
        </input-field>

        <input-field type="text">
            <name>contactFormButtonText</name>
            <defaultValue>Ask AI Assistant first?</defaultValue>
            <label>Button Text</label>
            <label lang="de-DE">Button Text</label>
            <helpText>Text displayed on the AI assistant button</helpText>
            <helpText lang="de-DE">Text der auf dem KI-Assistent Button angezeigt wird</helpText>
            <placeholder>Ask AI Assistant first?</placeholder>
        </input-field>

        <input-field type="text">
            <name>contactFormHelpMessage</name>
            <defaultValue>Hi! I'm here to help. What would you like to know?</defaultValue>
            <label>Help Message</label>
            <label lang="de-DE">Hilfe Nachricht</label>
            <helpText>Optional message shown when AI assistant opens from contact form</helpText>
            <helpText lang="de-DE">Optionale Nachricht die angezeigt wird wenn der KI-Assistent vom Kontaktformular geöffnet wird</helpText>
            <placeholder>Hi! I'm here to help. What would you like to know?</placeholder>
        </input-field>

    </card>

</config>
