<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Core\Content\ProductProperties\SalesChannel;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;

abstract class AbstractProductPropertiesRoute
{
    abstract public function getDecorated(): AbstractProductPropertiesRoute;

    abstract public function getProductProperties(Request $request, SalesChannelContext $context): ProductPropertiesRouteResponse;
}
