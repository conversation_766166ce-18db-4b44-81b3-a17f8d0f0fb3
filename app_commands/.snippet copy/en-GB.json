{"fel-assistant-manager": {"general": {"mainMenuItemGeneral": "5E OAI Assistants Manager", "brandNameShort": "5E", "shortName": "OAIAM", "yes": "Yes", "no": "No", "abortAllRequests": "Abort all requests", "abortRequest": "Abort request", "assistant": "Assistant", "assistantDescription": "Assistant description, for internal use only (max. 500 character)", "assistantId": "Assistant ID", "assistantName": "Assistant Name", "assistants": "Assistants", "cancel": "Cancel", "character": "chars", "close": "Close", "componentsAreloading": "Some components are loading in the background.", "configuration": "Configuration", "confirm": "Confirm", "confirmDeletion": "Confirm Deletion", "contentCheckMainMenuItemGeneral": "Content Checker", "contentEditorMainMenuItemGeneral": "Content Editor", "create": "Create", "createApiKey": "Create new secret key", "createAssistant": "Create Assistant", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "deleted": "Deleted", "description": "This page acts as a middleman between you and your OpenAI Account, which is connected to your OpenAI API Key.", "details": "Details", "edit": "Edit", "example": "Example", "goToConfiguration": "Go to Configuration", "help": "Help", "initialize": "Initialize", "loading": "loading…", "message": "Message", "messages": "Messages", "missingOpenAiKey": "Missing OpenAI API Key", "noAssistantsFound": "No assistants found", "noDataOnServerNote": "The data on this page is saved and managed on the OpenAI API Servers.", "openAIApiKey": "OpenAI API Key", "openAIAssistants": "OpenAI Assistants", "openAIFiles": "Files", "openAIStatus": "OpenAI Status", "openAITokenizer": "OpenAI Tokenizer", "openAIUsage": "OpenAI Usage", "openAIPricing": "Pricing", "requestAborted": "Request aborted", "requiredApiKeyInfo": "To access this page, a valid OpenAI API key authorized for \"All Sales Channels\" is required.", "reset": "Reset", "retry": "Retry", "save": "Save", "select": "Select", "selected": "Selected", "showFunctions": "Show Functions", "temperature": "Temperature", "temperatureDescription": "The \"Temperature\" setting adjusts the randomness of responses. A lower temperature results in more predictable and consistent responses. The scale ranges from 0.1 to 2, with 2 indicating a high level of randomness.", "update": "Update", "updateAssistant": "Update", "uploadFile": "Upload Files to OpenAI", "viewDetails": "View details", "aiToolsTranslation": {"fetch_initial_chat_guidelines": "Enable this feature to provide the assistant with initial guidelines, including important information about your shop and products. This ensures the assistant is well-informed before starting any conversation. Customize this information on the configuration page.", "get_categories": "(from get_product_properties) Enable this feature to allow the assistant to retrieve and present available shop categories, including their URLs, names, and breadcrumb structures.", "get_chatbot_name": "Enable this feature to allow the assistant to retrieve and use its name in conversations for a more personalized user experience.", "get_countries": "Enable this feature to allow the assistant to provide users with a list of available countries, useful for shipping and localization purposes.", "get_date_time": "Enable this feature to allow the assistant to provide users with the current date, time, and timezone information, ensuring accurate and timely responses.", "get_delivery_times": "Enable this feature to allow the assistant to inform users about the estimated delivery times for products or services.", "get_faqs": "Enable this feature to allow the assistant to retrieve frequently asked questions (FAQs) and provide customers with quick, relevant answers based on your stored FAQ data. Customize this information on the configuration page.", "get_manufacturer": "(from get_product_properties) Enable this feature to allow the assistant to provide users with detailed information about the manufacturers of your products.", "get_meta_information": "Enable this feature to allow the assistant to access and share metadata about your shop, such as contact details, opening hours, and FAQs. Customize this information through the dynamic data field on the configuration page.", "get_order_status": "Enable this function to allow the assistant to retrieve the details of a customer's order using the order number and zip code. This feature ensures the assistant can provide relevant order information, such as order status and tracking code, without sharing private customer details like names or contact information with OpenAI. The response will include only the order status and, if available, the tracking code.", "get_payment_methods": "Enable this feature to allow the assistant to provide users with information about the available payment methods in your shop.", "get_product_details": "Enable this feature to allow the assistant to fetch detailed information about specific products using their product IDs or numbers, offering users comprehensive product details.", "get_product_properties": "Enable this feature to allow the assistant to retrieve and utilize product properties, manufacturer names, and categories to enhance product search and filtering capabilities.", "fetch_url": "Enable this tool to allow your assistants to retrieve the content of URLs. These URLs can be stored in text format in the corresponding fields that manage content for assistants.", "go_to_url": "Enable this feature to allow the assistant to redirect users to specific URLs or paths within your shop based on user requests.", "product_search": "Enable this feature to allow the assistant to conduct product searches based on user queries, with the ability to filter results by categories, properties, price range, and sorting options.", "log": "Enable this feature to allow the assistant to log issues or suggestions encountered during conversations. This helps improve the chatbot's knowledge base and enhance overall service quality.", "search_logs": "Enable this feature to allow the assistant to search for existing log entries using specific tags before creating new logs. This prevents duplicate entries and ensures that previous issues are properly referenced.", "tags_for_logs": "Enable this feature to allow the assistant to retrieve available tags that are necessary for both the 'log' and 'search_logs' functions.", "get_unresolved_logs": "Enable this feature to allow the '5 Elements Backend Assistant' to automatically retrieve the latest unresolved public log entries—up to 5 user queries that have not yet been answered. This functionality helps ensure that customer questions are identified and addressed promptly, streamlining your support process while reducing manual effort."}}, "files": {"allowedFileTypes": "Allowed file types", "canNotDownloadFile": "Not allowed to download files of purpose: assistants", "files": "Files", "filesAttached": "Attached files", "filesAttachedToAssistants": "When you attach files to an assistant, this activates the 'code_interpreter,' which expects structured data.", "filesMaxAmountbreached": "Max allowed files: {max}, selected: {current}", "uploadHelp": "To ensure your assistant is fully equipped with all necessary information, please compile a comprehensive list detailing the specific knowledge, data, or instructions you wish to include. You may copy and paste content, such as your FAQ page, into the file without any modifications. Think of the file as a detailed brochure that provides all essential information an employee would need to accurately respond to customer inquiries.", "vectorStore": {"attachFiles": "Attach files", "attachVectorStoreToAssistant": "Attach a vector store to your assistant.", "createVectorStore": "Create a vector store", "detachFiles": "Detach file", "fileIsEmpty": "File is empty", "filenameInvalid": "Filename contains invalid character", "name": "Vector Store", "noFilesAttached": "No attached files", "noVectorsFound": "No vector stores found", "refreshStore": "Refresh", "selectVectorStore": "Select a vector store", "setName": "Vector Store Name", "aboutVectorStore": "Vector Store objects give the File Search tool the ability to search your files. Adding a file to a vector_store automatically parses, chunks, embeds and stores the file in a vector database that's capable of both keyword and semantic search. Each vector_store can hold up to 10,000 files. Vector stores can be attached to both Assistants and Threads. Today, you can attach at most one vector store to an assistant."}}, "create": {"expensiveModelWarning": "Please note that this model comes with significantly higher costs. Review the pricing details before proceeding.", "responseFormat": "Response format", "responseFormatHelp": "Set the format assistants should use for their responses.", "responseFormatHelpSchema": "The \"Schema\" format forces the assistant to respond with a JSON object. This enables structured and comprehensive replies in chats. However, it may lead to increased tool usage. Ensure all tools available to the assistant return compact and complete data.", "responseFormatHelpObject": "The \"Object\" format tries to generate the response as JSON object.", "responseFormatHelpAuto": "The \"auto\" format falls back to HTML.", "responseFormatMissingInstruction": "The instructions are missing a response section.", "previewConfig": "Preview config", "selectAllowedFunctions": "Customize Capabilities: Enable Tools as needed", "selectAllowedFunctionsInfo": "Enable the functions you want your assistant to access. These features use real-time data from your Shopware store. If you have pre-uploaded data or have configured information in dynamic fields, you may leave specific functions disabled to prevent duplication.", "selectModelToUse": "Select the Model", "setInstructions": "Instructions", "setInstructionsInfo": "Please adjust the instructions according to your needs. If you are using files, make sure to clearly indicate this in the instructions so that the assistant can take the files in the attachment into account. It is important to explicitly inform the assistant to ensure optimal processing. However, note that the GPT-3.5-Turbo model does not effectively support files. Although attaching files does not cause errors, the assistant cannot load them, and other errors may occur.", "form": {"setAssistantName": "Assistant name", "setAssistantNameHelp": "For internal use only"}}, "delete": {"backToIndex": "Back to overview", "delete": "Delete", "deleteAssistant": "You are about to delete the Assistant", "confirm": "Confirm", "cancel": "Cancel", "yes": "Yes", "no": "No", "confirmMessage": "Are you sure you want to delete this item?", "confirmDeleteLog": "Are you sure you want to delete this log?", "confirmDeleteTag": "Are you sure you want to delete this tag?", "confirmDeleteVectorStore": "Are you sure you want to delete this vector store?", "selectItemsToDelete": "Please select items to delete."}, "contentEditor": {"generalName": "Content Editor", "generalDescription": "Manage content your assistants receive.", "description": "This page allows you to customize plugin configurations. It provides an easy way to configure Blacklist and Whitelist options by using advanced filters for categories, manufacturers, properties, and property groups. Designed to make cleanup and configuration efficient, it empowers users to fine-tune their Assistants to the highest degree.", "inheritanceRestored": "Inheritance successfully restored to default settings", "restoreInheritance": "Restore Inheritance", "salesChannelActiveConfigEmpty": "No configurations overridden for the selected sales channel.", "salesChannelOverridesDefaultNote": "The selected sales channel overrides the default configuration.", "editable": {"enablePlugin": {"name": "Enable Plugin", "description": "This option allows you to quickly enable/disable the plugin without changing system configurations."}, "openAiWhitelistPropertyGroups": {"name": "Whitelist Property Groups", "description": "Select property groups to include. If none is selected, all property groups will be included."}, "openAiBlacklistPropertyGroups": {"name": "Blacklist Property Groups", "description": "Select property groups that should be excluded from the content generated for the assistant."}, "openAiBlacklistPropertyOptions": {"name": "Blacklist Property Options", "description": "Select options and properties that should be excluded from the content generated for the assistant."}, "openAiBlacklistManufacturer": {"name": "Blacklist Manufacturers", "description": "Select manufacturers that should be excluded from the content generated for the assistant."}, "openAiBlacklistCategory": {"name": "Blacklist Categories", "description": "Select categories that should be excluded from the content generated for the assistant."}, "openAiFilterCategoryLevel": {"name": "Filter Category Level", "description": "Set the starting level for category breadcrumbs."}, "openAiReturnCategoriesWithIds": {"name": "Add Category IDs to response", "description": "If there are not to many categories, the assistant can set IDs instead of names."}, "swApiSetPropertyIdsAsKeys": {"name": "Add Property IDs to response", "description": "If there are not to many properties, the assistant can be trained to filter products using property IDs instead of names."}, "openAiFilterMaxOptions": {"name": "Max Options", "description": "Set the maximum number of properties per property group that the assistant should process. Max: {max}"}, "openAiSearchLimit": {"name": "Search Limit", "description": "Set the default maximum number of product search results to return. The assistant can also adjust this limit within the specified range of: min {min}, max {max}."}, "felOpenAiAssistantId": {"name": "Assistant ID", "description": "The default Assistant ID"}, "openAiChatbotName": {"name": "Chatbot Name", "description": "The name entered here will be displayed as the chatbot name next to the avatar. Max length: {maxlength}"}, "openAiMetaInformationField": {"name": "Get Meta Information", "description": "Enable this to let the assistant access essential shop information, such as contact details, opening times, and other metadata, to assist customers better. Max length: {maxlength}"}, "openAiInitAdditionalInstructions": {"name": "Initial Chat Guidelines", "description": "Provide important shop-specific instructions that the assistant retrieves at the start of each chat session. These guidelines will be used throughout the conversation to ensure accurate and consistent information delivery. Max length: {maxlength}"}, "openAiFaqField": {"name": "Get FAQs", "description": "Enable this function to allow the assistant to retrieve frequently asked questions (FAQs) and provide customers with quick and relevant answers based on your stored FAQ data. Max length: {maxlength}"}, "notFound": {"name": "Configs not found", "description": "Configs not found"}, "idsNotFound": {"name": "IDs not found, properties are probably not assigned to active products", "description": "IDs not found"}, "felIdsToNamesFinished": {"name": "IDs were converted to names, remaining IDs couldn't be identified", "description": "Converted IDs to Names"}, "openAiFilterMaxDescriptionLengthSearchResults": {"name": "Max Description Length in Search Results", "description": "Limit the product description to a maximum number of characters in search results."}, "openAiFilterMaxDescriptionLength": {"name": "Max Description Length", "description": "Limit the product description to a maximum number of characters, if product details are requested."}, "openAiOrganizationId": {"name": "Organization ID", "description": "Provide the OpenAI Organization ID (not required)."}, "openAiUserInputMaxLength": {"name": "User Input Max Length", "description": "Set the maximum number of characters a user can enter into the assistant interface."}}}, "catchErrors": {"assistantIdIsRequired": "Assistant ID is required", "customPluginSeemsToNotExist": "It seems the selected plugin doesn't exist", "completedRuns": "Completed Runs you can try to resume", "error": "Error", "errorDeletingFile": "Error deleting file", "errorFetchingTagUsages": "Failed to fetch usage data", "errorLoadingData": "Failed to load requested data", "errorNotFound": "Not found", "errorWhileSavingLog": "An error occurred while saving the log", "failedAssistantNotFound": "Assistant not found", "failedBulkDelete": "Bulk delete has failed", "managerCanNotRunNoplugins": "The 5E Assistants Manager requires at least one installed Plugin from 5 Elements in order to run.", "missingAssstantId": "Missing an Assistant ID", "failedToStoreData": "Failed to store the data", "failedBulkReset": "Bulk reset failed.", "failedBulkResetAll": "Bulk reset of all usage counters failed.", "failedNoFilesFound": "No files found", "failedOaiServerError": "OAI Server Error", "failedToArchiveLog": "Failed to archive log", "failedToAttachFileToVectorStore": "Failed to attach files to vector store", "failedToCreateOrUpdateAssistant": "Failed to create or update assistant", "failedToCreateVectorStore": "Failed to create vector store", "failedToDebounceFunction": "Failed to debounce function", "failedToDeleteAssistant": "Failed to delete assistant", "failedToDeleteVectorStore": "Failed to delete vector store", "failedToDetachFileFromVectorStore": "Failed to detach file from vector store", "failedToDetachFileNotFound": "File not found, refreshing list", "failedToFetchLog": "Failed to fetch log", "failedToFetchSalesChannels": "Failed to fetch SalesChannels", "failedToFormatIdsToNames": "Failed to format IDs to names", "failedToInitializeClientComponent": "Failed to initialize the HTTP client component", "failedToInitializeComponent": "Failed to initialize component", "failedToInitializeRoute": "Failed to initialize route", "failedToLoadAssistantDetails": "Failed to load assistant details", "failedToLoadAssistants": "Failed to load assistants", "failedToLoadCustomPluginConfig": "Failed to load custom plugin configuration", "failedToLoadFile": "Failed to load file", "failedToContinueChat": "Failed to load the chat", "failedToReadMessage": "Failed to read message", "failedToLoadFiles": "Failed to load files", "failedToLoadLogRepository": "Failed to load log repository", "failedToLoadModels": "Failed to load models", "failedToLoadTagDetails": "Failed to load tag details", "failedToLoadTags": "Failed to load tags", "failedToLoadVectorStore": "Failed to load vector store", "failedToLoadVectorStores": "Failed to load vector stores", "failedToPrefetchProductProperties": "Failed to prefetch product properties", "failedToRestoreCustomPluginInheritance": "Failed to restore custom plugin inheritance", "failedToRunAction": "Failed to run action", "failedToSaveTag": "Failed to save {name} tag.", "failedToToggleJson": "Failed to toggle JSON", "failedToTranslateSnippet": "Snippet not found: '{requestedTranslation}'", "failedToUpdateAliasesFor": "Failed to update aliases for {name} tag.", "failedToUpdateLog": "Failed to update log", "failedToUpdatePluginConfiguration": "Failed to update plugin configuration", "failedToUpdateVectorStoreName": "Failed to update vector store name", "fileUploadFailed": "File upload failed", "failedToSendMessage": "Failed to send Message", "importEmptyLogs": "Logs is empty", "importFailed": "Import failed", "invalidConfigLongtextMaxError": "The allowed max length is: {maxlength}", "invalidConfigValueMaxError": "The allowed max value is: {max}", "invalidConfigValueMinError": "The allowed min value is: {min}", "invalidConfigValueNewTypeError": "The type of the new value ({newValueType}) does not match the type of the existing value ({existingValueType}).\nValue: {value}", "invalidSalesChannelDomain": "The SalesChannel Domain is not valid", "logDeleteError": "Deleting the Log failed", "messageIsRequired": "Message is required", "missingAccessData": "Missing access data", "missingPrefetchedProductProperties": "Missing prefetched product properties", "openAiMissingApiBaseUrl": "The required OpenAI API base URL is missing or not valid.", "salesChannelMustBeSelected": "Sales Channel must be selected", "selectItemsToDelete": "Please select at least one item to delete.", "tagAliasNotUnique": "Tag alias is not unique", "tagMustBeSelected": "Tag must be selected", "tagNameEmpty": "Tag name cannot be empty", "missingThreadId": "Missing threadId", "tagNameNotUnique": "Tag name must be unique.", "tagWithAssociationCanNotDelete": "This tag is associated with logs and cannot be deleted.", "unknownErrorOccured": "An unknown error occurred", "uploadFailedFileTooLarge": "The file submitted is too large for the upload", "uploadFileIsEmpty": "The file submitted for upload is empty  ", "uploadFilenameInvalid": "Filename contains invalid characters", "vectorStoreNameRequired": "Name required. Please provide a valid name."}, "catchSuccess": {"archiveSuccess": "Archive successful", "bulkDeleteSuccess": "Selected chat logs have been successfully deleted.", "created": "Created", "stored": "Stored", "deleted": "Deleted", "importSuccessful": "Import successful", "importAssistantLogsFinished": "Imported {importedTotal} from {total}", "inheritanceRestored": "Inheritance successfully restored to default settings", "logDeletedSuccessfully": "Log deleted successfully", "resetAllUsageCountersSuccess": "All usage counters have been reset.", "resetUsageCountersSuccess": "The usage counters for the selected items have been reset.", "tagCreateSuccessfull": "Tag created successfully!", "tagDeletedSuccessfully": "Tag deleted successfully", "tagUpdateSuccessfull": "Tag updated successfully!", "updatedAliasesForSuccessfully": "Updated aliases for {name} tag successfully.", "updateSuccessful": "Update successful", "savedTagAndAliasesSuccessfully": "{name} tag and aliases saved successfully."}, "catchInfo": {"delete": "Delete Confirmation", "confirmMessage": "Are you sure you want to proceed with this deletion?", "Storage State": "Storage State", "filesAttachedSuccessfully": "Files attached successfully", "noFilesSelected": "No files selected for attachment", "selectAssistantAndFile": "Please select at least one assistant and one file", "errorAttachingToAssistant": "Error attaching to assistant", "choice": "Make a Choice", "choiceMessage": "Please select an option:"}, "catchWarning": {"warning": "Warning", "attention": "Attention Required", "selectItemsToDelete": "Please select items to delete."}, "backendAssistant": {"helper": {"name": "5 Elements Backend Assistant", "label": "Backend Assistant", "nickname": "BaBu", "noBackEndAssistantInfo": "Enhance your plugin's capabilities with the 5 Elements Backend Assistant. BaBu is your dependable companion for testing server functions, running stress tests, and validating tool call efficiency (functions called by Assistants).", "noBackEndAssistantCreateOne": "Click this link and select 'Create Assistant' to get started", "tooltip": "Create your personalized '5 Elements Backend Assistant (BaBu).' This assistant is a versatile tool that generates custom content directly via OpenAI. Usage costs are the same as for other assistants in chats. BaBu has no restrictions—you can chat about anything. To test features with BaBu, enable the desired functionalities in the 'Customize Assistant Capabilities' section.", "description": "Meet your Backend Assistant (BaBu), specifically designed to support with tasks like testing server capabilities. It adapts dynamically to meet the requirements of your tasks. Configure its capabilities to suit your needs.\n\nThe assistant is identified by its name, so ensure the name remains unchanged."}}, "instructions": {"chatbotInstructionsName": "<PERSON><PERSON><PERSON> instructions", "chatbot": {"gptThree": "GPT-3", "gptFour": "GPT-4"}}, "form": {"toReadable": "Readable", "willBeSavedIn": {"salesChannel": "SalesChannel: {salesChannel}", "allSalesChannel": "All Sales Channels"}}, "filter": {"categoriesBlacklistNotNecessaryDescription": "If you notice any missing categories in this selection, please note that our plugin only works with categories that are part of the Main Navigation. Categories in other sections, such as the Service or Footer navigation, are not accessible to the assistant. This functionality is designed to allow you to blacklist categories from the assistant, but categories that are already inaccessible don't need to be blacklisted", "CategoriesPlaceholder": "Filter categories…", "filterCategories": "Categories", "filterManufacturer": "Manufacturers", "filterProperties": "Properties", "generalName": "Filter", "manufacturerPlaceholder": "Filter manufacturers…", "maxLength": "Max length", "more": "More", "propertiesPlaceholder": "Filter properties…", "propertyGroupPlaceholder": "Filter property groups…", "ui": {"autoSubmit": "auto submit", "categories": "Categories", "false": "off", "filteringFailedNoResultsFound": "No results found. Please adjust your search criteria.", "hideFilter": "hide filter", "limit": "limit", "liveFilter": "Live filter", "noResults": "No results", "order": "sort", "price_max": "max price", "price_min": "min price", "query": "query", "setVisibleToConfig": "Select all visible", "showFilter": "show filter", "showSelectedOnly": "Show selected items only", "start": "Start", "submit": "Submit", "true": "on", "type": "type: {type}"}}, "contentCheck": {"noInstalledPluginsToCheck": "No 5 Elements plugins are currently available.", "installedPluginNotActive": "The following plugins are installed but not activated: {plugins}", "clearPrefetchedData": "Clear prefetched data", "testRedirectionToUrl": "Test redirection to URL: ", "clickToToggleReadability": "Click to toggle between a readable and a not so readable format", "customConfigForSalesChannelApplied": "The default configuration is mixed with settings from salesChannel {salesChannelName}.", "customConfigurationHasBeenApplied": "Custom configuration for '{getCustomPluginConfig}' has been loaded and will be applied to all requests.", "expertView": "Expert view", "formatToReadableFormat": "Convert to a readable format", "generalDescription": "This page allows you to simulate an Assistant making requests. The responses returned by the tool are the same as those received by Assistants when they call functions (Tools). Select a Plugin Config for real-case scenarios, where the Blacklist and Whitelist options of the selected configuration are applied. By default, all accessible content is returned.", "getCustomConfiguration": "show configuration", "loadPluginConfig": "Load Plugin <PERSON>fig", "missingAccessData": "Missing access data. Please ensure that the Sales Channel Access Key and Domain are set.", "prefetchedData": "Prefetched Data", "prefetchedDataDescription": "This data is an extended version of 'get_product_properties' that includes additional IDs and custom formats. It is used to validate the inputs assistants provide when filtering products or other entities. Although users can blacklist or whitelist properties, these exclusions do not affect the prefetched data. The data represents the same information the assistant uses, with extra details for validation purposes.", "prefetchProductProperties": "Prefetch Product Properties", "readableFormat": "Readable format: The content is formatted for easier readability.", "responseTitle": "This is the response the assistant will receive when it requests '{function}', ca. '{strLength}' characters.", "salesChannel": "SalesChannel", "salesChannelAccessKey": "SW Access Key", "salesChannelDomains": "SalesChannel Domains", "salesChannelDomain": "SalesChannel Domain", "domain": "Domain", "selectFunction": "Select", "selectRequiredAction": "Request", "selectSalesChannelDomain": "Select a Domain", "customPluginConfig": "Custom plugin", "noCustomPluginConfigSelected": "No config selected", "selectSalesChannelLabel": "Select SalesChannel", "withIds": "With IDs"}, "storageManager": {"deleteAll": "Delete all", "description": "Temporary storage for dynamically loaded data within the application.", "disableStorage": "Disable Storage", "enableStorage": "Enable Storage", "hasContent": "Storage active", "isEmpty": "Storage is Empty", "items": "Items", "name": "Storage Manager", "storages": "Storages", "cacheName": {"assistantRequestCache": "Assistant Request Storage", "contentEditor": "Content Editor Storage", "customPluginConfigLoaded": "Custom Plugin Config", "managerIsLoading": "App State Storage"}}, "log": {"mainMenuItemGeneral": "Manage Logs", "description": "The logs on this page are created by Assistants to document interactions, especially when information gaps occur. Please review and address unresolved messages directly on the log-edit page to ensure your assistants have all the information they need to perform on a high level of accuracy.", "addNewLog": "Add New Log", "alias": "<PERSON><PERSON>", "answer": "Answer", "answered": "Answered", "areYouSureToDeleteLog": "Are you sure you want to delete this log?", "assistantId": "Assistant ID", "assistantLogs": "Assistant Logs", "assisttantIdNotAvailable": "The Assistant ID doesn't exist, possibly deleted.", "assisttantIdsInLogsNotAvailable": "The following Assistants don't exist", "backToPage": "Back to: {page}", "categoryId": "Category ID", "checkLogs": "Check logs", "clearFilters": "Clear Filters", "createATag": "Create a Tag", "createALog": "Create a Log", "isItResolved": "Resolved?", "import": "Import", "importHelp": "Import assistant logs", "logs": "Logs", "read": "Read", "write": "Write", "message": "Message", "metaData": "Meta Data (JSON)", "noDataTitle": "No Logs found", "note": "Note", "noteIsPrivate": "Note (private)", "noteDescription": "Use this field to keep private notes for internal use. These notes are not shared with assistants and are intended solely for reference or as reminders.", "notePlaceholder": "Note", "noTagsAvailable": "No Tags Available", "prepareDescription": "You can also prepare data in advance by defining tags, creating logs, and providing answers. This ensures that when an assistant checks the logs, it will have access to your pre-prepared data, enabling it to perform tasks more effectively and accurately.", "priority": "Priority", "selectAssistant": "Select Assistant", "selectSalesChannel": "Sales Channel", "selectTag": "Tag", "fieldsToConnectData": "Assistants often use flexible and unpredictable language when searching for data. These fields allow you to connect your content behind the scenes, so that no matter how the assistant phrases the request, it can still find the right information.", "searchTerms": "Search terms", "searchTermsDescription": "Enter comma-separated search terms that should lead to this data. You can also include terms that don't exist yet — they will appear as clickable buttons later.", "tagAliases": "Tag Aliases", "tagAliasesDescription": "Enter alternative comma separated tag names here, which you want assistants to use to find this information. This data will not be shared with assistants, but will be considered internally during searches. This allows you to network information and maintain it centrally without unnecessarily increasing the amount of data available to assistants.", "tagUsage": "Tag usage", "tagUsageCounterDisabled": "Tag usage counter is disabled", "writeAttempts": "Write attempts", "descriptions": {"aboutRecords": "Certain records are stored exclusively for filtering purposes and do not influence the data that assistants receive. Assistants always have access to all records unless they are marked as private. Additionally, these records are not restricted by SalesChannels or individual assistants; they are used internally for filtering and organizational purposes.", "aboutAssistantRetrieval": "When an assistant searches the logs by specific tags (or multiple tags), it retrieves all fields associated with those tags. You can use our built-in Content Checker tool with the default configuration to preview what data assistants will retrieve when using the log functions.", "fetchUrl": "Assistants can also download the content of a URL. Instead of copying and pasting the data manually, simply instruct the assistant to fetch it. For example: \"The information you're looking for is available at: https://example.com. Use the `fetch_url()` tool and discard the data after fetching.\" Note: If a log already contains relevant information, the assistant may prioritize that over fetching external content. Logs act as a reliable internal source of truth and should be kept up to date to reduce unnecessary external requests.", "tagFieldInfo": "The tag field comes with a 'More' button. When clicked, it reveals additional fields to fine-tune. Additionally, it lists all alias names and search terms as clickable buttons. These fields help connect data invisibly, so that assistants can find the correct information—even if the tag name itself wasn't mentioned.", "fullFaqNote": "Set the message to FEL_FULL_FAQ to earmark records as dedicated FAQ data. These entries are retrieved only when you search with the query FEL_FULL_FAQ using search_logs(), ensuring that they remain separate for specialized tasks with our 5 Elements Backend Assistant."}, "priorities": {"low": "Low", "medium": "Medium", "high": "High"}, "download": {"asCSV": "CSV", "asJSON": "JSON", "name": "Download"}, "filter": {"assistant": "Assistant", "selectPriority": "Priority", "selectResolved": "Resolved", "selectPrivate": "Private", "selectThreadDeleted": "Deleted", "applyFilters": "Apply Filters", "filterTagsAndAliases": "Filter tag names and aliases", "label": "Filter", "all": "All", "active": "Filter active", "resolved": "Resolved", "private": "Private", "privateInfo": "Marking an entry as Private ensures it remains hidden from assistants, making it useful for internal notes or in-progress messages. Furthermore, the plugin safeguards against duplicate entries made by assistants, regardless of whether the entry is private or not.", "unresolved": "Unresolved", "showMoreFilter": "Show more Filter", "threadDeleted": "Deleted", "threadNotDeleted": "Not deleted", "threadMessagesPlaceholder": "Filter messages"}, "tag": {"areYouSureToDeleteTag": "Are you sure you want to delete this tag?", "tagsAreRequiredFor": "Tags are required for the functions: ", "backToTags": "Back to Tags", "tagsAndAliasesList": "Tags and Aliases", "countAssistantsDescription": "Tool calls", "countAssistantsLabel": "Tool Usage Counter", "createdBy": "Created By", "description": "The tags defined on this page will be used by Assistants to categorize and log messages on various topics, helping to organize and filter conversations for easier analysis and management. To keep your tag list streamlined, you can use aliases as alternative names for a single tag. This enables flexibility for Assistants who may occasionally use unexpected or varied tag names, allowing you to group them under a preferred tag using aliases.", "isActive": "Active", "mainMenuItemGeneral": "Manage Tags", "noRelatedData": "No related data", "noTagsFound": "No Tags Found", "saveMustHaveTags": "Save All Must-Have Tags", "saveTag": "Save Tag", "selectedTags": "Tags: {tags}", "mustHaveTags": "Must have tags including aliases", "resetUsageCounter": "Reset usage counter", "resetAllUsageCounter": "Reset all usage counter", "resetSelectedUsageCounter": "Reset selected usage counter", "confirmResetAllUsageCounter": "This action will delete all counter-entries in 'fel_assistant_tag_usage'.", "noAliasesAssigned": "No Aliases assigned to the tag.", "tag": "Tag", "tags": "Tags", "tagAlias": "Tag Alias", "tagAliasDescription": "Enter a comma-separated list of alternative names for this tag. These aliases will be treated as equivalent to the main tag but remain hidden from assistants.", "tagName": "Tag Name", "tagAliases": "Tag Aliases", "aliases": "Aliases", "tagNamePlaceholder": "Enter tag name", "tagTips": "5E Tags Tips", "totalTags": "{tagNames} Tags, {tagAliases} Aliases"}}, "chatlog": {"mainMenuItemGeneral": "Chat Logs", "description": "The logs on this page are Chat logs that capture conversations between users and assistants. Each log entry contains the final, optimized conversation the assistant returns. It includes user queries and assistant responses. This feature helps you track and review AI-driven interactions to ensure quality and accuracy in responses.", "addNew": "Add a Chat log", "archiveSelected": "Archive", "bulkDeleteButton": "Delete Selected Items", "cancel": "Cancel", "tokenUsage": "Token usage: {tokenUsedTotal}", "calculateTokenUsage": "Calculate token usage", "calculateTokenUsageForAll": "Calculate token usage for all", "chatlogDisabled": "<PERSON><PERSON><PERSON> is disabled", "details": "Details", "nextChatInModal": "Open next <PERSON><PERSON> in Modal", "previousChatInModal": "Open previous Chat in Modal", "noDataTitle": "No Chat Logs found", "none": "none", "oaiUsage": "last_oai_usage", "requestTook": "last_request_took", "tokenUsedTotal": "Chats: {chatsTotal}, Token: {tokenUsedTotal}", "issuesFound": "{failed} failed, {errors} errors", "save": "Save", "selectItemsToDelete": "Please select at least one item to delete.", "showTrackedData": "Show tracked data", "unarchiveSelected": "Unarchive", "column": {"salesChannelId": "SalesChannelId", "threadId": "threadId", "assistantId": "AssistantId", "threadMessages": "Thread Messages", "threadDeleted": "Deleted", "threadArchived": "Archived", "createdAt": "Created", "updatedAt": "Updated", "runsCount": "Chats", "tokenUsage": "Token usage"}}, "chat": {"allow": "Allow", "autoScroll": "Auto scroll to last message", "capabilities": "Capabilities", "continueThreadDescription": "You can resume logged threads from the database as long as they have not been explicitly deleted or marked as ended. Resuming threads does not incur additional costs, as it simply loads the last state of the thread from the database. However, please note that some of these threads might be open customer chats. Continuing these chats could lead to confusion for customers, as they may see additional responses that don’t align with their previous interaction. To maintain clarity and avoid misunderstandings, consider using the resume functionality only for internal threads or ensure customer threads are handled carefully without sending additional responses unless explicitly intended.", "customConfigWillBeOverridden": "The selected Assistant will override the AssistantID specified in loadCustomConfig, if it exists. Additionally, the API Key from the Assistants Manager will be applied for subsequent requests.", "decline": "Decline", "deleteThread": "Delete the thread (deletes the thread on OpenAI)", "description": "Chat with Your OAI Assistants", "empty": "empty", "errorPutMessageToInputNoSubmit": "Put message back to input, don't submit", "failedToCreateThread": "Failed to create thread. Please try again.", "failedToSendMessage": "Failed to send message. Please try again.", "failedWhileCheckingStatus": "An error occurred while checking the status.", "failedWhileFetchingMessages": "An error occurred while fetching messages.", "failedWhileSendingYourData": "An error occurred while sending your message.", "hideChatConfig": "Hide chat config", "instructions": "Instructions", "label": "Playground", "mainMenuItemGeneral": "Playground Chat", "noAssistantsAvailable": "No assistants available", "notifications": "Notifications", "promptPlaceholder": "Type your message here…", "runIdPlaceholder": "RunId", "requiredAction": "Required Action", "requirements": "Requirements", "resume": "Resume", "resumeChatWithThreadId": "Resume chat with an threadID", "routeDescription": "This page allows you to test your Assistants in a live chat", "search": "Search", "send": "Send", "showChatConfig": "Show chat config", "shuffle": "Shuffle", "stop": "Stop", "stopping": "Stopping", "storefrontUrlNotValid": "The storefrontUrl is not a valid URL", "suggestions": "Suggestions", "tempRequestData": "Temp request data", "toggleAssistantDetails": "Toggle Assistant details", "toggleChatInputField": "Toggle chat input field", "total": "Total: {total}", "withRunId": "With RunId", "noRunId": "New RunId", "withThreadId": "With ThreadId", "willUseAssistantsManagerCredentials": "The selected Assistant and the API Key from the Assistants Manager will be used for all OpenAI API requests.", "tool": {"label": "Chat tools", "description": "Below are buttons with prepared instructions for testing Assistant capabilities or performing complex tasks. Token costs vary depending on the data in your store, such as properties and categories. After clicking a button, the instruction will appear in the message field for review and optional adjustments. You must click 'Send' to execute the instruction.", "requiredTools": "Required Tools: ", "productProperties": {"label": "Check Product Properties", "description": "Preview \"get_product_properties\" using our \"Content Checker\" tool. This is crucial for shops with extensive properties or categories, which could lead to high token costs. Use the Content Checker tool to understand the data assistants will process."}, "logMessages": {"label": "Log Assistant Messages", "description": "Conduct a general log test that simulates realistic assistant logs. This doubles as a performance test by requesting and processing dozens of assistant logs simultaneously, helping identify potential bottlenecks."}, "toolsPerformance": {"label": "Tools performance test", "description": "Evaluate system performance by testing multiple backend tools in one go. Check for seamless function execution and identify performance limits."}}, "autoChat": {"label": "Auto Chat Tool", "maxMessages": "Max messages", "runAutoChat": "Run Auto", "timeout": "Timeout ms", "autoQuestions": "Auto Questions", "enterSetOfQuestions": "Enter a set of questions for the auto chat.", "clearStoredData": "Clear", "storeQuestionsToLocalStorage": "Store the data temporary in my localStorage", "storeQuestionsToLocalStorageInfo": "The localStorage is only a temporary storage, the question list is not saved permanently."}, "missingRequired": {"salesChannelId": "SalesChannel ID is missing", "storefrontUrl": "Storefront URL is missing", "felOpenAiApiKey": "OpenAI API Key is missing", "felOpenAiAssistantId": "Assistant ID is missing"}, "threadArchive": {"label": "Logged active threads"}}, "instructionIssues": {"title": "Instruction issues", "note": "These warnings are for informational purposes only. Since the instructions are plain text and can be freely edited, we can't modify or interpret them to resolve these issues — we can only highlight possible inconsistencies.", "conflictingTools": "The tools <b>{toolB}()</b> and <b>{toolA}()</b> are both enabled. This can lead to confusion, as they may serve overlapping purposes or share content. It’s recommended to enable only one of them based on your assistant’s goal.", "productPropertyNote": "<b>Note</b><br>The <b>get_product_properties()</b> tool provides a complete map of options, attributes, manufacturers, and categories — all the data required for product search and recommendations. If the assistant is intended to search for or recommend products, consider disabling <b>get_categories()</b> and <b>get_manufacturer()</b> to avoid redundancy, as both internally get their data from <b>get_product_properties()</b>.", "searchLogsAggressiveUsage": "<b>Note</b><br>When <b>search_logs()</b> is enabled, assistants tend to use it heavily. To ensure meaningful and efficient responses, make sure the returned log entries are as precise and compact as possible. The Assistant Manager provides everything needed — including <b>tags</b>, <b>tagAliases</b>, and <b>searchTerms</b> for each item — but it relies on your input to break down information into clear, structured chunks. This not only improves response quality, but also reduces token usage by a factor of 3 to 5. The performance boost comes for free — as a reward for clean data.", "logFunctionNote": "<b>Note</b> on <b>log()</b><br>Assistants can be unreliable when it comes to logging issues — sometimes they do, sometimes they don't. To ensure nothing gets lost, we've added a fallback: whenever a <b>search_logs()</b> call fails, the data is automatically logged. This guarantees that relevant information still gets stored, even if the assistant forgets to call <b>log()</b> explicitly. <b>Best practice:</b> let the assistant run and observe what actually ends up in the Assistants-Logs. That's the original search request — based on this data, you can optimize more accurate.", "gpt4turboNotSupportedWithSchema": "The 'response_format' of type 'schema' is not supported with model version `gpt-4-turbo`.", "issues": "The tool <b>{tool}()</b> is referenced <b>{count}</b> time(s) in the instructions, but it is not included in the selected tools."}, "notifications": {"suggestionTitle": "Suggestion", "noFileSelected": "No file selected for import.", "logsImported": "{0} log(s) successfully imported.", "errorWhileImportingLogs": "There was an error while importing the logs."}, "exception": {"failedToInitThread": "OAI: Failed to create a thread: {message}", "maxLoopAttemptsReached": "Max loop attempts reached.", "missingActionInRequest": "ChatService: Missing action in request", "missingAssistantIdForCreateThread": "Assistant ID is missing. It is required for: create_thread.", "missingAssistantIdForSendMessage": "Assistant ID is missing. It is required for: send_message.", "missingMessageForCreateThread": "Message is missing. It is required for: create_thread.", "missingMessageForSendMessage": "Message is missing. It is required for: send_message.", "missingMessageIdForDeleteMessage": "Message ID is missing. It is required for: delete_message.", "missingOpenaiApiKey": "ChatService: Missing OpenAI API key in request", "missingOpenaiBaseUrl": "ChatService: Missing OpenAI base URL in request", "missingRunIdForCheckStatus": "Run ID is missing. It is required for: check_run_status.", "missingThreadIdForCheckStatus": "Thread ID is missing. It is required for: check_run_status.", "missingThreadIdForDeleteMessage": "Thread ID is missing. It is required for: delete_message.", "missingThreadIdForDeleteThread": "Thread ID is missing. It is required for: delete_thread.", "missingThreadIdForFetchMessages": "Thread ID is missing. It is required for: fetch_messages.", "missingThreadIdForSendMessage": "Thread ID is missing. It is required for: send_message.", "missingThreadIdForGetThread": "Thread ID is missing. It is required for: %s."}}}