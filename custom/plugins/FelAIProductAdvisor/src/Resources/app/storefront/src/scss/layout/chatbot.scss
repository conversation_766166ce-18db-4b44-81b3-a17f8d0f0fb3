:root {
    --fel-break-sm: 640px;
    --fel-bs-border-radius: var(--bs-border-radius);
    --fel-chatbot-theme-color: var(--bs-primary);
}
$fel-break-sm: 640px;

// Füge eine Mixin-Funktion hinzu, um die Theme-Farbe mit Fallback zu verwenden
@mixin theme-color($opacity: 1) {
    color: var(--fel-chatbot-theme-color, var(--bs-primary));
    @if $opacity < 1 {
        opacity: $opacity;
    }
}

@mixin theme-background($opacity: 1) {
    background-color: var(--fel-chatbot-theme-color, var(--bs-primary));
    @if $opacity < 1 {
        opacity: $opacity;
    }
}

@mixin theme-border($width: 1px, $style: solid) {
    border: $width $style var(--fel-chatbot-theme-color, var(--bs-primary));
}

#fel-chatbot-advisor {
    padding: 12px;
    position: relative;


    z-index: 9;
    border-radius: var(--fel-bs-border-radius);

    // &:not(.active) {
    //     transition: right .2s ease-in-out;
    //     &.contains-thread {
    //         .fel-toggle-chatbot-button {
    //             background-color: var(--bs-btn-active-bg);
    //         }
    //     }
    //     .fel-chatbot-header {
    //         .fel-chatbot-options {
    //             margin-right: 0;
    //         }
    //     }
    // }


    bottom: 0;
    right: 0;
    width: 100%;
    height: 75%;
    min-height: 560px;
    transition-property: height, max-width, box-shadow;
    transition-duration: .15s;
    transition-timing-function: ease-in-out;

    &.fel-chatbot-is-loading {
        .fel-btn {
            &:not(.fel-ui-btn) {
                pointer-events: none;
                opacity: .5;
            }
        }
        #fel-chatbot-form {
            .fel-submit-btn {
                .btn {
                    &:disabled,
                    &:active {
                        border-color: transparent;
                        opacity: .5;
                    }
                    span {
                        color: var(--bs-warning);
                    }
                }
            }
        }
    }
    .fel-text-plain {
        display: block;
    }
    .fel-chatbot-config-wrapper {
        max-width: 370px;
        box-shadow: 0 0 2px var(--bs-gray-300);
        .form-check {
            padding: 0;
            display: flex;
            align-items: center;

            [type="checkbox"] {
                &.form-check-input {
                    margin: 0 .5rem 0 0;
                    min-width: 16px;
                    height: 16px;
                    display: block;
                    + label {
                        color: var(--bs-gray-900);
                    }
                    &:checked {
                        + label {
                            color: var(--bs-gray-600);
                        }
                    }
                }
            }
        }
    }
    hr {
        color: var(--bs-gray-600);
    }
    .fel-pointer-none {
        pointer-events: none;
    }
    .fel-flex-100 {
        flex: 100%;
    }
    .fel-chatbot-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border-radius: var(--fel-bs-border-radius);
        background: var(--bs-light);
        box-shadow: 0 0 8px var(--bs-gray);
    }
    [name="fel-chatbot-user-input"] {
        border-bottom-left-radius: var(--fel-bs-border-radius);
        background-color: var(--bs-white);
    }
    #fel-chatbot-form {
        display: flex;
        border-bottom-left-radius: var(--fel-bs-border-radius);
        border-bottom-right-radius: var(--fel-bs-border-radius);
    }
    .fel-chatbot-header {
        margin-bottom: .5rem;
        padding: 0;
        @include theme-background();
        border-top-left-radius: var(--fel-bs-border-radius);
        border-top-right-radius: var(--fel-bs-border-radius);
        min-height:41px;
        .fel-btn {
            &.fel-zoom-button {
                svg {
                    transform: rotate(90deg);
                    transition: transform .4s ease-in-out;
                }
                &.fel-active {
                    svg {
                        transform: rotate(0);
                    }
                }
            }
        }
        .btn-group {
            border-radius: var(--fel-bs-border-radius);
            overflow: hidden;
        }
        .fel-chatbot-avatar {
            width: 70px;
            height: 60px;
            position: relative;
            z-index: 1;
            img {
                width: 76px;
                height: 76px;
                position: absolute;
                top: -4px;
                left: 3px;
            }
        }
    }
    .fel-header-chatbot-info {
        display: flex;
    }
    &.fel-zoom-chat {
        max-width: 864px;
        height: 90%;
        padding-top: 0;
        padding-left: 0;
        .fel-chatbot-container {
            box-shadow: 0 0 10px var(--bs-dark);
        }
    }


    .online-status-dot {
        display: inline-flex;
        width: 8px;
        height: 8px;
        position: relative;
        top: 2px;
        background: var(--bs-success);
        border-radius: 100%;
    }

    /* message item, user | chatbot */
    .chat-message {
        max-width: 90%;
        overflow-wrap: break-word;
    }

    .chat-message-item {
        position: relative;
        width: 100%;
        margin-bottom: 10px;

        .fel-delete-message {
            display: none;
            position: absolute;
            bottom: 100%;
            right: 0;
            opacity: 0;
        }
    }

    .fel-inner {
        padding: 10px 18px;
        border-radius: 16px;
        margin-bottom: 0.7rem;
        &:hover {
            .fel-delete-message {
                display: block;
                opacity: .5;
            }
        }
    }
    .user-message {
        .fel-inner {
            color: var(--bs-light);
            background-color: color-mix(in srgb, var(--fel-chatbot-theme-color, var(--bs-primary)) 75%, black 25%);
            box-shadow: 0 0 4px var(--fel-chatbot-theme-color, var(--bs-primary));
        }
    }
    .chatbot-message {
        .fel-inner {
            color: var(--bs-dark);
            background-color: var(--bs-white);
            box-shadow: 0 0 4px var(--bs-gray-300);
        }
        &.fel-system-exception {
            .fel-inner {
                margin-top: .1rem;
                color: var(--bs-red);
                box-shadow: 0 0 8px rgba(var(--bs-warning-rgb), .5);
            }
        }
    }
    .chatbot-message-item {
        justify-content: right;
        .chat-message {
            min-width: 90%;
        }
    }
    .chatbot-message-item:last-of-type,
    .fel-chatbot-load-container:last-of-type {
        min-height: calc(100% - 54px);
    }

    /* loader */
    .fel-chatbot-loader {
        height: 48px;
        background-position: center center;
        background-repeat: no-repeat;
        transform: scale(1.1);

        svg {
            .fel-loader-circle {
                fill: var(--fel-chatbot-theme-color, var(--bs-primary));
            }
        }
    }

    form {
        input {
            &:focus {
                box-shadow: 0 0 0 transparent;
            }
        }
        &:invalid {
            button {
                opacity: .3;
                pointer-events: none;
            }
        }
    }

    button.active span.not-active,
    &.active .toggle-open,
    &:not(.active) form,
    &:not(.active) #fel-chatbot-messages,
    &:not(.active) .fel-delete-thread-button,
    &:not(.active) .fel-zoom-button,
    &:not(.active) .toggle-close,
    &:not(.active) .fel-header-chatbot-info,
    &:not(.active) .fel-chat-divider,
    &:not(.contains-thread) .fel-delete-thread-button,
    &:not(.active) .fel-delete-thread-container {
        display: none;
    }
}

#fel-chatbot-messages {
    margin-bottom: 1px;
    padding: 0 .7rem .5rem;
    height: 100%;
    line-height: 1.5;
    overflow-x: hidden;
    overflow-y: auto;
    color: var(--bs-dark);
    overscroll-behavior: contain;

    ul {
        margin: 0;
        padding: 0;
        list-style: none;
        &:not(.fel-chat-product-list) {
            ul {
                margin: 0.3rem .7rem;
            }
        }
    }
    h2, h3 {
        margin: 0 0 .4rem;
        padding: 0;
        line-height: 1;
        font-size: var(--bs-body-font-size);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:first-of-type {
            padding-top: .4rem;
        }
        &:not(:first-of-type) {
            padding-top: .8rem;
        }
        + p,
        + ul {
            margin-top: .6rem;
            padding-top: .5rem;
            border-top: 1px solid var(--bs-gray-300);
        }
    }
    p {
        margin: 0 0 .5rem;
    }
    li {
        margin: 0 0 .3rem;
        p {
            margin: 0;
            &:not(:first-of-type) {
                padding: .3rem 0 0;
            }
        }
    }
    iframe,
    img {
        margin: .5rem auto;
        padding: .3rem;
        width: 100%;
        max-width: 360px;
        max-height: 160px;
        display: block;
        object-fit: contain;
        box-shadow: 0 0 5px var(--bs-gray-500);
    }
    a {
        &[href^="tel:"] {
            white-space: nowrap;
        }
    }
    .fel-text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .fel-dir-rtl {
        direction: rtl;
    }
    .fel-oai-response {
        > :last-child {
            margin-bottom: 0;
        }
    }
    .fel-chatbot-avatar {
        img {
            margin: 0 auto;
            width: auto;
            box-shadow: 0 0 0;
        }
    }
    .fel-chat-product-list {
        margin: 0;
        padding: .1rem 0 0;
        li {
            padding: 0 0 .5rem;
            &.active {
                img {
                    box-shadow: 0 0 8px rgba(var(--bs-info-rgb), .8);
                }
                .fel-item-wrapper {
                    .btn {
                        border-color: var(--bs-btn-hover-border-color);
                    }
                }
            }
            .fel-item-wrapper {
                .fel-item-thumbnail {
                    .alert-warning {
                        height: 150px;
                    }
                }
                .btn {
                    border-color: rgba(var(--bs-dark-rgb), .1);
                }
                .fel-subtitle {
                    padding-top: .25rem;
                    display: flex;
                    line-height: 1;
                    .fel-price {
                        + .fel-manufacturer {
                            margin-left: .4rem;
                            padding-left: .4rem;
                            max-width: 140px;
                            border-left: 1px solid rgba(var(--bs-dark-rgb), .4);
                        }
                    }
                }
                .fel-description {
                    padding: .2rem 0;
                    max-height: 44px;
                    overflow: hidden;
                }
                .fel-product-properties {
                    padding: .2rem 0 .6rem;
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    div {
                        flex: 0;
                    }
                    span {
                        margin: .3rem .3rem 0 0;
                        padding: 3px 5px;
                        display: inline-block;
                        font-size: 82%;
                        background: rgba(var(--bs-dark-rgb), .1);
                    }
                }
            }
            h2 {
                margin-bottom: .2rem;
                padding: 0;
                border: 0 none;
            }
        }
    }
    .fel-chat-link-category-list-wrapper {
        margin: 0 -.2rem;
    }
    .fel-chat-link-category-ul-list {
        margin: 0;
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        li {
            margin: 0;
            padding: 0;
            width: 100%;
            flex: 15% 1;

            a,
            span {
                margin: .2rem .2rem .3rem;
                padding: 0 1rem;
                display: block;
                font-weight: normal;
            }
            a {
                > span {
                    margin: 0 auto;
                    padding: 0;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    em {
                        opacity: .7;
                        font-size: 95%;
                    }
                }
            }
        }
    }
}

.fel-modal-backdrop {
    z-index: 999;
}

@media only screen and (min-width: 400px) {
    #fel-chatbot-advisor {

        .fel-chat-product-list {
            margin: 0 -8px;
            display: flex;
            flex-wrap: wrap;
            li {
                margin: 0;
                flex: 100%;
                .fel-item-wrapper {
                    padding: 0 8px;
                    min-height: 100%;
                }
            }


            .fel-chat-link-category-ul-list {
                li {
                    a {
                        > span {
                            max-width: 230px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
            .chatbot-message-item {
                .chat-message {
                    min-width: 95%;
                }
            }
            .fel-chat-product-list {
                li {
                    padding: 0 0 .9rem;
                    min-width: 33%;
                    flex: 33% 1;
                    scroll-margin-top: 0;

                    &:nth-child(6n) {
                        min-width: 62%;
                    }
                    .fel-item-wrapper {
                        margin: 8px 8px 0;
                        padding: 10px 15px 40px;
                        position: relative;
                        box-shadow: 0 0 8px var(--bs-gray-500);
                        transition: box-shadow .2s ease-in-out;
                        &:hover {
                            box-shadow: 0 0 8px var(--bs-gray-700);
                        }
                        img {
                            margin: 0 auto;
                            padding: 0;
                            box-shadow: unset;
                        }
                        .fel-item-footer {
                            width: calc(100% - 30px);
                            position: absolute;
                            bottom: 12px;
                        }
                    }
                    &.active {
                        .fel-item-wrapper {
                            box-shadow: 0 0 8px rgba(var(--bs-info-rgb), .8);
                        }
                    }
                    &.fel-highlight {
                        flex: 100%;
                    }
                }
            }

        }
    }
}

@media only screen and (max-width: $fel-break-sm) {
    #fel-chatbot-advisor {

        max-width: 100%;
        height: 85%;
        bottom: 0;
        right: 0;

        .fel-zoom-button {
            display: none;
        }
        &.fel-zoom-chat {
            padding-top: 0;
            padding-left: 0;
            .fel-chat-product-list {
                li {
                    flex: 50%;
                }
            }
        }
        .chatbot-message-item:last-of-type,
        .fel-chatbot-load-container:last-of-type {
            min-height: 90%;
        }

    }
    .fel-chatbot-options {
        margin-right: 0.5rem;
    }
}

@media only screen and (min-width: $fel-break-sm) {
    .fel-modal-backdrop {
        display: none;
    }
}

@media only screen and (max-width: 420px) {
    #fel-chatbot-advisor {
        .chatbot-message-item {
            .chat-message {
                min-width: 100%;
            }
        }
    }
}

.fel-fade-in {
    animation: felFadeIn .15s ease-in-out;
    animation-fill-mode: both;
}
@keyframes felFadeIn {0% {opacity: 0} 100% {opacity: 1}}


#fel-chatbot.fel-cms-chatbot {
    position: relative;
    right: inherit;
    left: inherit;
    bottom: inherit;
    z-index: inherit;
    max-width: 50%;
}

.fel-text-plain {
    display: block;
    white-space: pre-line;
}

.fel-chatbot-buttons-container {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.fel-chatbot-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    button, a {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
        background-color: var(--bs-white);
        border: 1px solid;
        padding: 5px;
        text-decoration: none;
        color: var(--fel-chatbot-theme-color, var(--bs-primary));
        border-color: var(--fel-chatbot-theme-color, var(--bs-primary));

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: var(--fel-chatbot-theme-color, var(--bs-primary));
            color: var(--bs-white);
            border-color: var(--fel-chatbot-theme-color, var(--bs-primary));
        }
    }
}

.cms-element-chatbot h2 {
    text-align: center;
    margin: 20px 0px;
}

.fel-header-avatar {
    width: 75px;
    height: 75px;
    overflow: hidden;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    position: absolute;
    top: -16px;
    left: -8px;

    .fel-header-avatar-img {
        width: 85px;
        height: 85px;
        object-fit: contain;
    }
}

.fel-header-advisor-name {
    font-size: 14px;
    font-weight: 500;
    margin: 10px 10px 10px 63px;
}

.fel-chat-initial {
    .fel-header-avatar, .fel-header-advisor-name {
        display: none;
    }
}

.fel-chatbot-is-loading {
    .fel-header-avatar {
        display: block;
    }
}