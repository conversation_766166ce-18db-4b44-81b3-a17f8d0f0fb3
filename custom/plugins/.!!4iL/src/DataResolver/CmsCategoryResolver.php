<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\DataResolver;

use Shopware\Core\Content\Category\CategoryCollection;
use Shopware\Core\Content\Category\CategoryDefinition;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\Content\Cms\DataResolver\Element\AbstractCmsElementResolver;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;

class CmsCategoryResolver extends AbstractCmsElementResolver
{
    public function getType(): string
    {
        return 'chatbot-category'; // The type of your CMS element
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        $config = $slot->getFieldConfig();
        $categoriesConfig = $config->get('cmsProductAdvisorCategory'); // Your chatbot config key

        if (!$categoriesConfig || !$categoriesConfig->getValue()) {
            return null;
        }

        $categoryIds = $categoriesConfig->getValue();
        $criteria = new Criteria($categoryIds);
        $criteria->addAssociation('media');

        $criteriaCollection = new CriteriaCollection();
        $criteriaCollection->add('categories_' . $slot->getUniqueIdentifier(), CategoryDefinition::class, $criteria);

        return $criteriaCollection;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $categories = new CategoryCollection();
        $slot->setData($categories);

        $searchResult = $result->get('categories_' . $slot->getUniqueIdentifier());
        if ($searchResult) {
            foreach ($searchResult as $category) {
                $categories->add($category);
            }
        }
    }
}
