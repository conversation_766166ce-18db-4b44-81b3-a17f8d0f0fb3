
	{% block sw_cms_element_chatbot %}
		<div class="sw-cms-el-chatbot">
			<div class="chatbot-window">
				<div class="messages">
					<div v-for="message in messages" :class="'message ' + message.sender">
						{{ message.text }}
					</div>
				</div>
				<div class="input-area">
					<input type="text" v-model="userMessage" @keyup.enter="sendMessage" placeholder="Type a message..."/>
					<button @click="sendMessage">Send</button>
				</div>
			</div>
		</div>
	{% endblock %}

