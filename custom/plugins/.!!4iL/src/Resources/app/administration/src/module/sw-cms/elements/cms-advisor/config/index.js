import template from './sw-cms-el-config-cms-advisor.html.twig';

const { Mixin } = Shopware;

/**
 * @private
 * @package buyers-experience
 */
Shopware.Component.register('sw-cms-el-config-cms-advisor',  {
    template,

    mixins: [
        Mixin.getByName('cms-element'),
    ],

    created() {
        this.createdComponent();
    },

    methods: {
        createdComponent() {
            this.initElementConfig('text');
        },

        onBlur(content) {
            this.emitChanges(content);
        },

        onInput(content) {
            this.emitChanges(content);
        },

        emitChanges(content) {
            if (content !== this.element.config.content.value) {
                this.element.config.content.value = content;
                this.$emit('element-update', this.element);
            }
        },
    },
});
