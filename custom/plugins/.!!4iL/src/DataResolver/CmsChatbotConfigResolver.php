<?php

declare(strict_types=1);

namespace Fel\AIProductAdvisor\DataResolver;

use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\Element\AbstractCmsElementResolver;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\Content\Cms\DataResolver\FieldConfig;
use Shopware\Core\Content\Media\MediaDefinition;
use Shopware\Core\Content\Media\MediaEntity;
use Shopware\Core\Content\Cms\SalesChannel\Struct\ImageStruct;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;

class CmsChatbotConfigResolver extends AbstractCmsElementResolver
{
    public function getType(): string
    {
        return 'chatbot'; // The type of your CMS element
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        $config = $slot->getFieldConfig();

        $cmsProductAdvisorAvatarConfig = $config->get('cmsProductAdvisorAvatar');

        if (!$cmsProductAdvisorAvatarConfig || $cmsProductAdvisorAvatarConfig->getValue() === null) {
            return null;
        }

        $criteria = new Criteria([$cmsProductAdvisorAvatarConfig->getValue()]);

        $criteriaCollection = new CriteriaCollection();
        $criteriaCollection->add('cms_avatar_media_' . $slot->getUniqueIdentifier(), MediaDefinition::class, $criteria);

        return $criteriaCollection;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $config = $slot->getFieldConfig();
        $mediaConfig = $config->get('cmsProductAdvisorAvatar');

        if ($mediaConfig && $mediaConfig->getValue()) {
            $searchResult = $result->get('cms_avatar_media_' . $slot->getUniqueIdentifier());

            if ($searchResult && $searchResult->first()) {
                /** @var MediaEntity $media */
                $media = $searchResult->first();
                $imageStruct = new ImageStruct();
                $imageStruct->setMediaId($media->getId());
                $imageStruct->setMedia($media);

                // Store the media data in the slot
                $slot->setData($imageStruct);
            }
        }
    }
}
