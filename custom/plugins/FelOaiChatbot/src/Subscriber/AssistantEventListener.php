<?php declare(strict_types=1);

namespace FelOaiChatbot\Subscriber;

use FelOAIAssistantsManager\Event\ActionRequiredEvent;
use FelOAIAssistantsManager\Event\ChatStatusEvent;
use FelOAIAssistantsManager\Event\CancelRunEvent;
use FelOAIAssistantsManager\Event\CompletedEvent;
use FelOAIAssistantsManager\Event\CreateThreadEvent;
use FelOAIAssistantsManager\Event\DeleteThreadEvent;
use FelOAIAssistantsManager\Event\DeleteMessageEvent;
use FelOAIAssistantsManager\Event\FetchMessagesEvent;
use FelOAIAssistantsManager\Event\SendMessageEvent;
use FelOAIAssistantsManager\Event\RunFailedEvent;
use Shopware\Storefront\Page\GenericPageLoader;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Psr\Log\LoggerInterface;
use function file_put_contents;
use function json_encode;
use function microtime;
use function realpath;
use function substr;

class AssistantEventListener implements EventSubscriberInterface
{
    public function __construct(
        protected readonly SystemConfigService $configService
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            FetchMessagesEvent::NAME  => 'onFetchMessages',
            CancelRunEvent::NAME      => 'onCancelRun',
            ChatStatusEvent::NAME     => 'onChatStatus',
            CreateThreadEvent::NAME   => 'onCreateThread',
            SendMessageEvent::NAME    => 'onSendMessage',
            CompletedEvent::NAME      => 'onCompleted',
            DeleteThreadEvent::NAME   => 'onDeleteThread',
            DeleteMessageEvent::NAME  => 'onDeleteMessage',
            ActionRequiredEvent::NAME => 'onActionRequired',
            RunFailedEvent::NAME      => 'onRunFailedEvent',
        ];
    }

    /**
     * @return array<string, mixed>
     */
    function eventCollector(string $eventFn, mixed $response): array
    {
        return [
            'eventName' => $eventFn,
            'responses' => $response,
        ];
    }

    public function onChatStatus(ChatStatusEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'    => 'ChatStatus',
            'threadId' => $event->getThreadId(),
            'status'   => $event->getStatus(),
            'runId'    => $event->getRunId(),
            'attempts' => $event->getAttempts(),
        ]);
    }

    public function onFetchMessages(FetchMessagesEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'FetchMessages',
            'threadId'       => $event->getThreadId(),
            'threadMessages' => $event->getThreadMessages(),
        ]);
    }

    public function onCancelRun(CancelRunEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'CancelRun',
            'statusResponse' => $event->getStatusResponse(),
            'threadId'       => $event->getThreadId(),
            'runId'          => $event->getRunId(),
        ]);
    }

    public function onCreateThread(CreateThreadEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'CreateThread',
            'statusResponse' => $event->getStatusResponse(),
            'assistantId'    => $event->getAssistantId(),
            'message'        => $event->getMessage(),
        ]);
    }

    public function onSendMessage(SendMessageEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'SendMessage',
            'statusResponse' => $event->getStatusResponse(),
            'threadId'       => $event->getThreadId(),
            'assistantId'    => $event->getAssistantId(),
            'message'        => $event->getMessage(),
        ]);
    }

    public function onCompleted(CompletedEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'Completed',
            'threadId'       => $event->getThreadId(),
            'runId'          => $event->getRunId(),
            'statusResponse' => $event->getStatusResponse(),
        ]);
    }

    public function onDeleteThread(DeleteThreadEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'    => 'DeleteThread',
            'threadId' => $event->getThreadId(),
            'deleted'  => $event->getDeleted(),
        ]);
    }

    public function onDeleteMessage(DeleteMessageEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'     => 'DeleteMessage',
            'threadId'  => $event->getThreadId(),
            'messageId' => $event->getMessageId(),
            'deleted'   => $event->getDeleted(),
        ]);
    }

    public function onActionRequired(ActionRequiredEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'            => 'ActionRequired',
            'toolCalls'        => $event->getToolCalls(),
            'processedActions' => $event->getProcessedActions(),
            'executedActions'  => $event->getExecutedActions(),
            'getUsedConfig'    => $event->getUsedConfig(),
            'storeApiError'    => $event->getStoreApiError(),
            'threadId'         => $event->getThreadId(),
            'runId'            => $event->getRunId(),
        ]);
    }

    public function onRunFailedEvent(RunFailedEvent $event): void
    {
        $this->eventCollector(__FUNCTION__, [
            'event'          => 'RunFailed',
            'statusResponse' => $event->getStatusResponse(),
            'threadId'       => $event->getThreadId(),
            'runId'          => $event->getRunId(),
        ]);
    }
}
