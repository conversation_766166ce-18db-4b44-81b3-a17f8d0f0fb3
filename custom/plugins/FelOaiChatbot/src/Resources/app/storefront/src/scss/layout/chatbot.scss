:root {
    --fel-break-sm: 640px;
    --fel-transition-1: .15s;
    --fel-transition-2: .2s;
    --fel-transition-3: .3s;
    --fel-chat-last-min-height: calc(100% - 54px);
    --fel-bs-border-radius: var(--bs-border-radius);
    --fel-hl-active-products-box-shadow: 0 0 8px rgba(var(--bs-info-rgb), 0.8);
}
$fel-break-sm: 640px;

#fel-chatbot {
    padding: 0;
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 1000;
    border-radius: var(--fel-bs-border-radius);

    &:not(.active) {
        transition: right var(--fel-transition-2) ease-in-out;
        &.contains-thread {
            .fel-toggle-chatbot-button {
                background-color: var(--bs-btn-active-bg);
            }
        }
        .fel-chatbot-header {
            .fel-chatbot-options {
                margin-right: 0;
            }
        }
    }

    &.active {
        bottom: 20px;
        right: 20px;
        width: 100%;
        max-width: 440px;
        height: 75%;
        max-height: 1200px;
        min-height: 480px;
        transition-property: height, max-width, box-shadow;
        transition-duration: var(--fel-transition-1);
        transition-timing-function: ease-in-out;

        &:not(.fel-chatbot-is-loading) {
            .fel-chatbot-load-container {
                display: none;
            }
        }
        &.fel-chatbot-is-loading {
            .fel-btn {
                &:not(.fel-ui-btn) {
                    pointer-events: none;
                    opacity: .5;
                }
            }
            #fel-chatbot-form {
                input {
                    &:disabled {
                        background: var(--bs-white);
                    }
                }
                .fel-submit-btn {
                    .btn {
                        &:disabled,
                        &:active {
                            opacity: .5;
                            border-color: transparent;
                            background-color: var(--bs-white);
                        }
                        span {
                            color: var(--bs-warning);
                        }
                    }
                }
            }
        }
        .fel-text-plain {
            white-space: pre-line;
        }
        .fel-chatbot-config-wrapper {
            max-width: 370px;
            box-shadow: 0 0 2px var(--bs-gray-300);
            .form-check {
                padding: 0;
                display: flex;
                align-items: center;

                [type="checkbox"] {
                    &.form-check-input {
                        margin: 0 .5rem 0 0;
                        min-width: 16px;
                        height: 16px;
                        display: block;
                        &:not(:checked) {
                            + label {
                                color: var(--bs-gray-800);
                            }
                        }
                        &:checked {
                            + label {
                                color: var(--bs-gray-600);
                            }
                        }
                    }
                }
            }
        }
        hr {
            color: var(--bs-gray-600);
        }
        .fel-pointer-none {
            pointer-events: none;
        }
        .fel-flex-100 {
            flex: 100%;
        }
        .fel-flex-1 {
            flex: 1;
        }
        .fel-flex-auto {
            flex: auto;
        }
        .fel-is-hidden {
            display: none;
        }
        .fel-chatbot-container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            border-radius: var(--fel-bs-border-radius);
            background: var(--bs-light);
            box-shadow: 0 0 8px var(--bs-gray);
        }
        [name="fel-chatbot-user-input"] {
            border-bottom-left-radius: var(--fel-bs-border-radius);
        }
        #fel-chatbot-form {
            display: flex;
            border-bottom-left-radius: var(--fel-bs-border-radius);
            border-bottom-right-radius: var(--fel-bs-border-radius);
        }
        .fel-chatbot-header {
            margin-bottom: .5rem;
            padding: .5rem .7rem .5rem .5rem;
            background-color: var(--bs-primary);
            border-top-left-radius: var(--fel-bs-border-radius);
            border-top-right-radius: var(--fel-bs-border-radius);
            .fel-btn {
                &.fel-zoom-button {
                    svg {
                        transform: rotate(90deg);
                        transition: transform var(--fel-transition-3) ease-in-out;
                    }
                }
            }
            .btn-group {
                border-radius: var(--fel-bs-border-radius);
                overflow: hidden;
            }
            .fel-chatbot-avatar {
                width: 70px;
                height: 60px;
                position: relative;
                z-index: 1;
                img {
                    width: 76px;
                    height: 76px;
                    position: absolute;
                    top: -4px;
                    left: 3px;
                }
            }
        }
        .fel-header-chatbot-info {
            display: flex;
        }
        &.fel-zoom-chat {
            max-width: 864px;
            height: 90%;
            padding-top: 20px;
            padding-left: 40px;
            .fel-chatbot-container {
                box-shadow: 0 0 10px var(--bs-dark);
            }
            .fel-btn {
                &.fel-zoom-button {
                    margin-left: 4px;
                    margin-right: 4px;
                    color: var(--bs-btn-active-color);
                    background-color: var(--bs-btn-active-bg);
                    border-color: var(--bs-btn-active-border-color);

                    svg {
                        transform: rotate(0);
                    }
                }
            }
        }
    }

    .online-status-dot {
        display: inline-flex;
        width: 8px;
        height: 8px;
        position: relative;
        top: 2px;
        background: var(--bs-success);
        border-radius: 100%;
    }

    /* message item, user | chatbot */

    .chat-message-item {
        display: flex;
        position: relative;

        .chat-message {
            max-width: 90%;
            overflow-wrap: break-word;
        }
        .fel-delete-message {
            display: none;
            position: absolute;
            bottom: 100%;
            right: 0;
            opacity: 0;

            button {
                border: 0;
            }
        }
        .fel-inner {
            padding: 10px 18px;
            border-radius: 16px;
            margin-bottom: 0.7rem;
        }
        .chatbot-message {
            &:hover {
                .fel-delete-message {
                    display: block;
                    opacity: .5;
                }
            }
        }
        .user-message {
            .fel-inner {
                color: var(--bs-light);
                background-color: var(--bs-primary);
                box-shadow: 0 0 4px var(--bs-primary);
            }
        }

        // &:last-of-type {
        //     &.user-message-item {
        //         min-height: 100%;
        //     }
        // }
    }

    .chatbot-message-item {
        justify-content: right;
        .chat-message {
            min-width: 90%;
        }
        .chatbot-message {
            .fel-inner {
                color: var(--bs-dark);
                background-color: var(--bs-white);
                box-shadow: 0 0 4px var(--bs-gray-300);
            }
            &.fel-system-exception {
                .fel-inner {
                    margin-top: .1rem;
                    color: var(--bs-red);
                    box-shadow: 0 0 8px rgba(var(--bs-warning-rgb), .5);
                }
            }
        }
    }

    .fel-conversation-pair:last-of-type {
        min-height: 100%;
    }

    /* loader */
    .fel-chatbot-loader {
        height: 48px;
        background-position: center center;
        background-repeat: no-repeat;
        transform: scale(1.1);
    }

    form {
        input {
            &:focus {
                box-shadow: 0 0 0 transparent;
            }
        }
        &:invalid {
            button {
                opacity: .3;
                pointer-events: none;
            }
        }
    }

    button.active span.not-active,
    &.active .toggle-open,
    &:not(.active) form,
    &:not(.active) #fel-chatbot-messages,
    &:not(.active) .fel-delete-thread-button,
    &:not(.active) .fel-zoom-button,
    &:not(.active) .toggle-close,
    &:not(.active) .fel-header-chatbot-info,
    &:not(.active) .fel-chat-divider,
    &:not(.contains-thread) .fel-delete-thread-button,
    &:not(.active) .fel-delete-thread-container {
        display: none;
    }
}

#fel-chatbot-messages {
    margin-bottom: 1px;
    padding: 0 .7rem .5rem;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    color: var(--bs-dark);
    overscroll-behavior: contain;

    ul {
        margin: 0;
        padding: 0;
        list-style: none;
        &:not(.fel-chat-product-list) {
            ul {
                margin: 0.3rem .7rem;
            }
        }
    }
    h2, h3 {
        margin: 0 0 .4rem;
        padding: 0;
        line-height: 1;
        font-size: var(--bs-body-font-size);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:first-of-type {
            padding-top: .4rem;
        }
        &:not(:first-of-type) {
            padding-top: .8rem;
        }
        + p,
        + ul {
            margin-top: .6rem;
            padding-top: .5rem;
            border-top: 1px solid var(--bs-gray-300);
        }
    }
    li,
    p {
        margin: 0 0 .3rem;
        line-height: 1.5;
    }
    li {
        p {
            margin: 0;
            &:not(:first-of-type) {
                padding: .3rem 0 0;
            }
        }
    }
    iframe,
    img {
        margin: .5rem auto;
        padding: .3rem;
        width: 100%;
        max-width: 360px;
        max-height: 160px;
        display: block;
        object-fit: contain;
        box-shadow: 0 0 5px var(--bs-gray-500);
    }
    a {
        &[href^="tel:"] {
            white-space: nowrap;
        }
    }
    .fel-text-ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .fel-dir-rtl {
        direction: rtl;
    }
    .fel-oai-response {
        > :last-child {
            margin-bottom: 0;
        }
    }
    .fel-chatbot-avatar {
        img {
            margin: 0 auto;
            width: auto;
            box-shadow: 0 0 0;
        }
    }
    .fel-search-failed-props {
        .fel-inner {
            box-shadow: 0 0 4px rgba(var(--bs-danger-rgb), .4);
        }
    }
    .fel-chat-product-list {
        margin: 0;
        padding: .1rem 0 0;
        li {
            padding: 0 0 .5rem;
            &.active {
                img {
                    box-shadow: 0 0 8px rgba(var(--bs-info-rgb), .8);
                }
                .fel-item-wrapper {
                    .btn {
                        border-color: var(--bs-btn-hover-border-color);
                    }
                }
            }
            .fel-item-wrapper {
                .fel-item-thumbnail {
                    .alert-warning {
                        height: 140px;
                        border: 0 solid var(--bs-white);
                        border-width: 10px 0 0;
                    }
                }
                .btn {
                    border-color: rgba(var(--bs-dark-rgb), .1);
                }
                .fel-subtitle {
                    padding-top: .25rem;
                    display: flex;
                    line-height: 1;
                    .fel-price {
                        + .fel-manufacturer {
                            margin-left: .5rem;
                            padding-left: .4rem;
                            max-width: 140px;
                            border-left: 1px solid rgba(var(--bs-dark-rgb), .4);
                        }
                    }
                }
                .fel-description {
                    padding: .2rem 0;
                    max-height: 44px;
                    overflow: hidden;
                }
                .fel-product-properties {
                    margin: 0 -.15rem;
                    padding: .3rem 0 .1rem;
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    max-height: 36px;
                    overflow: hidden;
                    opacity: .6;
                    &.fel-show-properties {
                        max-height: initial;
                        opacity: 1;
                    }
                    > div {
                        flex: 1 1 0;
                        padding: 4px 8px;
                        margin: .15rem;
                        text-align: center;
                        color: var(--bs-gray-800);
                        background: var(--bs-gray-200);
                        white-space: nowrap;
                        cursor: default;
                    }
                    b {
                        display: block;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 80%;
                        pointer-events: none;
                    }
                    .fel-prop-divider {
                        max-width: 30px;
                        color: var(--bs-gray-600);
                        font-size: 10px;
                    }
                }
            }
            h2 {
                margin-bottom: .2rem;
                padding: 0;
                border: 0 none;
            }
        }
    }
    .fel-chat-link-category-list-wrapper {
        margin: 0 -.2rem;
    }
    .fel-chat-link-category-ul-list {
        margin: 0;
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        li {
            margin: 0;
            padding: 0;
            width: 100%;
            flex: 15% 1;

            a,
            span {
                margin: .2rem .2rem .3rem;
                padding: 0 1rem;
                display: block;
                font-weight: normal;
            }
            a {
                > span {
                    margin: 0 auto;
                    padding: 0;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    em {
                        opacity: .7;
                        font-size: 95%;
                    }
                }
            }
        }
    }
    .fel-chat-search-args {
        .fel-props-in-diff-groups-fixer {
            .fix-group-btn-group {
                padding: .3rem .4rem;
                box-shadow: 0 0 4px rgba(var(--bs-dark-rgb), .4);
                .btn {
                    max-width: calc(100% - 8px);
                }
            }
        }
        .fel-chat-search-grid {
            .fel-grid-parent {
                margin: 0;
                padding: 8px 0;
                .fel-tags {
                    margin: 0 -4px;
                }
                .grid-key {
                    margin: 0 0 .5rem;
                    opacity: .7;
                }
                .fel-args-content {
                    display: flex;
                    flex-wrap: wrap;
                    .fel-tag-line {
                        b {
                            white-space: nowrap;
                        }
                    }
                    .fel-tag {
                        margin: 2px 2px;
                        padding: 8px 12px;
                        max-width: 100%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1;
                        text-align: center;
                        font-weight: 500;
                        background-color: var(--bs-gray-200);
                    }
                }
            }
        }
    }
}

.fel-modal-backdrop {
    z-index: 999;
}

@media only screen and (min-width: 400px) {
    #fel-chatbot {
        &.active {
            .fel-chat-product-list {
                margin: 0 -8px;
                display: flex;
                flex-wrap: wrap;
                li {
                    margin: 0;
                    flex: 100%;
                    max-width: 100%;
                    .fel-item-wrapper {
                        padding: 0 8px;
                        min-height: 100%;
                    }
                }
            }
            &:not(.fel-zoom-chat) {
                max-height: 840px;
            }

            &.fel-zoom-chat {
                .fel-chat-link-category-ul-list {
                    li {
                        a {
                            > span {
                                max-width: 230px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }
                .chatbot-message-item {
                    .chat-message {
                        min-width: 95%;
                    }
                }
                .fel-chat-product-list {
                    li {
                        padding: 0 0 .9rem;
                        min-width: 33%;
                        flex: 33% 1;
                        scroll-margin-top: 0;

                        &:nth-child(6n) {
                            min-width: 62%;
                        }
                        .fel-item-wrapper {
                            margin: 8px 8px 0;
                            padding: 10px 15px 40px;
                            position: relative;
                            box-shadow: 0 0 8px var(--bs-gray-500);
                            transition: box-shadow var(--fel-transition-3) ease-in-out;
                            &:hover {
                                box-shadow: 0 0 8px var(--bs-gray-700);
                            }
                            img {
                                margin: 0 auto;
                                padding: 0;
                                box-shadow: unset;
                            }
                            .fel-item-footer {
                                width: calc(100% - 30px);
                                position: absolute;
                                bottom: 12px;
                            }
                        }
                        &.active {
                            .fel-item-wrapper {
                                box-shadow: 0 0 8px rgba(var(--bs-info-rgb), .8);
                            }
                        }
                        &.fel-highlight {
                            flex: 100%;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: $fel-break-sm) {
    #fel-chatbot {
        &.active {
            max-width: 100%;
            height: 85%;
            bottom: 0;
            right: 0;

            .fel-zoom-button {
                display: none;
            }
            &.fel-zoom-chat {
                padding-top: 0;
                padding-left: 0;
                .fel-chat-product-list {
                    li {
                        flex: 50%;
                    }
                }
            }
        }
    }
    .fel-chatbot-options {
        margin-right: 0.5rem;
    }
}

@media only screen and (min-width: $fel-break-sm) {
    .fel-modal-backdrop {
        display: none;
    }
}

@media only screen and (max-width: 420px) {
    #fel-chatbot {
        .chatbot-message-item {
            .chat-message {
                min-width: 100%;
            }
        }
    }
}

.fel-fade-in {
    animation: felFadeIn var(--fel-transition-1) ease-in-out;
    animation-fill-mode: both;
}
@keyframes felFadeIn {0% {opacity: 0} 100% {opacity: 1}}
