import template from './fel-assistant-manager-search-log.html.twig';
import './fel-assistant-manager-search-log.scss';

const { Component, Mixin, Context, Data: { Criteria } } = Shopware;

Component.register('fel-assistant-manager-search-log', {
    template,

    inject: ['repositoryFactory'],

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('fel-assistant-manager-openai-mixin'),
        Mixin.getByName('fel-assistant-manager-plugin-config-mixin'),
        Mixin.getByName('fel-assistant-manager-logs-repository-mixin'),
    ],

    data() {
        return {
            isLoading: true,
            rawTotal: 0,
            page: this.$route.query.page || 1,
            limit: this.$route.query.limit || 5,
            searchLogsTerms: [],
            productSearchTerms: [],
            assistantDetails: {},
            filterTerm: '',
            activeTab: 'search_logs',
            filterTimeout: null,
            // Maps to track unique queries and their aggregated data
            searchQueryMap: {},
            productQueryMap: {}
        };
    },

    computed: {
        filteredSearchLogsTerms() {
            return this.searchLogsTerms;
        },
        filteredProductSearchTerms() {
            return this.productSearchTerms;
        },
        // Calculate the total based on the actual number of items after aggregation
        total() {
            if (this.activeTab === 'search_logs') {
                return this.searchLogsTerms.length;
            } else {
                return this.productSearchTerms.length;
            }
        },
        logPagesTotal() {
            return Math.ceil(this.total / this.limit) || 1;
        }
    },

    created() {
        this.initializeComponent();
    },

    beforeDestroy() {
        // Clear any pending timeouts when component is destroyed
        if (this.filterTimeout) {
            clearTimeout(this.filterTimeout);
        }
    },

    watch: {
        filterTerm: {
            handler() {
                // Reset to first page when filtering
                this.page = 1;
                // Clear any existing timeout
                if (this.filterTimeout) {
                    clearTimeout(this.filterTimeout);
                }
                // Set a new timeout to debounce the filter
                this.filterTimeout = setTimeout(() => {
                    this.initializeComponent();
                }, 300); // 300ms debounce
            },
            immediate: false
        }
    },

    methods: {

        fetchProductQueriesExec() {
            // Create criteria to filter for search-related entries
            const criteria = new Criteria();
            criteria.setTotalCountMode(1);
            criteria.setPage(this.page);
            criteria.setLimit(this.limit);

            // Filter for entries with null tagId (these are our search queries)
            criteria.addFilter(Criteria.equals('tagId', null));

            criteria.addFilter(Criteria.contains('assistantId', 'product_search_terms___'));

            // Add filter term if provided
            if (this.filterTerm && this.filterTerm.trim() !== '') {
                const filter = this.filterTerm.toLowerCase().trim();
                // Create a filter for assistantId only - we're looking for the search term in the assistantId
                // The assistantId format is: product_search_terms___[assistantId]::[query]
                // So we want to search in both the assistantId and the query part
                criteria.addFilter(Criteria.contains('assistantId', filter));
            }

            // Sort by createdAt to get newest first
            criteria.addSorting(Criteria.sort('createdAt', 'DESC'));

            // Fetch the data
            return this.tagUsageRepository.search(criteria, Context.api);
        },

        fetchSearchLogsExec() {
            // Create criteria to filter for search-related entries
            const criteria = new Criteria();
            criteria.setTotalCountMode(1);
            criteria.setPage(this.page);
            criteria.setLimit(this.limit);

            // Filter for entries with null tagId (these are our search queries)
            criteria.addFilter(Criteria.equals('tagId', null));

            // Add filter for assistantId starting with our search prefixes
            criteria.addFilter(Criteria.contains('assistantId', 'search_logs_terms___'));

            // Add filter term if provided
            if (this.filterTerm && this.filterTerm.trim() !== '') {
                const filter = this.filterTerm.toLowerCase().trim();
                // Create a filter for assistantId only - we're looking for the search term in the assistantId
                // The assistantId format is: search_logs_terms___[assistantId]::[query]
                // So we want to search in both the assistantId and the query part
                criteria.addFilter(Criteria.contains('assistantId', filter));
            }

            // Sort by createdAt to get newest first
            criteria.addSorting(Criteria.sort('createdAt', 'DESC'));

            // Fetch the data
            return this.tagUsageRepository.search(criteria, Context.api);
        },

        async fetchSearchLogs() {
            this.setManagerIsLoading('fetchSearchLogs', true);

            const response = await this.fetchSearchLogsExec();
            // Store the raw total from the database query
            this.rawTotal = response.total;

            this.setManagerIsLoading('fetchSearchLogs', false);

            return response;
        },

        async fetchProductQueries() {
            this.setManagerIsLoading('fetchProductQueries', true);

            const response = await this.fetchProductQueriesExec();
            // Store the raw total from the database query
            this.rawTotal = response.total;

            this.setManagerIsLoading('fetchProductQueries', false);

            return response;
        },

        async initializeComponent() {
            this.ensureTagUsageFactoryLoaded();

            try {
                let response = null;
                if (this.activeTab === 'search_logs') {
                    response = await this.fetchSearchLogs();
                } else {
                    response = await this.fetchProductQueries();
                }

                // Process the results
                this.searchLogsTerms = [];
                this.productSearchTerms = [];
                this.assistantDetails = {};

                // Make sure the query maps are properly initialized as empty objects
                this.searchQueryMap = {};
                this.productQueryMap = {};

                console.log('Maps initialized:',
                    'searchQueryMap type:', typeof this.searchQueryMap,
                    'productQueryMap type:', typeof this.productQueryMap);

                response.forEach(item => {
                    // Split the assistantId to get the function name and the rest
                    const [functionName, assistantIdWithQuery] = item.assistantId.split('___');

                    if (!assistantIdWithQuery) return; // Skip if format is invalid

                    // Split to get assistant ID and search query
                    const [assistantId, rawQuery] = assistantIdWithQuery.split('::');

                    if (!assistantId || !rawQuery) return; // Skip if format is invalid

                    // Track assistant details for later use
                    if (!this.assistantDetails[assistantId]) {
                        this.assistantDetails[assistantId] = {
                            id: assistantId,
                            searchCount: 0,
                            productSearchCount: 0
                        };
                    }

                    // Process based on function type
                    if (functionName === 'search_logs_terms') {
                        this.processSearchLogsTerm(item, assistantId, rawQuery);
                        this.assistantDetails[assistantId].searchCount += (item.usageCount || 1);
                    } else if (functionName === 'product_search_terms') {
                        this.processProductSearchTerm(item, assistantId, rawQuery);
                        this.assistantDetails[assistantId].productSearchCount += (item.usageCount || 1);
                    }
                });

                // Log the final state of the maps after processing all items
                console.log('After processing all items:');
                console.log('searchQueryMap keys:', Object.keys(this.searchQueryMap));
                console.log('searchQueryMap values:', Object.values(this.searchQueryMap));
                console.log('searchLogsTerms:', this.searchLogsTerms);

                // Make sure searchLogsTerms contains all the entries from searchQueryMap
                if (Object.keys(this.searchQueryMap).length !== this.searchLogsTerms.length) {
                    console.warn('Warning: searchQueryMap and searchLogsTerms have different lengths!');

                    // Rebuild searchLogsTerms from searchQueryMap
                    this.searchLogsTerms = Object.values(this.searchQueryMap);
                    console.log('Rebuilt searchLogsTerms:', this.searchLogsTerms);
                }

                // Do the same for product search terms
                if (Object.keys(this.productQueryMap).length !== this.productSearchTerms.length) {
                    console.warn('Warning: productQueryMap and productSearchTerms have different lengths!');

                    // Rebuild productSearchTerms from productQueryMap
                    this.productSearchTerms = Object.values(this.productQueryMap);
                    console.log('Rebuilt productSearchTerms:', this.productSearchTerms);
                }
            }
            catch (error) {
                this.catchErrors(error, 'errorFetchingTagUsages');
            }
            finally {
                // Log the searchQueryMap for debugging
                console.log('searchQueryMap keys:', Object.keys(this.searchQueryMap));
                console.log('searchQueryMap:', this.searchQueryMap);
                console.log('searchLogsTerms length:', this.searchLogsTerms.length);

                this.isLoading = false;
            }
        },

        onNewItemActive(item) {
            this.activeTab = item;
            this.page = 1;
            this.initializeComponent();
        },

        onPageChange(page) {
            this.page = page.page;
            this.limit = page.limit;
            this.initializeComponent();
        },

        resetFilterTerm() {
            this.filterTerm = '';
        },

        formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleString();
        },

        getManagerIsLoading() {
            return this.isLoading;
        },

        setManagerIsLoading(_, state) {
            this.isLoading = state;
        },

        /**
         * Process a search logs term entry
         */
        processSearchLogsTerm(item, assistantId, rawQuery) {
            // Parse the query and tags
            let query = rawQuery;
            const tags = [];

            // Check if the query contains tags (separated by |)
            if (rawQuery.includes('|')) {
                const parts = rawQuery.split('|').map(part => part.trim());
                query = parts[0]; // First part is the query

                // Remaining parts are tags
                for (let i = 1; i < parts.length; i++) {
                    if (parts[i]) {
                        tags.push(parts[i]);
                    }
                }
            }

            // Ensure string fields are never null
            const ensureString = (value) => {
                return value === null || value === undefined ? '' : value;
            };

            // Create a unique key for this query
            const queryKey = ensureString(query).toLowerCase();
            console.log('Processing search query:', query, 'with key:', queryKey);

            // Check if we've already seen this query
            if (this.searchQueryMap[queryKey]) {
                // Update existing entry
                console.log('Updating existing entry for key:', queryKey);
                const existingEntry = this.searchQueryMap[queryKey];
                existingEntry.usageCount += (item.usageCount || 1);
                existingEntry.writeAttempts += (item.writeAttempts || 1);

                // Note: We don't need to add to searchLogsTerms again since it already contains this entry

                // Add this assistant to the list if not already included
                if (!existingEntry.assistants.includes(assistantId)) {
                    existingEntry.assistants.push(assistantId);
                }

                // Update the updatedAt date if this entry is newer
                const itemDate = new Date(item.updatedAt);
                const existingDate = new Date(existingEntry.updatedAt);
                if (itemDate > existingDate) {
                    existingEntry.updatedAt = item.updatedAt;
                }

                // Merge tags if any new ones
                for (const tag of tags) {
                    if (!existingEntry.parsedQuery.tags.includes(tag)) {
                        existingEntry.parsedQuery.tags.push(tag);
                    }
                }
            } else {
                // Create a new entry
                const searchLogEntry = {
                    id: item.id,
                    assistantId: ensureString(assistantId),
                    assistants: [assistantId],
                    usageCount: item.usageCount || 1,
                    writeAttempts: item.writeAttempts || 1,
                    query: ensureString(rawQuery),
                    parsedQuery: {
                        query: ensureString(query),
                        tags: tags.map(ensureString)
                    },
                    createdAt: item.createdAt,
                    updatedAt: item.updatedAt
                };

                // Store in our map
                console.log('Adding new entry for key:', queryKey);
                this.searchQueryMap[queryKey] = searchLogEntry;

                // Add to the array
                this.searchLogsTerms.push(searchLogEntry);
            }
        },

        /**
         * Process a product search term entry
         */
        processProductSearchTerm(item, assistantId, rawQuery) {
            // Ensure string fields are never null
            const ensureString = (value) => {
                return value === null || value === undefined ? '' : value;
            };

            // Initialize the parsed query
            const parsedQuery = {
                query: '',
                categories: [],
                properties: [],
                manufacturer: [],
                price_min: null,
                price_max: null,
                limit: null,
                page: null,
                sort: ''
            };

            // Parse the query string
            const parts = rawQuery.split('|').map(part => part.trim());

            // First part is the base query
            if (parts.length > 0) {
                parsedQuery.query = ensureString(parts[0]);
            }

            // Process the remaining parts
            for (let i = 1; i < parts.length; i++) {
                const part = parts[i];

                if (part.startsWith('cats:')) {
                    // Categories
                    const categories = part.substring(5).trim().split(',')
                        .map(cat => ensureString(cat.trim()))
                        .filter(Boolean);
                    parsedQuery.categories = categories;
                } else if (part.startsWith('props:')) {
                    // Properties
                    const properties = part.substring(6).trim().split(' ')
                        .map(prop => ensureString(prop.trim()))
                        .filter(Boolean);
                    parsedQuery.properties = properties;
                } else if (part.startsWith('manu:')) {
                    // Manufacturer
                    const manufacturers = part.substring(5).trim().split(',')
                        .map(manu => ensureString(manu.trim()))
                        .filter(Boolean);
                    parsedQuery.manufacturer = manufacturers;
                } else if (part.startsWith('min-max:')) {
                    // Price range
                    const priceRange = part.substring(8).trim().split('-');
                    if (priceRange.length === 2) {
                        parsedQuery.price_min = parseInt(priceRange[0], 10) || null;
                        parsedQuery.price_max = parseInt(priceRange[1], 10) || null;
                    }
                } else if (part.startsWith('lim-pag:')) {
                    // Limit and page
                    const limitPage = part.substring(8).trim().split('-');
                    if (limitPage.length === 2) {
                        parsedQuery.limit = parseInt(limitPage[0], 10) || null;
                        parsedQuery.page = parseInt(limitPage[1], 10) || null;
                    }
                } else if (part.startsWith('srt:')) {
                    // Sort
                    parsedQuery.sort = ensureString(part.substring(4).trim());
                }
            }

            // Create a unique key for this query
            const queryKey = ensureString(parsedQuery.query).toLowerCase();
            console.log('Processing product search query:', parsedQuery.query, 'with key:', queryKey);

            // Check if we've already seen this query
            if (this.productQueryMap[queryKey]) {
                // Update existing entry
                console.log('Updating existing product entry for key:', queryKey);
                const existingEntry = this.productQueryMap[queryKey];
                existingEntry.usageCount += (item.usageCount || 1);
                existingEntry.writeAttempts += (item.writeAttempts || 1);

                // Note: We don't need to add to productSearchTerms again since it already contains this entry

                // Add this assistant to the list if not already included
                if (!existingEntry.assistants.includes(assistantId)) {
                    existingEntry.assistants.push(assistantId);
                }

                // Update the updatedAt date if this entry is newer
                const itemDate = new Date(item.updatedAt);
                const existingDate = new Date(existingEntry.updatedAt);
                if (itemDate > existingDate) {
                    existingEntry.updatedAt = item.updatedAt;
                }

                // Merge categories if any new ones
                if (parsedQuery.categories && parsedQuery.categories.length > 0) {
                    for (const category of parsedQuery.categories) {
                        if (!existingEntry.parsedQuery.categories.includes(category)) {
                            existingEntry.parsedQuery.categories.push(category);
                        }
                    }
                }

                // Merge properties if any new ones
                if (parsedQuery.properties && parsedQuery.properties.length > 0) {
                    for (const property of parsedQuery.properties) {
                        if (!existingEntry.parsedQuery.properties.includes(property)) {
                            existingEntry.parsedQuery.properties.push(property);
                        }
                    }
                }

                // Merge manufacturers if any new ones
                if (parsedQuery.manufacturer && parsedQuery.manufacturer.length > 0) {
                    for (const manu of parsedQuery.manufacturer) {
                        if (!existingEntry.parsedQuery.manufacturer.includes(manu)) {
                            existingEntry.parsedQuery.manufacturer.push(manu);
                        }
                    }
                }
            } else {
                // Create a new entry
                const productSearchEntry = {
                    id: item.id,
                    assistantId: ensureString(assistantId),
                    assistants: [assistantId],
                    usageCount: item.usageCount || 1,
                    writeAttempts: item.writeAttempts || 1,
                    query: ensureString(rawQuery),
                    parsedQuery: parsedQuery,
                    createdAt: item.createdAt,
                    updatedAt: item.updatedAt
                };

                // Store in our map
                console.log('Adding new product entry for key:', queryKey);
                this.productQueryMap[queryKey] = productSearchEntry;

                // Add to the array
                this.productSearchTerms.push(productSearchEntry);
            }
        },

    },

    metaInfo() {
        return { title: this.$createTitle() };
    }
});
