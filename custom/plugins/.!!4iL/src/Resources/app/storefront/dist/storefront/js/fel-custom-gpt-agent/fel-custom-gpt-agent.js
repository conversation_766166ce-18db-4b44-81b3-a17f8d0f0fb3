(()=>{"use strict";var t={857:t=>{var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==s},s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function a(t,e){return!1!==e.clone&&e.isMergeableObject(t)?n(Array.isArray(t)?[]:{},t,e):t}function i(t,e,s){return t.concat(e).map(function(t){return a(t,s)})}function o(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function r(t,e){try{return e in t}catch(t){return!1}}function n(t,s,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=a;var c,h,d=Array.isArray(s);return d!==Array.isArray(t)?a(s,l):d?l.arrayMerge(t,s,l):(h={},(c=l).isMergeableObject(t)&&o(t).forEach(function(e){h[e]=a(t[e],c)}),o(s).forEach(function(e){(!r(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(r(t,e)&&c.isMergeableObject(s[e])?h[e]=(function(t,e){if(!e.customMerge)return n;var s=e.customMerge(t);return"function"==typeof s?s:n})(e,c)(t[e],s[e],c):h[e]=a(s[e],c))}),h)}n.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,s){return n(t,s,e)},{})},t.exports=n}},e={};function s(a){var i=e[a];if(void 0!==i)return i.exports;var o=e[a]={exports:{}};return t[a](o,o.exports,s),o.exports}(()=>{s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e}})(),(()=>{s.d=(t,e)=>{for(var a in e)s.o(e,a)&&!s.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})}})(),(()=>{s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})(),(()=>{class t{static ucFirst(t){return t.charAt(0).toUpperCase()+t.slice(1)}static lcFirst(t){return t.charAt(0).toLowerCase()+t.slice(1)}static toDashCase(t){return t.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(e,s){let a=t.toUpperCamelCase(e,s);return t.lcFirst(a)}static toUpperCamelCase(e,s){return s?e.split(s).map(e=>t.ucFirst(e.toLowerCase())).join(""):t.ucFirst(e.toLowerCase())}static parsePrimitive(t){try{return/^\d+(.|,)\d+$/.test(t)&&(t=t.replace(",",".")),JSON.parse(t)}catch(e){return t.toString()}}}class e{static isNode(t){return"object"==typeof t&&null!==t&&(t===document||t===window||t instanceof Node)}static hasAttribute(t,s){if(!e.isNode(t))throw Error("The element must be a valid HTML Node!");return"function"==typeof t.hasAttribute&&t.hasAttribute(s)}static getAttribute(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!1===e.hasAttribute(t,s))throw Error('The required property "'.concat(s,'" does not exist!'));if("function"!=typeof t.getAttribute){if(a)throw Error("This node doesn't support the getAttribute function!");return}return t.getAttribute(s)}static getDataAttribute(s,a){let i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],o=a.replace(/^data(|-)/,""),r=t.toLowerCamelCase(o,"-");if(!e.isNode(s)){if(i)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===s.dataset){if(i)throw Error("This node doesn't support the dataset attribute!");return}let n=s.dataset[r];if(void 0===n){if(i)throw Error('The required data attribute "'.concat(a,'" does not exist on ').concat(s,"!"));return n}return t.parsePrimitive(n)}static querySelector(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!e.isNode(t))throw Error("The parent node is not a valid HTML Node!");let i=t.querySelector(s)||!1;if(a&&!1===i)throw Error('The required element "'.concat(s,'" does not exist in parent node!'));return i}static querySelectorAll(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!e.isNode(t))throw Error("The parent node is not a valid HTML Node!");let i=t.querySelectorAll(s);if(0===i.length&&(i=!1),a&&!1===i)throw Error('At least one item of "'.concat(s,'" must exist in parent node!'));return i}}class a{static iterate(t,e){if(t instanceof Map||Array.isArray(t))return t.forEach(e);if(t instanceof FormData){for(var s of t.entries())e(s[1],s[0]);return}if(t instanceof NodeList)return t.forEach(e);if(t instanceof HTMLCollection)return Array.from(t).forEach(e);if(t instanceof Object)return Object.keys(t).forEach(s=>{e(t[s],s)});throw Error("The element type ".concat(typeof t," is not iterable!"))}}class i{static isSupported(){return"undefined"!==document.cookie}static setItem(t,e,s){if(null==t)throw Error("You must specify a key to set a cookie");let a=new Date;a.setTime(a.getTime()+864e5*s);let i="";"https:"===location.protocol&&(i="secure"),document.cookie="".concat(t,"=").concat(e,";expires=").concat(a.toUTCString(),";path=/;sameSite=lax;").concat(i)}static getItem(t){if(!t)return!1;let e=t+"=",s=document.cookie.split(";");for(let t=0;t<s.length;t++){let a=s[t];for(;" "===a.charAt(0);)a=a.substring(1);if(0===a.indexOf(e))return a.substring(e.length,a.length)}return!1}static removeItem(t){document.cookie="".concat(t,"= ; expires = Thu, 01 Jan 1970 00:00:00 GMT;path=/")}static key(){return""}static clear(){}}class o{setItem(t,e){return this._storage[t]=e}getItem(t){return Object.prototype.hasOwnProperty.call(this._storage,t)?this._storage[t]:null}removeItem(t){return delete this._storage[t]}key(t){return Object.values(this._storage)[t]||null}clear(){return this._storage={}}constructor(){this._storage={}}}class r{_chooseStorage(){return r._isSupported(window.localStorage)?this._storage=window.localStorage:r._isSupported(window.sessionStorage)?this._storage=window.sessionStorage:i.isSupported()?this._storage=i:this._storage=new o}static _isSupported(t){try{let e="__storage_test";return t.setItem(e,"1"),t.removeItem(e),!0}catch(t){return!1}}_validateStorage(){if("function"!=typeof this._storage.setItem)throw Error('The storage must have a "setItem" function');if("function"!=typeof this._storage.getItem)throw Error('The storage must have a "getItem" function');if("function"!=typeof this._storage.removeItem)throw Error('The storage must have a "removeItem" function');if("function"!=typeof this._storage.key)throw Error('The storage must have a "key" function');if("function"!=typeof this._storage.clear)throw Error('The storage must have a "clear" function')}getStorage(){return this._storage}constructor(){this._storage=null,this._chooseStorage(),this._validateStorage()}}let n=Object.freeze(new r).getStorage();var l=s(857);class c{publish(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=new CustomEvent(t,{detail:e,cancelable:s});return this.el.dispatchEvent(a),a}subscribe(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=this,i=t.split("."),o=s.scope?e.bind(s.scope):e;if(s.once&&!0===s.once){let e=o;o=function(s){a.unsubscribe(t),e(s)}}return this.el.addEventListener(i[0],o),this.listeners.push({splitEventName:i,opts:s,cb:o}),!0}unsubscribe(t){let e=t.split(".");return this.listeners=this.listeners.reduce((t,s)=>([...s.splitEventName].sort().toString()===e.sort().toString()?this.el.removeEventListener(s.splitEventName[0],s.cb):t.push(s),t),[]),!0}reset(){return this.listeners.forEach(t=>{this.el.removeEventListener(t.splitEventName[0],t.cb)}),this.listeners=[],!0}get el(){return this._el}set el(t){this._el=t}get listeners(){return this._listeners}set listeners(t){this._listeners=t}constructor(t=document){this._el=t,t.$emitter=this,this._listeners=[]}}class h{static isTouchDevice(){return"ontouchstart"in document.documentElement}static isIOSDevice(){return h.isIPhoneDevice()||h.isIPadDevice()}static isNativeWindowsBrowser(){return h.isIEBrowser()||h.isEdgeBrowser()}static isIPhoneDevice(){return!!navigator.userAgent.match(/iPhone/i)}static isIPadDevice(){return!!navigator.userAgent.match(/iPad/i)}static isIEBrowser(){return -1!==navigator.userAgent.toLowerCase().indexOf("msie")||!!navigator.userAgent.match(/Trident.*rv:\d+\./)}static isEdgeBrowser(){return!!navigator.userAgent.match(/Edge\/\d+/i)}static getList(){return{"is-touch":h.isTouchDevice(),"is-ios":h.isIOSDevice(),"is-native-windows":h.isNativeWindowsBrowser(),"is-iphone":h.isIPhoneDevice(),"is-ipad":h.isIPadDevice(),"is-ie":h.isIEBrowser(),"is-edge":h.isEdgeBrowser()}}}let d="offcanvas";class u{open(t,e,s,a,i,o,r){this._removeExistingOffCanvas();let n=this._createOffCanvas(s,o,r,a);this.setContent(t,a,i),this._openOffcanvas(n,e)}setContent(t,e){let s=this.getOffCanvas();s[0]&&(s[0].innerHTML=t,this._registerEvents(e))}setAdditionalClassName(t){this.getOffCanvas()[0].classList.add(t)}getOffCanvas(){return document.querySelectorAll(".".concat(d))}close(t){let e=this.getOffCanvas();a.iterate(e,t=>{bootstrap.Offcanvas.getInstance(t).hide()}),setTimeout(()=>{this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:e})},t)}goBackInHistory(){window.history.back()}exists(){return this.getOffCanvas().length>0}_openOffcanvas(t,e){u.bsOffcanvas.show(),window.history.pushState("offcanvas-open",""),"function"==typeof e&&e()}_registerEvents(t){let e=h.isTouchDevice()?"touchend":"click",s=this.getOffCanvas();a.iterate(s,e=>{let a=()=>{setTimeout(()=>{e.remove(),this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:s})},t),e.removeEventListener("hide.bs.offcanvas",a)};e.addEventListener("hide.bs.offcanvas",a)}),window.addEventListener("popstate",this.close.bind(this,t),{once:!0});let i=document.querySelectorAll(".".concat("js-offcanvas-close"));a.iterate(i,s=>s.addEventListener(e,this.close.bind(this,t)))}_removeExistingOffCanvas(){u.bsOffcanvas=null;let t=this.getOffCanvas();return a.iterate(t,t=>t.remove())}_getPositionClass(t){return"left"===t?"offcanvas-start":"right"===t?"offcanvas-end":"offcanvas-".concat(t)}_createOffCanvas(t,e,s,a){let i=document.createElement("div");if(i.classList.add(d),i.classList.add(this._getPositionClass(t)),!0===e&&i.classList.add("is-fullwidth"),s){let t=typeof s;if("string"===t)i.classList.add(s);else if(Array.isArray(s))s.forEach(t=>{i.classList.add(t)});else throw Error('The type "'.concat(t,'" is not supported. Please pass an array or a string.'))}return document.body.appendChild(i),u.bsOffcanvas=new bootstrap.Offcanvas(i,{backdrop:!1!==a||"static"}),i}constructor(){this.$emitter=new c}}let g=Object.freeze(new u);class f{static open(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"left",a=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:350,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"";g.open(t,e,s,a,i,o,r)}static setContent(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:350;g.setContent(t,e,s)}static setAdditionalClassName(t){g.setAdditionalClassName(t)}static close(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:350;g.close(t)}static exists(){return g.exists()}static getOffCanvas(){return g.getOffCanvas()}static REMOVE_OFF_CANVAS_DELAY(){return 350}}class v{get(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/json",a=this._createPreparedRequest("GET",t,s);return this._sendRequest(a,null,e)}post(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("POST",t,a);return this._sendRequest(i,e,s)}delete(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("DELETE",t,a);return this._sendRequest(i,e,s)}patch(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let i=this._createPreparedRequest("PATCH",t,a);return this._sendRequest(i,e,s)}abort(){if(this._request)return this._request.abort()}_registerOnLoaded(t,e){e&&t.addEventListener("loadend",()=>{e(t.responseText,t)})}_sendRequest(t,e,s){return this._registerOnLoaded(t,s),t.send(e),t}_getContentType(t,e){return t instanceof FormData&&(e=!1),e}_createPreparedRequest(t,e,s){return this._request=new XMLHttpRequest,this._request.open(t,e),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),s&&this._request.setRequestHeader("Content-type",s),this._request}constructor(){this._request=null}}let p="loader",m={BEFORE:"before",INNER:"inner"};class b{create(){if(!this.exists()){if(this.position===m.INNER){this.parent.innerHTML=b.getTemplate();return}this.parent.insertAdjacentHTML(this._getPosition(),b.getTemplate())}}remove(){let t=this.parent.querySelectorAll(".".concat(p));a.iterate(t,t=>t.remove())}exists(){return this.parent.querySelectorAll(".".concat(p)).length>0}_getPosition(){return this.position===m.BEFORE?"afterbegin":"beforeend"}static getTemplate(){return'<div class="'.concat(p,'" role="status">\n                    <span class="').concat("visually-hidden",'">Loading...</span>\n                </div>')}static SELECTOR_CLASS(){return p}constructor(t,e=m.BEFORE){this.parent=t instanceof Element?t:document.body.querySelector(t),this.position=e}}let E=null;class C extends f{static open(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"left",i=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:f.REMOVE_OFF_CANVAS_DELAY(),r=arguments.length>6&&void 0!==arguments[6]&&arguments[6],n=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"";if(!t)throw Error("A url must be given!");g._removeExistingOffCanvas();let l=g._createOffCanvas(a,r,n,i);this.setContent(t,e,s,i,o),g._openOffcanvas(l)}static setContent(t,e,s,a,i){let o=new v;super.setContent('<div class="offcanvas-body">'.concat(b.getTemplate(),"</div>"),a,i),E&&E.abort();let r=t=>{super.setContent(t,a,i),"function"==typeof s&&s(t)};E=e?o.post(t,e,C.executeCallback.bind(this,r)):o.get(t,C.executeCallback.bind(this,r))}static executeCallback(t,e){"function"==typeof t&&t(e),window.PluginManager.initializePlugins()}}let w="element-loader-backdrop";class L extends b{static create(t){t.classList.add("has-element-loader"),L.exists(t)||(L.appendLoader(t),setTimeout(()=>{let e=t.querySelector(".".concat(w));e&&e.classList.add("element-loader-backdrop-open")},1))}static remove(t){t.classList.remove("has-element-loader");let e=t.querySelector(".".concat(w));e&&e.remove()}static exists(t){return t.querySelectorAll(".".concat(w)).length>0}static getTemplate(){return'\n        <div class="'.concat(w,'">\n            <div class="loader" role="status">\n                <span class="').concat("visually-hidden",'">Loading...</span>\n            </div>\n        </div>\n        ')}static appendLoader(t){t.insertAdjacentHTML("beforeend",L.getTemplate())}}window.router["frontend.cookie.offcanvas"];class y extends window.PluginBaseClass{init(){console.log("Initializing Global Chatbot Plugin"),this.setPluginOptions()&&(this.setPluginVars(),this.loadTemplates(),this.registerEvents())}registerEvents(){this.handleEvent=t=>{if(this.isLoading&&!t.target.classList.contains("fel-ui-btn"))return t.preventDefault(),!1;switch(t.type){case"submit":return this.chatEventCallback(t,t.target);case"change":return this.changeEventCallback(t,t.target);case"click":return this.clickEventCallback(t,t.target)}},this.setEventHandler("add"),this.setFromLastVisit(),this.swEventHelper(),this.setLocalStorageIsAllowed()}swEventHelper(){document.$emitter.subscribe("CookieConfiguration_Update",this.cookieCallback.bind(this));var t=window.PluginManager.getPluginInstances("ScrollUp");if(t.length){var e=t[0].el.firstElementChild;if(e){var s=e.classList.contains("is-visible"),a=window.getComputedStyle(e),i=this.felParseInt(a.right),o=this.felParseInt(a.right)+this.felParseInt(a.width)+3,r=(s?o:i)+"px";this.assistantEl.querySelector(".fel-toggle-chatbot-button.toggle-open").style.height=a.height,this.assistantEl.dataset.setBottom=a.bottom,this.assistantEl.dataset.setRight=r,this.assistantEl.classList.contains("active")||(this.assistantEl.style.bottom=a.bottom,this.assistantEl.style.right=r),t[0].$emitter.subscribe("toggleVisibility",()=>{r=((s=e.classList.contains("is-visible"))?o:i)+"px",this.assistantEl.dataset.setRight=r,this.assistantEl.style.right=this.assistantEl.classList.contains("active")?null:r})}}}setLocalStorageIsAllowed(){this.checkIfLocalStorageIsAllowed()?(this.assistantEl.classList.add("fel-localstorage-is-allowed"),this.assistantEl.classList.remove("fel-localstorage-is-disabled"),this.setCheckedConsentLocalStorageTemplate(!0),this.assistantEl.classList.contains("active")&&this.storageSet("".concat(this.options.storage.chat,"_chatOpen"),"active"),this.assistantEl.classList.contains("fel-zoom-chat")&&this.storageSet("".concat(this.options.storage.chat,"_chatZoom"),"active")):(this.assistantEl.classList.add("fel-localstorage-is-disabled"),this.assistantEl.classList.remove("fel-localstorage-is-allowed"),this.setCheckedConsentLocalStorageTemplate(!1),this.clearLocalStorage())}cookieCallback(t){var e=this.options.selector.cookieDefaultName;void 0!==t.detail[e]?(this.localStorageAllowed=t.detail[e],this.localStorageAllowed&&this.enableLocalStorage()):(this.localStorageAllowed=!1,this.clearLocalStorage());var s=this.domGet(this.assistantEl,'[name="fel-allow-localstorage"]',!1);s&&(s.checked=!!this.localStorageAllowed||null,this.setCheckedConsentLocalStorageTemplate(this.localStorageAllowed))}checkIfLocalStorageIsAllowed(){return i.getItem(this.options.selector.cookieDefaultName)}setCheckedConsentLocalStorageTemplate(t){var e=this.assistantEl.querySelector('[name="fel-allow-localstorage"]');e&&(e.checked=!!t),this.template.greeting.querySelector('[name="fel-allow-localstorage"]').checked=!!t}toggleLocalStorage(t,e){e.checked?i.setItem(this.options.selector.cookieDefaultName,"allowed"):i.removeItem(this.options.selector.cookieDefaultName),this.setCheckedConsentLocalStorageTemplate(e.checked),this.setLocalStorageIsAllowed(),e.blur()}chatEventCallback(t,e){try{return this._chatEventCallback(t,e)}catch(t){return this.putToMessages("chatbot",e.message||"error")}}checkIfMessageThrown(t){return!!t.error&&void 0!==t.error.exception&&t.error.exception}_chatEventCallback(t,e){if("fel-chatbot-form"===e.id){if(t.preventDefault(),this.getChatbotUserInput()){var s={threadId:this.getChatbotThreadId().value,runId:this.getChatbotRunId().value,userInput:this.getChatbotUserInput().value};""===s.runId?(this.isLoadingResponse=!1,this.clientPost("".concat(this.controllerUrl,"/create-thread"),s,t=>{if(t){if(this.$emitter.publish("felAssistantThreadCreated"),this.checkIfMessageThrown(t))return this.putToMessages("chatbot",this.checkIfMessageThrown(t),"",""," fel-system-exception");if(void 0!==t.id)return this.threadId=t.id,this.putToMessages("user",s.userInput,this.threadId,t.datetime),this.getChatbotUserInput().value="",this.getChatbotRunId().value=t.runId,this.getChatbotThreadId().value=t.threadId,this.assistantEl.classList.add("contains-thread"),this.chatbotForm.dispatchEvent(new Event("submit",{bubbles:!0,cancelable:!0}))}})):this.threadId&&(this.isLoadingResponse=!0,this.getChatbotRunId().value="",this.scrollThreadIntoView("smooth",!0),this.clientPost("".concat(this.controllerUrl,"/run"),s,t=>{let e=null;if(t){if(this.checkIfMessageThrown(t))this.putToMessages("chatbot",this.checkIfMessageThrown(t),"",""," fel-system-exception");else if(void 0!==t.id){if(void 0!==t.threadMessages){var s=t.threadMessages;if(e||(e=s),void 0!==s[0]&&void 0!==s[0].role&&"assistant"===s[0].role){var a=!0;this.foreach(s,(e,s)=>{if("user"===s.role&&(a=!1),a){if(void 0!==t.uiActionRequired.product_search)void 0!==s.originalValue&&(s=this.replaceAiProductList(s,t.uiActionRequired.product_search));else if(!s.value.includes("<p")&&!s.value.includes("<ul")){var i=document.createElement("div");i.classList.add("fel-text-plain","text-break"),i.insertAdjacentHTML("beforeend",s.value.trim()),s.value=i.outerHTML}this.putToMessages("chatbot",s.value,"",t.datetime)}})}else this.putToMessages("chatbot",this.options.translation.errorClearChat,"",t.datetime)}this.getChatbotUserInput().focus(),this.chatToStorage(t.threadId),void 0!==t.uiActionRequired&&t.uiActionRequired&&this.uiActionRequired(t.uiActionRequired)}}this.$emitter.publish("felAssistantMessageResponse")}))}return!1}}replaceAiProductList(t,e){var s=document.createElement("div"),a=document.createElement("div");if(s.insertAdjacentHTML("beforeend",t.originalValue),a.insertAdjacentHTML("beforeend",t.value),s.querySelector("ul")){var i=s.querySelectorAll("ul");if(i.length){if(1===i.length&&void 0!==e.items&&void 0!==e.items.products&&e.items.products.length){for(var o=0;o<e.items.products.length;o++)if(t.originalValue.includes(e.items.products[o].id)){s.querySelector("ul").replaceWith(a),t.value=s.innerHTML;break}}else t.value=t.originalValue}}return t}uiActionRequired(t){void 0!==t.redirectTo&&t.redirectTo&&t.redirectTo!==location.href&&(location.href=t.redirectTo)}deleteChatThread(){var t=this.getChatbotThreadId().value;t?(this.addIsLoading(),this._client.post("".concat(this.controllerUrl,"/delete-thread"),JSON.stringify({threadId:t}),t=>{this.removeIsLoading(),t&&(t=JSON.parse(t),this.assistantEl.classList.remove("contains-thread"),this.getChatbotRunId().value="",this.getChatbotThreadId().value="",this.chatClearStorage(),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML),this.getChatbotUserInput().focus())})):(this.chatClearStorage(),this.assistantEl.classList.remove("contains-thread"),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML))}clientPost(t,e,s){this.addIsLoading(),this._client.post(t,JSON.stringify(e),t=>{if(this.removeIsLoading(),t)try{var e=JSON.parse(t);e&&(t=e)}catch(t){var a=document.createElement("div");return a.appendChild(document.createTextNode(t)),s({error:{exception:a.innerHTML.substring(0,200)}})}return s(t)})}scrollIntoView(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"smooth",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;var i="object"==typeof t?t:this.domGet(this.assistantEl,t,!1);i&&this.debounce(()=>{i.scrollIntoView(a||{behavior:s})},e)}scrollThreadIntoView(t,e){if(!e&&!this.options.compactView){var s=this.getActiveProductsInChat();if(s.length)return this.scrollIntoView(s[s.length-1],200,null,{behavior:"smooth",block:"start"})}var a=this.domGetAll(this.assistantEl,".user-message-item",!1);a.length&&this.scrollIntoView(a[a.length-1],200,t)}addIsLoading(){var t=this.template.loading;if(this.isLoading=!0,this.assistantEl.classList.add("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=!0,this.getChatbotUserInput().disabled=!0),this.removeGreetingContainer(),this.isLoadingResponse){var e=t.cloneNode(!0),s=this.domGet(e,".fel-chatbot-loader-info",!1);s&&s.dataset.secondRun&&(this.domGet(e,".fel-chatbot-loader-info").innerHTML=s.dataset.secondRun),t=e}this.putToMessages("loading",t.outerHTML)}removeIsLoading(){this.isLoading=!1,this.assistantEl.classList.remove("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=null,this.getChatbotUserInput().disabled=null);var t=this.domGetAll(this.assistantEl,".fel-chatbot-load-container",!1);if(t.length)for(let e=0;e<t.length;e++)t[e].classList.add("position-absolute","w-100"),this.debounce(()=>{t[e].remove()},150)}removeGreetingContainer(){var t=this.domGet(this.assistantEl,this.options.selector.greetingContainer,!1);t&&(t.remove(),this.assistantEl.classList.remove("fel-chat-initial"))}getChatSubmitButton(){return this.domGet(this.assistantEl,this.options.selector.submitChatButton,!1)}getChatbotMessagesEl(){return this.domGet(this.assistantEl,this.options.selector.chatMessages)}getChatbotUserInput(){return this.domGet(this.chatbotForm,this.options.selector.chatUserInput)}getChatbotThreadId(){return this.domGet(this.chatbotForm,this.options.selector.chatThreadId)}getChatbotRunId(){return this.domGet(this.chatbotForm,this.options.selector.chatRunId)}addToChatSubmit(t,e){var s=e.dataset.prompt;s&&(this.getChatbotUserInput().value=s,this.getChatSubmitButton().dispatchEvent(new MouseEvent("click",{bubbles:!0,cancelable:!0})))}toggleChatbot(t,e){var s=e.dataset.toggleClass;if(s){this.assistantEl.classList.toggle(s);var a="".concat(this.options.storage.chat,"_chatOpen");this.assistantEl.classList.contains("active")?(this.assistantEl.style.right=null,this.assistantEl.style.bottom=null,this.storageSet(a,"active"),this.scrollThreadIntoView(),this.getChatbotUserInput().focus(),this._addBackdrop()):(this.assistantEl.style.right=this.assistantEl.dataset.setRight,this.assistantEl.style.bottom=this.assistantEl.dataset.setBottom,this.storageSet(a,"not-active"),this._removeBackdrop())}}_addBackdrop(){this.options.compactView&&this.putHtml(this.assistantEl,"afterend",'\n				<div class="modal-backdrop fel-modal-backdrop fel-fade-in show" onclick="this.remove()"></div>\n			')}_removeBackdrop(){var t=document.querySelector(".fel-modal-backdrop");t&&t.remove()}enableLocalStorage(){i.setItem(this.options.selector.cookieDefaultName,"allowed"),this.setLocalStorageIsAllowed()}setFromLastVisit(){if(this.setLastChatZoom(),"active"===this.storageGet("".concat(this.options.storage.chat,"_chatOpen"))){var t=this.assistantEl.querySelector(".fel-toggle-chatbot-button.toggle-open");t&&t.dispatchEvent(new MouseEvent("click",{bubbles:!0}))}}toggleChatZoom(t,e){if(e.classList.toggle("fel-active"),this.assistantEl){var s="".concat(this.options.storage.chat,"_chatZoom");e.classList.contains("fel-active")?(this.assistantEl.classList.add("fel-zoom-chat"),this.storageSet(s,"active")):(this.assistantEl.classList.remove("fel-zoom-chat"),this.storageSet(s,"not-active")),this.getChatbotUserInput().focus()}}setLastChatZoom(){if("active"===this.storageGet("".concat(this.options.storage.chat,"_chatZoom"))){var t=this.assistantEl.querySelector(".fel-zoom-button");t&&(t.classList.add("fel-active"),this.assistantEl.classList.add("fel-zoom-chat"))}}removeChatMessage(t){if(void 0!==t.target.offsetParent&&void 0!==t.target.offsetParent.offsetParent){var e=t.target.offsetParent.offsetParent;e&&(e.previousElementSibling.classList.contains("user-message-item")&&e.previousElementSibling.remove(),e.remove(),this.chatToStorage(this.getChatbotThreadId().value),""==this.storageGet(this.options.storage.chat).trim()&&this.deleteChatThread(),this.scrollThreadIntoView())}}clickEventCallback(t,e){if(e.classList.contains("fel-btn")){var s=e.dataset.callback;if(e.dataset.preventDefault&&t.preventDefault(),e.dataset.stopPropagation&&t.stopPropagation(),e.dataset.blur&&e.blur(),s){if("function"==typeof this[s])return this[s](...arguments);if("function"==typeof window[s])return window[s](...arguments)}}}changeEventCallback(t,e){if(e.classList.contains("fel-checkbox")){t.preventDefault();var s=e.dataset.callback;return"function"==typeof this[s]&&this[s](t,e),!1}}putToMessages(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"";s&&(s=' id="'.concat(s,'"')),a&&(a=' data-datetime="'.concat(a,'"'));var o="chatbot"===t?'<div class="fel-delete-message">\n				<span title="'.concat(this.options.translation.removeChatMessage,'"\n					class="btn fel-btn" data-callback="removeChatMessage">X</span>\n			</div>'):"",r=["loading","greeting"].includes(t)?e:'\n			<div class="'.concat(this.options.selector.chatMessageEl," ").concat(t,'-message-item fel-fade-in d-flex"').concat(s,'>\n				<div class="').concat(this.options.selector.chatMessage," ").concat(t,"-message").concat(i,'">\n					<div class="fel-inner"').concat(a,">\n						").concat(o,"\n						").concat(e,"\n					</div>\n				</div>\n			</div>");this.putHtml(this.getChatbotMessagesEl(),"beforeend",r)}domGet(){return e.querySelector(...arguments)}domGetAll(){return e.querySelectorAll(...arguments)}storageGet(){return n.getItem(...arguments)}storageSet(){this.checkIfLocalStorageIsAllowed()&&n.setItem(...arguments)}storageRemove(){n.removeItem(...arguments)}chatClearStorage(){this.storageRemove(this.options.storage.chat),this.storageRemove("".concat(this.options.storage.chat,"_threadId"))}chatToStorage(t){var e=this.getChatbotMessagesEl();if(e){var s=this.domGetAll(e,this.options.selector.loadingContainer,!1);s.length&&this.foreach(s,(t,e)=>{e.remove()}),this.removeActiveProducts(),this.storageSet(this.options.storage.chat,e.innerHTML),this.storageSet("".concat(this.options.storage.chat,"_threadId"),t),this.setActiveProducts()}}clearLocalStorage(){this.storageRemove("".concat(this.options.storage.chat,"_threadId")),this.storageRemove("".concat(this.options.storage.chat,"_chatOpen")),this.storageRemove("".concat(this.options.storage.chat,"_chatZoom"))}foreach(t,e){return a.iterate(t,function(t,s){e(s,t)})}objectMerge(){for(var t=arguments.length,e=Array(t),s=0;s<t;s++)e[s]=arguments[s];var a=(t,e)=>(Object.entries(e).forEach(e=>{let[s,i]=e;t[s]=i&&"object"==typeof i?a(t[s]||{},i):i}),t);return e.reduce(a,{})}putHtml(t,e,s){t.insertAdjacentHTML(e,s)}debounce(){return"function"==typeof arguments[0]?setTimeout(()=>{arguments[0].apply()},arguments[1]||0):void 0}felParseInt(t){var t=t.replace(/[^-\d\.]/g,"");return t?Math.round(parseFloat(t)):0}loadTemplates(){this.template={greeting:"",loading:"",messageItem:""};var t=document.getElementById("fel-chatbot-template");if(t){if(this.template.greeting=t.content.querySelector(this.options.selector.greetingContainer),this.template.greeting){var e=this.storageGet(this.options.storage.chat);this.putToMessages("greeting",e||this.template.greeting.outerHTML),e&&(this.setActiveProducts(),this.assistantEl.classList.add("contains-thread"),this.getChatbotThreadId().value=this.storageGet(this.options.storage.chat+"_threadId"))}this.template.loading=t.content.querySelector(this.options.selector.loadingContainer)}}getActiveProductsInChat(){return this.assistantEl.querySelectorAll(".fel-chat-product-list li[data-ref-id].active")}removeActiveProducts(){var t=this.getActiveProductsInChat();if(t.length)for(let e=0;e<t.length;e++)t[e].classList.remove("active")}setActiveProducts(){if(this.options.page.refId){var t=this.assistantEl.querySelectorAll('.fel-chat-product-list li[data-ref-id="'.concat(this.options.page.refId,'"]'));if(t.length)for(let e=0;e<t.length;e++)t[e].classList.add("active")}}setPluginVars(){this._client=new v,this.eventsRegistered=!1,this.isLoading=!1,this.isLoadingResponse=!1,this.threadId=null,this.options.eventHandlerMap=this.chatbotForm?[[this.chatbotForm,"submit.felBtn",!0],[this.assistantEl,"click.felBtn",!0],[this.assistantEl,"change.felBtn",!0]]:[]}setPluginOptions(){this.assistantEl=this.domGet(document.body,"[data-assistant-gpt-plugin]"),this.chatbotForm=this.domGet(this.assistantEl,"form#fel-chatbot-form"),this.controllerUrl=this.chatbotForm.action,this.chatMessages=this.domGet(this.assistantEl,this.options.selector.chatMessages);var t=this.assistantEl.dataset.options;return!!(t&&"string"==typeof t&&(t=JSON.parse(t)))&&(this.options=this.objectMerge(this.options,t),!0)}removeEvents(){this.setEventHandler("remove")}resetEvents(){this.removeEvents(),this.registerEvents()}setEventHandler(t){var e={add:"addEventListener",remove:"removeEventListener"};if("remove"===t&&!this.eventsRegistered)return!1;"add"===t&&this.eventsRegistered&&this.removeEvents(),this.foreach(this.options.eventHandlerMap,(s,a)=>{if(void 0!==a[1]){var i=a[1].split(".");a[0][e[t]](i[0],this,a[2]||!0)}}),this.eventsRegistered="add"===t}}y.options={compactView:640>window.innerWidth,selector:{chatMessages:"#fel-chatbot-messages",greetingContainer:".fel-chatbot-greeting-container",loadingContainer:".fel-chatbot-load-container",submitChatButton:".fel-submit-chat-btn",chatThreadId:'[name="fel-chatbot-thread-id"]',chatRunId:'[name="fel-chatbot-run-id"]',chatUserInput:'[name="fel-chatbot-user-input"]',chatMessageEl:"chat-message-item",chatMessage:"chat-message",cookieDefaultName:"fel-chatbot-localstorage-accepted"},page:{refId:null},storage:{chat:"felChatStorage"}};class I extends window.PluginBaseClass{init(){console.log("Initializing CMS Chatbot Plugin"),this._registerEvents()}_registerEvents(){let t=this.el.querySelector(".fel-submit-chat-btn");t&&t.addEventListener("click",this._onSubmit.bind(this))}_onSubmit(t){let e=this.el.querySelector("#fel-chatbot-form");if(e){let t=document.createElement("input");t.type="hidden",t.name="cms-assistant-id",t.value=this.options.cmsAssistantId,e.appendChild(t)}}}window.PluginManager.register("FelAssistantPlugin",y,"[data-assistant-gpt-plugin]"),window.PluginManager.register("CmsChatbotPlugin",I,"[data-cms-assistant-gpt-plugin]")})()})();