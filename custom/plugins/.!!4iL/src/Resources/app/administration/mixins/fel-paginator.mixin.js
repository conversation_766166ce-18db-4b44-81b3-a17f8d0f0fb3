const { Component, Mixin } = Shopware;

Mixin.register('fel-paginator-mixin', {

    data() {
        return {
            optQueries: {
                page: +(this.$route.query.page || 1),
                limit: +(this.$route.query.limit || 5),
                sort: this.$route.query.sort,
                pages: 0,
                filterable: null,
            },
        };
    },

    methods: {

        refreshOptQueries() {
            this.optQueries.page = +(this.$route.query.page || this.optQueries.page);
            this.optQueries.limit = +(this.$route.query.limit || this.optQueries.limit);
            this.optQueries.sort = this.$route.query.sort || this.optQueries.sort;
            this.optQueries.filterable = this.$route.query.filterable;
            this.optQueries.pages = +this.optQueries.pages;
            this.optQueries.setFilterCriteria = [];
            if (this.optQueries.filterable) {
                var filterToArray = this.optQueries.filterable.split(',');
                for (var i = 0; i < filterToArray.length; i++) {
                    let setFilter = filterToArray[i];
                    if (setFilter) {
                        var [filterName, filterIds] = setFilter.split(':');
                        if (filterName && filterIds) {
                            filterIds = filterIds.split('|');
                            this.optQueries.setFilterCriteria.push({filterName, filterIds});
                        }
                    }
                }
            }
        },

        felRouteLink(name, params, queries) {
            return this.$router.resolve({
                name: name,
                params: params || {},
                query: queries || {}
            }).href;
        },

        createLogLink(params, queries) {
            var defaultQueries = {
                page: this.optQueries.page,
                limit: this.optQueries.limit,
                sort: this.optQueries.sort,
            };

            if (this.optQueries.filterable) {
                defaultQueries.filterable = this.optQueries.filterable;
            }

            queries = {...defaultQueries, ...queries};

            var resUrl = this.$router.resolve({
                name: 'fel.gpt.assistant.logs',
                params: params || {},
                query: queries || {}
            });

            var absoluteURL = new URL(resUrl.href, window.location.origin + window.location.pathname);

            return absoluteURL.href;
        },

        getLogPagination() {
            return this.fetchLogPagination();
        },

        fetchLogPagination() {
            let page = +this.optQueries.page,
                pages = +this.optQueries.pages,
                limit = +this.optQueries.limit,
                currentLength = this.optQueries.currentLength;

            let getNumbers = this.createNavNumbers(page, pages),
                stepNav = this.createNavSteps(page, pages),
                from = page * limit - limit;

            let index = {
                from: from ? from + 1 : 1,
                to: from + currentLength,
                total: this.optQueries.currentTotal,
            };

            if (from > this.optQueries.currentTotal) {
                index = {from: 0, to: 0, total: 0};
            }

            return {
                meta: {index},
                numbers: getNumbers,
                steps: stepNav,
                numbersMap: 500 >= pages ? this.createNavNumbersMap(pages) : null,
                pageError: {
                    dontExists: page > pages,
                },
                queries: this.optQueries,
            };
        },

        createNavSteps(page, pages) {
            return {
                first: 1 === page ? null : 1,
                prev: page > 1 && page <= pages ? page - 1 : null,
                next: page >= 1 && page < pages ? page + 1 : null,
                last: page === pages ? null : pages,
            };
        },

        createNavNumbersMap(pages) {
            return Array(pages).fill(1).map((x, y) => x + y);
        },

        createNavNumbers(page, pages) {
            if (pages <= 5) {
                return Array.from({length: pages}, (_, i) => i + 1);
            }

            let steps = [2, 1, 2, 3, 4];

            page = Math.max(page, 1);

            let rangeStart, rangeEnd;

            if (page - steps[0] > 0) {
                rangeStart = page - steps[0];
                rangeEnd = page + steps[2];
                if (rangeEnd > pages) {
                    rangeStart = pages - steps[4];
                    rangeEnd = pages;
                }
            } else if (page - steps[1] > 0) {
                rangeStart = page - steps[1];
                rangeEnd = page + steps[3];
            } else {
                rangeStart = page;
                rangeEnd = page + steps[4];
            }

            if (rangeStart < 1) {
                rangeStart = 1;
            }

            return Array.from({length: (rangeEnd - rangeStart + 1)}, (_, i) => i + rangeStart);
        }

    },

});