<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use FelOAIAssistantsManager\Service\OpenAI\OpenAIClientService as BaseClientService;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;
use Exception;

class OpenAIClientAdapter
{
    private ?string $threadId = null;

    public function __construct(
        private ?BaseClientService $baseClientService,
        private OpenAIConfigAdapter $aiConfig,
        private HttpClientInterface $httpClient,
        private LoggerInterface $logger
    ) {
    }

    public function setThreadId(string $threadId): self
    {
        $this->threadId = $threadId;
        return $this;
    }

    /**
     * Delete a thread
     *
     * @param string|null $threadId
     * @return array<string, mixed>
     */
    public function deleteThread(?string $threadId = null): array
    {
        $threadId ??= $this->threadId;

        if (!$threadId) {
            return ['message' => 'No thread'];
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for deleting thread');
            return $this->baseClientService->deleteThread($threadId);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to delete thread', [
                'error' => $e->getMessage(),
                'threadId' => $threadId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Check the status of a run
     *
     * @param string $runId
     * @return array<string, mixed>
     */
    public function checkRunStatus(string $runId): array
    {
        if (!$this->threadId) {
            return ['exception' => 'No thread ID set'];
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for checking run status');
            return $this->baseClientService->getRunStatus($this->threadId, $runId);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to check run status', [
                'error' => $e->getMessage(),
                'threadId' => $this->threadId,
                'runId' => $runId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Get messages from a thread
     *
     * @return array<string, mixed>
     */
    public function getThreadMessages(): array
    {
        if (!$this->threadId) {
            return ['exception' => 'No thread ID set'];
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for getting thread messages');

            // Get the raw messages from the API
            $messages = $this->baseClientService->getMessages($this->threadId);

            // Log the messages for debugging
            $this->logger->debug('OpenAIClientAdapter: Received thread messages', [
                'threadId' => $this->threadId,
                'messageCount' => count($messages['data'] ?? [])
            ]);

            return $messages;
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to get thread messages', [
                'error' => $e->getMessage(),
                'threadId' => $this->threadId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Submit tool outputs for a run
     *
     * @param string $threadId
     * @param string $runId
     * @param array<int, mixed> $output
     * @return array<string, mixed>
     */
    public function submitToolOutputs(string $threadId, string $runId, array $output): array
    {
        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for submitting tool outputs');
            return $this->baseClientService->submitToolOutputs($threadId, $runId, $output);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to submit tool outputs', [
                'error' => $e->getMessage(),
                'threadId' => $threadId,
                'runId' => $runId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Add a message to a thread
     *
     * @param string $role
     * @param mixed $content
     * @param string|null $threadId
     * @return array<string, mixed>
     */
    public function addThreadMessage(string $role, mixed $content, ?string $threadId = null): array
    {
        $threadId ??= $this->threadId;

        if (!$threadId) {
            return ['exception' => 'No thread ID set'];
        }

        // Handle empty messages
        if (empty(trim((string)$content))) {
            $content = 'Hello';
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for adding thread message');
            return $this->baseClientService->addThreadMessage($threadId, $role, $content);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to add thread message', [
                'error' => $e->getMessage(),
                'threadId' => $threadId,
                'role' => $role
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Add a message to a thread and create a run
     *
     * @param string $assistantId
     * @param string $threadId
     * @param string $role
     * @param mixed $content
     * @return array<string, mixed>
     */
    public function addMessageAndRun(string $assistantId, string $threadId, string $role, mixed $content): array
    {
        // Handle empty messages
        if (empty(trim((string)$content))) {
            $content = 'Hello';
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for adding message and creating run');
            return $this->baseClientService->addMessageAndRun($assistantId, $threadId, $role, $content);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to add message and create run', [
                'error' => $e->getMessage(),
                'threadId' => $threadId,
                'assistantId' => $assistantId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Create a thread, add a message, and create a run
     *
     * @param string $assistantId
     * @param string $role
     * @param string $prompt
     * @param string|null $cmsCategoryId
     * @return array<string, mixed>
     */
    public function createThreadRun(string $assistantId, string $role, string $prompt, ?string $cmsCategoryId = null): array
    {
        // Handle empty messages
        if (empty(trim($prompt))) {
            $prompt = 'Hello';
        }

        try {
            $this->setupBaseService();
            $this->logger->debug('OpenAIClientAdapter: Using base service for creating thread and run');

            // The base service doesn't support cmsCategoryId directly, so we'll need to handle it differently
            // For now, we'll just log it and use the base service's method
            if ($cmsCategoryId) {
                $this->logger->debug('OpenAIClientAdapter: cmsCategoryId provided but not used by base service', [
                    'cmsCategoryId' => $cmsCategoryId
                ]);
            }

            return $this->baseClientService->createThreadRun($assistantId, $role, $prompt);
        } catch (Exception $e) {
            $this->logger->error('OpenAIClientAdapter: Failed to create thread and run', [
                'error' => $e->getMessage(),
                'assistantId' => $assistantId
            ]);
            return ['exception' => $e->getMessage()];
        }
    }

    /**
     * Setup the base client service with the necessary configuration
     *
     * @throws Exception
     */
    private function setupBaseService(): void
    {
        if (!$this->baseClientService) {
            throw new Exception('Base OpenAI client service is not available');
        }

        $apiKey = $this->aiConfig->getApiKey();
        $baseUrl = $this->aiConfig->getApiBaseUrl();
        $betaVersion = $this->aiConfig->getOpenAiBetaVersion();

        if (!$apiKey || !$baseUrl) {
            throw new Exception('API key or base URL is not set');
        }

        $this->baseClientService->setBaseUrl($baseUrl);
        $this->baseClientService->setHeaders($apiKey, $betaVersion);
    }
}
