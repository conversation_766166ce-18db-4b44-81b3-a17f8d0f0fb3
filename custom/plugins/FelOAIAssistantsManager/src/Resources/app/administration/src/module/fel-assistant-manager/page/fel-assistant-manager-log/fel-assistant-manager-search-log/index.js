import template from './fel-assistant-manager-search-log.html.twig';
import './fel-assistant-manager-search-log.scss';

const { Component, Mixin, Context, Data: { Criteria } } = Shopware;

Component.register('fel-assistant-manager-search-log', {
    template,

    inject: ['repositoryFactory'],

    mixins: [
        Mixin.getByName('notification'),
        Mixin.getByName('fel-assistant-manager-openai-mixin'),
        Mixin.getByName('fel-assistant-manager-plugin-config-mixin'),
        Mixin.getByName('fel-assistant-manager-logs-repository-mixin'),
    ],

    data() {
        return {
            isLoading: true,
            total: 0,
            page: this.$route.query.page || 1,
            limit: this.$route.query.limit || 5,
            searchLogsTerms: [],
            productSearchTerms: [],
            assistantDetails: {},
            filterTerm: '',
            activeTab: 'search_logs'
        };
    },

    computed: {
        filteredSearchLogsTerms() {
            if (!this.filterTerm) return this.searchLogsTerms;
            const filter = this.filterTerm.toLowerCase();

            const criteria = new Criteria();
            criteria.setTotalCountMode(1);
            criteria.setPage(this.page);
            criteria.setLimit(this.limit);
            criteria.addFilter(Criteria.contains('assistantId', filter));
            criteria.addFilter(Criteria.contains('assistantId', 'search_logs_terms___'));

            // Filter for entries with null tagId (these are our search queries)
            criteria.addFilter(Criteria.equals('tagId', null));

            // Sort by createdAt to get newest first
            criteria.addSorting(Criteria.sort('createdAt', 'DESC'));

            // return this.searchLogsTerms.filter(item =>
            //     item.query.toLowerCase().includes(filter) ||
            //     item.assistantId.toLowerCase().includes(filter) ||
            //     (item.parsedQuery.tags && item.parsedQuery.tags.some(tag =>
            //         tag.toLowerCase().includes(filter)
            //     ))
            // );


        },

        filteredProductSearchTerms() {
            if (!this.filterTerm) return this.productSearchTerms;
            const filter = this.filterTerm.toLowerCase();
            return this.productSearchTerms.filter(item =>
                item.query.toLowerCase().includes(filter) ||
                item.assistantId.toLowerCase().includes(filter) ||
                (item.parsedQuery.categories && item.parsedQuery.categories.some(cat =>
                    cat.toLowerCase().includes(filter)
                )) ||
                (item.parsedQuery.properties && item.parsedQuery.properties.some(prop =>
                    prop.toLowerCase().includes(filter)
                )) ||
                (item.parsedQuery.manufacturer && item.parsedQuery.manufacturer.some(manu =>
                    manu.toLowerCase().includes(filter)
                ))
            );
        }
    },

    created() {
        this.initializeComponent();
    },

    methods: {

        async fetchProductQueriesExec() {
            // Create criteria to filter for search-related entries
            const criteria = new Criteria();
            criteria.setTotalCountMode(1);
            criteria.setPage(this.page);
            criteria.setLimit(this.limit);

            // Filter for entries with null tagId (these are our search queries)
            criteria.addFilter(Criteria.equals('tagId', null));

            criteria.addFilter(Criteria.contains('assistantId', 'product_search_terms___'));

            // Sort by createdAt to get newest first
            criteria.addSorting(Criteria.sort('createdAt', 'DESC'));

            // Fetch the data
            return this.tagUsageRepository.search(criteria, Context.api);
        },

        async fetchSearchLogsExec() {
            // Create criteria to filter for search-related entries
            const criteria = new Criteria();
            criteria.setTotalCountMode(1);
            criteria.setPage(this.page);
            criteria.setLimit(this.limit);

            // Filter for entries with null tagId (these are our search queries)
            criteria.addFilter(Criteria.equals('tagId', null));

            // Add OR filter for assistantId starting with our search prefixes
            const searchLogsCriteria = await Criteria.contains('assistantId', 'search_logs_terms___');
            criteria.addFilter(searchLogsCriteria);

            // Sort by createdAt to get newest first
            criteria.addSorting(Criteria.sort('createdAt', 'DESC'));

            // Fetch the data
            return this.tagUsageRepository.search(criteria, Context.api);
        },

        async fetchSearchLogs() {
            const response = await this.fetchSearchLogsExec();
            this.total = response.total;

            return response;
        },

        async fetchProductQueries() {
            const response = await this.fetchProductQueriesExec();
            this.total = response.total;

            return response;
        },

        async initializeComponent() {
            this.ensureTagUsageFactoryLoaded();

            try {
                let response = null;
                if (this.activeTab === 'search_logs') {
                    response = await this.fetchSearchLogs();
                } else {
                    response = await this.fetchProductQueries();
                }

                // Process the results
                this.searchLogsTerms = [];
                this.productSearchTerms = [];
                this.assistantDetails = {};

                // this.total = response.total;

                response.forEach(item => {
                    // Split the assistantId to get the function name and the rest
                    const [functionName, assistantIdWithQuery] = item.assistantId.split('___');

                    if (!assistantIdWithQuery) return; // Skip if format is invalid

                    // Split to get assistant ID and search query
                    const [assistantId, rawQuery] = assistantIdWithQuery.split('::');

                    if (!assistantId || !rawQuery) return; // Skip if format is invalid

                    // Track assistant details for later use
                    if (!this.assistantDetails[assistantId]) {
                        this.assistantDetails[assistantId] = {
                            id: assistantId,
                            searchCount: 0,
                            productSearchCount: 0
                        };
                    }

                    // Process based on function type
                    if (functionName === 'search_logs_terms') {
                        this.processSearchLogsTerm(item, assistantId, rawQuery);
                        this.assistantDetails[assistantId].searchCount++;
                    } else if (functionName === 'product_search_terms') {
                        this.processProductSearchTerm(item, assistantId, rawQuery);
                        this.assistantDetails[assistantId].productSearchCount++;
                    }
                });
            }
            catch (error) {
                this.catchErrors(error, 'errorFetchingTagUsages');
            }
            finally {
                this.isLoading = false;
            }
        },

        onNewItemActive(item) {
            this.activeTab = item;
            this.page = 1;
            this.initializeComponent();
        },

        onPageChange(page) {
            this.page = page.page;
            this.limit = page.limit;
            this.initializeComponent();
        },

        /**
         * Process a search logs term entry
         */
        processSearchLogsTerm(item, assistantId, rawQuery) {
            // Parse the query and tags
            let query = rawQuery;
            const tags = [];

            // Check if the query contains tags (separated by |)
            if (rawQuery.includes('|')) {
                const parts = rawQuery.split('|').map(part => part.trim());
                query = parts[0]; // First part is the query

                // Remaining parts are tags
                for (let i = 1; i < parts.length; i++) {
                    if (parts[i]) {
                        tags.push(parts[i]);
                    }
                }
            }

            // Ensure string fields are never null
            const ensureString = (value) => {
                return value === null || value === undefined ? '' : value;
            };

            // Create the search log entry
            const searchLogEntry = {
                id: item.id,
                assistantId: ensureString(assistantId),
                usageCount: item.usageCount || 1,
                writeAttempts: item.writeAttempts || 1,
                query: ensureString(rawQuery),
                parsedQuery: {
                    query: ensureString(query),
                    tags: tags.map(ensureString)
                },
                createdAt: item.createdAt,
                updatedAt: item.updatedAt
            };

            this.searchLogsTerms.push(searchLogEntry);
        },

        /**
         * Process a product search term entry
         */
        processProductSearchTerm(item, assistantId, rawQuery) {
            // Ensure string fields are never null
            const ensureString = (value) => {
                return value === null || value === undefined ? '' : value;
            };

            // Initialize the parsed query
            const parsedQuery = {
                query: '',
                categories: [],
                properties: [],
                manufacturer: [],
                price_min: null,
                price_max: null,
                limit: null,
                page: null,
                sort: ''
            };

            // Parse the query string
            const parts = rawQuery.split('|').map(part => part.trim());

            // First part is the base query
            if (parts.length > 0) {
                parsedQuery.query = ensureString(parts[0]);
            }

            // Process the remaining parts
            for (let i = 1; i < parts.length; i++) {
                const part = parts[i];

                if (part.startsWith('cats:')) {
                    // Categories
                    const categories = part.substring(5).trim().split(',')
                        .map(cat => ensureString(cat.trim()))
                        .filter(Boolean);
                    parsedQuery.categories = categories;
                } else if (part.startsWith('props:')) {
                    // Properties
                    const properties = part.substring(6).trim().split(' ')
                        .map(prop => ensureString(prop.trim()))
                        .filter(Boolean);
                    parsedQuery.properties = properties;
                } else if (part.startsWith('manu:')) {
                    // Manufacturer
                    const manufacturers = part.substring(5).trim().split(',')
                        .map(manu => ensureString(manu.trim()))
                        .filter(Boolean);
                    parsedQuery.manufacturer = manufacturers;
                } else if (part.startsWith('min-max:')) {
                    // Price range
                    const priceRange = part.substring(8).trim().split('-');
                    if (priceRange.length === 2) {
                        parsedQuery.price_min = parseInt(priceRange[0], 10) || null;
                        parsedQuery.price_max = parseInt(priceRange[1], 10) || null;
                    }
                } else if (part.startsWith('lim-pag:')) {
                    // Limit and page
                    const limitPage = part.substring(8).trim().split('-');
                    if (limitPage.length === 2) {
                        parsedQuery.limit = parseInt(limitPage[0], 10) || null;
                        parsedQuery.page = parseInt(limitPage[1], 10) || null;
                    }
                } else if (part.startsWith('srt:')) {
                    // Sort
                    parsedQuery.sort = ensureString(part.substring(4).trim());
                }
            }

            // Create the product search entry
            const productSearchEntry = {
                id: item.id,
                assistantId: ensureString(assistantId),
                usageCount: item.usageCount || 1,
                writeAttempts: item.writeAttempts || 1,
                query: ensureString(rawQuery),
                parsedQuery: parsedQuery,
                createdAt: item.createdAt,
                updatedAt: item.updatedAt
            };

            this.productSearchTerms.push(productSearchEntry);
        },

    },

    metaInfo() {
        return { title: this.$createTitle() };
    }
});
