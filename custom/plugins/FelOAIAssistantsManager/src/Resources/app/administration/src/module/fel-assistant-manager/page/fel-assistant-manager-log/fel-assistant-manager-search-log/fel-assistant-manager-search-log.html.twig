{% block fel_assistant_manager_search_log %}
<sw-page class="fel-assistant-manager-search-log fel-assistant-manager-log fel-assistant-manager">
    <template #smart-bar-actions>
    	<fel-api-loading-indicator
            :componentsAreLoading="isLoading || getManagerIsLoading()"
		></fel-api-loading-indicator>
    </template>
    <template #content>
        <template v-if="isLoading">
 			<div class="fel-full-height-skeleton-bar">
			    <mt-skeleton-bar v-for="idx in pluginConfig.setLoadSkeleton" :key="idx"/>
            </div>
        </template>
		<template v-else>
 			<div class="fel-assistant-manager-app-container">
                <div class="fel-box fel-p-3 fel-border-1 fel-border-radius-2">
                    <div class="fel-mb-3">
                        <h2 class="fel-mb-2 fel-size-s">{{ $t('fel-assistant-manager.search.title') }}</h2>
                        <p class="fel-mb-2 fel-size-xs">{{ $t('fel-assistant-manager.search.description') }}</p>

                        {# Filter #}
                        <div class="fel-mb-2">
                            <div class="fel-mt-1 fel-flex">
                                <label for="filterTerm"
                                    class="mt-button fel-border-radius-r-0 fel-border-r-0 fel-grid-row-bg-odd">
                                    {{ $t('fel-assistant-manager.search.searchFilter') }}
                                </label>
                                <input
                                    type="text"
                                    v-model="filterTerm"
                                    id="filterTerm"
                                    class="fel-p-1 fel-border-radius-0 fel-w-100"
                                    :class="{'fel-info-notification-border': filterTerm}"
                                    :title="$t('fel-assistant-manager.search.filterPlaceholder')"
                                    :placeholder="$t('fel-assistant-manager.search.filterPlaceholder')">
                                <mt-button
                                    v-if="filterTerm"
                                    ghost
                                    size="small"
                                    type="button"
                                    variant="critical"
                                    class="fel-px-2 fel-border-1 fel-border-l-0 fel-border-radius-l-0"
                                    :title="$t('fel-assistant-manager.general.delete')"
                                    :aria-label="$t('fel-assistant-manager.general.delete')"
                                    @click="resetFilterTerm">
                                    <mt-icon name="regular-times" size="10px" />
                                </mt-button>
                            </div>
                            <div class="fel-mt-1 fel-flex fel-size-xxs fel-text-muted">
                                <span class="fel-mr-2 fel-pr-2 fel-border-r-1">
                                    {{ $t('fel-assistant-manager.chat.total', { total: total }) }}
                                </span>
                                <span class="fel-mr-2 fel-pr-2">
                                    {{ $t('fel-assistant-manager.search.limitPage', { limit: limit, page: page + ' / ' + logPagesTotal }) }}
                                </span>
                            </div>
                        </div>

                        {# Tab navigation #}
                        <div class="fel-mb-3">
                            <mt-tabs
                                position="top"
                                :small="true"
                                :alignRight="false"
                                :defaultItem="activeTab"
                                @new-item-active="onNewItemActive"
                                :items="[
                                    {
                                        'label': $t('fel-assistant-manager.search.logsTab'),
                                        'name': 'search_logs'
                                    },{
                                        'label': $t('fel-assistant-manager.search.productSearchTab'),
                                        'name': 'product_search'
                                    }
                                ]">
                            </mt-tabs>
                        </div>
                    </div>

                    <div v-if="isLoading || getManagerIsLoading()" class="fel-full-height-skeleton-bar">
                        <mt-skeleton-bar/>
                    </div>

                    {# Search Logs Tab Content #}
                    <div v-if="activeTab === 'search_logs'" class="fel-search-logs-content">
                        <div v-if="!isLoading && !getManagerIsLoading() && filteredSearchLogsTerms.length === 0" class="fel-empty-state">
                            <p>{{ $t('fel-assistant-manager.search.noSearchLogsFound') }}</p>
                        </div>

                        <div dat-a-v-else
                        class="fel-search-logs-list">
                            <mt-card
                                v-for="item in filteredSearchLogsTerms"
                                :key="item.id"
                                class="fel-mb-3">
                                <template #title>
                                    <div class="fel-flex-between-center">
                                        <span class="fel-search-query">{{ item.parsedQuery.query }}</span>
                                        <span class="fel-badge fel-badge-info">{{ $t('fel-assistant-manager.search.usedCount', {'count': item.usageCount}) || `Used ${item.usageCount} times` }}</span>
                                    </div>

                                    <pre>{{ assistantDetails }}</pre>
                                    <pre>{{ item }}</pre>
                                </template>

                                <template #default>
                                    <div class="fel-search-details">

                                        <div v-if="item.parsedQuery.tags && item.parsedQuery.tags.length > 0"
                                            class="fel-p-2 fel-size-xxs fel-border-b-1">
                                            <strong>{{ $t('fel-assistant-manager.search.searchTags') }}:</strong>
                                            <span
                                                v-for="tag in item.parsedQuery.tags"
                                                :key="tag"
                                                class="fel-tag fel-mr-1">
                                                {{ tag }}
                                            </span>
                                        </div>

                                        <div class="fel-search-meta fel-text-muted fel-size-xxs fel-py-1 fel-px-2 fel-flex-between-center">
                                            <span>
                                                {{ $t('fel-assistant-manager.search.lastUsed') }}:
                                                {{ formatDate(item.updatedAt || item.createdAt) }}
                                            </span>
                                            <span>{{ item.assistantId }}</span>
                                        </div>
                                    </div>
                                </template>
                            </mt-card>
                        </div>
                    </div>

                    {# Product Search Tab Content #}
                    <div v-if="activeTab === 'product_search'" class="fel-product-search-content">

                        <div v-if="!isLoading && !getManagerIsLoading() && filteredProductSearchTerms.length === 0" class="fel-empty-state">
                            <p>{{ $t('fel-assistant-manager.search.noProductSearchFound') }}</p>
                        </div>

                        <div v-else class="fel-product-search-list">
                            <mt-card
                                v-for="item in filteredProductSearchTerms"
                                :key="item.id"
                                class="fel-mb-2">
                                <template #title>
                                    <div class="fel-flex fel-justify-content-between fel-align-items-center">
                                        <span class="fel-search-query">{{ item.parsedQuery.query }}</span>
                                        <span class="fel-badge fel-badge-info">{{ $t('fel-assistant-manager.search.usedCount', {'count': item.usageCount}) || `Used ${item.usageCount} times` }}</span>
                                    </div>
                                </template>

                                <template #default>
                                    <div class="fel-search-details">
                                        <div class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchAssistant') }}:</strong>
                                            {{ item.assistantId }}
                                        </div>

                                        <div v-if="item.parsedQuery.categories && item.parsedQuery.categories.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchCategories') }}:</strong>
                                            <span
                                                v-for="category in item.parsedQuery.categories"
                                                :key="category"
                                                class="fel-tag fel-tag-category fel-mr-1">
                                                {{ category }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.properties && item.parsedQuery.properties.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchProperties') }}:</strong>
                                            <span
                                                v-for="property in item.parsedQuery.properties"
                                                :key="property"
                                                class="fel-tag fel-tag-property fel-mr-1">
                                                {{ property }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.manufacturer && item.parsedQuery.manufacturer.length > 0" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.search.searchManufacturer') }}:</strong>
                                            <span
                                                v-for="manu in item.parsedQuery.manufacturer"
                                                :key="manu"
                                                class="fel-tag fel-tag-manufacturer fel-mr-1">
                                                {{ manu }}
                                            </span>
                                        </div>

                                        <div v-if="item.parsedQuery.price_min !== null || item.parsedQuery.price_max !== null" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.price_min') || 'Price Range' }}:</strong>
                                            {{ item.parsedQuery.price_min || '0' }} - {{ item.parsedQuery.price_max || '∞' }}
                                        </div>

                                        <div v-if="item.parsedQuery.sort" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.order') || 'Sorting' }}:</strong>
                                            {{ item.parsedQuery.sort }}
                                        </div>

                                        <div v-if="item.parsedQuery.limit !== null || item.parsedQuery.page !== null" class="fel-mb-2">
                                            <strong>{{ $t('fel-assistant-manager.filter.ui.limit') }}:</strong>
                                            {{ `Limit: ${item.parsedQuery.limit || 'default'}, Page: ${item.parsedQuery.page || '1'}` }}
                                        </div>

                                        <div class="fel-search-meta fel-text-muted">
                                            <small>
                                                {{ $t('fel-assistant-manager.search.lastUsed') }}:
                                                {{ new Date(item.updatedAt).toLocaleString() }}
                                            </small>
                                        </div>
                                    </div>
                                </template>
                            </mt-card>
                        </div>
                    </div>



                    <sw-pagination
                        v-if="!isLoading && !getManagerIsLoading()"
                        class="fel-pagination fel-px-1 fel-flex-center-center fel-border-0"
                        :total="total"
                        :limit="limit"
                        :page="page"
                        :steps="[5, 10, 25, 50, 100]"
                        @page-change="onPageChange"
                    ></sw-pagination>

                </div>
            </div>
            <fel-footer-component :isLoading="isLoading || getManagerIsLoading()"></fel-footer-component>
		</template>
    </template>
</sw-page>
{% endblock %}
