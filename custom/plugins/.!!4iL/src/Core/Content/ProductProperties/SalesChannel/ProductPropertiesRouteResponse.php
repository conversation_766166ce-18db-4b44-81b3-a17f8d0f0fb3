<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Core\Content\ProductProperties\SalesChannel;

use Shopware\Core\System\SalesChannel\StoreApiResponse;

class ProductPropertiesRouteResponse extends StoreApiResponse
{
    /**
     * @var array<string, mixed>
     */
    protected $object;

    /**
     * @param array<string, mixed> $data
     */
    public function __construct(array $data)
    {
        parent::__construct($data);
    }

    /**
     * @return array<string, mixed>
     */
    public function getProductProperties(): array
    {
        return $this->object;
    }
}
