"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["custom_plugins_FelAIProductAdvisor_src_Resources_app_storefront_src_assistant-plugin_cms-chatbo-f0d611"],{659:(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});class i extends window.PluginBaseClass{init(){console.log("Initializing CMS Chatbot Plugin"),this._registerEvents()}_registerEvents(){let t=this.el.querySelector(".fel-submit-chat-btn");t&&t.addEventListener("click",this._onSubmit.bind(this))}_onSubmit(t){let e=this.el.querySelector("#fel-chatbot-form");if(e){let t=document.createElement("input");t.type="hidden",t.name="cms-assistant-id",t.value=this.options.cmsAssistantId,e.appendChild(t)}}}}}]);