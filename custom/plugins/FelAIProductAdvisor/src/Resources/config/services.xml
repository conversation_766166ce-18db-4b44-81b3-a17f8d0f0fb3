<?xml version="1.0" encoding="UTF-8" ?>
<container xmlns="http://symfony.com/schema/dic/services"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="Fel\AIProductAdvisor\Service\CustomCookieProvider" decorates="Shopware\Storefront\Framework\Cookie\CookieProviderInterface">
            <argument type="service" id="Fel\AIProductAdvisor\Service\CustomCookieProvider.inner" />
            <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter"/>
            <argument type="service" id="Psr\Log\LoggerInterface"/>
        </service>



        <!-- Adapter for configuration compatibility -->
        <service id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <!-- <argument type="service" id="Fel\AIProductAdvisor\DataResolver\CmsAssistantIdResolver"/> -->
            <!-- <argument type="service" id="FelOAIAssistantsManager\Service\FelConfigService" on-invalid="null"/> -->
        </service>

        <!-- Use the base plugin's OpenAIClientService -->
        <service id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIClientAdapter" public="true">
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIClientService" on-invalid="null"/>
            <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter"/>
            <argument type="service" id="http_client"/>
            <argument type="service" id="Psr\Log\LoggerInterface"/>
        </service>

        <!-- Adapter for OpenAIRequiredAction -->
        <service id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIRequiredActionAdapter" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="http_client"/>
            <argument type="service" id="router"/>
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <argument type="service" id="sales_channel.repository"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction" on-invalid="null"/>
        </service>

        <!-- Refactored OpenAIService to use base plugin services -->
        <service id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIService" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="http_client"/>
            <argument type="service" id="router"/>
            <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter"/>
            <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIClientAdapter"/>
            <argument type="service" id="translator" />
            <!-- <argument type="service" id="Psr\Log\LoggerInterface"/>
            <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIRequiredActionAdapter"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService" on-invalid="null"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIRequiredAction" on-invalid="null"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIChatLogService" on-invalid="null"/> -->
        </service>

        <service id="Fel\AIProductAdvisor\Storefront\Controller\ChatbotController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <!-- <argument type="service" id="Fel\AIProductAdvisor\Service\OpenAI\OpenAIService"/> -->
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService"/>

            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <service id="Fel\AIProductAdvisor\DataResolver\CmsAssistantIdResolver" public="true">
            <argument type="service" id="logger" />
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
        </service>

        <!-- <service id="Fel\AIProductAdvisor\DataResolver\CmsChatbotConfigResolver" public="true">
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <tag name="shopware.cms.data_resolver" type="chatbot" priority="100" />
        </service>

        <service id="Fel\AIProductAdvisor\DataResolver\CmsCategoryResolver" public="true">
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <tag name="shopware.cms.data_resolver" type="category" priority="100" />
        </service> -->

        <!-- Custom API controller for product properties -->
        <!-- <service id="Fel\AIProductAdvisor\Storefront\Controller\Api\ProductPropertiesController" public="true">
            <argument type="service" id="category.repository"/>
            <argument type="service" id="product.repository"/>
            <argument type="service" id="property_group.repository"/>
            <argument type="service" id="product_manufacturer.repository"/>
            <argument type="service" id="Psr\Log\LoggerInterface"/>
            <tag name="controller.service_arguments"/>
        </service> -->

    </services>
</container>