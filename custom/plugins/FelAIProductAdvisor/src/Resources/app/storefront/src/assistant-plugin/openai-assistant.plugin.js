import Debouncer from 'src/helper/debouncer.helper';
import DomAccess from 'src/helper/dom-access.helper';
import Iterator from 'src/helper/iterator.helper';
import CookieStorageHelper from 'src/helper/storage/cookie-storage.helper';
import Storage from 'src/helper/storage/storage.helper';
import {COOKIE_CONFIGURATION_UPDATE} from 'src/plugin/cookie/cookie-configuration.plugin';
import HttpClient from 'src/service/http-client.service';

export default class FelAssistantPlugin extends window.PluginBaseClass {
	static options = {
		compactView: 640 > window.innerWidth,
		selector: {
			chatMessages:      '#fel-chatbot-messages',
			greetingContainer: '.fel-chatbot-greeting-container',
			loadingContainer:  '.fel-chatbot-load-container',
			submitChatButton:  '.fel-submit-chat-btn',
			chatThreadId:      '[name="fel-chatbot-thread-id"]',
			chatRunId:         '[name="fel-chatbot-run-id"]',
			chatUserInput:     '[name="fel-chatbot-user-input"]',
			chatMessageEl:     'chat-message-item',
			chatMessage:       'chat-message',
			cookieDefaultName: 'fel-chatbot-localstorage-accepted',
		},
		page: {
			refId: null,
		},
		storage: {
			chat: 'felChatStorage',
		}
	};

	/**
	 * Initialisiert das Chatbot-Theme basierend auf der CMS-Konfiguration
	 */
	initChatbotTheme() {
		// Prüfen, ob ein Theme-Farbwert in den Datenattributen vorhanden ist
		const chatbotElement = document.getElementById('fel-chatbot-advisor');
		if (!chatbotElement) return;

		// Lese die Theme-Farbe aus dem data-attribute
		const themeColor = chatbotElement.dataset.themeColor || '';

		if (themeColor) {
			// Setze die CSS-Variable für das Theme
			document.documentElement.style.setProperty('--fel-chatbot-theme-color', themeColor);
			// Setze auch die CSS-Variable für die Hover-Farbe
			document.documentElement.style.setProperty('--fel-chatbot-theme-color-hover', this._darkenColor(themeColor, 15));
			// Setze die RGB-Werte für die Theme-Farbe
			const rgbValues = this._hexToRgb(themeColor);
			if (rgbValues) {
				document.documentElement.style.setProperty('--fel-chatbot-theme-color-rgb', `${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}`);
			}
		}
	}

	init() {
		if (this.setPluginOptions()) {
			this.setPluginVars();
			this.loadTemplates();
			this.registerEvents();

			// Stelle sicher, dass die Theme-Farbe angewendet wird
			this.initChatbotTheme();
		}
	}

	registerEvents() {
		this.handleEvent = (event) => {
			if (this.isLoading && !event.target.classList.contains('fel-ui-btn')) {
				event.preventDefault();
				return false;
			}
			switch (event.type) {
				case 'submit': return this.chatEventCallback(event, event.target);
				case 'change': return this.changeEventCallback(event, event.target);
				case 'click':  return this.clickEventCallback(event, event.target);
			}
		};
		this.setEventHandler('add');
		this.setLocalStorageIsAllowed();
		this.swEventHelper();
	}

	swEventHelper() {
		document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, this.cookieCallback.bind(this));

		// Remove the ScrollUp plugin integration since we don't need the toggle button anymore
		// The chatbot will be visible all the time
	}

	setLocalStorageIsAllowed() {
		if (this.checkIfLocalStorageIsAllowed()) {
			this.assistantEl.classList.add('fel-localstorage-is-allowed');
			this.assistantEl.classList.remove('fel-localstorage-is-disabled');
			this.setCheckedConsentLocalStorageTemplate(true);
			if (this.assistantEl.classList.contains('active')) {
				this.storageSet(`${this.options.storage.chat}_chatOpen`, 'active');
			}
			if (this.assistantEl.classList.contains('fel-zoom-chat')) {
				this.storageSet(`${this.options.storage.chat}_chatZoom`, 'active');
			}
		} else {
			this.assistantEl.classList.add('fel-localstorage-is-disabled');
			this.assistantEl.classList.remove('fel-localstorage-is-allowed');
			this.setCheckedConsentLocalStorageTemplate(false);
			this.clearLocalStorage();
		}
	}

	cookieCallback(updatedCookies) {
		var cookieName = this.options.selector.cookieDefaultName;
		if (typeof updatedCookies.detail[cookieName] !== 'undefined') {
			this.localStorageAllowed = updatedCookies.detail[cookieName];
			if (this.localStorageAllowed) {
				this.enableLocalStorage();
			}
		} else {
			this.localStorageAllowed = false;
			this.clearLocalStorage();
		}
		var getConsentEl = this.domGet(this.assistantEl, '[name="fel-allow-localstorage"]', false);
		if (getConsentEl) {
			getConsentEl.checked = this.localStorageAllowed ? true : null ;
			this.setCheckedConsentLocalStorageTemplate(this.localStorageAllowed);
		}
	}

	checkIfLocalStorageIsAllowed() {
		return CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
	}

	setCheckedConsentLocalStorageTemplate(localstorage) {
		var getMainStore = this.assistantEl.querySelector('[name="fel-allow-localstorage"]');
		if (getMainStore) {
			getMainStore.checked = localstorage ? true : false;
		}
		this.template.greeting.querySelector('[name="fel-allow-localstorage"]').checked = localstorage ? true : false;
	}

	toggleLocalStorage(event, el) {
		if (el.checked) CookieStorageHelper.setItem(this.options.selector.cookieDefaultName, 'allowed');
		else CookieStorageHelper.removeItem(this.options.selector.cookieDefaultName);
		this.setCheckedConsentLocalStorageTemplate(el.checked);
		this.setLocalStorageIsAllowed();
		el.blur();
	}

	/**
	 * Chat handler
	 */
    chatEventCallback(event, el) {
		try {
			return this._chatEventCallback(event, el);
		} catch (error) {
			return this.putToMessages('chatbot', el.message || 'error');
		}
	}

	checkIfMessageThrown(response) {
		return (response.error && 'undefined' !== typeof response.error.exception) ? response.error.exception : false ;
	}

    _chatEventCallback(event, el) {
		if ('fel-chatbot-form' === el.id) {
			event.preventDefault();

			if (this.getChatbotUserInput()) {
				var userInput = this.getChatbotUserInput().value;
				var systemPrefix = document.querySelector('[name="system-prefix"]').value;
				var displayMessage = userInput; // Store original user input for display

				// Only add system prefix for the first message in a thread
				if (!this.threadId || !this.getChatbotThreadId().value) {
					// Add system prefix to the message sent to backend
					userInput = systemPrefix + userInput;
				}

				// Display user's message in the chat (without system prefix)
				this.putToMessages('user', displayMessage);

				var sendMessage = {
					threadId:  this.getChatbotThreadId().value,
					runId:     this.getChatbotRunId().value,
					userInput: userInput, // This contains the system prefix for the first message
					assiId:    document.querySelector('[name="fel-chatbot-assistant-id"]').value,
				};




				if ('' === sendMessage.runId) {
					this.isLoadingResponse = false;
					this.clientPost(`${this.controllerUrl}/create-thread`, sendMessage, response => {
						if (response) {
							this.$emitter.publish('felAssistantThreadCreated');
							if (this.checkIfMessageThrown(response)) {
								return this.putToMessages('chatbot', this.checkIfMessageThrown(response), '', '', ' fel-system-exception');
							} else if ('undefined' !== typeof response.id) {
								this.threadId = response.id;
								// Clear the input field before dispatching the next request
								this.getChatbotUserInput().value = '';
								this.getChatbotRunId().value = response.runId;
								this.getChatbotThreadId().value = response.threadId;
								this.assistantEl.classList.add('contains-thread');

								// Create a new run request with the original userInput
								// We need to include the userInput again for the run request
								var originalUserInput = sendMessage.userInput;

								var runMessage = {
									threadId: response.threadId,
									runId: response.runId,
									userInput: originalUserInput, // Include the original message
									assiId: document.querySelector('[name="fel-chatbot-assistant-id"]').value,
								};

								// Send the run request
								this.isLoadingResponse = true;
								this.clientPost(`${this.controllerUrl}/run`, runMessage, runResponse => {
									this.handleRunResponse(runResponse);
								});

								return;
							}
						}
					});
				}
				else {
					if (this.threadId) {
						this.isLoadingResponse = true;
						// Don't reset the runId here, as it's needed to track the conversation
						// this.getChatbotRunId().value = '';

						// Clear the input field before sending the request
						this.getChatbotUserInput().value = '';

						this.scrollThreadIntoView('smooth', true);
						this.clientPost(`${this.controllerUrl}/run`, sendMessage, response => {
							this.handleRunResponse(response);
						});
					}
				}
			}
			return false;
		}
    }

	replaceAiProductList(data, productFetch) {

		// Create a custom product listing template
		if (productFetch && productFetch.items && productFetch.items.products && productFetch.items.products.length > 0) {
			// Create a context object for the template
			const templateContext = {
				products: productFetch.fetch.elements || [],
				productsResponse: productFetch,
				isSearch: 'product_search'
			};

			// Create a URL to fetch the template
			const templateUrl = '/fel-product-listing-template';

			// Make a request to fetch the template
			this._client.get(templateUrl, response => {
				try {
					// Replace the original HTML with the template
					data.value = response;
					// Update the message in the chat
					const chatMessages = this.getChatbotMessagesEl();
					if (chatMessages) {
						const lastMessage = chatMessages.querySelector('.chatbot-message-item:last-child .chatbot-message .fel-inner');
						if (lastMessage) {
							lastMessage.innerHTML = data.value;
						}
					}
				} catch (error) {
					// Fallback to original behavior
					var replaceUl = document.createElement('div');
					var replacWith = document.createElement('div');
					replaceUl.insertAdjacentHTML('beforeend', data.originalValue);
					replacWith.insertAdjacentHTML('beforeend', data.value);
					if (replaceUl.querySelector('ul')) {
						var ulsToString = replaceUl.querySelectorAll('ul');
						if (ulsToString.length) {
							if (1 === ulsToString.length &&
								'undefined' !== typeof productFetch.items &&
								'undefined' !== typeof productFetch.items.products &&
								productFetch.items.products.length
							) {
								for (var i = 0; i < productFetch.items.products.length; i++) {
									if (data.originalValue.includes(productFetch.items.products[i].id)) {
										replaceUl.querySelector('ul').replaceWith(replacWith);
										data.value = replaceUl.innerHTML;
										break;
									}
								}
							} else {
								data.value = data.originalValue;
							}
						}
					}
				}
			});
		}

		return data;
	}

	uiActionRequired(uiAction) {
		if ('undefined' !== typeof uiAction.redirectTo && uiAction.redirectTo) {
			if (uiAction.redirectTo !== location.href) {
				location.href = uiAction.redirectTo;
			}
		}
	}

	deleteChatThread() {
		var getThreadId = this.getChatbotThreadId().value;

		if (getThreadId) {
			this.addIsLoading();
			this._client.post(`${this.controllerUrl}/delete-thread`, JSON.stringify({threadId: getThreadId}), response => {
				this.removeIsLoading();
				if (response) {
					response = JSON.parse(response);
					this.assistantEl.classList.remove('contains-thread');
					this.getChatbotRunId().value = '';
					this.getChatbotThreadId().value = '';
					this.chatClearStorage();
					if (this.getChatbotMessagesEl()) {
						this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
					}
					// Clear the input field
					this.getChatbotUserInput().value = '';

					// Scroll to the bottom of the chat
					this.scrollThreadIntoView();
				}
			});
		} else {
			this.chatClearStorage();
			this.assistantEl.classList.remove('contains-thread');
			if (this.getChatbotMessagesEl()) {
				this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
			}
		}
	}

	clientPost(url, sendMessage, callback) {
		this.addIsLoading();
		this._client.post(url, JSON.stringify(sendMessage), response => {
			this.removeIsLoading();
			if (response) {
				try {
					var parse = JSON.parse(response);
					if (parse) {
						response = parse;
					}
				} catch (error) {
					var div = document.createElement('div');
					div.appendChild(document.createTextNode(error));
					return callback({error: {exception: div.innerHTML.substring(0, 200)}});
				}
			}
			return callback(response);
		});
	}

	scrollIntoView(selector, timeout=100, setBehavior='smooth', setBehaviorObj=null) {
		// Diese Methode wird nicht mehr verwendet
	}

	scrollThreadIntoView(setBehavior, force) {
		// Get the chat container
		const chatContainer = this.getChatbotMessagesEl();
		if (chatContainer) {
			// Get all messages
			const messages = chatContainer.querySelectorAll('.chat-message-item');

			// Find the last user message
			let lastUserMessage = null;
			for (let i = messages.length - 1; i >= 0; i--) {
				if (messages[i].classList.contains('fel-user')) {
					lastUserMessage = messages[i];
					break;
				}
			}

			// If we found a user message, scroll to show it at the top
			if (lastUserMessage) {
				// Position the user message at the top of the visible area with some padding
				const scrollPosition = lastUserMessage.offsetTop - 10; // 10px padding from top

				chatContainer.scrollTo({
					top: scrollPosition,
					behavior: setBehavior || 'smooth'
				});
				return;
			}

			// Fallback: If no user message found, scroll to the last message
			const lastMessage = messages[messages.length - 1];
			if (lastMessage) {
				const scrollPosition = lastMessage.offsetTop - 20;
				chatContainer.scrollTo({
					top: scrollPosition,
					behavior: setBehavior || 'smooth'
				});
			}
		}
	}

	addIsLoading() {
		var useTemplate = this.template.loading;
		this.isLoading = true;
		this.assistantEl.classList.add('fel-chatbot-is-loading');
		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = true;
			this.getChatbotUserInput().disabled = true;
		}
		this.removeGreetingContainer();
		if (this.isLoadingResponse) {
			var cloneLoader = useTemplate.cloneNode(true);
			var getInfoBox = this.domGet(cloneLoader, '.fel-chatbot-loader-info', false);
			if (getInfoBox && getInfoBox.dataset.secondRun) {
				this.domGet(cloneLoader, '.fel-chatbot-loader-info').innerHTML = getInfoBox.dataset.secondRun;
			}
			useTemplate = cloneLoader;
		}

		this.putToMessages('loading', useTemplate.outerHTML);

		// Scroll to show the loading indicator
		setTimeout(() => {
			this.scrollThreadIntoView();
		}, 50);
	}

	removeIsLoading() {
		this.isLoading = false;
		this.assistantEl.classList.remove('fel-chatbot-is-loading');
		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = null;
			this.getChatbotUserInput().disabled = null;
		}
		var getLoader = this.domGetAll(this.assistantEl, '.fel-chatbot-load-container', false);
		if (getLoader.length) {
			for (let i = 0; i < getLoader.length; i++) {
				getLoader[i].classList.add('position-absolute', 'w-100');
				this.debounce(() => {getLoader[i].remove()}, 150);
			}
		}
	}

	removeGreetingContainer() {
		var checkGreetings = this.domGet(this.assistantEl, this.options.selector.greetingContainer, false);
		if (checkGreetings) {
			checkGreetings.remove();
			this.assistantEl.classList.remove('fel-chat-initial');
		}
	}

	getChatSubmitButton() {
		return this.domGet(this.assistantEl, this.options.selector.submitChatButton, false);
	}

    getChatbotMessagesEl() {
        return this.domGet(this.assistantEl, this.options.selector.chatMessages);
    }

	/**
	 * Process buttons in the message
	 * @param {string} messageText - The message text
	 * @returns {string} - The processed message text
	 */
	processButtonsInMessage(messageText) {
		if (!messageText) {
			return messageText;
		}

		// Replace [BUTTON:Text] with actual buttons
		const buttonRegex = /\[BUTTON:([^\]]+)\]/g;
		return messageText.replace(buttonRegex, (match, buttonText) => {
			return `<button type="button" class="btn btn-outline-primary fel-btn" data-callback="chatSendMessage">${buttonText}</button>`;
		});
	}

    getChatbotUserInput() {
        return this.domGet(this.chatbotForm, this.options.selector.chatUserInput);
    }

	getChatbotThreadId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatThreadId);
    }

	getChatbotRunId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatRunId);
    }

	// click callbacks

	addToChatSubmit(event, el) {
		var getStrToSet = el.dataset.prompt;
		if (getStrToSet) {
			this.getChatbotUserInput().value = getStrToSet;
			this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
		}
	}

	/**
	 * Send a message directly from a button click
	 * @param {Event} event - The click event
	 * @param {HTMLElement} el - The button element
	 */
	chatSendMessage(event, el) {
		const buttonText = el.textContent.trim();
		if (buttonText) {
			this.getChatbotUserInput().value = buttonText;
			this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
		}
	}

	/**
	 * Show all products when the "Show All Products" button is clicked
	 * @param {Event} event - The click event
	 * @param {HTMLElement} el - The button element
	 */
	showAllProducts(event, el) {
		// Get all product IDs from the button's data attribute
		const productIdsString = el.dataset.productIds;
		if (!productIdsString) return;

		// Convert the comma-separated string to an array
		const productIds = productIdsString.split(',');
		if (productIds.length === 0) return;

		// Find the parent product listing container
		const productListingContainer = el.closest('.fel-product-listing');
		if (!productListingContainer) return;

		// Get the product list element
		const productList = productListingContainer.querySelector('.fel-product-list');
		if (!productList) return;

		// Remove the "Show All Products" button
		el.remove();

		// Generate HTML for all products that aren't already displayed
		const existingProductElements = productList.querySelectorAll('.fel-product-item');
		const existingProductIds = Array.from(existingProductElements).map(item => item.dataset.productId);
		const newProductIds = productIds.filter(id => !existingProductIds.includes(id));

		// Add new product elements to the list
		newProductIds.forEach(productId => {
			const productHtml = `
			<div class="fel-product-item" data-product-id="${productId}">
				<div class="fel-product-image">
					<!-- SVG-Platzhalterbild wird angezeigt, bis das echte Produktbild geladen ist -->
					<svg id="meteor-icon-kit__regular-image" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 20.4948L9.25259 12.3356C9.63485 11.9056 10.3003 11.886 10.7071 12.2929L12.7071 14.2929C12.7219 14.3077 12.7361 14.3228 12.7498 14.3382L15.2318 11.3598C15.6082 10.9081 16.2913 10.8771 16.7071 11.2929L22 16.5858V3C22 2.44772 21.5523 2 21 2H3C2.44772 2 2 2.44772 2 3V20.4948ZM3.33795 22H21C21.5523 22 22 21.5523 22 21V19.4142L16.0672 13.4814L11.7682 18.6402C11.4147 19.0645 10.7841 19.1218 10.3598 18.7682C9.93554 18.4147 9.87821 17.7841 10.2318 17.3598L11.4842 15.857C11.416 15.8159 11.3517 15.7659 11.2929 15.7071L10.0428 14.457L3.33795 22ZM3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3C0 1.34315 1.34315 0 3 0ZM7.5 11C5.567 11 4 9.433 4 7.5C4 5.567 5.567 4 7.5 4C9.433 4 11 5.567 11 7.5C11 9.433 9.433 11 7.5 11ZM7.5 9C8.32843 9 9 8.32843 9 7.5C9 6.67157 8.32843 6 7.5 6C6.67157 6 6 6.67157 6 7.5C6 8.32843 6.67157 9 7.5 9Z" fill="#758CA3"/></svg>
					<!-- Das echte Produktbild wird hier eingefügt, wenn es geladen ist -->
				</div>
				<div class="fel-product-info">
					<div class="fel-product-name">Produkt wird geladen...</div>
					<div class="fel-product-price">Preis wird geladen...</div>
					<div class="fel-product-manufacturer">Hersteller wird geladen...</div>
					<div class="fel-product-description">Beschreibung wird geladen...</div>
					<a href="/detail/${productId}" class="fel-product-button">Details ansehen</a>
				</div>
			</div>`;

			// Add the product element to the list
			productList.insertAdjacentHTML('beforeend', productHtml);
		});

		// Initialize product loading for the new products
		this.initProductLoading(newProductIds);

		// Scroll to show the new products
		this.scrollThreadIntoView();
	}

	toggleChatbot(event, el) {
		var getToggleClass = el.dataset.toggleClass;
		if (getToggleClass) {
			this.assistantEl.classList.toggle(getToggleClass);
			var useStorageName = `${this.options.storage.chat}_chatOpen`;
			if (this.assistantEl.classList.contains('active')) {
				this.storageSet(useStorageName, 'active');
				this.scrollThreadIntoView();
			} else {
				this.storageSet(useStorageName, 'not-active');
			}
		}
	}

	toggleChatZoom(event, el) {
		el.classList.toggle('fel-active');
		if (this.assistantEl) {
			var useStorageName = `${this.options.storage.chat}_chatZoom`;
			if (el.classList.contains('fel-active')) {
				this.assistantEl.classList.add('fel-zoom-chat');
				this.storageSet(useStorageName, 'active');
			} else {
				this.assistantEl.classList.remove('fel-zoom-chat');
				this.storageSet(useStorageName, 'not-active');
			}
			this.scrollThreadIntoView();
		}
	}

	removeChatMessage(event) {
		if ('undefined' !== typeof event.target.offsetParent && 'undefined' !== typeof event.target.offsetParent.offsetParent) {
			var targetParent = event.target.offsetParent.offsetParent;
			if (targetParent) {
				if (targetParent.previousElementSibling.classList.contains('user-message-item')) {
					targetParent.previousElementSibling.remove();
				}
				targetParent.remove();
				this.chatToStorage(this.getChatbotThreadId().value);
				var getStorage = this.storageGet(this.options.storage.chat);
				if ('' == getStorage.trim()) {
					this.deleteChatThread();
				}
				this.scrollThreadIntoView();
			}
		}
	}

	clickEventCallback(event, el) {
		// Prüfe, ob es sich um den "Alle Produkte anzeigen"-Button handelt
		if (el.classList.contains('fel-show-all-products')) {
			event.preventDefault();
			return this.showAllProducts(event, el);
		}

		if (!el.classList.contains('fel-btn')) {
			return;
		}

		var callback = el.dataset.callback;

		if (el.dataset.preventDefault) event.preventDefault();
		if (el.dataset.stopPropagation) event.stopPropagation();
		if (el.dataset.blur) el.blur();

		if (callback) {
			if (typeof this[callback] === 'function') {
				return this[callback](...arguments);
			} else if (typeof window[callback] === 'function') {
				return window[callback](...arguments);
			}
		}
	}

	changeEventCallback(event, el) {
		if (el.classList.contains('fel-checkbox')) {
			event.preventDefault();
			var callbackFn = el.dataset.callback;
			if ('function' === typeof this[callbackFn]) {
				this[callbackFn](event, el);
			}
			return false;
		}
	}

	/**
	 * Verarbeitet die Antwort der KI und wandelt [BUTTON:Text]-Muster in echte Buttons um
	 * @param {string} message - Die Nachricht der KI
	 * @return {string} - Die verarbeitete Nachricht mit HTML-Buttons
	 */
	processButtonsInMessage(message) {
		if (!message || typeof message !== 'string') {
			return message;
		}

		// Suche nach [BUTTON:Text] Mustern
		const buttonPattern = /\[BUTTON:(.*?)\]/g;
		let processedMessage = message;

		// Sammle alle Button-Texte
		const buttons = [];
		let match;
		while ((match = buttonPattern.exec(message)) !== null) {
			buttons.push(match[1]);
		}

		// Entferne die [BUTTON:Text] Muster aus der Nachricht
		processedMessage = processedMessage.replace(buttonPattern, '');

		// Wenn Buttons gefunden wurden, erstelle ein separates div für die Buttons
		if (buttons.length > 0) {
			// Prüfe, ob die Nachricht bereits in einem fel-text-plain div ist
			if (processedMessage.includes('class="fel-text-plain')) {
				// Extrahiere den Text aus dem fel-text-plain div
				const tempDiv = document.createElement('div');
				tempDiv.innerHTML = processedMessage;
				const textPlainDiv = tempDiv.querySelector('.fel-text-plain');

				if (textPlainDiv) {
					// Erstelle das Buttons-HTML
					let buttonsHtml = '<div class="fel-chatbot-buttons-container mt-3">';

					buttonsHtml += '<div class="fel-chatbot-buttons">';
					buttons.forEach(buttonText => {
						buttonsHtml += `<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"
							data-callback="addToChatSubmit" data-prompt="${buttonText}">${buttonText}</button>`;
					});
					buttonsHtml += '</div></div>';

					// Füge die Buttons nach dem fel-text-plain div ein
					processedMessage = tempDiv.innerHTML + buttonsHtml;
				}
			} else {
				// Erstelle das Buttons-HTML
				let buttonsHtml = '<div class="fel-chatbot-buttons-container mt-3">';

				buttonsHtml += '<div class="fel-chatbot-buttons">';
				buttons.forEach(buttonText => {
					buttonsHtml += `<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"
						data-callback="addToChatSubmit" data-prompt="${buttonText}">${buttonText}</button>`;
				});
				buttonsHtml += '</div></div>';

				// Füge die Buttons am Ende der Nachricht hinzu
				processedMessage += buttonsHtml;
			}
		}

		return processedMessage;
	}



	/**
	 * Überschreibe die putToMessages-Methode, um Buttons zu verarbeiten
	 * @param {*} messageFrom ['user', 'chatbot']
	 * @param {*} message
	 */
	putToMessages(messageFrom, message, id='', datetime='', addClass='') {
		// Überprüfen, ob die Nachricht leer ist
		if (!message || message.trim() === '') {
			return;
		}

		// Verarbeite JSON-Nachrichten und extrahiere den Output
		let processedMessage = message;
		let jsonData = null;

		// Versuche, die Nachricht als JSON zu parsen
		try {
			if (message.trim().startsWith('{') && message.trim().endsWith('}')) {
				jsonData = JSON.parse(message);

				// Extrahiere den Output aus dem JSON
				if (jsonData.output) {
					processedMessage = jsonData.output;
				}
			}
		} catch (e) {
			// Error parsing JSON message
		}

		// Verarbeite Buttons in allen Nachrichten
		// Zuerst verarbeiten wir spezielle Produktsuchergebnisse in Chatbot-Nachrichten
		if (messageFrom === 'chatbot') {
			// Prüfe, ob es sich um ein Produkt-Suchergebnis handelt
			if (jsonData && jsonData.request_type === 'product_search') {
				// Extrahiere die Produkt-IDs aus dem HTML-Output
				const productIds = this.extractProductIdsFromHtml(processedMessage);

				if (productIds.length > 0) {
					// Ersetze den Output mit unserem benutzerdefinierten Produkt-Template
					const productTemplate = this.generateProductTemplate(productIds, processedMessage);

					// Erstelle ein spezielles Template für Produktanzeigen
					if (id) {
						id = ` id="${id}"`;
					}
					if (datetime) {
						datetime = ` data-datetime="${datetime}"`;
					}
					var setDeleteBtn = `<div class="fel-delete-message">
						<span title="${this.options.translation.removeChatMessage}"
							class="btn fel-btn" data-callback="removeChatMessage">X</span>
					</div>`;

					var template = `
					<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex"${id}>
						<div class="${this.options.selector.chatMessage} ${messageFrom}-message${addClass}">
							<div class="fel-inner"${datetime}>
								${setDeleteBtn}
								<div class="fel-product-container">${productTemplate}</div>
							</div>
						</div>
					</div>`;

					// Füge das Template direkt ein
					this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);

					// Initialisiere das Laden der Produktdaten
					this.initProductLoading(productIds);

					// Aktualisiere die Theme-Farbe für alle Chat-Buttons
					setTimeout(() => {
						this.updateChatButtonsTheme();
						// Scroll to the bottom of the chat after adding a message
						this.scrollThreadIntoView();
					}, 50);

					// Beende die Funktion hier, da wir das Template bereits eingefügt haben
					return;
				}
			}

			// Verarbeite Buttons in der Nachricht
			processedMessage = this.processButtonsInMessage(processedMessage);
		}

		if (id) {
			id = ` id="${id}"`;
		}
		if (datetime) {
			datetime = ` data-datetime="${datetime}"`;
		}
		var setDeleteBtn = 'chatbot' === messageFrom
			? `<div class="fel-delete-message">
				<span title="${this.options.translation.removeChatMessage}"
					class="btn fel-btn" data-callback="removeChatMessage">X</span>
			</div>` : '' ;

		var template = ['loading', 'greeting'].includes(messageFrom)
			? processedMessage : `
			<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex ${messageFrom === 'user' ? 'fel-user' : ''}"${id}>
				<div class="${this.options.selector.chatMessage} ${messageFrom}-message${addClass}">
					<div class="fel-inner"${datetime}>
						${setDeleteBtn}
						${processedMessage}
					</div>
				</div>
			</div>`;
		this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);

		// Wende die Theme-Farbe auf neue Benutzer-Nachrichten an
		if (messageFrom === 'user') {
			const chatbotElement = document.getElementById('fel-chatbot-advisor');
			if (chatbotElement) {
				const themeColor = chatbotElement.dataset.themeColor || '';
				if (themeColor) {
					const userMessages = this.getChatbotMessagesEl().querySelectorAll('.user-message .fel-inner');
					const lastMessage = userMessages[userMessages.length - 1];
					if (lastMessage) {
						lastMessage.style.backgroundColor = this._darkenColor(themeColor, 25);
						lastMessage.style.boxShadow = `0 0 4px ${themeColor}`;
					}
				}
			}
		}

		// Aktualisiere die Theme-Farbe für alle Chat-Buttons
		if (messageFrom === 'chatbot') {
			// Warte kurz, bis das DOM aktualisiert wurde
			setTimeout(() => {
				this.updateChatButtonsTheme();
				// Scroll to the bottom of the chat after adding a message
				this.scrollThreadIntoView();
			}, 50);
		} else {
			// Scroll to the bottom of the chat immediately for user messages
			this.scrollThreadIntoView();
		}
	}

	/**
	 * Extrahiert Produkt-IDs aus dem HTML-Output
	 * @param {string} html - Der HTML-Output aus der KI-Antwort
	 * @returns {Array} - Ein Array mit Produkt-IDs
	 */
	extractProductIdsFromHtml(html) {
		const productIds = [];

		// Match product IDs in detail URLs
		const detailRegex = /\/detail\/([a-f0-9]{32})/g;
		let match;
		while ((match = detailRegex.exec(html)) !== null) {
			productIds.push(match[1]);
		}

		// Also try to match product IDs in href attributes
		const hrefRegex = /href="[^"]*\/detail\/([a-f0-9]{32})"/g;
		while ((match = hrefRegex.exec(html)) !== null) {
			if (!productIds.includes(match[1])) {
				productIds.push(match[1]);
			}
		}

		return productIds;
	}

	/**
	 * Generiert ein benutzerdefiniertes Produkt-Template
	 * @param {Array} productIds - Ein Array mit Produkt-IDs
	 * @param {string} originalHtml - Der ursprüngliche HTML-Output
	 * @returns {string} - Das generierte HTML-Template
	 */
	/**
	 * Verarbeitet Buttons in einer Nachricht
	 * @param {string} message - Die zu verarbeitende Nachricht
	 * @returns {string} - Die verarbeitete Nachricht
	 */
	processButtonsInMessage(message) {
		// Wenn die Nachricht ein JSON-Objekt ist, extrahiere den Output
		let processedMessage = message;
		try {
			if (message.trim().startsWith('{') && message.trim().endsWith('}')) {
				const jsonData = JSON.parse(message);
				if (jsonData.output) {
					// Verarbeite nur den Output-Teil
					jsonData.output = this._processButtonsInHtml(jsonData.output);
					processedMessage = JSON.stringify(jsonData);
				}
			} else {
				// Verarbeite die gesamte Nachricht als HTML
				processedMessage = this._processButtonsInHtml(message);
			}
		} catch (e) {
			// Fallback: Verarbeite die gesamte Nachricht als HTML
			processedMessage = this._processButtonsInHtml(message);
		}
		return processedMessage;
	}

	/**
	 * Verarbeitet Buttons in HTML
	 * @param {string} html - Der zu verarbeitende HTML-String
	 * @returns {string} - Der verarbeitete HTML-String
	 */
	_processButtonsInHtml(html) {
		// Sammle alle [BUTTON:Text] Muster
		const buttonRegex = /\[BUTTON:(.*?)\]/g;
		const buttons = [];
		let match;

		// Kopiere das Original-HTML
		let processedHtml = html;

		// Sammle alle Buttons
		while ((match = buttonRegex.exec(html)) !== null) {
			buttons.push(match[1]);
		}

		// Entferne die [BUTTON:Text] Muster aus dem HTML
		processedHtml = processedHtml.replace(buttonRegex, '');

		// Erstelle ein temporäres DOM-Element, um das HTML zu parsen
		const tempDiv = document.createElement('div');
		tempDiv.innerHTML = processedHtml;

		// Verarbeite alle Links mit onclick-Attributen
		const links = tempDiv.querySelectorAll('a[onclick]');
		links.forEach(link => {
			// Entferne das onclick-Attribut
			const onclickValue = link.getAttribute('onclick');
			link.removeAttribute('onclick');

			// Extrahiere den Text aus dem alert-Aufruf, falls vorhanden
			let buttonText = link.textContent.trim();
			const alertMatch = onclickValue.match(/alert\(['"](.*?)['"]\)/i);
			if (alertMatch && alertMatch[1]) {
				// Verwende den Text aus dem alert, falls vorhanden
				buttonText = alertMatch[1];
			}

			// Erstelle einen neuen Button und ersetze den Link
			const button = document.createElement('button');
			button.setAttribute('type', 'button');
			button.setAttribute('data-prompt', buttonText);
			button.setAttribute('data-callback', 'addToChatSubmit');
			button.classList.add('btn', 'btn-outline-primary', 'fel-btn');
			button.textContent = buttonText;

			// Ersetze den Link durch den Button
			link.parentNode.replaceChild(button, link);
		});

		// Entferne alle Escape-Zeichen aus Klassen und Attributen
		const allElements = tempDiv.querySelectorAll('*');
		allElements.forEach(element => {
			// Entferne Escape-Zeichen aus class-Attributen
			if (element.hasAttribute('class')) {
				const classValue = element.getAttribute('class');
				if (classValue.includes('\"')) {
					element.setAttribute('class', classValue.replace(/\"/g, ''));
				}
			}

			// Entferne Escape-Zeichen aus data-prompt-Attributen
			if (element.hasAttribute('data-prompt')) {
				const promptValue = element.getAttribute('data-prompt');
				if (promptValue.includes('\"')) {
					element.setAttribute('data-prompt', promptValue.replace(/\"/g, ''));
				}
			}

			// Entferne Escape-Zeichen aus data-callback-Attributen
			if (element.hasAttribute('data-callback')) {
				const callbackValue = element.getAttribute('data-callback');
				if (callbackValue.includes('\"')) {
					element.setAttribute('data-callback', callbackValue.replace(/\"/g, ''));
				}
			}

			// Entferne Inline-Styles
			if (element.hasAttribute('style')) {
				element.removeAttribute('style');
			}
		});

		// Verarbeite alle ul-Listen
		const lists = tempDiv.querySelectorAll('ul');
		lists.forEach(list => {
			list.classList.add('fel-chatbot-buttons');
		});

		// Verarbeite alle li-Elemente in den Listen
		const listItems = tempDiv.querySelectorAll('ul li');
		listItems.forEach(item => {
			item.classList.add('fel-chatbot-button-item');
		});

		// Verarbeite alle Buttons mit der Klasse fel-btn
		const buttonElements = tempDiv.querySelectorAll('.fel-btn');
		buttonElements.forEach(button => {
			// Verwende die Theme-Farbe, falls verfügbar
			const chatbotElement = document.getElementById('fel-chatbot-advisor');
			if (chatbotElement && chatbotElement.dataset.themeColor) {
				const themeColor = chatbotElement.dataset.themeColor;
				button.style.setProperty('--theme-color', themeColor);
			}
		});

		// Erstelle einen Container für die gesammelten Buttons
		if (buttons.length > 0) {
			// Hole die Theme-Farbe
			const chatbotElement = document.getElementById('fel-chatbot-advisor');
			const themeColor = chatbotElement ? chatbotElement.dataset.themeColor || '' : '';

			// Erstelle das Buttons-HTML
			let buttonsHtml = '<div class="fel-chatbot-buttons-container mt-3"><div class="fel-chatbot-buttons">';

			buttons.forEach(buttonText => {
				// Füge inline Styles für die Theme-Farbe hinzu
				const buttonStyle = themeColor ?
					`style="border-color: ${themeColor}; color: ${themeColor};"` : '';

				buttonsHtml += `<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"
					${buttonStyle} data-callback="addToChatSubmit" data-prompt="${buttonText}">${buttonText}</button>`;
			});

			buttonsHtml += '</div></div>';

			// Füge die Buttons am Ende des HTML ein
			return tempDiv.innerHTML + buttonsHtml;
		}

		// Gib das verarbeitete HTML zurück
		return tempDiv.innerHTML;
	}

	/**
	 * Generiert ein benutzerdefiniertes Produkt-Template
	 * @param {Array} productIds - Ein Array mit Produkt-IDs
	 * @param {string} originalHtml - Der ursprüngliche HTML-Output
	 * @returns {string} - Das generierte HTML-Template
	 */
	/**
	 * Initialisiert das Laden der Produktdaten
	 * @param {Array} productIds - Ein Array mit Produkt-IDs
	 */
	initProductLoading(productIds) {
		// Warte, bis das DOM vollständig geladen ist
		setTimeout(() => {
			// Lade die Produktdaten für jede Produkt-ID
			productIds.forEach(productId => {
				fetch('/detail/' + productId, {
					method: 'GET',
					headers: {
						'Accept': 'text/html'
					}
				})
				.then(response => response.text())
				.then(html => {
					// Extrahiere die Produktdaten aus dem HTML
					const parser = new DOMParser();
					const doc = parser.parseFromString(html, 'text/html');

					// Finde das Produktelement im DOM
					const productElement = document.querySelector('.fel-product-item[data-product-id="' + productId + '"]');
					if (productElement) {
						// Aktualisiere die Produktdaten
						const nameElement = productElement.querySelector('.fel-product-name');
						const priceElement = productElement.querySelector('.fel-product-price');
						const manufacturerElement = productElement.querySelector('.fel-product-manufacturer');
						const descriptionElement = productElement.querySelector('.fel-product-description');

						// Setze die Produktdaten
						if (nameElement) {
							const productName = doc.querySelector('.product-detail-name');
							if (productName) {
								nameElement.textContent = productName.textContent.trim();
							}
						}

						if (priceElement) {
							const productPrice = doc.querySelector('.product-detail-price');
							if (productPrice) {
								priceElement.textContent = productPrice.textContent.trim();
							}
						}

						if (manufacturerElement) {
							const productManufacturer = doc.querySelector('.product-detail-manufacturer-link');
							if (productManufacturer) {
								manufacturerElement.textContent = productManufacturer.textContent.trim();
							}
						}

						if (descriptionElement) {
							const productDescription = doc.querySelector('.product-detail-description');
							if (productDescription) {
								descriptionElement.textContent = productDescription.textContent.trim();
							}
						}

						// Setze das Produktbild
						const productImage = doc.querySelector('.gallery-slider-image');
						if (productImage && productImage.src) {
							// Ersetze das SVG mit dem tatsächlichen Produktbild
							const imageContainer = productElement.querySelector('.fel-product-image');
							if (imageContainer) {
								// Entferne das SVG, falls vorhanden
								const svg = imageContainer.querySelector('svg');
								if (svg) {
									svg.remove();
								}

								// Erstelle das Bild-Element oder verwende das vorhandene
								let imgElement = imageContainer.querySelector('img');
								if (!imgElement) {
									imgElement = document.createElement('img');
									imageContainer.appendChild(imgElement);
								}

								// Setze das Bild
								imgElement.src = productImage.src;
								imgElement.alt = nameElement ? nameElement.textContent : 'Produktbild';
							}
						}
					}
				})
				.catch(error => {
					// Error loading product data
				});
			});
		}, 100);
	}

	/**
	 * Generiert ein benutzerdefiniertes Produkt-Template
	 * @param {Array} productIds - Ein Array mit Produkt-IDs
	 * @param {string} originalHtml - Der ursprüngliche HTML-Output
	 * @returns {string} - Das generierte HTML-Template
	 */
	generateProductTemplate(productIds, originalHtml = '') {
		// Extrahiere den Titel aus dem Original-HTML, falls vorhanden
		let title = 'Produktergebnisse';
		if (originalHtml) {
			const titleMatch = originalHtml.match(/<h[1-3]>(.*?)<\/h[1-3]>/i);
			if (titleMatch && titleMatch[1]) {
				title = titleMatch[1];
			}
		}

		// Hole die maximale Anzahl der anzuzeigenden Produkte aus den Plugin-Optionen
		const maxProductsToShow = this.options.config && this.options.config.maxProductsInChat ? this.options.config.maxProductsInChat : 4; // Standardwert: 4
		const totalProducts = productIds.length;
		const hasMoreProducts = totalProducts > maxProductsToShow;

		// Begrenze die Anzahl der anzuzeigenden Produkte
		const displayProductIds = hasMoreProducts ? productIds.slice(0, maxProductsToShow) : productIds;

		// Generiere das HTML für die Produktliste
		let html = `<div class="fel-product-listing">
			<h3 class="fel-product-listing-title">${title}</h3>
			<div class="fel-product-list">`;

		// Füge für jede Produkt-ID einen Platzhalter hinzu
		displayProductIds.forEach(productId => {
			html += `
			<div class="fel-product-item" data-product-id="${productId}">
				<div class="fel-product-image">
					<!-- SVG-Platzhalterbild wird angezeigt, bis das echte Produktbild geladen ist -->
					<svg id="meteor-icon-kit__regular-image" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 20.4948L9.25259 12.3356C9.63485 11.9056 10.3003 11.886 10.7071 12.2929L12.7071 14.2929C12.7219 14.3077 12.7361 14.3228 12.7498 14.3382L15.2318 11.3598C15.6082 10.9081 16.2913 10.8771 16.7071 11.2929L22 16.5858V3C22 2.44772 21.5523 2 21 2H3C2.44772 2 2 2.44772 2 3V20.4948ZM3.33795 22H21C21.5523 22 22 21.5523 22 21V19.4142L16.0672 13.4814L11.7682 18.6402C11.4147 19.0645 10.7841 19.1218 10.3598 18.7682C9.93554 18.4147 9.87821 17.7841 10.2318 17.3598L11.4842 15.857C11.416 15.8159 11.3517 15.7659 11.2929 15.7071L10.0428 14.457L3.33795 22ZM3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3C0 1.34315 1.34315 0 3 0ZM7.5 11C5.567 11 4 9.433 4 7.5C4 5.567 5.567 4 7.5 4C9.433 4 11 5.567 11 7.5C11 9.433 9.433 11 7.5 11ZM7.5 9C8.32843 9 9 8.32843 9 7.5C9 6.67157 8.32843 6 7.5 6C6.67157 6 6 6.67157 6 7.5C6 8.32843 6.67157 9 7.5 9Z" fill="#758CA3"/></svg>
					<!-- Das echte Produktbild wird hier eingefügt, wenn es geladen ist -->
				</div>
				<div class="fel-product-info">
					<div class="fel-product-name">Produkt wird geladen...</div>
					<div class="fel-product-price">Preis wird geladen...</div>
					<div class="fel-product-manufacturer">Hersteller wird geladen...</div>
					<div class="fel-product-description">Beschreibung wird geladen...</div>
					<a href="/detail/${productId}" class="fel-product-button">Details ansehen</a>
				</div>
			</div>`;
		});

		// Füge einen "Alle Produkte anzeigen"-Button hinzu, wenn mehr Produkte vorhanden sind
		if (hasMoreProducts) {
			const remainingCount = totalProducts - maxProductsToShow;
			html += `
			<button class="fel-show-all-products" data-product-ids="${productIds.join(',')}">
				Alle ${totalProducts} Produkte anzeigen (${remainingCount} weitere)
			</button>`;
		}

		html += `</div></div>`;

		return html;
	}

	// querySelector
	domGet() {
		return DomAccess.querySelector(...arguments);
	}

	domGetAll() {
		return DomAccess.querySelectorAll(...arguments);
	}

	// storage
	storageGet() {
		return Storage.getItem(...arguments);
	}

	// setItem(keyName, keyValue)
	storageSet() {
		if (this.checkIfLocalStorageIsAllowed()) {
			Storage.setItem(...arguments);
		}
	}

	storageRemove() {
		Storage.removeItem(...arguments);
	}

	// shorties
	chatClearStorage() {
		this.storageRemove(this.options.storage.chat);
		this.storageRemove(`${this.options.storage.chat}_threadId`);
	}

	chatToStorage(threadId) {
		var getChat = this.getChatbotMessagesEl();
		if (getChat) {
			var getLoader = this.domGetAll(getChat, this.options.selector.loadingContainer, false);
			if (getLoader.length) {
				this.foreach(getLoader, (idx, elm) => {
					elm.remove();
				});
			}
			this.removeActiveProducts();
			this.storageSet(this.options.storage.chat, getChat.innerHTML);
			this.storageSet(`${this.options.storage.chat}_threadId`, threadId);
			this.setActiveProducts();
		}
	}

	clearLocalStorage() {
		this.storageRemove(`${this.options.storage.chat}_threadId`);
		this.storageRemove(`${this.options.storage.chat}_chatOpen`);
		this.storageRemove(`${this.options.storage.chat}_chatZoom`);
	}

	// misc
	foreach(toIterate, callback) {
		return Iterator.iterate(toIterate, function(value, key) {
			callback(key, value);
		});
	}

	objectMerge(...objects) {
		var m = (t, s) => {
			Object.entries(s).forEach(([k, v]) => {
				t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
			});
			return t;
		}
		return objects.reduce(m, {});
	}

	// beforebegin afterbegin beforeend afterend
	putHtml(el, toPosition, html) {
		el.insertAdjacentHTML(toPosition, html);
	}

	// debounce(callback, delay, immediate = false)
	debounce() {
		if (typeof Debouncer === 'function') {
			return Debouncer.debounce(...arguments).apply();
		} else if(typeof arguments[0] === 'function') {
			return setTimeout(() => {arguments[0].apply()}, arguments[1] || 0);
		}
	}

	felParseInt(str) {
		var str = str.replace(/[^-\d\.]/g, '');
		return str ? Math.round(parseFloat(str)) : 0 ;
	}

	// init plugin vars / options

	loadTemplates() {
		this.template = {greeting: '', loading: '', messageItem: ''};
		var templates = document.getElementById('fel-chatbot-template');
		if (templates) {
			this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
			if (this.template.greeting) {
				var getChatFromStorage = this.storageGet(this.options.storage.chat);
				this.putToMessages('greeting', getChatFromStorage
					? getChatFromStorage
					: this.template.greeting.outerHTML
				);
				if (getChatFromStorage) {
					this.setActiveProducts();
					this.assistantEl.classList.add('contains-thread');
					this.getChatbotThreadId().value = this.storageGet(this.options.storage.chat + '_threadId');
				}
			}
			this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
		}
	}

	getActiveProductsInChat() {
		return this.assistantEl.querySelectorAll(`.fel-chat-product-list li[data-ref-id].active`);
	}

	removeActiveProducts() {
		var getActiveProducts = this.getActiveProductsInChat();
		if (getActiveProducts.length) {
			for (let i = 0; i < getActiveProducts.length; i++) {
				getActiveProducts[i].classList.remove('active');
			}
		}
	}

	setActiveProducts() {
		if (this.options.page.refId) {
			var getActiveProducts = this.assistantEl.querySelectorAll(`.fel-chat-product-list li[data-ref-id="${this.options.page.refId}"]`);
			if (getActiveProducts.length) {
				for (let i = 0; i < getActiveProducts.length; i++) {
					getActiveProducts[i].classList.add('active');
				}
			}
		}
	}

	setPluginVars() {
		this._client = new HttpClient();
		this.eventsRegistered = false;
		this.isLoading = false;
		this.isLoadingResponse = false;
		this.threadId = null;
        this.options.eventHandlerMap = this.chatbotForm ? [
			[this.chatbotForm, 'submit.felBtn', true],
			[this.assistantEl, 'click.felBtn', true],
			[this.assistantEl, 'change.felBtn', true],
		] : [];
	}

	setPluginOptions() {
		if ('undefined' === typeof DomAccess) {
			return false;
		}
        // assistant element
        this.assistantEl = this.domGet(document.body, '[data-assistant-gpt-plugin]');
        // assistant form
		this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-chatbot-form');
        // controller url
        this.controllerUrl = this.chatbotForm.action;
		// messages container
		this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages);
        // options
		var pluginOptions = this.assistantEl.dataset.options;
		if (pluginOptions && typeof pluginOptions === 'string') {
			pluginOptions = JSON.parse(pluginOptions);
			if (pluginOptions) {
				this.options = this.objectMerge(this.options, pluginOptions);
				return true;
			}
		}
		return false;
	}

	// event handler
	removeEvents() {
		this.setEventHandler('remove');
	}

	resetEvents() {
		this.removeEvents();
		this.registerEvents();
	}

	setEventHandler(setEvent) {
		var eventFn = {
			'add': 'addEventListener',
			'remove': 'removeEventListener',
		};
		if ('remove' === setEvent && !this.eventsRegistered) {
			return false;
		}
		if ('add' === setEvent && this.eventsRegistered) {
			this.removeEvents();
		}
		this.foreach(this.options.eventHandlerMap, (i, arr) => {
			if (typeof arr[1] !== 'undefined') {
				var names = arr[1].split('.');
				arr[0][eventFn[setEvent]](names[0], this, arr[2] || true);
			}
		});
		this.eventsRegistered = 'add' === setEvent;
	}

	/**
	 * Verdunkelt eine Farbe um einen bestimmten Prozentsatz
	 * @param {string} color - Die Farbe im Hex-Format (z.B. #FF0000)
	 * @param {number} percent - Der Prozentsatz, um den die Farbe verdunkelt werden soll
	 * @returns {string} - Die verdunkelte Farbe im Hex-Format
	 */
	_darkenColor(color, percent) {
		// Entferne # wenn vorhanden
		color = color.replace('#', '');

		// Konvertiere zu RGB
		const r = parseInt(color.substring(0, 2), 16);
		const g = parseInt(color.substring(2, 4), 16);
		const b = parseInt(color.substring(4, 6), 16);

		// Verdunkle die Farbe
		const darkenedR = Math.floor(r * (100 - percent) / 100);
		const darkenedG = Math.floor(g * (100 - percent) / 100);
		const darkenedB = Math.floor(b * (100 - percent) / 100);

		// Konvertiere zurück zu Hex
		return `#${darkenedR.toString(16).padStart(2, '0')}${darkenedG.toString(16).padStart(2, '0')}${darkenedB.toString(16).padStart(2, '0')}`;
	}

	/**
	 * Konvertiert einen Hex-Farbwert in RGB-Komponenten
	 * @param {string} hex - Der Hex-Farbwert (mit oder ohne #)
	 * @returns {Object|null} - Ein Objekt mit r, g, b Werten oder null bei ungültigem Format
	 */
	_hexToRgb(hex) {
		// Entferne # wenn vorhanden
		hex = hex.replace('#', '');

		// Überprüfe, ob es ein gültiger Hex-Wert ist
		if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
			return null;
		}

		// Konvertiere zu RGB
		const r = parseInt(hex.substring(0, 2), 16);
		const g = parseInt(hex.substring(2, 4), 16);
		const b = parseInt(hex.substring(4, 6), 16);

		return { r, g, b };
	}
	/**
	 * Handle the response from the run request
	 * @param {Object} response - The response from the run request
	 */
	handleRunResponse(response) {
		let emitResponse = null;
		if (response) {

			// Check if there's a product search in the UI action required or _trackActions
			let productSearchData = null;
			let hasProductSearch = false;

			// Check in uiActionRequired
			if (response.uiActionRequired &&
				'undefined' !== typeof response.uiActionRequired.product_search &&
				response.uiActionRequired.product_search.items &&
				response.uiActionRequired.product_search.items.products &&
				response.uiActionRequired.product_search.items.products.length > 0) {
				hasProductSearch = true;
				productSearchData = response.uiActionRequired.product_search;
			}

			// Check in _trackActions
			if (!hasProductSearch && response._trackActions && response._trackActions.length > 0) {
				for (const action of response._trackActions) {
					// Check if this is a product_search action
					if (action.response && action.response.length > 0) {
						for (const resp of action.response) {
							// Check for product search in output.products
							if (resp.output && resp.output.products && resp.output.products.length > 0) {
								hasProductSearch = true;
								productSearchData = resp;
								break;
							}

							// Check for product search in tool_call_id (for OpenAI function calls)
							if (resp.tool_call_id && resp.exec === 'product_search') {
								hasProductSearch = true;
								productSearchData = resp;
								break;
							}

							// Check for product search in output.output.products (nested structure)
							if (resp.output && resp.output.output && resp.output.output.products) {
								hasProductSearch = true;
								productSearchData = resp.output;
								break;
							}
						}
					}
					if (hasProductSearch) break;
				}
			}



			if (this.checkIfMessageThrown(response)) {
				this.putToMessages('chatbot', this.checkIfMessageThrown(response), '', '', ' fel-system-exception');
			} else if ('undefined' !== typeof response.id) {
				if ('undefined' !== typeof response.threadMessages) {
					var tMessages = response.threadMessages;
					if (!emitResponse) {
						emitResponse = tMessages;
					}
					if ('undefined' !== typeof tMessages[0] &&
						'undefined' !== typeof tMessages[0].role &&
						'assistant' === tMessages[0].role
					) {
						var lastAssistantMessages = true;

						// Special handling for product search results
						if (hasProductSearch) {

							// First display the AI message text without product list
							this.foreach(tMessages, (i, data) => {
								if ('user' === data.role) {
									lastAssistantMessages = false;
								}
								if (lastAssistantMessages) {
									// Process the message to extract buttons
									let processedValue = this.processButtonsInMessage(data.value);

									// Remove product list from the message
									let messageWithoutProductList = processedValue;

									// Remove <ul> lists that likely contain products
									const tempDiv = document.createElement('div');
									tempDiv.innerHTML = processedValue;
									const ulElements = tempDiv.querySelectorAll('ul');
									if (ulElements.length > 0) {
										// Check if any UL contains product links
										for (const ul of ulElements) {
											if (ul.innerHTML.includes('/detail/')) {
												ul.remove();
											}
										}
										messageWithoutProductList = tempDiv.innerHTML;
									}

									// Add the message to the chat
									this.putToMessages('chatbot', messageWithoutProductList, '', response.datetime);
								}
							});

							// Extract product IDs from the message
							let productIds = [];

							// Check different possible locations for product data
							if (productSearchData && productSearchData.output && productSearchData.output.products) {
								productIds = productSearchData.output.products.map(product => product.id);
							} else if (productSearchData && productSearchData.items && productSearchData.items.products) {
								productIds = productSearchData.items.products.map(product => product.id);
							} else if (productSearchData && productSearchData.output && productSearchData.output.output && productSearchData.output.output.products) {
								productIds = productSearchData.output.output.products.map(product => product.id);
							} else if (productSearchData && productSearchData.fetch && productSearchData.fetch.elements) {
								productIds = productSearchData.fetch.elements.map(product => product.id);
							}

							// If no products found yet, try to extract from the message text
							if (productIds.length === 0 && tMessages && tMessages.length > 0) {
								for (const message of tMessages) {
									if (message.role === 'assistant' && message.value) {
										const extractedIds = this.extractProductIdsFromHtml(message.value);
										if (extractedIds.length > 0) {
											productIds = extractedIds;
											break;
										}
									}
								}
							}

							if (productIds.length > 0) {
								// Generate a custom product template
								const productTemplate = this.generateProductTemplate(productIds);

								// Add the product listing as a new message
								this.putToMessages('chatbot', productTemplate, '', '');

								// Initialize product loading
								this.initProductLoading(productIds);
							}
						} else {
							// Standard message handling
							this.foreach(tMessages, (i, data) => {
								if ('user' === data.role) {
									lastAssistantMessages = false;
								}
								if (lastAssistantMessages) {
									if (response.uiActionRequired && 'undefined' !== typeof response.uiActionRequired.product_search) {
										if ('undefined' !== typeof data.originalValue) {
											data = this.replaceAiProductList(data, response.uiActionRequired.product_search);
										}
									} else {
										if (!data.value.includes('<p') && !data.value.includes('<ul')) {
											var plainTextEl = document.createElement('div');
											plainTextEl.classList.add('fel-text-plain', 'text-break');
											plainTextEl.insertAdjacentHTML('beforeend', data.value.trim());
											data.value = plainTextEl.outerHTML;
										}
									}

									// Process buttons in the message
									data.value = this.processButtonsInMessage(data.value);

									// Add the message to the chat
									this.putToMessages('chatbot', data.value, '', response.datetime);
								}
							});
						}
					} else {
						this.putToMessages('chatbot', this.options.translation.errorClearChat, '', response.datetime);
					}
				}
				this.chatToStorage(response.threadId);
				this.$emitter.publish('felAssistantMessageResponse');
			}
		}
	}
}
