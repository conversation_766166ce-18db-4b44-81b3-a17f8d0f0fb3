{"fel-assistant-manager": {"general": {"mainMenuItemGeneral": "5E <PERSON>-As<PERSON>enten-Manager", "brandNameShort": "5E", "shortName": "OAIAM", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "abortAllRequests": "Alle Anfragen abbrechen", "abortRequest": "<PERSON><PERSON><PERSON> abbrechen", "assistant": "Assistent", "assistantDescription": "Assistentenbeschreibung, nur für internen Gebrauch (max. 500 Zeichen)", "assistantId": "Assistenten-ID", "assistantName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assistants": "Assistenten", "cancel": "Abbrechen", "character": "<PERSON><PERSON><PERSON>", "close": "Schließen", "componentsAreloading": "Einige Komponenten laden im Hintergrund.", "configuration": "Konfiguration", "confirm": "Bestätigen", "confirmDeletion": "Löschung bestätigen", "contentCheckMainMenuItemGeneral": "Inhaltsprüfer", "contentEditorMainMenuItemGeneral": "Inhaltseditor", "create": "<PERSON><PERSON><PERSON><PERSON>", "createApiKey": "Neuen geheimen Schlüssel erstellen", "createAssistant": "Assistent erstellen", "default": "Standard", "delete": "Löschen", "deleted": "Gelöscht", "description": "Diese Seite fungiert als Vermittler zwischen Ihnen und Ihrem OpenAI-Konto, das mit Ihrem OpenAI-API-Schlüssel verbunden ist.", "details": "Details", "edit": "<PERSON><PERSON><PERSON>", "example": "Beispiel", "goToConfiguration": "Zur Konfiguration", "help": "<PERSON><PERSON><PERSON>", "initialize": "Initialisieren", "loading": "<PERSON><PERSON><PERSON>…", "message": "Nachricht", "messages": "Nachrichten", "missingOpenAiKey": "Fehlender OpenAI-API-Schlüssel", "noAssistantsFound": "<PERSON><PERSON> As<PERSON>enten gefunden", "noDataOnServerNote": "Die Daten auf dieser Seite werden auf den OpenAI-API-Servern gespeichert und verwaltet.", "openAIApiKey": "OpenAI-API-Schlüssel", "openAIAssistants": "OpenAI-Assistenten", "openAIFiles": "<PERSON><PERSON>", "openAIStatus": "OpenAI-Status", "openAITokenizer": "OpenAI-Tokenizer", "openAIUsage": "OpenAI-Nutzung", "openAIPricing": "<PERSON><PERSON>", "requestAborted": "Anfrage abgebrochen", "requiredApiKeyInfo": "Zum Zugriff auf diese Seite ist ein gültiger OpenAI-API-Sc<PERSON><PERSON><PERSON>, der für \"Alle Vertriebskanäle\" autorisiert ist.", "reset": "Z<PERSON>ücksetzen", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Speichern", "select": "Auswählen", "selected": "Ausgewählt", "showFunctions": "Funktionen anzeigen", "temperature": "Temperatur", "temperatureDescription": "Die Einstellung \"Temperatur\" passt die Zufälligkeit der Antworten an. Eine niedrigere Temperatur führt zu vorhersehbareren und konsistenteren Antworten. Die Skala reicht von 0,1 bis 2, wobei 2 ein hohes Maß an Zufälligkeit bedeutet.", "update": "Aktualisieren", "updateAssistant": "Assistent aktualisieren", "uploadFile": "<PERSON><PERSON> zu OpenAI hochladen", "viewDetails": "Details anzeigen", "aiToolsTranslation": {"fetch_initial_chat_guidelines": "Aktivieren Sie diese Funktion, um dem Assistenten anfängliche Richtlinien bereitzustellen, einschließlich wichtiger Informationen über Ihren Shop und Ihre Produkte. Dies stellt sicher, dass der Assistent gut informiert ist, bevor er eine Konversation beginnt. Passen Sie diese Informationen auf der Konfigurationsseite an.", "get_categories": "(von get_product_properties) Aktivieren Sie diese Funktion, damit der Assistent verfügbare Shop-Kategorien abrufen und präsentieren kann, einsch<PERSON>ßlich ihrer URLs, Namen und Breadcrumb-Strukturen.", "get_chatbot_name": "Aktivieren Sie diese Funktion, damit der Assistent seinen Namen in Gesprächen abrufen und verwenden kann, um eine personalisiertere Benutzererfahrung zu bieten.", "get_countries": "Aktivieren Sie diese Funktion, damit der Assistent den Benutzern eine Liste verfügbarer Länder bereitstellen kann, nützlich für Versand und Lokalisierung.", "get_date_time": "Aktivieren Sie diese Funktion, damit der Assistent den Benutzern das aktuelle Datum, die Uhrzeit und Zeitzoneninformationen bereitstellen kann, um genaue und zeitnahe Antworten zu gewährleisten.", "get_delivery_times": "Aktivieren Sie diese Funktion, damit der Assistent die Benutzer über die geschätzten Lieferzeiten für Produkte oder Dienstleistungen informieren kann.", "get_faqs": "Aktivieren Sie diese Funktion, damit der Assistent häufig gestellte Fragen (FAQs) abrufen und Kunden schnelle, relevante Antworten basierend auf Ihren gespeicherten FAQ-Daten bereitstellen kann. Passen Sie diese Informationen auf der Konfigurationsseite an.", "get_manufacturer": "(von get_product_properties) Aktivieren Sie diese Funktion, damit der Assistent den Benutzern detaillierte Informationen über die Hersteller Ihrer Produkte bereitstellen kann.", "get_meta_information": "Aktivieren Sie diese Funktion, damit der Assistent auf Metadaten über Ihren Shop zugreifen und diese teilen kann, wie z. B. Kontaktdetails, Öffnungszeiten und FAQs. Passen Sie diese Informationen über das dynamische Datenfeld auf der Konfigurationsseite an.", "get_order_status": "Aktivieren Sie diese Funktion, damit der Assistent die Details einer Kundenbestellung mithilfe der Bestellnummer und der Postleitzahl abrufen kann. Diese Funktion stellt sicher, dass der Assistent relevante Bestellinformationen wie Bestellstatus und Tracking-Code bereitstellen kann, ohne private Kundendaten wie Namen oder Kontaktdaten an OpenAI weiterzugeben. Die Antwort enthält nur den Bestellstatus und, falls verfügbar, den Tracking-Code.", "get_payment_methods": "Aktivieren Sie diese Funktion, damit der Assistent den Benutzern Informationen über die verfügbaren Zahlungsmethoden in Ihrem Shop bereitstellen kann.", "get_product_details": "Aktivieren Sie diese Funktion, damit der Assistent detaillierte Informationen über spezifische Produkte mithilfe ihrer Produkt-IDs oder -Nummern abrufen kann, um den Benutzern umfassende Produktdetails zu bieten.", "get_product_properties": "Aktivieren Sie diese Funktion, damit der Assistent Produktattribute, Herstellernamen und Kategorien abrufen und nutzen kann, um die Produktsuche und Filterfunktionen zu verbessern.", "fetch_url": "Aktivieren Si<PERSON>, damit Ihre Assistenten den Inhalt von URLs abrufen können. Diese URLs können im Textformat in den entsprechenden Feldern gespeichert werden, die Inhalte für Assistenten verwalten.", "go_to_url": "Aktivieren Sie diese Funktion, damit der Assistent die Benutzer basierend auf Benutzeranfragen zu spezifischen URLs oder Pfaden innerhalb Ihres Shops weiterleiten kann.", "product_search": "Aktivieren Sie diese Funktion, damit der Assistent Produktsuchen basierend auf Benutzeranfragen durchführen kann, mit der Möglichkeit, Ergebnisse nach Kategorien, Eigenschaften, Preisspanne und Sortieroptionen zu filtern.", "log": "Aktivieren Sie diese Funktion, damit der Assistent Probleme oder Vorschläge protokollieren kann, die während der Konversationen auftreten. <PERSON><PERSON> hi<PERSON>, die Wissensbasis des Chatbots zu verbessern und die Gesamtservicequalität zu steigern.", "search_logs": "Aktivieren Sie diese Funktion, damit der Assistent vorhandene Protokolleinträge mithilfe spezifischer Tags durchsuchen kann, bevor er neue Protokolle erstellt. Dies verhindert doppelte Einträge und stellt sicher, dass frühere Probleme ordnungsgemäß referenziert werden.", "tags_for_logs": "Aktivieren Sie diese Funktion, damit der Assistent verfügbare Tags abrufen kann, die für die Funktionen 'log' und 'search_logs' erforderlich sind.", "get_unresolved_logs": "Aktiviere diese Funktion, um dem \"5 Elements Backend Assistenten\" zu ermöglichen, automatisch die neuesten ungelösten nicht Privaten Log-Einträge abzurufen – bis zu 5 Kundenanfragen, die noch nicht beantwortet wurden. Diese Funktion hilft dabei, Kundenfragen schnell zu erkennen und zu bearbeiten, wodurch Ihr Support-Prozess optimiert und manueller Aufwand reduziert wird."}}, "files": {"allowedFileTypes": "Erlaubte Dateitypen", "canNotDownloadFile": "<PERSON> Herunterladen von <PERSON> mit dem Zweck \"assistants\" ist nicht erlaubt", "files": "<PERSON><PERSON>", "filesAttached": "Angehängte Dateien", "filesAttachedToAssistants": "When you attach files to an assistant, this activates the \"code_interpreter,\" which expects structured data.", "filesMaxAmountbreached": "Maximal zulässige Dateien: {max}, ausgewählt: {current}", "uploadHelp": "Um sicherzustellen, dass Ihr Assistent mit allen notwendigen Informationen ausgestattet ist, erstellen Si<PERSON> bitte eine umfassende Liste, die das spezifische Wissen, die Daten oder Anweisungen enthält, die Sie einbeziehen möchten. Sie können Inhalte wie Ihre FAQ-Seite kopieren und als blanken Text in die Datei einfügen, ohne Änderungen vorzunehmen. Betrachten Sie die Datei als detaillierte Broschüre, die alle wesentlichen Informationen enthält, die ein Mitarbeiter benötigen würde, um Kundenanfragen beantworten zu können.", "vectorStore": {"attachFiles": "<PERSON><PERSON>", "attachVectorStoreToAssistant": "Hängen Sie einen Vektorspeicher an Ihren Assistenten an.", "createVectorStore": "Einen Vektorspeicher erstellen", "detachFiles": "<PERSON><PERSON> trennen", "fileIsEmpty": "Datei ist leer", "filenameInvalid": "Dateiname enthält ungültige Zeichen", "name": "Vektorspeicher", "noFilesAttached": "<PERSON><PERSON> an<PERSON>", "noVectorsFound": "<PERSON><PERSON> Vektorspeicher gefunden", "refreshStore": "Aktualisieren", "selectVectorStore": "Einen Vektorspeicher auswählen", "setName": "Name des Vektorspeichers", "aboutVectorStore": "Vektorspeicherobjekte geben dem Dateisuchwerkzeug die Fähigkeit, in Ihren Dateien zu suchen. Das Hinzufügen einer Datei zu einem Vektorspeicher analysiert, tokenisiert und speichert die Datei automatisch in einer Vektordatenbank, die sowohl Schlüsselwort- als auch semantische Suche ermöglicht. Jeder Vektorspeicher kann bis zu 10.000 Dateien enthalten. Vektorspeicher können individuell an Assistenten angehängt werden."}}, "create": {"expensiveModelWarning": "<PERSON>te beachten Si<PERSON>, dass dieses Modell mit deutlich höheren Kosten verbunden ist. Bitte prüfen Sie vor dem Fortfahren die Preisdetails.", "responseFormat": "Antwortformat", "responseFormatHelp": "Legen Sie fest, welches Format Assistenten für ihre Antworten verwenden sollen.", "responseFormatHelpSchema": "Das Format \"Schema\" zwingt den Assistenten dazu, mit einem JSON-Objekt zu antworten. Dies ermöglicht strukturierte und umfassende Antworten im Chat, kann jedoch zu einer erhöhten Nutzung von Tools führen. <PERSON><PERSON><PERSON>, dass alle dem Assistenten zur Verfügung stehenden Tools kompakte und vollständige Daten zurückgeben.", "responseFormatHelpObject": "Das Format \"Object\" versucht, die Antwort als JSON-Objekt zu generieren.", "responseFormatHelpAuto": "Das Format \"auto\" wechselt automatisch zu HTML.", "responseFormatMissingInstruction": "Die Anweisungen enthalten keinen Abschnitt für das Antwortformat.", "previewConfig": "Konfiguration anzeigen", "selectAllowedFunctions": "Anpassung der Assistentenfähigkeiten: Wählen Sie die Funktionen aus, die Ihr Assistent verwenden kann", "selectAllowedFunctionsInfo": "Aktivieren Sie die Funktionen, auf die Ihr Assistent zugreifen soll. Diese Features nutzen Echtzeitdaten aus Ihrem Shopware-Shop. Wenn Sie vorab Daten hochgeladen oder Informationen in dynamischen Feldern konfiguriert haben, wie z.B. <PERSON>möglichkeiten, können Sie bestimmte Funktionen deaktiviert lassen, um Redundanzen zu vermeiden.", "selectModelToUse": "<PERSON><PERSON> auswählen", "setInstructions": "Anweisungen", "setInstructionsInfo": "Bitte passen Sie die Anweisungen nach Ihren Bedürfnissen an. <PERSON>n Sie Dateien verwenden, stellen <PERSON> sic<PERSON>, dass <PERSON><PERSON> dies in den Anweisungen deutlich angeben, damit der Assistent die Dateien im Anhang berücksichtigt. <PERSON><PERSON> ist wichtig, den Assistenten ausdrücklich zu informieren, um eine optimale Verarbeitung zu gewährleisten.", "form": {"setAssistantName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setAssistantNameHelp": "Nur für internen Gebrauch"}}, "delete": {"backToIndex": "Zurück zur Übersicht", "delete": "Löschen", "deleteAssistant": "<PERSON><PERSON> sind dabei, den Assistenten zu löschen", "confirm": "Bestätigen", "cancel": "Abbrechen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "confirmMessage": "Sind <PERSON> sicher, dass Sie diesen Eintrag löschen möchten?", "confirmDeleteLog": "Sind <PERSON> sicher, dass Sie diesen Log löschen möchten?", "confirmDeleteTag": "Sind <PERSON> sicher, dass Si<PERSON> diesen Tag löschen möchten?", "confirmDeleteVectorStore": "Sind <PERSON> sicher, dass Si<PERSON> diesen Vector Store löschen möchten?", "selectItemsToDelete": "Bitte wählen Sie Einträge zum Löschen aus."}, "contentEditor": {"generalName": "Inhaltseditor", "generalDescription": "<PERSON>erwalten Sie Inhalte, die Ihre Assistenten erhalten.", "description": "Diese Seite ermöglicht es Ihnen, Plugin-Konfigurationen anzupassen. Sie bietet eine einfache Möglichkeit, Blacklist- und Whitelist-Optionen mit erweiterten Filtern für Kategorien, Hersteller, Eigenschaften und Eigenschaftsgruppen zu konfigurieren. Entworfen, um Bereinigung und Konfiguration effizient zu gestalten, ermöglicht es Benutzern, ihre Assistenten exakt nach Bedarf zu optimieren.", "inheritanceRestored": "Vererbung erfolgreich auf Standardeinstellungen zurückgesetzt", "restoreInheritance": "Vererbung wiederherstellen", "salesChannelActiveConfigEmpty": "<PERSON>ine überschriebenen Konfigurationen für den ausgewählten Vertriebskanal.", "salesChannelOverridesDefaultNote": "Der ausgewählte Vertriebskanal überschreibt die Standardkonfiguration.", "editable": {"enablePlugin": {"name": "Plugin aktivieren", "description": "Diese Option ermöglicht es Ihnen, das Plugin schnell zu aktivieren/deaktivieren, ohne Systemkonfigurationen zu ändern."}, "openAiWhitelistPropertyGroups": {"name": "Whitelist Eigenschaftsgruppen", "description": "Wählen Sie Eigenschaftsgruppen aus, die Assistenten angezeigt werden sollen. Wenn nichts ausgewählt ist, werden alle Eigenschaftsgruppen geladen und angezeigt. Die maximale Anzahl Optionen pro Eigenschaftsgruppe kann individuell eingestellt werden, Standard ist: 20."}, "openAiBlacklistPropertyGroups": {"name": "Blacklist Eigenschaftsgruppen", "description": "Wählen Sie Eigenschaftsgruppen aus, die vom generierten Inhalt für den Assistenten ausgeschlossen werden sollen. Diese Gruppen werden von der Ausgabe komplett ausgeschlossen."}, "openAiBlacklistPropertyOptions": {"name": "Blacklist Eigenschaftsoptionen", "description": "Wählen Sie einzelne Optionen und Eigenschaften aus, die vom generierten Inhalt für den Assistenten ausgeschlossen werden sollen. Dies können Platzhalter sein, die Sie möglicherweise verwenden, oder komplexe Eigenschaftsnamen, die Assitenten nicht unbedingt berücksichtigen brauchen. Faustregel: Je simpler die Optionen gehalten sind, desto effektiver können Assistenten mit den Daten arbeiten."}, "openAiBlacklistManufacturer": {"name": "Blacklist <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Sie Hersteller aus, die vom generierten Inhalt für den Assistenten ausgeschlossen werden sollen."}, "openAiBlacklistCategory": {"name": "Blacklist <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> Sie Kategorien aus, die vom generierten Inhalt für den Assistenten ausgeschlossen werden sollen."}, "openAiFilterCategoryLevel": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtern", "description": "Legen Sie die Startebene für Kategorie-Breadcrumbs fest, die Assistenten erhalten. Setzen sie diese Option auf 1, wenn Sie eine Strukturierung wie \"Katalog #1\" verwenden. Andernfalls erscheint \"Katalog #1\" als oberster Parent der geladenen Kategorien."}, "openAiReturnCategoriesWithIds": {"name": "Kategorie-IDs zur Antwort hinzufügen", "description": "<PERSON>n nicht zu viele Kategorien vorhanden sind, kann der Assistent IDs anstelle von Namen für Produktsuchen setzen. Diese Option hebt den allgemeinen Tokenverbrauch, je nach Anzahl der Kategorien, um einige Prozentpunkte."}, "swApiSetPropertyIdsAsKeys": {"name": "Eigenschafts-IDs zur Antwort hinzufügen", "description": "<PERSON>n nicht zu viele Eigenschaften vorhanden sind, kann der Assistent darauf trainiert werden, Produkte mit Eigenschafts-IDs anstelle von Eigenschafts-Namen zu suchen. Diese Option hebt den allgemeinen Tokenverbrauch, je nach Anzahl der Eigenschaften und Optionen in ihrem Shop, um einige Prozentpunkte."}, "openAiFilterMaxOptions": {"name": "Maximale Optionen", "description": "Legen Sie die maximale Anzahl von Eigenschaften und Optionen pro Eigenschaftsgruppe fest, die der Assistent verarbeiten soll. Max: {max}"}, "openAiSearchLimit": {"name": "Suchlimit", "description": "Legen Sie die maximale Standardanzahl von Produktsuchergebnissen fest, die zurückgegeben werden sollen. Der Assistent kann dieses Limit auch innerhalb des angegebenen Bereichs selbst anpassen: min {min}, max {max}. Assistenten können bei Bedarf durch die Such-Ergebnisse blättern."}, "felOpenAiAssistantId": {"name": "Assistenten-ID", "description": "Die Standard-Assistenten-ID"}, "openAiChatbotName": {"name": "Chatbot-Name", "description": "Der hier eingegebene Name wird als Chatbot-Name neben dem Avatar angezeigt. Maximale Länge: {maxlength}"}, "openAiMetaInformationField": {"name": "Metainformationen abrufen", "description": "Aktivier<PERSON><PERSON> dies, damit der Assistent auf wichtige Shop-Informationen wie Kontaktdetails, Öffnungszeiten und andere Metadaten zugreifen kann, um Kunden besser zu unterstützen. Maximale Länge: {maxlength}. Passen Sie diese Informationen auf der Konfigurationsseite oder mit unserem Inhaltseditor an."}, "openAiInitAdditionalInstructions": {"name": "Anfängliche Chat-Richtlinien", "description": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>, shopspezifische Anweisungen an, die der Assistent zu Beginn jeder Chat-Sitzung abruft. Diese Richtlinien werden während der gesamten Konversation verwendet, um eine genaue und konsistente Informationsbereitstellung sicherzustellen. Maximale Länge: {maxlength}. Passen Sie diese Informationen auf der Konfigurationsseite oder mit unserem Inhaltseditor an."}, "openAiFaqField": {"name": "FAQs abrufen", "description": "Aktivieren Sie diese Funktion, damit der Assistent häufig gestellte Fragen (FAQs) abrufen und Kunden schnelle und relevante Antworten basierend auf Ihren gespeicherten FAQ-Daten bereitstellen kann. Maximale Länge: {maxlength}. Passen Sie diese Informationen auf der Konfigurationsseite oder mit unserem Inhaltseditor an."}, "notFound": {"name": "Konfigurationen nicht gefunden", "description": "Konfigurationen nicht gefunden"}, "idsNotFound": {"name": "IDs nicht gefunden, Eigenschaften sind wahrscheinlich nicht aktiven Produkten zugeordnet", "description": "IDs nicht gefunden"}, "felIdsToNamesFinished": {"name": "IDs wurden in Namen umgewandelt, verbleibende IDs konnten nicht identifiziert werden", "description": "IDs in Namen umgewandelt"}, "openAiFilterMaxDescriptionLengthSearchResults": {"name": "Maximale Beschreibungslänge in Suchergebnissen", "description": "Begrenzen Sie die Produktbeschreibung auf eine maximale An<PERSON><PERSON> von Zeichen in Suchergebnissen."}, "openAiFilterMaxDescriptionLength": {"name": "Maximale Beschreibungslänge", "description": "Begrenzen Sie die Produktbeschreibung auf eine maximale An<PERSON><PERSON> von <PERSON>, wenn Produktdetails angefordert werden."}, "openAiOrganizationId": {"name": "Organisations-ID", "description": "Geben Sie die OpenAI-Organisations-ID an (nicht erforderlich)."}, "openAiUserInputMaxLength": {"name": "Maximale Benutzereingabelänge", "description": "Legen Sie die maximale Anzahl von Zeichen fest, die ein Benutzer in die Assistentenoberfläche eingeben kann."}}}, "catchErrors": {"assistantIdIsRequired": "Assistenten-ID ist erford<PERSON>lich", "customPluginSeemsToNotExist": "<PERSON><PERSON> scheint, dass das ausgewählte Plugin nicht existiert", "completedRuns": "Abgeschlossene Runs, die Si<PERSON> versuchen können, fortzusetzen", "error": "<PERSON><PERSON>", "errorDeletingFile": "Fehler beim Löschen der Datei", "errorFetchingTagUsages": "Fehler beim Abrufen der Nutzungsdaten", "errorLoadingData": "Fehler beim Laden der angeforderten Daten", "errorNotFound": "Nicht gefunden", "errorWhileSavingLog": "<PERSON><PERSON>ichern des Logs ist ein Fehler aufgetreten", "failedAssistantNotFound": "Assistent nicht gefunden", "failedBulkDelete": "Massenlöschung fehlgeschlagen", "managerCanNotRunNoplugins": "Der 5E-Assistenten-Manager er<PERSON><PERSON> mind<PERSON> ein installiertes Plugin von 5 Elements, um zu laufen.", "missingAssstantId": "Fehlende Assistenten-ID", "failedToStoreData": "Speichern fehlgeschlagen", "failedBulkReset": "Massen-Reset fehlgeschlagen.", "failedBulkResetAll": "Massen-Reset aller Nutzungszähler fehlgeschlagen.", "failedNoFilesFound": "<PERSON><PERSON> gefunden", "failedOaiServerError": "OAI Server Error", "failedToArchiveLog": "Archivierung des Logs fehlgeschlagen", "failedToAttachFileToVectorStore": "Fe<PERSON> beim Anhängen von Dateien an den Vektorspeicher", "failedToCreateOrUpdateAssistant": "Fehler beim Erstellen oder Aktualisieren des Assistenten", "failedToCreateVectorStore": "Fehler beim Erstellen des Vektorspeichers", "failedToDebounceFunction": "Fehler beim Entprellen der Funktion", "failedToDeleteAssistant": "Fehler beim Löschen des Assistenten", "failedToDeleteVectorStore": "Fehler beim Löschen des Vektorspeichers", "failedToDetachFileFromVectorStore": "Fehler beim Trennen der Datei vom Vektorspeicher", "failedToDetachFileNotFound": "Datei nicht gefunden, Liste wird aktualisiert", "failedToFetchLog": "Fehler beim Abrufen des Logs", "failedToFetchSalesChannels": "Fehler beim Abrufen der Vertriebskanäle", "failedToFormatIdsToNames": "Fehler beim Formatieren der IDs zu Namen", "failedToInitializeClientComponent": "Fehler beim Initialisieren der HTTP-Client-Komponente", "failedToInitializeComponent": "Fehler beim Initialisieren der Komponente", "failedToInitializeRoute": "Fehler beim Initialisieren der Route", "failedToLoadAssistantDetails": "Fehler beim Laden der Assistentendetails", "failedToLoadAssistants": "Fehler beim Laden der Assistenten", "failedToLoadCustomPluginConfig": "Fehler beim Laden der benutzerdefinierten Plugin-Konfiguration", "failedToLoadFile": "Fehler beim Laden der Datei", "failedToContinueChat": "Fehler beim Laden des Chats", "failedToReadMessage": "Fehler beim Lesen der Nachricht", "failedToLoadFiles": "Fehler beim Laden der Dateien", "failedToLoadLogRepository": "Fehler beim Laden des Log-Repositorys", "failedToLoadModels": "Fehler beim Laden der Modelle", "failedToLoadTagDetails": "Fehler beim Laden der Tag-Details", "failedToLoadTags": "Fehler beim Laden der Tags", "failedToLoadVectorStore": "Fehler beim Laden des Vektorspeichers", "failedToLoadVectorStores": "Fehler beim Laden der Vektorspeicher", "failedToPrefetchProductProperties": "Fehler beim Vorabrufen der Produkteigenschaften", "failedToRestoreCustomPluginInheritance": "Fehler beim Wiederherstellen der benutzerdefinierten Plugin-Vererbung", "failedToRunAction": "Fehler beim Ausführen der Aktion", "failedToSaveTag": "Fehler beim Speichern des Tags: {name}.", "failedToToggleJson": "<PERSON><PERSON> beim Umschalten von JSON", "failedToTranslateSnippet": "Snippet nicht gefunden: '{requestedTranslation}'", "failedToUpdateAliasesFor": "Fehler beim Aktualisieren der Aliase für das Tag: {name}.", "failedToUpdateLog": "Fehler beim Aktualisieren des Logs", "failedToUpdatePluginConfiguration": "Fehler beim Aktualisieren der Plugin-Konfiguration", "failedToUpdateVectorStoreName": "Fehler beim Aktualisieren des Namens des Vektorspeichers", "fileUploadFailed": "Datei-Upload fehlgeschlagen", "failedToSendMessage": "Fehler beim Senden der Nachricht", "importEmptyLogs": "Logs sind leer", "importFailed": "Importieren fehlgeschlagen", "invalidConfigLongtextMaxError": "Die erlaubte maximale Länge ist: {maxlength}", "invalidConfigValueMaxError": "Der erlaubte Maximalwert ist: {max}", "invalidConfigValueMinError": "Der erlaubte Minimalwert ist: {min}", "invalidConfigValueNewTypeError": "Der Typ des neuen Werts ({newValueType}) stimmt nicht mit dem Typ des bestehenden Werts ({existingValueType}) überein.\nWert: {value}", "invalidSalesChannelDomain": "Die Domain des Vertriebskanals ist nicht gültig", "logDeleteError": "Löschen des Logs fehlgeschlagen", "messageIsRequired": "Na<PERSON>richt ist erforderlich", "missingAccessData": "Fehlende Zugangsdaten", "missingPrefetchedProductProperties": "Fehlende vorab geladene Produkteigenschaften", "openAiMissingApiBaseUrl": "Die erforderliche OpenAI API-Basis-URL fehlt oder ist nicht gültig.", "salesChannelMustBeSelected": "Vertriebskanal muss ausgewählt werden", "selectItemsToDelete": "Bitte wählen Si<PERSON> mindestens ein Element zum Löschen aus.", "tagAliasNotUnique": "Tag-Alias ist nicht einzigartig", "tagMustBeSelected": "Tag muss ausgewählt werden", "tagNameEmpty": "Tag-Name darf nicht leer sein", "missingThreadId": "<PERSON><PERSON><PERSON><PERSON> threadId", "tagNameNotUnique": "Tag-Name muss ein<PERSON>ig sein.", "tagWithAssociationCanNotDelete": "Dieses Tag ist mit Logs verknüpf<PERSON> und kann nicht gelöscht werden.", "unknownErrorOccured": "Ein unbekannter Fehler ist aufgetreten", "uploadFailedFileTooLarge": "Die hochgeladene Datei ist zu groß", "uploadFileIsEmpty": "Die hochgeladene Datei ist leer", "uploadFilenameInvalid": "Dateiname enthält ungültige Zeichen", "vectorStoreNameRequired": "Name erforderlich. Bitte geben Si<PERSON> einen gültigen Namen an."}, "catchSuccess": {"archiveSuccess": "Archivierung erfolgreich", "bulkDeleteSuccess": "Ausgewählte Chat-Logs wurden erfolgreich gelöscht.", "created": "<PERSON><PERSON><PERSON><PERSON>", "stored": "Gespe<PERSON>rt", "deleted": "Gelöscht", "importSuccessful": "Import war erfolgreich", "importAssistantLogsFinished": "Importiert: {importedTotal} von {total}", "inheritanceRestored": "Vererbung erfolgreich auf Standardeinstellungen zurückgesetzt", "logDeletedSuccessfully": "Log erfolg<PERSON>ich <PERSON>", "resetAllUsageCountersSuccess": "Alle Nutzungszähler wurden zurückgesetzt.", "resetUsageCountersSuccess": "Die Nutzungszähler für die ausgewählten Elemente wurden zurückgesetzt.", "tagCreateSuccessfull": "Tag erfolgreich erstellt!", "tagDeletedSuccessfully": "Tag erfolgreich gelöscht", "tagUpdateSuccessfull": "Tag erfolgreich aktualisiert!", "updatedAliasesForSuccessfully": "Aliase für das Tag {name} erfolgreich aktualisiert.", "updateSuccessful": "Aktualisierung erfolgreich", "savedTagAndAliasesSuccessfully": "Tag {name} und Aliase erfolgreich gespeichert."}, "catchInfo": {"delete": "Löschen bestätigen", "confirmMessage": "Sind <PERSON> sicher, dass Si<PERSON> mit dem Löschen fortfahren möchten?", "Storage State": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filesAttachedSuccessfully": "<PERSON><PERSON> erfolgreich angehängt", "noFilesSelected": "<PERSON><PERSON> zum Anhängen ausgewählt", "selectAssistantAndFile": "Bitte mindestens einen Assistenten und eine Datei auswählen", "errorAttachingToAssistant": "Fe<PERSON> beim <PERSON> an Assistent", "choice": "Treffen Sie eine Auswahl", "choiceMessage": "Bitte wählen Sie eine Option:"}, "catchWarning": {"warning": "<PERSON><PERSON><PERSON>", "attention": "Achtung erforderlich", "selectItemsToDelete": "Bitte wählen Sie Einträge zum Löschen aus."}, "backendAssistant": {"helper": {"name": "5 Elements Backend-Assistent", "label": "Backend-Assistent", "nickname": "BaBu", "noBackEndAssistantInfo": "Verbessern Sie die Fähigkeiten Ihres Plugins mit dem 5 Elements Backend-Assistenten. BaBu ist Ihr verlässlicher Begleiter zum Testen von Serverfu<PERSON>en, Durchfü<PERSON> von Stresstests und Validieren der Effizienz von Toolaufrufen (Funktionen, die von Assistenten aufgerufen werden).", "noBackEndAssistantCreateOne": "<PERSON>licken Sie auf diesen Link und anschließend auf 'Assistent erstellen'.", "tooltip": "<PERSON>rstellen Sie Ihren personalisierten '5 Elements Backend-Assistenten (BaBu)'. Dieser Assistent ist ein vielseitiges Tool, das benutzerdefinierte Inhalte direkt über OpenAI generiert. Die Nutzungskosten sind die gleichen wie für andere Assistenten in Chats. BaBu hat keine Einschränkungen – Sie können über alles chatten. Um verschiedene Funktionen mit BaBu zu testen, aktivieren/daktivieren Sie die gewünschten Funktionalitäten im Abschnitt 'Anpassung der Assistentenfähigkeiten' nach Bedarf. BaBu kann Grundsätzlich keine Funktionen aufrufen, die nicht aktiviert sind. Er wird es in der Regel versuchen, aber höchstwahrscheinlich scheitern.", "description": "Lernen Sie Ihren Backend-Assistenten (BaBu) kennen, der speziell dafür entwick<PERSON>t wurde, Aufgaben wie das Testen der Serverkapazitäten zu unterstützen. Er passt sich dynamisch an die Anforderungen Ihrer Aufgaben an. Konfigurieren Sie seine Fähigkeiten, um Ihren Bedürfnissen gerecht zu werden.\n\nDer Assistent wird durch seinen Namen identifiziert, stel<PERSON> also sicher, dass der Name unverändert bleibt."}}, "instructions": {"chatbotInstructionsName": "Chatbot-Anweisungen", "chatbot": {"gptThree": "GPT-3", "gptFour": "GPT-4"}}, "form": {"toReadable": "<PERSON><PERSON>", "willBeSavedIn": {"salesChannel": "Vertriebskanal: {salesChannel}", "allSalesChannel": "Alle Vertriebskanäle"}}, "filter": {"categoriesBlacklistNotNecessaryDescription": "Wenn Sie fehlende Kategorien in dieser Auswahl bemerken, <PERSON><PERSON> Si<PERSON>, dass unser Plugin nur Kategorien berücksichtigt, die Teil der Hauptnavigation sind. Kategorien in anderen Abschnitten, wie der Service- oder Fußzeilennavigation, sind für Assistenten nicht zugänglich.", "CategoriesPlaceholder": "<PERSON><PERSON><PERSON> filtern…", "filterCategories": "<PERSON><PERSON><PERSON>", "filterManufacturer": "<PERSON><PERSON><PERSON>", "filterProperties": "Eigenschaften", "generalName": "Filter", "manufacturerPlaceholder": "<PERSON><PERSON><PERSON> filtern…", "maxLength": "Maximale Länge", "more": "<PERSON><PERSON>", "propertiesPlaceholder": "Eigenschaften filtern…", "propertyGroupPlaceholder": "Eigenschaftsgruppen filtern…", "ui": {"autoSubmit": "automatisch senden", "categories": "<PERSON><PERSON><PERSON>", "false": "aus", "filteringFailedNoResultsFound": "<PERSON><PERSON> Ergebnisse gefunden. Bitte passen Sie Ihre Suchkriterien an.", "hideFilter": "<PERSON><PERSON> au<PERSON>", "limit": "Limit", "liveFilter": "Live-Filter", "noResults": "<PERSON><PERSON>", "order": "sortieren", "price_max": "Maximaler <PERSON>", "price_min": "Minimaler Preis", "query": "Anfrage", "setVisibleToConfig": "Alle sichtbaren auswählen", "showFilter": "Filter anzeigen", "showSelectedOnly": "Nur ausgewählte Elemente anzeigen", "start": "Start", "submit": "Senden", "true": "ein", "type": "Typ: {type}"}}, "contentCheck": {"noInstalledPluginsToCheck": "Derzeit sind keine 5 Elements Plugins aktiviert.", "installedPluginNotActive": "Die folgenden Plugins sind installiert, aber nicht aktiviert: {plugins}", "clearPrefetchedData": "Vorab geladene <PERSON>n löschen", "testRedirectionToUrl": "Weiterleitung zu URL testen: ", "clickToToggleReadability": "<PERSON><PERSON><PERSON>, um zwischen einem lesbaren und einem weniger lesbaren Format umzuschalten", "customConfigForSalesChannelApplied": "Die Standardkonfiguration wird mit Einstellungen aus dem Vertriebskanal {salesChannelName} gemischt.", "customConfigurationHasBeenApplied": "Benutzerdefinierte Konfiguration für '{getCustomPluginConfig}' wurde geladen und wird auf alle Anfragen angewendet.", "expertView": "Expertenansicht", "formatToReadableFormat": "In ein lesbares Format umwandeln", "generalDescription": "Diese Seite ermöglicht es I<PERSON>en, Assistenten zu simulieren. Die vom Tool zurückgegebenen Antworten sind die gleichen, die Assistenten erhalten, wenn sie Funktionen (Tools) aufrufen. Wählen Sie eine Plugin-Konfiguration für reale Szenarien aus, bei denen die Blacklist- und Whitelist-Optionen der ausgewählten Konfiguration angewendet werden. Standardmäßig werden alle zugänglichen Inhalte ausgegeben.", "getCustomConfiguration": "Konfiguration anzeigen", "loadPluginConfig": "Plugin-Konfiguration laden", "missingAccessData": "Fehlende Zugangsdaten. Bitte stellen Sie sicher, dass der Vertriebskanal-Zugangsschlüssel und die Domain gesetzt sind.", "prefetchedData": "<PERSON><PERSON><PERSON> gelade<PERSON>", "prefetchedDataDescription": "Diese Daten sind eine erweiterte Version von 'get_product_properties', die zusätzliche IDs und benutzerdefinierte Formate für den internen Gebrauch enthält. <PERSON>e werden verwendet, um die Eingaben zu validieren, die Assistenten beim Filtern von Produkten oder anderen Entitäten setzen. Obwohl Benutzer Eigenschaften auf die Blacklist oder Whitelist setzen können, wirken sich diese Ausschlüsse nicht auf die vorab geladenen Daten aus. Die Daten repräsentieren die gleichen Informationen, die der Assistent verwendet, mit zusätzlichen Details für Validierungszwecke.", "prefetchProductProperties": "Produkteigenschaften vorab laden", "readableFormat": "Lesbares Format: Der Inhalt ist für eine einfachere Lesbarkeit formatiert.", "responseTitle": "Dies ist die Antwort, die der Assistent erhält, wenn er '{function}' an<PERSON><PERSON>, ca. '{strLength}' <PERSON><PERSON><PERSON>.", "salesChannel": "Vertriebskanal", "salesChannelAccessKey": "SW Zugangsschlüssel", "salesChannelDomains": "Vertriebskanal-Domains", "salesChannelDomain": "Vertriebskanal-Domain", "domain": "Domain", "selectFunction": "Auswählen", "selectRequiredAction": "Anfrage", "selectSalesChannelDomain": "Wählen Sie eine Domain", "customPluginConfig": "Benutzerdefiniertes Plugin", "noCustomPluginConfigSelected": "<PERSON>ine Konfiguration ausgewählt", "selectSalesChannelLabel": "Vertriebskanal auswählen", "withIds": "Mit IDs"}, "storageManager": {"deleteAll": "Alle löschen", "description": "Temporärer Speicher für dynamisch geladene Daten innerhalb der Anwendung.", "disableStorage": "<PERSON><PERSON><PERSON><PERSON> deaktivieren", "enableStorage": "Speicher aktivieren", "hasContent": "Speicher aktiv", "isEmpty": "<PERSON><PERSON><PERSON><PERSON> ist leer", "items": "Elemente", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "storages": "<PERSON><PERSON><PERSON><PERSON>", "cacheName": {"assistantRequestCache": "Assistenten-http-<PERSON><PERSON><PERSON><PERSON>", "contentEditor": "Inhaltseditor-Speicher", "customPluginConfigLoaded": "Benutzerdefinierte Plugin-Konfiguration", "managerIsLoading": "Anwendungsstatus-Speicher"}}, "log": {"mainMenuItemGeneral": "Logs verwalten", "description": "Die Logs auf dieser Seite werden von Assistenten erstellt, um Interaktionen zu dokumentieren, insbesondere wenn Informationslücken auftreten. Bitte überprüfen und bearbeiten Sie nicht beantwortete Nachrichten direkt auf der Log-Bearbeitungsseite, um sicherzustellen, dass Ihre Assistenten alle Informationen haben, die sie benötigen, um mit hoher Genauigkeit zu arbeiten.", "addNewLog": "Neues Log hinzufügen", "alias": "<PERSON><PERSON>", "answer": "Antwort", "answered": "Beantwortet", "areYouSureToDeleteLog": "Sind <PERSON> sicher, dass Sie dieses Log löschen möchten?", "assistantId": "Assistenten-ID", "assistantLogs": "Assistenten-Logs", "assisttantIdNotAvailable": "Die Assistenten-ID existiert nicht, möglicherweise gelöscht.", "assisttantIdsInLogsNotAvailable": "Die folgenden Assistenten existieren nicht", "backToPage": "<PERSON><PERSON><PERSON> <PERSON>: {page}", "categoryId": "Kategorie-ID", "checkLogs": "Logs prüfen", "clearFilters": "<PERSON><PERSON>", "createATag": "Einen Tag erstellen", "createALog": "Einen Log erstellen", "isItResolved": "G<PERSON>öst?", "import": "Importieren", "importHelp": "Importiere Assistenten Logs", "logs": "Logs", "read": "<PERSON><PERSON>", "write": "Schreiben", "message": "Nachricht", "metaData": "<PERSON><PERSON><PERSON> (JSON)", "noDataTitle": "<PERSON>ine Logs gefunden", "note": "Notiz", "noteIsPrivate": "<PERSON>iz (privat)", "noteDescription": "Verwenden Sie dieses Feld für private Notizen für den internen Gebrauch. Diese Notizen werden nicht mit Assistenten geteilt und dienen ausschließlich als Referenz oder Erinnerung. Notizen werden beim Filtern berücksichtigt.", "notePlaceholder": "Notiz", "noTagsAvailable": "Keine Tags verfügbar", "prepareDescription": "Sie können auch Daten im Voraus vorbereiten, indem Sie Tags definieren, Logs (Fragen) für die Tags erstellen und besagte Logs direkt Beantworten. Dies stellt sicher, dass wenn ein Assistent die Logs durchsucht, er auch die entsprechenden Informationen findet. Sie können das Feld Tag-Alias auch für Suchbegriffe verwenden, da Assistenten des öfteren auch Schlüsselwörter als Tags verwenden, selbst wenn es ausdrücklich anders definiert ist.", "priority": "Priorität", "selectAssistant": "Assistent ausw<PERSON>hlen", "selectSalesChannel": "Vertriebskanal", "selectTag": "Tag", "fieldsToConnectData": "Assistenten verwenden oft flexible und unvorhersehbare Formulierungen bei der Suche nach Daten. Diese <PERSON>lder ermöglichen es, Inhalte im Hintergrund zu verknüpfen, sodass Assistenten die richtigen Informationen finden – unabh<PERSON><PERSON><PERSON> davon, wie die Anfrage gestellt wird.", "searchTerms": "Suchbegriffe", "searchTermsDescription": "G<PERSON>en Sie hier kommagetrennte Suchbegriffe ein, mit denen diese Daten gefunden werden sollen. Sie können auch Begriffe eingeben, die noch nicht existieren – diese werden später als reguläre anklickbare Button angezeigt.", "tagAliases": "Tag Aliase", "tagAliasesDescription": "<PERSON><PERSON><PERSON> Sie hier alternative Tag-Namen ein, unter denen Assistenten diese Informationen finden sollen. Diese Daten werden nicht an Assistenten weitergegeben, sondern intern bei der Suche berücksichtigt. So können Sie Informationen vernetzen und zentral pflegen, ohne die Datenmenge für Assistenten unnötig zu vergrößern.", "tagUsage": "Tag-Nutzung", "tagUsageCounterDisabled": "Tag-Nutzungszähler ist deaktiviert", "writeAttempts": "Schreibversuche", "descriptions": {"aboutRecords": "Bestimmte Einträge werden ausschließlich für Filterzwecke gespeichert und beeinflussen nicht die Daten, die Assistenten erhalten. Assistenten haben immer Zugriff auf alle Einträge, es sei denn, sie sind als privat markiert. Zusätzlich sind diese Einträge nicht durch Vertriebskanäle oder einzelne Assistenten eingeschränkt; besagte Daten werden intern für Filter- und Organisationszwecke verwendet.", "aboutAssistantRetrieval": "Wenn ein Assistent die Logs nach spezifischen Tags (oder mehreren Tags) durchsucht, ruft er alle Felder ab, die mit diesen Tags verknüpft sind. Sie können unser eingebautes Inhaltsprüfer-Tool mit der Standardkonfiguration verwenden, um eine Vorschau der Daten zu erhalten, die Assistenten erhalten, wenn sie die Log-Funktionen verwenden.", "fetchUrl": "Assistenten können auch den Inhalt einer URL abrufen. Statt Daten manuell zu kopieren und einzufügen, weise den Assistenten einfach an, die URL zu verwenden. Zum Beispiel: \"Die benötigten Informationen findest du unter: https://example.com. Nutze das Tool `fetch_url()` und verwerfe die Daten nach dem Abruf.\" Hinweis: Wenn ein Log bereits relevante Informationen enthält, kann der Assistent diese bevorzugen und den externen Abruf überspringen. Logs dienen als verlässliche interne Informationsquelle und sollten regelmäßig gepflegt werden, um unnötige externe Anfragen zu vermeiden.", "tagFieldInfo": "Das Tag-Feld verfügt über eine 'Mehr'-Schaltfläche. Be<PERSON> werden zusätzliche Felder zur Feinabstimmung angezeigt. Außerdem werden alle Aliasnamen und Suchbegriffe als klickbare Schaltflächen aufgelistet. Diese <PERSON>lder helf<PERSON> da<PERSON>, Daten unsichtbar zu verk<PERSON>, sodass Assistenten auch dann die richtigen Informationen finden, wenn der eigentliche Tag-Name nicht genannt wurde.", "fullFaqNote": "Setzen Sie die Nachricht auf FEL_FULL_FAQ, um Einträge als spezialisierte FAQ-Daten zu kennzeichnen. Diese Datensätze werden nur abgerufen, wenn Sie über search_logs() mit der Abfrage FEL_FULL_FAQ suchen, wodurch sie für spezifische Aufgaben mit unserem 5 Elements Backend Assistant gen<PERSON>t werden können."}, "priorities": {"low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch"}, "download": {"asCSV": "CSV", "asJSON": "JSON", "name": "Download"}, "filter": {"assistant": "Assistent", "selectPriority": "Priorität", "selectResolved": "<PERSON><PERSON><PERSON><PERSON>", "selectPrivate": "Privat", "selectThreadDeleted": "Gelöscht", "applyFilters": "<PERSON><PERSON> anwenden", "filterTagsAndAliases": "Tag-Namen und Aliase filtern", "label": "Filter", "all": "Alle", "active": "Filter aktiv", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "private": "Privat", "privateInfo": "<PERSON>n Si<PERSON> einen Eintrag als privat markieren, bleibt er für Assistenten verborgen, was ihn für interne Notizen oder sich in der Bearbeitung befindenden Nachrichten nützlich macht. Dar<PERSON>ber hinaus schützt das Plugin vor doppelten Einträgen, die von Assistenten erstellt werden könnten, un<PERSON><PERSON><PERSON><PERSON><PERSON> davon, ob der Eintrag privat ist oder nicht.", "unresolved": "<PERSON><PERSON><PERSON><PERSON>", "showMoreFilter": "<PERSON><PERSON> Filter anzeigen", "threadDeleted": "Gelöscht", "threadNotDeleted": "<PERSON><PERSON>", "threadMessagesPlaceholder": "Nachrichten filtern"}, "tag": {"areYouSureToDeleteTag": "Sind <PERSON> sicher, dass Sie dieses Tag löschen möchten?", "tagsAreRequiredFor": "Tags sind für die Funktionen erforderlich: ", "backToTags": "<PERSON><PERSON><PERSON> zu Tags", "tagsAndAliasesList": "Tags und Aliase", "countAssistantsDescription": "Tool-Aufrufe", "countAssistantsLabel": "Tool-Nutzungszähler", "createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "description": "Die auf dieser Seite definierten Tags werden von Assistenten verwendet, um Nachrichten zu kategorisieren und zu protokollieren, was hi<PERSON><PERSON>, Gespräche für eine einfachere Analyse und Verwaltung zu organisieren und zu filtern. Um Ihre Tag-Liste schlank zu halten, können Sie Aliase als alternative Namen für ein einzelnes Tag verwenden. Dies ermöglicht Flexibilität für Assistenten, die gelegentlich unerwartete oder verschiedene Tag-Namen verwenden (oder gar Suchbegriffe direkt als Tags verwenden), sodass Sie sie unter einem bevorzugten Tag mithilfe von Aliase gruppieren können.", "isActive": "Aktiv", "mainMenuItemGeneral": "<PERSON>s verwalten", "noRelatedData": "<PERSON><PERSON> zu<PERSON>hörig<PERSON> Daten", "noTagsFound": "Keine Tags gefunden", "saveMustHaveTags": "Alle erforderlichen Tags speichern", "saveTag": "<PERSON> speichern", "selectedTags": "Tags: {tags}", "mustHaveTags": "Must-Have-Tags e<PERSON>ch<PERSON><PERSON><PERSON> Aliase", "resetUsageCounter": "Nutzungszähler zurücksetzen", "resetAllUsageCounter": "Alle Nutzungszähler zurücksetzen", "resetSelectedUsageCounter": "Ausgewählte Nutzungszähler zurücksetzen", "confirmResetAllUsageCounter": "Diese Aktion löscht alle Zähler-Einträge in der Tabelle 'fel_assistant_tag_usage'.", "noAliasesAssigned": "<PERSON>ine Aliase dem Tag zugewiesen.", "tag": "Tag", "tags": "Tags", "tagAlias": "Tag-Alias", "tagAliasDescription": "Geben Sie eine durch Kommas getrennte Liste von alternativen Namen und Suchbegriffen für dieses Tag ein. Diese Aliase werden innerhalb der Suche verwendet, bleiben aber für Assistenten verborgen. Dies spart zum einen Token und vereinfacht das prozessieren der Tags für Assistenten erheblich.", "tagName": "Tag-Name", "tagAliases": "Tag-Aliase", "aliases": "<PERSON><PERSON>", "tagNamePlaceholder": "Tag-Name eingeben", "tagTips": "5E Tag-Tipps", "totalTags": "{tagNames} Tags, {tagAliases} Aliase"}}, "chatlog": {"mainMenuItemGeneral": "Chat-Logs", "description": "Die Logs auf dieser Seite sind Chat-Logs, die Gespräche zwischen Benutzern und Assistenten erfassen. Jeder Log-Eintrag enthält das finale, optimierte Gespräch, das der Assistent zurückgibt. Es umfasst Benutzeranfragen und Assistentenantworten. Diese Funktion hilft Ihnen, KI-gesteuerte Interaktionen zu verfolgen und sicherzustellen, dass die generierten Antworten von Assistenten Ihren Erwartungen entsprechen.", "addNew": "Ein Chat-Log hinzufügen", "archiveSelected": "Archivieren", "bulkDeleteButton": "Ausgewählte Elemente löschen", "cancel": "Abbrechen", "tokenUsage": "Token-Nutzung: {tokenUsedTotal}", "calculateTokenUsage": "Token-Nutzung berechnen", "calculateTokenUsageForAll": "Token-Nutzung für alle berechnen", "chatlogDisabled": "Chat-Log ist deaktiviert", "details": "Details", "nextChatInModal": "Nächsten Chat im Modal öffnen", "previousChatInModal": "Vorherigen Chat im Modal öffnen", "noDataTitle": "<PERSON><PERSON> gefunden", "none": "keine", "oaiUsage": "last_oai_usage", "requestTook": "last_request_took", "tokenUsedTotal": "Chats: {chatsTotal}, Token: {tokenUsedTotal}", "issuesFound": "{failed} f<PERSON><PERSON><PERSON><PERSON><PERSON>, {errors} <PERSON><PERSON>", "save": "Speichern", "selectItemsToDelete": "Bitte wählen Si<PERSON> mindestens ein Element zum Löschen aus.", "showTrackedData": "Ereignisdaten anzeigen", "unarchiveSelected": "Archivierung aufheben", "column": {"salesChannelId": "Vertriebskanal-ID", "threadId": "threadId", "assistantId": "Assistenten-ID", "threadMessages": "Thread-<PERSON><PERSON><PERSON><PERSON>", "threadDeleted": "Gelöscht", "threadArchived": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON>ktual<PERSON><PERSON>", "runsCount": "Chats", "tokenUsage": "Token-Nutzung"}}, "chat": {"allow": "Erlauben", "autoScroll": "Automatisch zur letzten Nachricht scrollen", "capabilities": "Fähigkeiten", "continueThreadDescription": "Sie können protokollierte Threads aus der Datenbank fortsetzen, solange sie nicht explizit gelöscht oder als beendet markiert wurden. Das abrufen von laufenden Threads verursacht keine zusätzlichen Kosten, da es einfach den letzten Stand des Threads aus der Datenbank oder auch direkt von OpenAI lädt. Bitte beachten Si<PERSON> jedoch, dass einige dieser Threads offene Kundenchats sein könnten. Das Fortsetzen dieser Chats könnte zu Verwirrung bei Kunden führen, da sie zusätzliche Antworten sehen würden, die nicht mit den vorherigen Interaktionen übereinstimmen. Um Missverständnisse zu vermeiden, sollten Sie die Fortsetzungsfunktion nur für interne Threads verwenden oder sicherstellen, dass Kunden-Threads sorgfältig behandelt werden, ohne zusätzliche Antworten zu senden, es sei denn, dies ist ausdrücklich beabsichtigt. OpenAI speichert bis zu maximal 20 Chat-Nachrichten pro Thread als eine Art \"Kurzzeitgedächtnis\" und optimiert die gespeicherten Nachrichten automatisch nach Relevanz. Die vom Plugin gespeicherten Logs sind im Gegensatz dazu vollständig, alle ausgetauschten Nachrichten werden erfasst.", "customConfigWillBeOverridden": "Der ausgewählte Assistent überschreibt die in loadCustomConfig angegebene AssistantID, falls vorhanden. Zusätzlich wird der API-Schlüssel des Assistenten-Managers für nachfolgende Anfragen verwendet.", "decline": "<PERSON><PERSON><PERSON><PERSON>", "deleteThread": "Thread l<PERSON> (löscht den Thread bei OpenAI)", "description": "Chatten Si<PERSON> mit Ihren OAI-Assistenten", "empty": "leer", "errorPutMessageToInputNoSubmit": "Nachricht zurück ins Eingabefeld kopieren, nicht abschicken.", "failedToCreateThread": "Thread konnte nicht erstellt werden. Bitte versuchen Si<PERSON> es erneut.", "failedToSendMessage": "Na<PERSON>richt konnte nicht gesendet werden. Bitte versuchen Sie es erneut.", "failedWhileCheckingStatus": "Beim Überprüfen des Status ist ein Fehler aufgetreten.", "failedWhileFetchingMessages": "Beim Abrufen der Nachrichten ist ein Fehler aufgetreten.", "failedWhileSendingYourData": "Beim Senden Ihrer Nachricht ist ein Fehler aufgetreten.", "hideChatConfig": "Chat-Konfiguration ausblenden", "instructions": "Anweisungen", "label": "Playground", "mainMenuItemGeneral": "Playground-Chat", "noAssistantsAvailable": "<PERSON><PERSON>n verfügbar", "notifications": "Benachrichtigungen", "promptPlaceholder": "G<PERSON><PERSON> Sie hier Ihre Nachricht ein…", "runIdPlaceholder": "RunId", "requiredAction": "Aktion Erforderlich", "requirements": "Anforderungen", "resume": "Fortsetzen", "resumeChatWithThreadId": "Chat mit einer threadID fortsetzen", "routeDescription": "Diese Seite ermöglicht es Ihnen, <PERSON>hre Assistenten in einem Live-<PERSON><PERSON> zu testen", "search": "<PERSON><PERSON>", "send": "Senden", "showChatConfig": "Chat-Konfiguration anzeigen", "shuffle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop": "Stop", "stopping": "Stoppt", "storefrontUrlNotValid": "Die storefrontUrl ist keine gültige URL", "suggestions": "Vorschläge", "tempRequestData": "Temporä<PERSON>", "toggleAssistantDetails": "Assistentendetails umschalten", "toggleChatInputField": "Chat-Eingabefeld umschalten", "total": "Gesamt: {total}", "withRunId": "<PERSON><PERSON>", "noRunId": "Neue RunId", "withThreadId": "Mit ThreadId", "willUseAssistantsManagerCredentials": "Der ausgewählte Assistent und der API-Schlüssel aus dem Assistenten-Manager werden für alle OpenAI-API-Anfragen verwendet.", "tool": {"label": "Chat-Tools", "description": "Unten finden Sie Schaltflächen mit Anweisungen zum Testen der Fähigkeiten des Assistenten oder zum Ausführen komplexer Aufgaben. Die Token-Kosten variieren je nach den Daten in Ihrem Shop, wie Eigenschaften und Kategorien. Nach dem Klicken auf eine Schaltfläche erscheint die Anweisung im Nachrichtenfeld zur Überprüfung und optionalen Anpassung. Sie müssen auf 'Senden' klicken, um die Anweisung auszuführen.", "requiredTools": "Erforderliche Tools: ", "productProperties": {"label": "Produkteigenschaften prüfen", "description": "Nutzen Sie unser 'Inhaltsprüfer'-Too<PERSON> zur vorschau von 'get_product_properties', bevor <PERSON> diese Instruktion ausführen. Dies ist entscheidend für Shops mit umfangreichen Eigenschaften oder Kategorien, die zu hohen Token-Kosten führen. Verwenden Sie das Inhaltsprüfer-Tool, um die Daten zu verstehen, die Assistenten verarbeiten werden und optimieren Sie diese Daten mit unserem Inhaltseditor-Tool."}, "logMessages": {"label": "Assistenten-Nachrichten protokollieren", "description": "Führen Sie einen allgemeinen Log-Test durch, der realistische Assistenten-Logs simuliert. Dies dient auch als Leistungstest, indem Dutzende von Assistenten-Logs gleichzeitig angefordert und verarbeitet werden, um potenzielle Engpässe zu identifizieren."}, "toolsPerformance": {"label": "Tools-Leistungstest", "description": "Bewerten Sie die Systemleistung, indem Sie mehrere Backend-Tools in einem Durchgang testen. Überprüfen Sie nahtlose Funktionsausführung und identifizieren Sie Leistungsgrenzen. Aktivieren sie möglichst viele Funktionen für BaBu."}}, "autoChat": {"label": "Auto-Chat-Tool", "maxMessages": "<PERSON>", "runAutoChat": "Chat <PERSON>", "timeout": "Pause in ms", "autoQuestions": "Auto Fragenliste", "enterSetOfQuestions": "<PERSON><PERSON><PERSON> Si<PERSON> hier eine Liste mit Fragen für den Auto-Chat ein.", "clearStoredData": "Aufräumen", "storeQuestionsToLocalStorage": "Daten Temporär im localStorage des Browsers speichern", "storeQuestionsToLocalStorageInfo": "Der localStorage ist nur ein Temporärer Speicher, die Fragenliste wird nicht dauerhaft gespeichert."}, "missingRequired": {"salesChannelId": "Vertriebskanal-ID fehlt", "storefrontUrl": "Storefront-URL fehlt", "felOpenAiApiKey": "OpenAI-API-Schlüssel fehlt", "felOpenAiAssistantId": "Assistenten-ID fehlt"}, "threadArchive": {"label": "Protokollierte aktive Threads"}}, "instructionIssues": {"title": "Anweisungsprobleme", "note": "Diese Hinweise dienen ausschließlich zu Informationszwecken. Da die Anweisungen als einfacher Text vorliegen und frei bearbeitet werden können, können wir sie nicht automatisch auswerten oder korrigieren — wir können aber auf mögliche Inkonsistenzen hinweisen.", "conflictingTools": "Die Tools <b>{toolB}()</b> und <b>{toolA}()</b> sind beide aktiviert. Dies kann zu Fehlern führen, da beide ähnliche Aufgaben erfüllen oder Inhalte gleichen Ursprungs teilen. E<PERSON> wird empfoh<PERSON>, nur eines dieser Tools zu aktivieren – abhängig vom Einsatzzweck des Assistenten.", "productPropertyNote": "<b><PERSON><PERSON><PERSON><PERSON></b><br><PERSON> <b>get_product_properties()</b> stellt eine vollständige Übersicht über Optionen, Eigenschaften, Hersteller und Kategorien bereit – also alle Daten, die für Produktsuche und Empfehlungen benötigt werden. Wenn der Assistent Produkte suchen oder empfehlen soll, solltest du <b>get_categories()</b> und <b>get_manufacturer()</b> deaktivieren, um Redundanzen zu vermeiden, da beide intern ihre Daten von <b>get_product_properties()</b> beziehen.", "searchLogsAggressiveUsage": "<b><PERSON><PERSON><PERSON><PERSON></b><br><PERSON><PERSON> <b>search_logs()</b> aktiv<PERSON>t ist, neigen Assistenten dazu, diese Funktion intensiv zu nutzen. Um sinnvolle und effiziente Antworten zu gewährleisten, sollten die zurückgegebenen Log-Einträge so präzise und kompakt wie möglich sein. Der Assistant-Manager stellt alles Notwendige bereit — einsch<PERSON>ßlich <b>Tags</b>, <b>TagAliases</b> und <b>SearchTerms</b> für jeden Eintrag — ist jedoch auf deine Mithilfe angewiesen, um die Informationen in klare, strukturierte Abschnitte aufzuteilen. Das verbessert nicht nur die Antwortqualität, sondern reduziert auch die Token-Nutzung um den Faktor 3 bis 5. Der Performance-Boost ist dabei kostenlos inklusive — als Belohnung für saubere Daten.", "logFunctionNote": "<b><PERSON><PERSON><PERSON><PERSON></b><br>Assistenten sind nicht immer zuverlässig, wenn es um das Protokollieren von Problemen geht – manchmal tun sie es, manchmal nicht. Damit dennoch nichts verloren geht, haben wir einen Fallback eingebaut: Immer wenn ein Auf<PERSON><PERSON> von <b>search_logs()</b> f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, wird die Anfrage automatisch protokolliert. So wird sichergestellt, dass relevante Informationen gespeichert werden, auch wenn der Assistent <b>log()</b> nicht explizit aufruft. <b>Best Practice:</b> Lass den Assistenten einfach laufen und beobachte, was tats<PERSON><PERSON><PERSON> in den Assistants-Logs landet. Das ist die ursprüngliche Suchanfrage – auf dieser Basis kannst du gezielter optimieren.", "gpt4turboNotSupportedWithSchema": "Das \"response_format\" des Typs \"Schema\" wird nicht mit der Modellversion \"GPT-4-Turbo\" unterstützt.", "issues": "<PERSON> Tool <b>{tool}</b> wird {count} mal in den Anweisungen erwähnt, ist jedoch nicht unter den ausgewählten Tools enthalten."}, "notifications": {"suggestionTitle": "Empfehlung", "noFileSelected": "<PERSON><PERSON> für den Import ausgewählt.", "logsImported": "{0} log (s) erfolgreich importiert.", "errorWhileImportingLogs": "<PERSON>s gab einen Fehler beim Importieren der Protokolle."}, "exception": {"failedToInitThread": "OAI: <PERSON><PERSON> konnte kein Thread erstellt werden: {message}", "maxLoopAttemptsReached": "Maximale Anzahl von Schleifenversuchen erreicht.", "missingActionInRequest": "ChatService: Aktion in Anfrage fehlt", "missingAssistantIdForCreateThread": "Assistenten-ID fehlt. Erforderlich für: create_thread.", "missingAssistantIdForSendMessage": "Assistenten-ID fehlt. Erforderlich für: send_message.", "missingMessageForCreateThread": "Nachricht fehlt. Erforderlich für: create_thread.", "missingMessageForSendMessage": "Nachricht fehlt. Erforderlich für: send_message.", "missingMessageIdForDeleteMessage": "Nachrichten-ID fehlt. Erforderlich für: delete_message.", "missingOpenaiApiKey": "ChatService: OpenAI-API-Schlüssel in Anfrage fehlt", "missingOpenaiBaseUrl": "ChatService: OpenAI-Basis-URL in Anfrage fehlt", "missingRunIdForCheckStatus": "Run-ID fehlt. Erforderlich für: check_run_status.", "missingThreadIdForCheckStatus": "Thread-ID fehlt. Erforderlich für: check_run_status.", "missingThreadIdForDeleteMessage": "Thread-ID fehlt. Erforderlich für: delete_message.", "missingThreadIdForDeleteThread": "Thread-ID fehlt. Erforderlich für: delete_thread.", "missingThreadIdForFetchMessages": "Thread-ID fehlt. Erforderlich für: fetch_messages.", "missingThreadIdForSendMessage": "Thread-ID fehlt. Erforderlich für: send_message.", "missingThreadIdForGetThread": "Thread-ID fehlt. Erforderlich für: %s."}}}