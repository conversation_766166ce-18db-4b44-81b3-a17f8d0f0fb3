{"attempts": 1, "threadId": "thread_ISISiqGUmCrwAGLxeBHhftRW", "runId": null, "lastRunId": "run_rKuYLIizjGRabeHzDPFE51x1", "message": null, "error": null, "status": "completed", "getStatus": {"id": "run_rKuYLIizjGRabeHzDPFE51x1", "object": "thread.run", "created_at": 1748762879, "assistant_id": "asst_ZLw4yKcC3ktRSLmDYBd0vw7F", "thread_id": "thread_ISISiqGUmCrwAGLxeBHhftRW", "status": "completed", "started_at": 1748762880, "expires_at": null, "cancelled_at": null, "failed_at": null, "completed_at": 1748762881, "required_action": null, "last_error": null, "model": "gpt-4o-mini", "instructions": "You are a customer support chatbot in a Shopware web shop environment ...", "tools": [], "tool_resources": [], "metadata": [], "temperature": 0.3, "top_p": 1, "reasoning_effort": null, "max_completion_tokens": null, "max_prompt_tokens": null, "truncation_strategy": {"type": "auto", "last_messages": null}, "incomplete_details": null, "usage": {"prompt_tokens": 2802, "completion_tokens": 27, "total_tokens": 2829, "prompt_token_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}, "response_format": "auto", "tool_choice": "auto", "parallel_tool_calls": true}, "threadMessages": {"object": "list", "data": [{"id": "msg_7BM1mddxN9QVwURSMX8FdkvZ", "object": "thread.message", "created_at": 1748762878, "assistant_id": null, "thread_id": "thread_ISISiqGUmCrwAGLxeBHhftRW", "run_id": null, "role": "user", "content": [{"type": "text", "text": {"value": "hallo", "annotations": []}}], "attachments": [], "metadata": []}, {"id": "msg_lWGXoosTZBI1aFWHSZuvIJG7", "object": "thread.message", "created_at": 1748762881, "assistant_id": "asst_ZLw4yKcC3ktRSLmDYBd0vw7F", "thread_id": "thread_ISISiqGUmCrwAGLxeBHhftRW", "run_id": "run_rKuYLIizjGRabeHzDPFE51x1", "role": "assistant", "content": [{"type": "text", "text": {"value": "<div class=\"fel-oai-response\">\n    <p><PERSON><PERSON>! Wie kann ich Ihnen helfen?</p>\n</div>", "annotations": []}}], "attachments": [], "metadata": []}], "first_id": "msg_lWGXoosTZBI1aFWHSZuvIJG7", "last_id": "msg_7BM1mddxN9QVwURSMX8FdkvZ", "has_more": false, "last_thread": [{"id": "msg_7BM1mddxN9QVwURSMX8FdkvZ", "object": "thread.message", "created_at": 1748762878, "assistant_id": null, "thread_id": "thread_ISISiqGUmCrwAGLxeBHhftRW", "run_id": null, "role": "user", "content": [{"type": "text", "text": {"value": "hallo", "annotations": []}}], "attachments": [], "metadata": []}, {"id": "msg_lWGXoosTZBI1aFWHSZuvIJG7", "object": "thread.message", "created_at": 1748762881, "assistant_id": "asst_ZLw4yKcC3ktRSLmDYBd0vw7F", "thread_id": "thread_ISISiqGUmCrwAGLxeBHhftRW", "run_id": "run_rKuYLIizjGRabeHzDPFE51x1", "role": "assistant", "content": [{"type": "text", "text": {"value": "<div class=\"fel-oai-response\">\n    <p><PERSON><PERSON>! Wie kann ich Ihnen helfen?</p>\n</div>", "annotations": []}}], "attachments": [], "metadata": []}], "last_chat_offset": 0, "count_last_thread": 2, "last_message": "hallo", "information_gaps": []}, "tracked": [{"init_thread": 1748762878.149507}, {"send_message": 1748762878.549647}, {"get_run_status": 1748762881.275624}, {"fetch_messages": 1748762883.737564}, {"request_took": 5.5881}, {"oai_usage": {"prompt_tokens": 2802, "completion_tokens": 27, "total_tokens": 2829, "prompt_token_details": {"cached_tokens": 0}, "completion_tokens_details": {"reasoning_tokens": 0}}}], "requiredActions": [], "action": null, "dataBag": {"message": null, "threadId": "thread_ISISiqGUmCrwAGLxeBHhftRW", "action": null, "autoHandleChat": true, "lastMessage": "hallo", "runId": null, "lastMessageId": null, "lastRunId": "run_rKuYLIizjGRabeHzDPFE51x1"}, "assistantId": "asst_ZLw4yKcC3ktRSLmDYBd0vw7F", "isBeChat": false, "trackApiRequest": {"message": "no tracked data"}, "trackTimeouts": [[]], "suggestions": []}