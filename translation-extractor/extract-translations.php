#!/usr/bin/php
<?php error_reporting(E_ALL);

class TranslationExtractor
{
    private $srcDir = [];
    private $sourceFiles = [];
    private $translations = [];
    private $translationFiles = [];
    private $translationData = [];
    private $config = [
        'extensions' => ['twig', 'js', 'json'],
        'translationFunctions' => ['\$t', 'this\.\$t'],
        'ignore' => [
            'prefix' => [],
            'removePrefix' => []
        ],
        'dynamicParts' => []
    ];
    private $translationSummary = [];
    private $comparisonSummary = [];
    private $unusedTranslations = [];

    public function __construct($srcDir, $config = [])
    {
        if (!is_dir($srcDir)) {
            die("Error: Invalid directory provided.");
        }

        $this->srcDir = $srcDir;
        $this->config = array_replace_recursive($this->config, $config);
        $this->sourceFiles = $this->fetchFiles($srcDir, $this->config['extensions']);
        $this->translationFiles = $this->fetchFiles($srcDir, ['json']);

        if (empty($this->sourceFiles)) {
            die("Error: No source files found in: $srcDir.");
        }

        $this->translationData = $this->loadTranslationData($this->translationFiles);
        $this->translations = $this->extractTranslations($this->sourceFiles);
        $this->reverseCheckTranslations();
        $this->printSummary();
    }

    // Fetch all files in the directory based on allowed extensions
    private function fetchFiles($dir, $extensions)
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($iterator as $file) {
            if (in_array(pathinfo((string) $file, PATHINFO_EXTENSION), $extensions)) {
                $files[] = $file->getPathname();
            }
        }
        return $files;
    }

    // Extract translations from the source files
    private function extractTranslations($files)
    {
        $translations = [];
        $pattern = '/\$t\(\s*\'(.*?)\'\s*(?:,|\))/';

        foreach ($files as $file) {
            $content = file_get_contents($file);
            preg_match_all($pattern, $content, $matches);
            if (!empty($matches[1])) {
                foreach ($matches[1] as $match) {
                    // Split by comma and take the first part (key)
                    $translations[] = explode(',', $match)[0];
                }
            }
        }
        return array_unique($translations);
    }

    // Load translation data from JSON files and preserve unique paths
    private function loadTranslationData($files)
    {
        $translationData = [];
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $json = json_decode($content, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $flattenedKeys = $this->flattenTranslationKeys($json);
                foreach ($flattenedKeys as $key) {
                    if ($this->config['dynamicParts']) {
                        foreach ($this->config['dynamicParts'] as $dynamicPrefix => $dynamicParts) {
                            if (strpos($key, $dynamicPrefix) === 0) {
                                $getRealDynamicPart = explode('.', str_replace($dynamicPrefix, '', $key));
                                if (count($getRealDynamicPart) > 1) {
                                    $key = $dynamicPrefix . $getRealDynamicPart[0];
                                }
                            }
                        }
                    }
                    if (!isset($translationData[$key])) {
                        $translationData[$key] = $file; // Keep track of the file origin
                    }
                }
            }
        }
        return $translationData;
    }

    // Reverse check: Check if JSON translations are used in the source files
    private function reverseCheckTranslations()
    {
        $flattenedTranslations = array_keys($this->translationData);
        foreach ($flattenedTranslations as $translation) {
            $originalTranslation = $translation;
            $translation = $this->removePrefixIfConfigured($translation);
            $this->incrementComparisonSummary($originalTranslation);
            if ($this->shouldIgnoreTranslation($originalTranslation)) {
                continue;
            }
            if (!$this->isTranslationUsed($translation)) {
                if ($this->isDynamicTranslationUsed($translation)) {
                    continue;
                }
                $this->comparisonSummary[$originalTranslation]['unused']++;
                $this->unusedTranslations[] = $originalTranslation;
            }
        }
    }

    // Check if a dynamic translation is used
    private function isDynamicTranslationUsed($translation)
    {
        foreach ($this->config['dynamicParts'] as $prefix => $dynamicParts) {
            if (strpos($translation, $prefix) === 0) {
                // Extract the dynamic part and use it to search
                $searchTrans = str_replace($prefix, '', $translation);
                $dynamicKey = explode('.', $searchTrans)[0];
                if ($dynamicKey && $this->isTranslationUsed($dynamicKey)) {
                    return true;
                }
            }
        }
        return false;
    }

    // Remove prefix if configured
    private function removePrefixIfConfigured($translation)
    {
        foreach ($this->config['ignore']['removePrefix'] as $prefix) {
            if (strpos($translation, $prefix) === 0) {
                return substr($translation, strlen($prefix));
            }
        }
        return $translation;
    }

    // Check if a translation should be ignored based on config
    private function shouldIgnoreTranslation($translation)
    {
        foreach ($this->config['ignore']['prefix'] as $prefix) {
            if (strpos($translation, $prefix) === 0) {
                return true;
            }
        }
        return false;
    }

    // Check if a translation is used in any form in the source files (within single quotes)
    private function isTranslationUsed($translation)
    {
        $pattern = '/\'' . preg_quote($translation, '/') . '\'/';

        foreach ($this->sourceFiles as $file) {
            $content = file_get_contents($file);
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        return false;
    }

    // Flatten nested translation keys into dot notation, excluding numeric keys
    private function flattenTranslationKeys($array, $prefix = '')
    {
        $keys = [];
        foreach ($array as $key => $value) {
            if (is_numeric($key)) {
                continue; // Skip numeric keys
            }
            $newKey = $prefix === '' ? $key : $prefix . '.' . $key;
            if (is_array($value)) {
                $keys = array_merge($keys, $this->flattenTranslationKeys($value, $newKey));
            } else {
                $keys[] = $newKey;
            }
        }
        return $keys;
    }

    // Increment the comparison summary for prefix counts
    private function incrementComparisonSummary($translation)
    {
        $parts = explode('.', $translation);
        $prefix = '';
        foreach ($parts as $index => $part) {
            $prefix = $index === 0 ? $part : $prefix . '.' . $part;
            if (!isset($this->comparisonSummary[$prefix])) {
                $this->comparisonSummary[$prefix] = [
                    'found' => 0,
                    'unused' => 0
                ];
            }
            $this->comparisonSummary[$prefix]['found']++;
        }
    }

    // Print translation summary, unused translations, and total count
    private function printSummary()
    {
        $parentSummary = [];
        foreach ($this->unusedTranslations as $unused) {
            $parts = explode('.', $unused);
            $parentKey = implode('.', array_slice($parts, 0, -1));
            if (!isset($parentSummary[$parentKey])) {
                $parentSummary[$parentKey] = 0;
            }
            $parentSummary[$parentKey]++;
        }

        echo "\nUnused Translations Summary:\n";
        echo json_encode($parentSummary, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        echo "\nUnused Translations List:\n";
        echo json_encode($this->unusedTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        $allSummary = [];
        foreach ($this->comparisonSummary as $key => $summary) {
            if ($summary['found'] > 1) { // Only include non-final keys (keys with more than 1 occurrence)
                $allSummary[$key] = $summary['found'];
            }
        }

        echo "\nAll Translations Summary:\n";
        echo json_encode($allSummary, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

        // Count total number of real translations (strings)
        $totalTranslations = count(array_filter($this->flattenTranslationKeys($this->translationData), function ($key) {
            return !is_array($key);
        }));

        echo "\nTotal Real Translations: " . $totalTranslations;
        echo "\nTotal unused Translations: " . count($this->unusedTranslations) . "\n";
    }
}


if ($argc < 2) {
    exit("Usage: ./extract-translations.php [src_directory]\n");
}

$srcDir = $argv[1];

$config = [
    'ignore' => [

    ]
];

$config = [
    'extensions' => ['twig', 'js', 'json'],
    'translationFunctions' => ['\$t', 'this\.\$t'],
    'ignore' => [
        'prefix' => [
        ],
        'removePrefix' => [
            'fel-assistant-manager.catchErrors.',
            'fel-assistant-manager.catchSuccess.',
            'fel-assistant-manager.storageManager.cacheName.',
            'fel-assistant-manager.general.aiToolsTranslation.',
            'fel-assistant-manager.filter.ui.',
        ]
    ],
    'dynamicParts' => [
        'fel-assistant-manager.tags-tips.' => [],
        'fel-assistant-manager.contentEditor.editable.' => []
    ],
];

new TranslationExtractor($srcDir, $config);
