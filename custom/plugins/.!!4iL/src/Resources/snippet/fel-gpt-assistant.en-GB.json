{"fel-gpt-assistant": {"chatbot": "<PERSON><PERSON><PERSON>", "chat": {"avatarTitle": "<PERSON><PERSON><PERSON>", "box": "Chatbox", "categories": "Categories", "category": "Category", "close": "<PERSON><PERSON>", "delete": "Clear Chat", "deleteChatMessage": "Remove question and answer", "errorClearChat": "An error occurred, please delete the chat", "initial.greeting": "How can i help you?", "onlineStatus": "online", "open": "Start Advisor", "placeholder": "Enter your Message", "submit": "Submit", "toggleChatbox": "Toggle Chatbox", "toggleZoomChat": "Toggle Chatbox size", "wait.a.second": "Give me a moment", "wait.a.second.more": "Give me one more moment", "whats.your.purpose": "How could you help me?", "productListing": {"noImageAssigned": "Image not found"}}, "cookie": {"name": "Local storage", "description": "Activate the option to allow the chatbot to save the conversation locally in your browser and maintain the chat across different pages.", "privacy": "No personal data is shared with the AI. Only your inputs within the chatbox are transmitted to the AI."}, "error": {"chat": {"queryFailedTruncatedTo": "No products found for search term '%from%', changed to '%to%' and tried again."}, "input": {"max.length": "Input Length Exceeded, max: %max%", "is.not.string": "The input provided is not in text format. Please ensure your input is a text string and try again."}}}}