const ApiService = Shopware.Classes.ApiService;
const { Application } = Shopware;

class FelApiService extends ApiService {
    constructor(httpClient, loginService) {
        super(httpClient, loginService);
    }
}

Application.addServiceProvider('felApiService', (container) => {
    const initContainer = Application.getContainer('init');
    return new FelApiService(initContainer.httpClient, container.loginService);
});
