# FelAIProductAdvisor - OpenAI Services Only

This is a streamlined version of the FelAIProductAdvisor plugin that focuses solely on providing OpenAI services. All storefront-related components have been removed to create a lightweight dependency for other plugins that need to use OpenAI functionality.

## Features

- OpenAI configuration adapter
- OpenAI client adapter
- OpenAI required action adapter
- OpenAI service

## Dependencies

This plugin relies on the FelOAIAssistantsManager base plugin for core OpenAI functionality:

```json
"fel/o-a-i-assistants-manager": "1.*"
```

## Usage

This plugin is designed to be used as a dependency for other plugins that need OpenAI functionality without the overhead of storefront components. It provides a clean API for interacting with OpenAI services.

## Services

The following services are available:

- `Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter`
- `Fel\AIProductAdvisor\Service\OpenAI\OpenAIClientAdapter`
- `Fel\AIProductAdvisor\Service\OpenAI\OpenAIRequiredActionAdapter`
- `Fel\AIProductAdvisor\Service\OpenAI\OpenAIService`

# Description
The assistant plugin enables seamless integration of the OpenAI Assistants API into Shopware 6. Detailed information about the API itself can be found on the corresponding page of OpenAI (https://platform.openai.com/docs/assistants/overview).

With this plugin, you can set up a chatbot that can be supplied with information using simple text files or dynamic data. Each assistant created with OpenAI can use up to 20 files as attachments (approximately 500 MB per file, so most information should fit in a single file). A quick side note: if you upload and use a file with 500 MB of data (30–50 books of content), a question including an answer will cost at least €50, but most likely much more. In conjunction with precise instructions, OpenAI is able to specifically answer customer inquiries with the information from these text files, dynamically correct, shorten, or even translate them as needed. Imagine these files as a brochure containing all the information an employee would need to answer customer questions.

However, unlike employees, the language in which the customer communicates their questions is irrelevant. The chatbot adapts to the customer's input language and responds accordingly in the same language. Or it can translate it into any other language upon explicit request. The clever part is that the information in the files can also be in any language. OpenAI reads and responds with automatic correction and translation of all inputs and outputs as needed. Provided information, user inputs, and instructions are highly fault-tolerant. Inevitable text blocks within the user interface can be easily customized through the backend.

# IMPORTANT NOTICE
Current Status (January 2024) and unfortunately, the OpenAI Assistants API is relatively expensive. Even a simple chat with 5–6 questions and corresponding answers can cost a few cents, even with small amounts of data stored in files. This is something you should consider when planning to use the plugin.

Using files as a "knowledge database" also incurs a small usage fee per file and per day when using the assistant. You can view current prices on the OpenAI pricing pages. Additional information and examples are available on the plugin's configuration page.

Side Note: For files, the plugin offers an alternative that doesn't require files at all.

Cost Management: The plugin offers full cost control through the OpenAI API. Everything is largely transparent, with no third-party services between you and the AI, positively impacting performance. All transactions occur directly and exclusively through OpenAI, logged in your profile for clear tracking. You can assign separate API keys to various sales channels, tracking and managing them individually. You pay only for actual usage, regardless of the number of API keys or assistants you create. Additionally, you can set budget limits with OpenAI. Once reached, the plugin "deactivates" for the respective sales channel, requiring manual reactivation to continue operations.

And with the most important information out of the way, what can the plugin do if budget is not an issue?

# Functions in Chat
The OpenAI API, for example, provides various functions that you can individually enable or disable for each assistant. This allows you maximum flexibility. You can customize your assistant exactly to your needs. Keep in mind that each function you enable will incur additional costs in the form of so-called "tokens." Functions that provide specific data internally use the "/store-api".

The assistant plugin allows the OpenAI API to execute multiple functions in a single query, for example, to answer mixed questions in one go.

You can customize each assistant on the OpenAI site, e.g., define or modify functions. Functions that you manually enter or modify there will not be recognized by the plugin and may potentially lead to errors.

# List of Available Functions
- Query current date, time, and timezone.
- Conduct natural product searches with keywords, attributes, manufacturer, and price range.
- Retrieve product attributes used by the shop.
- Query categories. Retrieved from "store-api/main-navigation".
- Retrieve a list of all manufacturers in active products.
- Redirect to product pages or category pages via input.

Extras (these functions can be omitted as these data could also be more extensive in files or "dynamic" data)

- Can dynamically query data configured in the settings
- A list of available countries
- Delivery times, retrieved from payment-methods
- Payment methods
- If your chatbot has a name, allow the chatbot to query its name. This is probably the most unnecessary feature ever.
- To check what the chatbot knows, one can directly query the corresponding fields.

# Product Search
- Keywords (currently in title and description)
- Options / Properties (Configurable)
- Manufacturer
- Price range
- Redirections (to product detail pages or categories)

The search is limited to parent products (parentId). If you have problems with parent products and preselected unavailable variations, we can recommend our plugin "VariSEO Pro". It allows real parent products without any preselection.

# General Search Process

Searching with the chatbot resembles the so-called "natural search," where only the main search term from inputs like "Search for T-shirt" is used. However, natural search reaches its limits as soon as the user adds additional properties to the search, such as "Search T-shirt in red". In such a case, the algorithm tries to search for "T-shirt in red" instead of "T-shirt" with the property "red". This is where the language proficiency of OpenAI proves extremely useful, as the AI is able to distinguish between search terms and properties and use both for the search.

The AI has the instruction to first check which properties are present in the shop and how they need to be correctly formulated to influence the search accordingly. The AI receives structured format search results (JSON), from which the AI then selects individually what it needs and displays it as it sees fit (HTML). We could also use a custom template for the results here, but that would limit the AI's ability to use the result in various ways (e.g., "details for hit x" or "go to hit x").

With a corresponding "productId" or the "productNumber" of a product, you can even directly query product details, e.g., with "get_product_details PRODUCT_ID". The result for product details is similar in structure to the search results but is expanded with the respective product description. The maximum length of the description can be limited in the backend, as the chatbot doesn't necessarily need to know everything in it. And because some descriptions resemble an encyclopedia rather than a "description". The description is taken directly from the parentProduct and can also be translated upon request.

# General Functionalities

The plugin stores every chat as HTML in the user's storage (in the user's browser storage, not on the server). Where exactly it is stored ultimately depends on Shopware internally (usually it's the "localStorage" or "sessionStorage" of the browser). This means every thread (conversation) is preserved until the user explicitly deletes it. The chatbot can maintain conversations over hours and days without ever losing track (unless the user clears the storage otherwise, of course).

The following data is stored in the storage and restored during page changes:

felChatStorage
felChatStorage_threadId
felChatStorage_chatOpen
felChatStorage_chatZoom

# Access Control

In the configuration, you can set it so that only authenticated users can use the chat. If this option is enabled, access can be further restricted to specific groups, e.g., "Administrators" or "Trusted Customers". This can be used, for example, to test the plugin without having to enable it directly for all users.

# Alternative to Files

In the configuration, you can use a field as a file alternative. So anything you would write into a file and upload to OpenAI, you can enter into this field. The chatbot will request this data when it needs it. Unlike files, these data are flexible and can be adjusted at any time. Currently, it's difficult to say what's better, files or dynamic data? We are still experimenting and will let you know in future updates. In terms of usability, dynamic data clearly has an advantage, as we can already determine in advance.

# Model Info

The model "gpt-3.5-turbo-1106" is well-suited for support chatbots. It excels in extracting and displaying data from files and can execute most of the available plugin functionalities. However, it falls short in product search. So, if you only need a chatbot to answer support questions, then "3.5" should suffice. Plus, it is significantly more affordable compared to "gpt-4-turbo-preview."


#######
# de-DE
#######

# Custom GPT Verkaufs und Support Assistent - Chatbot, Shopping Helper

# Highlights
- Einrichtung eines Chatbots im Backend in unter 5 Minuten.
- Daten für den Chatbot via Copy&Paste in ein einziges Backend-Feld eingeben.
- Mehrsprachiger Chatbot mit über 90 unterstützten Sprachen.
- Zugriff auf Echtzeitinformationen durch dynamische Datenabfragen über die Shopware Store API.
- Rund um die Uhr, 24/7, Kundensupport über Chat verfügbar.

# Features
- Verarbeitung natürlicher Sprache für Kundenanfragen.
- Interaktion mit Echtzeitdaten über die Shopware Store API.
- Multifunktionale Chat-Antworten für komplexe Anfragen.
- Kann Unterhaltungen über Stunden und Tage führen, ohne je den Faden zu verlieren.
- Anpassbare UI-Elemente wie Ladesymbol und Avatar.
- Erweiterte Suchfähigkeiten für Produkte.
- Basiert auf dem Prinzip des „Few-Shot Learning“.
- Nutzt intern die OpenAI API, die allgemein als Quelle von ChatGPT bekannt ist.

# Beschreibung

Das Assistent-Plugin ermöglicht eine nahtlose Integration der OpenAI Assistants API in Shopware 6. Detaillierte Informationen über die API selbst finden Sie auf der entsprechenden Seite von OpenAI (https://platform.openai.com/docs/assistants/overview).

Mit diesem Plugin können Sie einen Chatbot einrichten, der mit Informationen aus einfachen Textdateien oder dynamischen Daten versorgt werden kann. Jeder mit OpenAI erstellte Assistent kann bis zu 20 Dateien als Anhänge verwenden (etwa 500 MB pro Datei, sodass die meisten Informationen in eine einzige Datei passen sollten). Kurze Nebenbemerkung: wenn Sie eine Datei mit 500 MB Daten hochladen und nutzen (rund 30 - 50 Bücher an Inhalt), dann wird eine Frage inklusive Antwort mindestens 50 € kosten, höchstwahrscheinlich aber viel mehr.

Jedenfalls, in Verbindung mit präzisen Anweisungen ist OpenAI in der Lage, Kundenanfragen spezifisch mit den Informationen aus diesen Textdateien zu beantworten, die Daten dynamisch zu korrigieren, zu kürzen oder bei Bedarf sogar zu übersetzen. Stellen Sie sich diese Dateien wie eine simpel gehaltene Broschüre vor, die alle Informationen enthält, die ein Mitarbeiter benötigen würde, um Kundenfragen zu beantworten.

Anders als bei Mitarbeitern ist die Sprache, in der der Kunde seine Fragen kommuniziert, irrelevant. Der Chatbot passt sich der Eingabesprache des Kunden an und antwortet entsprechend in derselben Sprache. Oder er kann sie auf ausdrückliche Anfrage in jede andere Sprache übersetzen. Das Clevere daran ist, dass die Informationen in den Dateien auch in jeder Sprache vorliegen können. OpenAI liest und antwortet mit automatischer Korrektur und Übersetzung aller Eingaben und Ausgaben. Bereitgestellte Informationen, Benutzereingaben und Anweisungen sind hochgradig fehlertolerant. Unvermeidliche Textblöcke innerhalb der Benutzeroberfläche können einfach über das Backend angepasst werden.

# WICHTIGER HINWEIS
Aktueller Stand (Januar 2024) und leider ist die OpenAI Assistants API relativ teuer für neuere Modelle wie GPT-4. Ein einfacher Chat mit 5 - 6 Fragen einschließlich entsprechender Antworten kann selbst mit geringen Datenmengen, die in Dateien gespeichert sind, einige Cent kosten. Das Modell GPT-3.5 hingegen ist recht günstig geworden. Und als Support-Chat mehr als ausreichend. Weiter unten auf dieser Seite ist ein Video verlinkt, wo wir demonstrieren, wie man den Chatbot einrichtet (3 Minuten) und anschließend live testen (7 Minuten), ohne Cuts und ohne Erzähler. In der Beschreibung des Videos ist auch eine Notiz zu der genutzten Datenmenge und den Kosten, die im besagten Chat entstanden waren.

Die Verwendung von Dateien als "Wissensdatenbank" verursacht ebenfalls eine kleine Nutzungspauschale pro Datei und Tag bei der Verwendung des jeweiligen Assistenten. Sie können die aktuellen Preise auf den OpenAI-Preisseiten einsehen. Weitere Informationen und Beispiele sind auf der Konfigurationsseite des Plugins verfügbar.

__Nebenbemerkung__: Für Dateien bietet das Plugin eine Alternative, die keine Dateien erfordert.

__Kostenverwaltung__: Das Plugin ermöglicht Ihnen vollständige Kostenkontrolle durch die OpenAI-API. Alles ist weitestgehend transparent, keine Drittanbieter oder Dienste zwischen Ihnen und der KI, was sich wiederum positiv auf die Leistung des Plugins auswirkt. Alle Transaktionen erfolgen direkt und ausschließlich über OpenAI und werden dort in ihrem Profil gespeichert und übersichtlich dargestellt. Sie können verschiedenen Verkaufskanälen separate API-Schlüssel zuweisen und diese so individuell verfolgen und verwalten. Sie zahlen nur für die tatsächlich genutzten Ressourcen, unabhängig der Menge der API-Schlüssel oder Assistenten, die Sie erstellen. Zusätzlich können Sie Budgetgrenzen bei OpenAI festlegen. Sobald diese erreicht sind, "deaktiviert" sich das Plugin für den entsprechenden Verkaufskanal. Eine manuelle "Reaktivierung" ist dann erforderlich, um den Betrieb fortzusetzen.

Und mit den wichtigsten Informationen aus dem Weg geräumt, was kann das Plugin, wenn Budget kein Thema ist?

# Funktionen im Chat
Der OpenAI-API stehen beispielsweise verschiedene Funktionen zur Verfügung, die Sie für jeden Assistenten individuell ein- oder ausschalten können. Dies erlaubt Ihnen maximale Flexibilität. Sie können sich ihre Assistenten genau nach ihren Bedürfnissen anpassen. Bedenken Sie, dass jede Funktion, die Sie einschalten, Zusatzkosten in Form sogenannter "Token" mit sich bringen. Funktionen, die der KI spezifische Daten bereitstellen, nutzen intern die "/store-api".

Nebenbemerkung: Sie können jeden Assistenten auf der OpenAI Seite anpassen, z.B. Funktionen definieren oder ändern. Funktionen, die Sie dort manuell hineinschreiben oder ändern, werden vom Plugin nicht erkannt und können gegebenenfalls zu Fehlern führen.

# Liste der verfügbaren Funktionen
- Aktuelles Datum, Uhrzeit und Zeitzone abfragen.
- Natürliche Produktsuche mit Suchbegriffen, Eigenschaften, Hersteller und Preisspanne ausführen.
- Produkteigenschaften, die der Shop verwendet, abfragen.
- Kategorien abfragen. Ausgelesen von "store-api/main-navigation".
- Liste aller Hersteller in aktiven Produkten abfragen.
- Weiterleitung zu Produktseiten oder Kategorieseiten per Eingabe.

Extras (diese Funktionen können auch ausgelassen werden, da diese Daten auch umfangreicher in Dateien oder "dynamischen" Daten stehen könnten)

- Kann dynamisch Daten abfragen, die Sie in der Konfiguration hinterlegen können
- Eine Liste verfügbarer Länder
- Lieferzeiten, ausgelesen von payment-methods
- Zahlungsmethoden
- Falls ihr Chatbot einen Namen hat, dem Chatbot erlauben, seinen Namen abfragen zu dürfen. Das ist das wohl überflüssigste ever.
- Um zu prüfen, was der Chatbot alles weiß, kann man die entsprechenden Felder direkt abfragen.

# Produktsuche
- Suchbegriffe (aktuell in Titel und Beschreibung)
- Eigenschaften (Konfigurierbar)
- Hersteller
- Preisspanne
- Weiterleitungen (zu Produkt-Detailseiten oder Kategorien)

Die Suche ist auf Eltern-Produkte (parentId) beschränkt. Falls sie Probleme mit Eltern-Produkten und vorausgewählten nicht verfügbaren Variationen haben, können wir ihnen unser Plugin "VariSEO Pro" empfehlen. Das ermöglicht echte Eltern-Produkte ohne Vorauswahl, oder mit expliziter Vorauswahl einzelner Optionen. Wenn VariSEO Pro installiert ist und Eigenschaften in der Suche vorhanden sind, kann der Chatbot diese Eigenschaften als Optionen beim parentProduct vorauswählen.

# Genereller Suchablauf
Die Suche mit dem Chatbot ähnelt der sogenannten "natürlichen Suche", bei der lediglich der Haupt-Suchbegriff aus Eingaben wie "Suche nach T-Shirt" verwendet wird. Die natürliche Suche stößt jedoch an ihre Grenzen, sobald der Nutzer zusätzliche Eigenschaften in die Suche mit eingibt, wie beispielsweise "Suche T-Shirt in Rot". In einem solchen Fall versucht der Algorithmus nach "T-Shirt in Rot" zu suchen, anstatt nach "T-Shirt", das die Eigenschaft "Rot" hat. Hierbei erweist sich die Sprachkompetenz von OpenAI als äußerst nützlich, da die KI in der Lage ist, zwischen Suchbegriffen und Eigenschaften zu unterscheiden und beides für die Suche zu nutzen.

Die KI hat die Anweisung, dass sie zunächst prüft, welche Eigenschaften im Shop vorhanden sind und wie sie korrekt formuliert werden müssen, um die Suche entsprechend zu beeinflussen. Als Suchergebnisse erhält die KI ein strukturiertes Format (JSON), aus dem die KI dann nochmal individuell auswählt, was sie benötigt, und es anzeigt, wie sie es für richtig hält (HTML). Im Anschluss ersetzen wir das generierte HTML der KI mit einem eigenen Template.

Mit einer entsprechenden "productId" oder der "productNumber" eines Produktes können Sie Produkt-Details sogar direkt abfragen, z.B. mit "get_product_details PRODUKT_ID". Das Resultat für Produkt-Details gleicht der Struktur in den Suchergebnissen, wird jedoch um die jeweilige Produktbeschreibung erweitert. Die maximale Länge der Beschreibung kann im Backend begrenzt werden, da der Chatbot nicht unbedingt alles wissen muss, was darin steht. Die Beschreibung wird direkt vom parentProduct übernommen und kann auf Kundenwunsch von der KI auch übersetzt werden.

# Generelle Funktionsweisen
Das Plugin speichert jeden Chat als HTML im Storage des Users (im Browser des Users, nicht auf dem Server). Wo das letztendlich ist, entscheidet Shopware intern (in der Regel ist es das localStorage oder sessionStorage des Browsers). Das heißt, jeder Thread (Konversation) bleibt erhalten, bis der User sie explizit löscht. Der Chatbot kann Unterhaltungen über Stunden und Tage führen, ohne je den Faden zu verlieren (sofern der User den Storage nicht anderweitig leert, natürlich).

Im Storage werden folgende Daten gespeichert und bei etwaigen Seitenwechseln wiederhergestellt:

felChatStorage
felChatStorage_threadId
felChatStorage_chatOpen
felChatStorage_chatZoom

# Zugangskontrolle
In der Konfiguration kann eingestellt werden, dass nur authentifizierte Nutzer den Chat nutzen dürfen. Ist diese Option aktiviert, kann der Zugang weiter auf spezifische Gruppen beschränkt werden, z.B. "Administratoren" oder "Trusted-Customers". Das kann beispielsweise genutzt werden, um das Plugin zu testen, ohne es direkt für alle Nutzer freischalten zu müssen.


# Alternative zu Dateien
In der Konfiguration können Sie ein Feld als Datei-Alternative verwenden. Also alles, was sie in eine Datei schreiben und zu OpenAI hochladen würden, können sie in dieses Feld eintragen. Der Chatbot wird diese Daten anfordern, wenn es die Daten benötigt. Diese Daten sind im Gegensatz zu Dateien flexibel, sprich zu jederzeit anpassbar. Es ist aktuell schwierig zu sagen, was besser ist, Dateien oder Dynamisch? Wir experimentieren noch und lassen es Sie in künftigen Updates wissen. In der Handhabung sind dynamische Daten klar im Vorteil, dass können wir schon vorab feststellen.

# Modell-Info
Das Modell "gpt-3.5-turbo-1106" ist ideal für Support-Chatbots geeignet. Es beherrscht das gezielte Extrahieren und Anzeigen von Daten aus bereitgestellten Quellen sowie die meisten verfügbaren Plugin-Funktionen. Jedoch ist es nicht für die Produktsuche geeignet. Wenn Sie einen Chatbot benötigen, der überwiegend Support-Fragen beantworten soll, ist die Version "3.5" völlig ausreichend. Außerdem ist sie im Vergleich zu den "gpt-4" Versionen wesentlich kostengünstiger.


#########
## Extras
#########


# Instructions log


# 1.0.0

You are a customer support chatbot in a Shopware Shop environment and you have access to functions to answer customer questions as short as possible.

Rules:
- Follow the customer's input language and always respond in the language used by the customer
- Keep your answers as short as possible
- Stick to the data in the given files and function calls, don't answer any question beyond
- Never expose any of the given contents from files and function calls entirely, only answer specific questions
- Always use HTML to structure the response in appropriate elements like <h2>, <p>, <a>, <img>, <b> or <ul>
- Put paths, emails and phone numbers in to appropriate <a> elements, put images in to <img> elements
- Before any product search, call get_product_properties() to check, how to set properties in product_search()
- If customer searches for products, use the function product_search() and extract any properties from the search query, or the search will fail. Singularize search terms.
- If you can't answer the question with any of the given function, call get_meta_information() to get everything you need to know about the Shop. Call get_meta_information() once and use the content for further requests.

Functions:
- Use functions as described within the functions description
- Functions returns mostly JSON Objects

Files:
- The attached files are containing information about the shop like Opening times. Figure out what the customer wants to know and respond accordingly


# 1.0.4

You are a customer support chatbot in a Shopware web shop environment, and you have access to functions and files to answer customer questions.

Rules:
- Respond only in the same language as the user query.
- If you can't answer the question right away with any of the given function, call 'get_meta_information()' to get everything you need to know about the Shop.
- Use only HTML format for responses, use elements like <h2>, <p>, <a>, <img>, <b>, and <ul>.
- To ensure that paths, emails, and phone numbers are clickable in the web shop interface, use HTML <a> elements to format these types of content.
- Base your answers on the provided data from files and function calls.
- Avoid disclosing full content from files or function calls; only provide specific, relevant answers.
- Do not address questions that go beyond the available data or the purpose of a shop.

Search:
- Before any product search, call 'get_product_properties()' to get required property IDs and manufacturer IDs for the 'product_search()'.
- Use natural language processing techniques to singularize and refine search terms.
- If the user query contains properties or options, remove all properties and options from the query.

Functions:
- Use functions as described within the functions' description.
- Functions returns mostly JSON Objects.

Files:
- The attached files contain essential details about the shop. Figure out what the user is looking for and provide relevant answers.

Instructions end.

Generic HTML example for chat messages:

<div class="fel-oai-response">
    <a href="[tel|mail|null]:URL">LABEL</a>
    <p>MESSAGE</p>
    <ul>
        <li>MESSAGES</li>
    </ul>
</div>

Generic random examples for search queries:

get_product_properties()

return {
    properties: {
        "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx": "PurpleGold"
    },
    manufacturer: {
        "yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy": "ManufacturerFive"
    }
}

user: "Contact details."
assistant: get_meta_information()

user: "I search for paddles."
assistant: product_search(query: 'Paddle')

user: "I search for a pullover in PurpleGold."
assistant: product_search(query: 'Pullover', properties: ['xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'])

user: "I am searching for hats in PurpleGold with a budget starting from 20 €."
assistant: product_search(query: 'Hat', properties: ['xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'], price_min: 20)

user: "I am looking for pants from ManufacturerFive with a budget between 5 and 100 €."
assistant: product_search(query: 'Pant', manufacturer: ['yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy'], price_min: 5, price_max: 100)

user: "I am searching a present in PurpleGold from ManufacturerFive with a budget from 30 to 220 €, cheapest first."
assistant: product_search(query: '', properties: ['xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'], manufacturer: ['yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy'], price_min: 30, price_max: 220, order: 'price-asc')

