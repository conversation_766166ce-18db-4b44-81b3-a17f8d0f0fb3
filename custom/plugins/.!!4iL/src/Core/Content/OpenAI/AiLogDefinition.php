<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Core\Content\OpenAI;

use Fel\AIProductAdvisor\Core\Content\OpenAI\AiLogCollection;
use Fel\AIProductAdvisor\Core\Content\OpenAI\AiLogEntity;
use Shopware\Core\Framework\DataAbstractionLayer\EntityDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Field\BoolField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\PrimaryKey;
use Shopware\Core\Framework\DataAbstractionLayer\Field\Flag\Required;
use Shopware\Core\Framework\DataAbstractionLayer\Field\IdField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\LongTextField;
use Shopware\Core\Framework\DataAbstractionLayer\Field\StringField;
use Shopware\Core\Framework\DataAbstractionLayer\FieldCollection;

class AiLogDefinition extends EntityDefinition
{
    public const ENTITY_NAME = 'fel_openai_assistants_log';

    public function getEntityName(): string
    {
        return self::ENTITY_NAME;
    }

    public function getEntityClass(): string
    {
        return AiLogEntity::class;
    }

    public function getCollectionClass(): string
    {
        return AiLogCollection::class;
    }

    protected function defineFields(): FieldCollection
    {
        return new FieldCollection([
            (new IdField('id', 'id'))->addFlags(new Required(), new PrimaryKey()),
            (new StringField('sales_channel_id', 'salesChannelId')),
            (new StringField('assistant_id', 'assistantId')),
            (new StringField('thread_id', 'threadId')),
            (new BoolField('thread_deleted', 'threadDeleted')),
            (new LongTextField('thread_messages', 'threadMessages')),
            (new LongTextField('additional_data', 'additionalData')),
            (new LongTextField('errors', 'errors')),
        ]);
    }

}
