
```php
// Controller
return require '/var/www/site/shopware.loc/development/custom/plugins/FelAIProductAdvisor/.notes/_test.response.php';

'property_group_option' => ['id', 'groupId', 'group.id', 'name', 'translated.name'],
'property_group' => ['id', 'name', 'translated.name'],


// associations
$fetchProducts = $this->storeApiClient('POST', '/product', [
    'limit' => 100,
    'total-count-mode' => 'exact',
    'filter' => [[
        'type' => 'equalsAny',
        'field' => 'product.id',
        'value' => $ids,
    ]],
    'sort' => $setFilter['sort'] ?? [],
    'includes' => $this->swApiMap['product']['includes'],
    'associations' => [
        'options' => ['associations' => ['group' => []]],
        'properties' => ['associations' => ['group' => []]],
    ],
]);
```




```template
{% block base_header %}
    {{ parent() }}

    {# {{ dump(config('FelAIProductAdvisor.config', context.salesChannelId)) }} #}
    {# {{ dump(config('FelAIProductAdvisor.config', 16516)) }} #}
    {# {{ dump(context.salesChannelId) }} #}
{% endblock %}


{% set felBuyable = product.available and product.childCount <= 0 and product.calculatedMaxPurchase > 0 %}
{% if felBuyable %}
    <div class="product-action mb-2">
        {% sw_include '@Storefront/storefront/page/product-detail/buy-widget-form.html.twig' with {
            page: {
                product: product
            },
            element: {
                page: {
                    product: product
                },
            }
        } %}
    </div>
{% endif %}
```

```php
$this->container->get('sw-sales-channel-context	');
```

```js
init() {
    window.CookieStorageHelper = CookieStorageHelper;
    window.Debouncer = Debouncer;
    window.DomAccess = DomAccess;
    window.Iterator = Iterator;
    window.Storage = Storage;
    window.HttpClient = HttpClient;
    window.COOKIE_CONFIGURATION_UPDATE = COOKIE_CONFIGURATION_UPDATE;
}




ui_action: {
    name: 'ui_action',
    description: 'Call a UI action, check enum for available actions',
    parameters: {
        type: 'object',
        properties: {
            action: {
                type: 'string',
                description: 'Name of the action to perform, check enum for available',
                enum: [
                    'put_to_basket',
                ],
            }
        },
        required: [
            'action',
        ]
    }
}
```


```html

{# {% sw_include '@Storefront/storefront/assistant-dev.js' %} #}
<script>
    // window.onload = function() {
    //     document.querySelector('[name="fel-chatbot-user-input"]').value = 'suche nach t-shirt';
    //     setTimeout(() => {document.querySelector('.fel-submit-chat-btn').click()}, 250);
    // }
</script>
<style>
    /* #fel-chatbot.active.fel-zoom-chat {
        padding: 0 !important;
        bottom: 0 !important;
        right: 0 !important;
        max-width: 100% !important;
        height: 100% !important;
    } */
</style>

```

















```php

// test
if (1) {
    /*
        get_categories
        get_chatbot_name
        get_countries
        get_delivery_time
        get_manufacturer
        get_meta_information
        get_payment_methods
        get_product_details
        get_product_properties
        product_search
        ui_action
    */
    $useFunction = 'get_payment_methods';
    /**
     * rot paret:    018a240768dc732db6b9f845008f8938
     * rot child:    018a240771577039b8ad5996b3e6d282
     * purple: 018b9792ec5471b69eff2a2e0cdbcae9
     * advanced looper: 018b0fa92fdc744c909f5f937b1111ba
     */
    $setArguments = [
        // 'action' => 'put_to_basket',
        // 'query' => 'T-Shirt',
        'properties' => [
            // 'V3',
            // 'M',
            // 'L',
            // 'Nylon',
            // 'Polyester',
            // 'Children',
            // 'Leather',
        ],
        'manufacturer' => [
            // 'Shopware Fashion'
        ],
        // 'price_min' => 20,
        // 'price_max' => 30,
        // 'id' => 'SW10003.1',
        // 'id' => '018a240768dc732db6b9f845008f8938',
    ];

    $sleep = [
        'create' => 0,
        'default' => 0,
        'delete' => 0,
    ];

    if (file_exists($incTest = realpath(__DIR__ . '/../../..') . '/.notes/_test.response.php')) {
        return include $incTest;
    }
}

```



```php
        // test response
        if (!$request) {



            $j = '{
                "id": "chatblock-1828764327' . time() . '",
                "datetime": "' . date(DATE_RFC850) . '",
                "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                "threadMessages": [
                    {
                        "value": "Mein Name ist Fely. Wie kann ich Ihnen helfen?",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "assistant",
                        "id": "msg_nJOtTm04M8mGXd1dqD3T4pJj"
                    },
                    {
                        "value": "wie ist dein name?",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "user",
                        "id": "msg_yf3tDUZPhLXHcLF7KtMUM7S9"
                    },
                    {
                        "value": "Ich bin der Kunden-Support-Chatbot des Shops hier, um Ihnen zu helfen. Wie kann ich Ihnen weiterhelfen?",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "assistant",
                        "id": "msg_PmWsEtJfVtfaYM21OUacmbHU"
                    },
                    {
                        "value": "wie ist dein name?",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "user",
                        "id": "msg_p2ZNy4sXV0xZjdqu02GzA1Hy"
                    },
                    {
                        "value": "<h2>Öffnungszeiten:</h2>\n<p>Heute, Mittwoch: 10:00 - 18:00</p>\n<b>Wir sind derzeit noch geöffnet!</b>\n\n<h2>Aktuelles Datum und Uhrzeit:</h2>\n<p>Mittwoch, 13. Dez. 2023, 14:13 Uhr CET</p>",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "assistant",
                        "id": "msg_McAfsSEaVFVB5wDwApIoHqCd"
                    },
                    {
                        "value": "öffnungszeiten und aktuelles datum uhrzeit und habt ihr noch offen?",
                        "annotations": [],
                        "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
                        "role": "user",
                        "id": "msg_53ZzxY7aejT34mqqHLkMpih3"
                    }
                ],
                "actionRequired": {
                    "id": "run_OE0ADdpYKyTRuqtcdT0p03ep",
                    "object": "thread.run",
                    "created_at": 1702474147,
                    "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
                    "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                    "status": "requires_action",
                    "started_at": 1702474147,
                    "expires_at": 1702474747,
                    "cancelled_at": null,
                    "failed_at": null,
                    "completed_at": null,
                    "required_action": {
                        "type": "submit_tool_outputs",
                        "submit_tool_outputs": {
                            "tool_calls": [
                                {
                                    "id": "call_aeXeJuFjkQYuiGI2DwPjIObF",
                                    "type": "function",
                                    "function": {
                                        "name": "get_product_properties",
                                        "arguments": "{}"
                                    }
                                }
                            ]
                        }
                    },
                    "last_error": null,
                    "model": "gpt-4-1106-preview",
                    "instructions": "You are a customer support chatbot in a Shop environment. Use functions and attached files to respond to customer queries quick, accurate and as short as possible.\n\nRules:\n- Always use the provided context first. Always stay in context of the users inputs\n- Follow the customer\'s input language and only respond in the language used by the customer\n- The Shop is in a HTML context, put links and emails in appropiate html elements like <a href=\"WEB_ADDRESS\">NAME</a> \n- The response you give is in a little chatbox with little space, so keep your answers as short as possible\n- Stick to the data in the given files, don\'t answer any questions beyond\n- Never expose any of the files contents entirely, only answer specific questions\n- Use always HTML to structure the response in appropiate elements like <h2>, <p>, <b>, <address> or <ul>\n\nFiles:\n- The attached files are containing information about the shop. Try to find out what the customer wants and respond according to the content of the attached files.\n\nFunctions:\n- We have a function called get_meta_data(KEY), that allows you to fetch information about a given topic. The keys you can use are the following:\n    - chatbot-name\n    - date-time\n    - opening-times\n    - company-data\n    - contact-data\n    - payment-metheods\n    - shipping-methods\n    - categories\n    - countries\n    - currencies\n- If user seems to search for a specific product, use the input as QUERY and call function query_db(QUERY)",
                    "tools": [
                        {
                            "type": "retrieval"
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "get_meta_data",
                                "description": "Fetch information to a specific topic using KEY",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "key": {
                                            "type": "string",
                                            "enum": [
                                                "date-time",
                                                "chatbot-name",
                                                "opening-times",
                                                "payment-methods",
                                                "countries",
                                                "delivery-time",
                                                "company-data",
                                                "contact-data",
                                                "shipping-methods"
                                            ],
                                            "description": "The key for the topic to fetch the informations about"
                                        },
                                        "additional": {
                                            "type": "string",
                                            "description": "Some extra data"
                                        }
                                    },
                                    "required": [
                                        "key"
                                    ]
                                }
                            }
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "query_db",
                                "description": "Executes a search based on a text query",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "The text query for searching client instructions"
                                        }
                                    },
                                    "required": [
                                        "query"
                                    ]
                                }
                            }
                        }
                    ],
                    "file_ids": [
                        "file-ylL5cRuUHhCDw56SxAfqGg3v"
                    ],
                    "metadata": []
                },
                "actionRequiredResponse": [
                    {
                        "tool_call_id": "call_aeXeJuFjkQYuiGI2DwPjIObF",
                        "output": "Fely"
                    }
                ],
                "trackError": [],
                "waitCycle": 2,
                "error": null,
                "_post": {
                    "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                    "runId": "run_OE0ADdpYKyTRuqtcdT0p03ep",
                    "userInput": ""
                },
                "_messages": {
                    "object": "list",
                    "data": [
                        {
                            "id": "msg_nJOtTm04M8mGXd1dqD3T4pJj",
                            "object": "thread.message",
                            "created_at": 1702474151,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "Mein Name ist Fely. Wie kann ich Ihnen helfen?",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
                            "run_id": "run_OE0ADdpYKyTRuqtcdT0p03ep",
                            "metadata": []
                        },
                        {
                            "id": "msg_yf3tDUZPhLXHcLF7KtMUM7S9",
                            "object": "thread.message",
                            "created_at": 1702474146,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "wie ist dein name?",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": null,
                            "run_id": null,
                            "metadata": []
                        },
                        {
                            "id": "msg_PmWsEtJfVtfaYM21OUacmbHU",
                            "object": "thread.message",
                            "created_at": 1702474065,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "Ich bin der Kunden-Support-Chatbot des Shops hier, um Ihnen zu helfen. Wie kann ich Ihnen weiterhelfen?",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
                            "run_id": "run_DNVYR54hp38y53mabCWvRsEz",
                            "metadata": []
                        },
                        {
                            "id": "msg_p2ZNy4sXV0xZjdqu02GzA1Hy",
                            "object": "thread.message",
                            "created_at": 1702474064,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "wie ist dein name?",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": null,
                            "run_id": null,
                            "metadata": []
                        },
                        {
                            "id": "msg_McAfsSEaVFVB5wDwApIoHqCd",
                            "object": "thread.message",
                            "created_at": 1702473217,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "<h2>Öffnungszeiten:</h2>\n<p>Heute, Mittwoch: 10:00 - 18:00</p>\n<b>Wir sind derzeit noch geöffnet!</b>\n\n<h2>Aktuelles Datum und Uhrzeit:</h2>\n<p>Mittwoch, 13. Dez. 2023, 14:13 Uhr CET</p>",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
                            "run_id": "run_7csceXLo8LRhS9pv06nQggwM",
                            "metadata": []
                        },
                        {
                            "id": "msg_53ZzxY7aejT34mqqHLkMpih3",
                            "object": "thread.message",
                            "created_at": 1702473201,
                            "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": {
                                        "value": "öffnungszeiten und aktuelles datum uhrzeit und habt ihr noch offen?",
                                        "annotations": []
                                    }
                                }
                            ],
                            "file_ids": [],
                            "assistant_id": null,
                            "run_id": null,
                            "metadata": []
                        }
                    ],
                    "first_id": "msg_nJOtTm04M8mGXd1dqD3T4pJj",
                    "last_id": "msg_53ZzxY7aejT34mqqHLkMpih3",
                    "has_more": false
                }
            }
            ';











            $j = '
                {
                    "id": "chatblock-1828764327' . time() . '",
                    "object": "thread.run",
                    "created_at": 1705554682,
                    "datetime": "' . date(DATE_RFC850) . ',
                    "assistant_id": "asst_UcHE0r0ymkaLDueV4tCV5VaT",
                    "thread_id": "thread_FmiAkMc3nKgKFXy0M0VyMEHA",
                    "status": "requires_action",
                    "started_at": 1705554682,
                    "expires_at": 1705555282,
                    "cancelled_at": null,
                    "failed_at": null,
                    "completed_at": null,
                    "required_action": {
                        "type": "submit_tool_outputs",
                        "submit_tool_outputs": {
                            "tool_calls": [
                                {
                                    "id": "call_JQY6ieyxlKF2khmbBxWxO1Rd",
                                    "type": "function",
                                    "function": {
                                        "name": "query_db",
                                        "arguments": "{\"query\":\"t-shirt\",\"properties\":[\"Rot\"]}"
                                    }
                                }
                            ]
                        }
                    },
                    "last_error": null,
                    "model": "gpt-4-1106-preview",
                    "instructions": "You are a customer support chatbot in a Shop environment and you have access to functions to answer customer questions as short as possible.\n\nRules:\n- Follow the customer\'s input language and always respond in the language used by the customer\n- Keep your answers as short as possible\n- Stick to the data in the given files and function calls, don\'t answer any question beyond\n- Never expose any of the given contents entirely, only answer specific questions\n- Use always HTML to structure the response in appropiate elements like <h2>, <p>, <a>, <b> or <ul>\n- Put paths, emails and phone numbers in to appropiate <a> elements\n- If you can\'t answer the question with any of the given function, call get_meta_information() to get everything you need to know about the Shop. Call get_meta_information() once and use the content for further requests.\n\nFunctions:\n- Use functions as described within the functions description",
                    "tools": [
                        {
                            "type": "retrieval"
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "get_date_time",
                                "description": "Get date, time and timezone",
                                "parameters": {
                                    "type": "object",
                                    "properties": [],
                                    "required": []
                                }
                            }
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "query_db",
                                "description": "Executes a product search based on a text query",
                                "parameters": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "The text query to search for"
                                        },
                                        "manufacturer": {
                                            "type": "array",
                                            "description": "Manufacturers, if customer asks for",
                                            "items": {
                                                "type": "string"
                                            }
                                        },
                                        "properties": {
                                            "type": "array",
                                            "description": "Product properties like Colour, Size, etc., if customer asks for",
                                            "items": {
                                                "type": "string"
                                            }
                                        }
                                    },
                                    "required": [
                                        "query"
                                    ]
                                }
                            }
                        },
                        {
                            "type": "function",
                            "function": {
                                "name": "get_meta_information",
                                "description": "Get all the information you need to know about the Shop",
                                "parameters": {
                                    "type": "object",
                                    "properties": [],
                                    "required": []
                                }
                            }
                        }
                    ],
                    "file_ids": [],
                    "metadata": []
                }
            ';

            if ('create-thread' === $fn) {
                sleep(0);
            } else {
                sleep(0);
            }

            $j = json_decode($j, true);

            if ('create-thread' !== $fn AND ($j['actionRequired'] ?? null)) {

                $actionRequired = $this->openAiService->requiredAction($j['actionRequired'], $request, $salesContext, $context, $this->container);
                $j['actionExecuted'] = $actionRequired;
                $j['threadMessages'][0] = [
                    'id' => 'assistant-' . date(DATE_RFC850),
                    'role' => 'assistant',
                    'datetime' => date(DATE_RFC850),
                    'value' => '<pre>' . print_r($actionRequired[0]['output'], true) . '</pre>'
                ];

                // $j['threadMessages'] = array_merge([
                //     [
                //         'id' => 'assistant-' . date(DATE_RFC850),
                //         'role' => 'assistant',
                //         'datetime' => date(DATE_RFC850),
                //         'value' => print_r($actionRequired, true)
                //     ],
                //     // [
                //     //     'id' => 'assistant--1-' . date(DATE_RFC850),
                //     //     'role' => 'assistant',
                //     //     'datetime' => date(DATE_RFC850),
                //     //     'value' => print_r($actionRequired, true)
                //     // ]
                // ], $j['threadMessages']);
            }

            return new JsonResponse($j, 200, ['x-robots-tag' => ['noindex', 'nofollow']]);
        }

```

```html

<script>
    window.onload = function() {
        // consoleLogWindow();
        // new Assistant;
        document.querySelector('[name="fel-chatbot-user-input"]').value = 'wie ist dein name?';
        setTimeout(() => {document.querySelector('.fel-toggle-chatbot-button').click()}, 250);
        // setTimeout(() => {document.querySelector('.fel-submit-chat-btn').click()}, 250);
    }
</script>
<style>
    #fel-chatbot.active {
        /* width: 97% !important;
        max-width: 2000px !important;
        height: 97%; */
    }
</style>

```
