"use strict"

// import <PERSON>ieStorageHelper from 'src/helper/storage/cookie-storage.helper';


window.onload = function() {
	// consoleLogWindow();
	// new Assistant;
	// document.querySelector('[name="fel-chatbot-user-input"]').value = 'wie ist dein name?';
	// setTimeout(() => {document.querySelector('.fel-toggle-chatbot-button').click()}, 250);
	// setTimeout(() => {document.querySelector('.fel-submit-chat-btn').click()}, 250);
}
function deleteAllCookies() {
    const cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i];
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT";
    }
}

class Assistant {constructor() {this.init();}

	// this._client | this.isLoading
	// this.assistantEl | this.controllerUrl
	// this.options.storage.chat
	// this.options.selector.loadingContainer

	options = {
		selector: {
			chatMessages:      '#fel-chatbot-messages',
			greetingContainer: '.fel-chatbot-greeting-container',
			loadingContainer:  '.fel-chatbot-load-container',
			submitChatButton:  '.fel-submit-chat-btn',
			chatThreadId:      '[name="fel-chatbot-thread-id"]',
			chatRunId:         '[name="fel-chatbot-run-id"]',
			chatUserInput:     '[name="fel-chatbot-user-input"]',
			chatMessageEl:     'chat-message-item',
			chatMessage:       'chat-message',
			cookieDefaultName: 'fel-chatbot-localstorage-accepted',
		},
		storage: {
			chat: 'felChatStorage',
		}
	};

	init() {
		if (this.setPluginOptions()) {
			this.setPluginVars();
			this.loadTemplates();
			this.registerEvents();
			this.setLocalStorageIsAllowed();
		}
	}

	checkIfLocalStorageIsAllowed() {
		return CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);
	}

	setLocalStorageIsAllowed() {
		var getCookie = CookieStorageHelper.getItem(this.options.selector.cookieDefaultName);

		if (getCookie) {
			this.assistantEl.classList.add('fel-localstorage-is-allowed');
			this.assistantEl.classList.remove('fel-localstorage-is-disabled');

			if (this.assistantEl.classList.contains('active')) {
				this.storageSet(`${this.options.storage.chat}_chatOpen`, 'active');
			}
			if (this.assistantEl.classList.contains('fel-zoom-chat')) {
				this.storageSet(`${this.options.storage.chat}_chatZoom`, 'active');
			}
		} else {
			this.assistantEl.classList.add('fel-localstorage-is-disabled');
			this.assistantEl.classList.remove('fel-localstorage-is-allowed');
		}

		return getCookie;
	}

	cookieCallback(updatedCookies) {
		var cookieName = this.options.selector.cookieDefaultName;
		if (typeof updatedCookies.detail[cookieName] !== 'undefined') {
			this.localStorageAllowed = updatedCookies.detail[cookieName];
		} else {
			this.localStorageAllowed = false;
		}
		return this.localStorageAllowed;
	}

	registerEvents() {
		this.handleEvent = event => {
			if (this.isLoading) {
				event.preventDefault();
				return false;
			}
            if ('submit' === event.type) {
				event.preventDefault();
				this.handleChat(event);
				return false;
			}
			else if ('click' === event.type) {
				return this.clickEventCallback(event, event.target);
			}
		};
		this.setEventHandler('add');
		this.setFromLastVisit();

		document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, this.cookieCallback.bind(this));
	}

	checkIfMessageThrown(response) {
		return (response.error && 'undefined' !== typeof response.error.exception) ? response.error.exception : false ;
	}

	/**
	 * Chat handler
	 */
    handleChat(event) {
		if (this.getChatbotUserInput()) {
			var sendMessage = {
				threadId:  this.getChatbotThreadId().value,
				runId:     this.getChatbotRunId().value,
				userInput: this.getChatbotUserInput().value,
			};

			if ('' === sendMessage.runId) {
				this.isLoadingResponse = false;
				this.clientPost(`${this.controllerUrl}/create-thread`, sendMessage, response => {
					if (response) {
						if (this.checkIfMessageThrown(response)) {
							return this.putToMessages('chatbot', this.checkIfMessageThrown(response));
						} else if ('undefined' !== typeof response.id) {
							this.threadId = response.id;
							this.putToMessages('user', sendMessage.userInput, this.threadId, response.datetime);
							this.getChatbotUserInput().value = '';
							this.getChatbotRunId().value = response.runId;
							this.getChatbotThreadId().value = response.threadId;
							this.assistantEl.classList.add('contains-thread');
							return this.chatbotForm.dispatchEvent(new Event('submit', {bubbles: true, cancelable: true}));
						}
					}
				});
			}
			else {
				if (this.threadId) {
					this.isLoadingResponse = true;
					this.getChatbotRunId().value = '';
					this.scrollThreadIntoView();
					this.clientPost(this.controllerUrl, sendMessage, response => {
						if (response) {
							if (this.checkIfMessageThrown(response)) {
								this.putToMessages('chatbot', this.checkIfMessageThrown(response));
							} else if ('undefined' !== typeof response.id) {
								if ('undefined' !== typeof response.threadMessages) {
									if ('assistant' === response.threadMessages[0].role) {
										var lastAssistantMessages = true;
										this.foreach(response.threadMessages, (i, data) => {
											if ('user' === data.role) {
												lastAssistantMessages = false;
											}
											if (lastAssistantMessages) {
												this.putToMessages('chatbot', data.value, '', response.datetime);
											}
										});
									} else {
										this.putToMessages('chatbot', 'Error, please clear the Chat', '', response.datetime);
									}
								}
								this.getChatbotUserInput().focus();
								this.chatToStorage(response.threadId);
								if ('undefined' !== typeof response.uiActionRequired && response.uiActionRequired) {
									this.uiActionRequired(response.uiActionRequired);
								}
							}
						}
					});
				}
			}
		}
    }

	uiActionRequired(uiAction) {
		if ('undefined' !== typeof uiAction.redirectTo && uiAction.redirectTo) {
			if (uiAction.redirectTo !== location.href) {
				location.href = uiAction.redirectTo;
			}
		}
		if ('undefined' !== typeof uiAction.uiAction && uiAction.uiAction)
		{
			if ('undefined' !== typeof uiAction.uiAction.args && uiAction.uiAction.args)
			{
				if ('undefined' !== typeof uiAction.uiAction.args.action && uiAction.uiAction.args.action)
				{
					var calledAction = uiAction.uiAction.args.action;
					if ('put_to_basket' === calledAction) {
						var getPutToBasketBtn = document.querySelector('.fel-add-to-basket-btn');
						if (getPutToBasketBtn) {
							getPutToBasketBtn.dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}))
						} else {
							console.log('Add to basket not possible');
						}
					}
				}
			}
		}
	}

	deleteChatThread() {
		var getThreadId = this.getChatbotThreadId().value;

		if (getThreadId) {
			this.addIsLoading();
			this._client.post(`${this.controllerUrl}/delete-thread`, JSON.stringify({threadId: getThreadId}), response => {
				this.removeIsLoading();
				if (response) {
					response = JSON.parse(response);
					this.assistantEl.classList.remove('contains-thread');
					this.getChatbotRunId().value = '';
					this.getChatbotThreadId().value = '';
					this.chatClearStorage();
					if (this.getChatbotMessagesEl()) {
						this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
					}
					this.getChatbotUserInput().focus();
				}
			});
		} else {
			this.chatClearStorage();
			this.assistantEl.classList.remove('contains-thread');
			if (this.getChatbotMessagesEl()) {
				this.getChatbotMessagesEl().innerHTML = this.template.greeting.outerHTML;
			}
		}
	}

	clientPost(url, sendMessage, callback) {
		this.addIsLoading();
		this._client.post(url, JSON.stringify(sendMessage), response => {
			this.removeIsLoading();
			if (response) {
				try {
					var parse = JSON.parse(response);
					if (parse) {
						response = parse;
					}
				} catch (error) {
					var div = document.createElement('div');
					div.appendChild(document.createTextNode(error));
					return callback({
						error: {exception: div.innerHTML.substring(0, 200),}
					});
				}
			}
			return callback(response);
		});
	}

	scrollIntoView(selector, timeout=100, setBehavior) {
		var getTarget = this.domGet(this.assistantEl, selector, false);
		if (getTarget) {
			this.debounce(() => {
				getTarget.scrollIntoView({behavior: setBehavior? setBehavior : 'smooth'});
			}, timeout);
		}
	}

	scrollThreadIntoView(setBehavior) {
		var getLastUserMsgId = this.domGetAll(this.assistantEl, '.user-message-item', false);
		if (getLastUserMsgId.length) {
			var getLast = getLastUserMsgId[getLastUserMsgId.length - 1];
			if (getLast) {
				this.scrollIntoView(`#${getLast.id}`, 200, setBehavior);
			}
		}
	}

	addIsLoading() {
		this.isLoading = true;
		var useTemplate = this.template.loading;

		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = true;
			this.getChatbotUserInput().disabled = true;
		}
		this.removeGreetingContainer();

		if (this.isLoadingResponse) {
			var cloneLoader = useTemplate.cloneNode(true);
			var getInfoBox = this.domGet(cloneLoader, '.fel-chatbot-loader-info', false);
			if (getInfoBox && getInfoBox.dataset.secondRun) {
				this.domGet(cloneLoader, '.fel-chatbot-loader-info').innerHTML = getInfoBox.dataset.secondRun;
			}
			useTemplate = cloneLoader;
		}

		this.putToMessages('loading', useTemplate.outerHTML);
	}

	removeIsLoading() {
		this.isLoading = false;
		if (this.getChatSubmitButton()) {
			this.getChatSubmitButton().disabled = null;
			this.getChatbotUserInput().disabled = null;
		}
		var getLoader = this.domGetAll(this.assistantEl, '.fel-chatbot-load-container', false);
		if (getLoader.length) {
			for (let i = 0; i < getLoader.length; i++) {
				getLoader[i].classList.add('position-absolute', 'w-100');
				this.debounce(() => {getLoader[i].remove()}, 150);
			}
		}
	}

	removeGreetingContainer() {
		var checkGreetings = this.domGet(this.assistantEl, this.options.selector.greetingContainer, false);
		if (checkGreetings) {
			checkGreetings.remove();
			this.assistantEl.classList.remove('fel-chat-initial');
		}
	}

	getChatSubmitButton() {
		return this.domGet(this.assistantEl, this.options.selector.submitChatButton, false);
	}

    getChatbotMessagesEl() {
        return this.domGet(this.assistantEl, this.options.selector.chatMessages);
    }

    getChatbotUserInput() {
        return this.domGet(this.chatbotForm, this.options.selector.chatUserInput);
    }

	getChatbotThreadId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatThreadId);
    }

	getChatbotRunId() {
        return this.domGet(this.chatbotForm, this.options.selector.chatRunId);
    }

	// click callbacks

	addToChatSubmit(event, el) {
		var getStrToSet = el.dataset.prompt;
		if (getStrToSet) {
			this.getChatbotUserInput().value = getStrToSet;
			this.getChatSubmitButton().dispatchEvent(new MouseEvent('click', {bubbles: true, cancelable: true}));
		}
	}

	toggleChatbot(event, el) {
		var getToggleClass = el.dataset.toggleClass;
		if (getToggleClass) {
			this.assistantEl.classList.toggle(getToggleClass);
			var useStorageName = `${this.options.storage.chat}_chatOpen`;
			if (this.assistantEl.classList.contains('active')) {
				this.storageSet(useStorageName, 'active');
				this.scrollThreadIntoView();
				this.getChatbotUserInput().focus();
			} else {
				this.storageSet(useStorageName, 'not-active');
			}
		}
	}

	enableLocalStorage() {
		CookieStorageHelper.setItem(this.options.selector.cookieDefaultName, 'allowed');
		this.setLocalStorageIsAllowed();
	}

	setFromLastVisit() {
		this.setLastChatZoom();
		if ('active' === this.storageGet(`${this.options.storage.chat}_chatOpen`)) {
			var getToggleButton = this.assistantEl.querySelector('.fel-toggle-chatbot-button.toggle-open');
			if (getToggleButton) {
				getToggleButton.dispatchEvent(new MouseEvent('click', {bubbles: true}));
			}
		}
	}

	toggleChatZoom(event, el) {
		el.classList.toggle('fel-active');
		if (this.assistantEl) {
			var useStorageName = `${this.options.storage.chat}_chatZoom`;
			if (el.classList.contains('fel-active')) {
				this.assistantEl.classList.add('fel-zoom-chat');
				this.storageSet(useStorageName, 'active');
			} else {
				this.assistantEl.classList.remove('fel-zoom-chat');
				this.storageSet(useStorageName, 'not-active');
			}
			this.scrollThreadIntoView();
			this.getChatbotUserInput().focus();
		}
	}

	setLastChatZoom() {
		var getCurrent = this.storageGet(`${this.options.storage.chat}_chatZoom`);
		if ('active' === getCurrent) {
			var getZoomButton = this.assistantEl.querySelector('.fel-zoom-button');
			if (getZoomButton) {
				getZoomButton.classList.add('fel-active');
				this.assistantEl.classList.add('fel-zoom-chat');
			}
		}
	}

	removeChatMessage(event) {
		if ('undefined' !== typeof event.target.offsetParent && 'undefined' !== typeof event.target.offsetParent.offsetParent) {
			var targetParent = event.target.offsetParent.offsetParent;
			if (targetParent) {
				if (targetParent.previousElementSibling.classList.contains('user-message-item')) {
					targetParent.previousElementSibling.remove();
				}
				targetParent.remove();
				this.chatToStorage(this.getChatbotThreadId().value);
				var getStorage = this.storageGet(this.options.storage.chat);
				if ('' == getStorage.trim()) {
					this.deleteChatThread();
				}
				this.scrollThreadIntoView();
			}
		}
	}

	clickEventCallback(event, el) {
		if (!el.classList.contains('fel-btn')) {
			return;
		}

		var callback = el.dataset.callback;

		if (el.dataset.preventDefault) event.preventDefault();
		if (el.dataset.stopPropagation) event.stopPropagation();
		if (el.dataset.blur) el.blur();

		if (callback) {
			if (typeof this[callback] === 'function') {
				return this[callback](...arguments);
			} else if (typeof window[callback] === 'function') {
				return window[callback](...arguments);
			} else {
				console.log('Not callable', callback);
			}
		}
	}

    // helper / shortcuts

	/**
	 * @param {*} messageFrom ['user', 'chatbot']
	 * @param {*} message
	 */
	putToMessages(messageFrom, message, id='', datetime='') {
		if (id) {
			id = ` id="${id}"`;
		}
		if (datetime) {
			datetime = ` title="${datetime}"`;
		}
		var setDeleteBtn = 'chatbot' === messageFrom
			? `<div class="fel-delete-message">
				<span title="${this.options.translation.removeChatMessage}"
					class="btn fel-btn" data-callback="removeChatMessage">X</span>
			</div>` : '' ;

		var template = ['loading', 'greeting'].includes(messageFrom)
			? message : `
			<div class="${this.options.selector.chatMessageEl} ${messageFrom}-message-item fel-fade-in d-flex"${id}>
				<div class="${this.options.selector.chatMessage} ${messageFrom}-message">
					<div class="fel-inner"${datetime}>
						${setDeleteBtn}
						${message}
					</div>
				</div>
			</div>`;
		this.putHtml(this.getChatbotMessagesEl(), 'beforeend', template);
	}

	// querySelector
	domGet() {
		return DomAccess.querySelector(...arguments);
	}

	domGetAll() {
		return DomAccess.querySelectorAll(...arguments);
	}

	// storage
	storageGet() {
		return Storage.getItem(...arguments);
	}

	// setItem(keyName, keyValue)
	storageSet() {
		if (this.checkIfLocalStorageIsAllowed()) {
			Storage.setItem(...arguments);
		}
	}

	storageRemove() {
		Storage.removeItem(...arguments);
	}

	// shorties
	chatClearStorage() {
		this.storageRemove(this.options.storage.chat);
		this.storageRemove(`${this.options.storage.chat}_threadId`);
	}

	chatToStorage(threadId) {
		var getChat = this.getChatbotMessagesEl();
		if (getChat) {
			var getLoader = this.domGetAll(getChat, this.options.selector.loadingContainer, false);
			if (getLoader.length) {
				this.foreach(getLoader, (idx, elm) => {
					elm.remove();
				});
			}
			this.storageSet(this.options.storage.chat, getChat.innerHTML);
			this.storageSet(`${this.options.storage.chat}_threadId`, threadId);
		}
	}

	// misc
	foreach(toIterate, callback) {
		return Iterator.iterate(toIterate, function(value, key) {
			callback(key, value);
		});
	}

	objectMerge(...objects) {
		var m = (t, s) => {
			Object.entries(s).forEach(([k, v]) => {
				t[k] = v && typeof v === 'object' ? m(t[k] || {}, v) : v;
			});
			return t;
		}
		return objects.reduce(m, {});
	}

	// beforebegin afterbegin beforeend afterend
	putHtml(el, toPosition, html) {
		el.insertAdjacentHTML(toPosition, html);
	}

	// debounce(callback, delay, immediate = false)
	debounce() {
		if (typeof Debouncer === 'function') {
			return Debouncer.debounce(...arguments).apply();
		} else if(typeof arguments[0] === 'function') {
			return setTimeout(() => {arguments[0].apply()}, arguments[1] || 0);
		}
	}

	// init plugin vars / options

	loadTemplates() {
		this.template = {greeting: '', loading: '', messageItem: ''};
		var templates = document.getElementById('fel-chatbot-template');
		if (templates) {
			this.template.greeting = templates.content.querySelector(this.options.selector.greetingContainer);
			if (this.template.greeting) {
				var getChatFromStorage = this.storageGet(this.options.storage.chat);
				this.putToMessages('greeting', getChatFromStorage
					? getChatFromStorage
					: this.template.greeting.outerHTML
				);
				if (getChatFromStorage) {
					this.assistantEl.classList.add('contains-thread');
					this.getChatbotThreadId().value = this.storageGet(this.options.storage.chat + '_threadId');
				}
			}
			this.template.loading = templates.content.querySelector(this.options.selector.loadingContainer);
		}
	}

	setPluginVars() {
		this._client = new HttpClient();
		this.eventsRegistered = false;
		this.isLoading = false;
		this.isLoadingResponse = false;
		this.threadId = null;
        this.options.eventHandlerMap = this.chatbotForm ? [
			[this.chatbotForm, 'submit.felBtn', true],
			[this.assistantEl, 'click.felBtn', true],
		] : [];
		var remoteStarter = this.domGetAll(document.body, '.fel-toggle-chatbot-button-remote', false);
		if (remoteStarter.length) {
			this.foreach(remoteStarter, (i, elm) => {
				this.options.eventHandlerMap.push([elm, 'click.felRemoteBtn', true]);
			});
		}
	}

	setPluginOptions() {
		if ('undefined' === typeof DomAccess) {
			return false;
		}
        // assistant element
        this.assistantEl = this.domGet(document.body, '[data-assistant-gpt-plugin]');
        // assistant form
		this.chatbotForm = this.domGet(this.assistantEl, 'form#fel-chatbot-form');
        // controller url
        this.controllerUrl = this.chatbotForm.action;
		// messages container
		this.chatMessages = this.domGet(this.assistantEl, this.options.selector.chatMessages);
        // options
		var pluginOptions = this.assistantEl.dataset.options;
		if (pluginOptions && typeof pluginOptions === 'string') {
			pluginOptions = JSON.parse(pluginOptions);
			if (pluginOptions) {
				this.options = this.objectMerge(this.options, pluginOptions);
				return true;
			} else {
				console.log('Failed to get plugin options');
			}
		}
		return false;
	}

	// event handler
	removeEvents() {
		this.setEventHandler('remove');
	}

	resetEvents() {
		this.removeEvents();
		this.registerEvents();
	}

	setEventHandler(setEvent) {
		var eventFn = {
			'add': 'addEventListener',
			'remove': 'removeEventListener',
		};
		if ('remove' === setEvent && !this.eventsRegistered) {
			return false;
		}
		if ('add' === setEvent && this.eventsRegistered) {
			this.removeEvents();
		}
		this.foreach(this.options.eventHandlerMap, (i, arr) => {
			if (typeof arr[1] !== 'undefined') {
				var names = arr[1].split('.');
				arr[0][eventFn[setEvent]](names[0], this, arr[2] || true);
			}
		});
		this.eventsRegistered = 'add' === setEvent;
	}

}


/**
 * available classes in window (ManyDev/src/Resources/app/storefront/src/many-plugin/many.plugin.js)
 */
// window.Debouncer
// window.DeviceDetection
// window.DomAccess
// window.ElementReplaceHelper
// window.NativeEventEmitter
// window.Iterator
// window.CookieStorage
// window.Storage
// window.ViewportDetection
// window.Plugin
// window.CmsSlotOptionValidatorHelper
// window.CmsSlotReloadService
// window.CookiePermissionPlugin
// window.FadingPlugin
// window.OffCanvasSingleton
// window.RemoteClickPlugin
// window.VariantSwitchPlugin
// window.HttpClient
// window.Backdrop
// window.FormSerializeUtil
// window.HistoryUtil
// window.ButtonLoadingIndicatorUtil
// window.ElementLoadingIndicatorUtil
// window.LoadingIndicatorUtil
// window.PageLoadingIndicatorUtil
// window.PseudoModalUtil
function consoleLogWindow() {
	console.log({
		Debouncer: window.Debouncer,
		DeviceDetection: window.DeviceDetection,
		DomAccess: window.DomAccess,
		ElementReplaceHelper: window.ElementReplaceHelper,
		NativeEventEmitter: window.NativeEventEmitter,
		Iterator: window.Iterator,
		CookieStorage: window.CookieStorage,
		Storage: window.Storage,
		ViewportDetection: window.ViewportDetection,
		CmsSlotOptionValidatorHelper: window.CmsSlotOptionValidatorHelper,
		CmsSlotReloadService: window.CmsSlotReloadService,
		CookiePermissionPlugin: window.CookiePermissionPlugin,
		FadingPlugin: window.FadingPlugin,
		OffCanvasSingleton: window.OffCanvasSingleton,
		RemoteClickPlugin: window.RemoteClickPlugin,
		VariantSwitchPlugin: window.VariantSwitchPlugin,
		HttpClient: window.HttpClient,
		Backdrop: window.Backdrop,
		FormSerializeUtil: window.FormSerializeUtil,
		HistoryUtil: window.HistoryUtil,
		ButtonLoadingIndicatorUtil: window.ButtonLoadingIndicatorUtil,
		ElementLoadingIndicatorUtil: window.ElementLoadingIndicatorUtil,
		LoadingIndicatorUtil: window.LoadingIndicatorUtil,
		PageLoadingIndicatorUtil: window.PageLoadingIndicatorUtil,
		PseudoModalUtil: window.PseudoModalUtil,
	});
}












// require("dotenv").config();

// const OpenAI = require("openai");
// const readline = require("readline").createInterface({
//   input: process.stdin,
//   output: process.stdout,
// });

// // Create a OpenAI connection
// const secretKey = process.env.OPENAI_API_KEY;
// const openai = new OpenAI({
//   apiKey: secretKey,
// });

// async function askQuestion(question) {
//   return new Promise((resolve, reject) => {
//     readline.question(question, (answer) => {
//       resolve(answer);
//     });
//   });
// }

// async function main() {
//   try {
//     const assistant = await openai.beta.assistants.create({
//       name: "Math Tutor",
//       instructions:
//         "You are a personal math tutor. Write and run code to answer math questions.",
//       tools: [{ type: "code_interpreter" }],
//       model: "gpt-4-1106-preview",
//     });

//     // Log the first greeting
//     console.log(
//       "\nHello there, I'm your personal math tutor. Ask some complicated questions.\n"
//     );

//     // Create a thread
//     const thread = await openai.beta.threads.create();

//     // Use keepAsking as state for keep asking questions
//     let keepAsking = true;
//     while (keepAsking) {
//       const userQuestion = await askQuestion("\nWhat is your question? ");

//       // Pass in the user question into the existing thread
//       await openai.beta.threads.messages.create(thread.id, {
//         role: "user",
//         content: userQuestion,
//       });

//       // Use runs to wait for the assistant response and then retrieve it
//       const run = await openai.beta.threads.runs.create(thread.id, {
//         assistant_id: assistant.id,
//       });

//       let runStatus = await openai.beta.threads.runs.retrieve(
//         thread.id,
//         run.id
//       );

//       // Polling mechanism to see if runStatus is completed
//       // This should be made more robust.
//       while (runStatus.status !== "completed") {
//         await new Promise((resolve) => setTimeout(resolve, 2000));
//         runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
//       }

//       // Get the last assistant message from the messages array
//       const messages = await openai.beta.threads.messages.list(thread.id);

//       // Find the last message for the current run
//       const lastMessageForRun = messages.data
//         .filter(
//           (message) => message.run_id === run.id && message.role === "assistant"
//         )
//         .pop();

//       // If an assistant message is found, console.log() it
//       if (lastMessageForRun) {
//         console.log(`${lastMessageForRun.content[0].text.value} \n`);
//       }

//       // Then ask if the user wants to ask another question and update keepAsking state
//       const continueAsking = await askQuestion(
//         "Do you want to ask another question? (yes/no) "
//       );
//       keepAsking = continueAsking.toLowerCase() === "yes";

//       // If the keepAsking state is falsy show an ending message
//       if (!keepAsking) {
//         console.log("Alrighty then, I hope you learned something!\n");
//       }
//     }

//     // close the readline
//     readline.close();
//   } catch (error) {
//     console.error(error);
//   }
// }

// // Call the main function
// main();
