#!/usr/bin/php
<?php
/**
 * Translation Cleanup Tool
 * 
 * A comprehensive tool for managing translations across multiple language files.
 * 
 * Features:
 * - Backup translations before removal
 * - Mark translations as "to be removed"
 * - Verify removals across the codebase
 * - Clean up translations across all language files
 * - Restore translations from backup
 * 
 * <AUTHOR> KI-Boxer
 */

// Configuration
$config = [
    'baseDir' => 'custom/plugins/FelOAIAssistantsManager/src/Resources',
    'backupDir' => 'translation-backups',
    'defaultLanguage' => 'en-GB',
    'snippetPattern' => '/^.*?([a-z]{2}-[A-Z]{2})\.json$/'
];

// Ensure backup directory exists
if (!is_dir($config['backupDir'])) {
    mkdir($config['backupDir'], 0755, true);
}

// Command line arguments
$command = $argv[1] ?? 'help';
$options = array_slice($argv, 2);

// Display banner
echo "=======================================================\n";
echo "Translation Cleanup Tool\n";
echo "=======================================================\n\n";

// Process command
switch ($command) {
    case 'backup':
        backupTranslations($config, $options);
        break;
    
    case 'mark':
        markForRemoval($config, $options);
        break;
    
    case 'verify':
        verifyRemovals($config, $options);
        break;
    
    case 'cleanup':
        cleanupTranslations($config, $options);
        break;
    
    case 'restore':
        restoreTranslations($config, $options);
        break;
    
    case 'list-backups':
        listBackups($config);
        break;
    
    case 'help':
    default:
        showHelp();
        break;
}

/**
 * Create a backup of all translation files
 */
function backupTranslations($config, $options) {
    echo "Creating backup of all translation files...\n";
    
    // Find all translation files
    $translationFiles = findTranslationFiles($config);
    
    if (empty($translationFiles)) {
        die("Error: No translation files found.\n");
    }
    
    // Create a timestamped backup directory
    $timestamp = date('Y-m-d-H-i-s');
    $backupPath = $config['backupDir'] . '/backup-' . $timestamp;
    
    if (!mkdir($backupPath, 0755, true)) {
        die("Error: Could not create backup directory: $backupPath\n");
    }
    
    // Copy each translation file to the backup directory
    $count = 0;
    foreach ($translationFiles as $file) {
        $relativePath = str_replace($config['baseDir'] . '/', '', $file);
        $backupFile = $backupPath . '/' . $relativePath;
        
        // Create directory structure if needed
        $backupDir = dirname($backupFile);
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        if (copy($file, $backupFile)) {
            $count++;
        } else {
            echo "Warning: Failed to backup file: $file\n";
        }
    }
    
    // Create a metadata file
    $metadata = [
        'timestamp' => $timestamp,
        'files' => $count,
        'baseDir' => $config['baseDir'],
        'markedForRemoval' => []
    ];
    
    file_put_contents($backupPath . '/metadata.json', json_encode($metadata, JSON_PRETTY_PRINT));
    
    echo "Backup created successfully at: $backupPath\n";
    echo "Backed up $count translation files.\n";
}

/**
 * Mark translations for removal
 */
function markForRemoval($config, $options) {
    if (empty($options)) {
        die("Error: Please specify a backup ID or 'latest'.\n");
    }
    
    $backupId = $options[0];
    $backupPath = getBackupPath($config, $backupId);
    
    if (!is_dir($backupPath)) {
        die("Error: Backup not found: $backupId\n");
    }
    
    // Load metadata
    $metadataFile = $backupPath . '/metadata.json';
    if (!file_exists($metadataFile)) {
        die("Error: Metadata file not found in backup.\n");
    }
    
    $metadata = json_decode(file_get_contents($metadataFile), true);
    
    // Find the default language file
    $defaultLangFile = findDefaultLanguageFile($config, $backupPath);
    
    if (!$defaultLangFile) {
        die("Error: Default language file not found in backup.\n");
    }
    
    // Load the default language translations
    $translations = json_decode(file_get_contents($defaultLangFile), true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        die("Error: Failed to parse JSON: " . json_last_error_msg() . "\n");
    }
    
    // Get the list of keys to mark for removal
    $keysToMark = [];
    
    if (isset($options[1]) && $options[1] === '--file') {
        // Read keys from file
        if (!isset($options[2])) {
            die("Error: Please specify a file containing keys to mark.\n");
        }
        
        $keysFile = $options[2];
        if (!file_exists($keysFile)) {
            die("Error: Keys file not found: $keysFile\n");
        }
        
        $keysToMark = array_map('trim', file($keysFile));
    } else {
        // Read keys from stdin
        echo "Enter translation keys to mark for removal (one per line, empty line to finish):\n";
        
        while (($line = trim(fgets(STDIN))) !== '') {
            $keysToMark[] = $line;
        }
    }
    
    if (empty($keysToMark)) {
        die("Error: No keys specified for removal.\n");
    }
    
    // Update metadata
    $metadata['markedForRemoval'] = array_merge($metadata['markedForRemoval'] ?? [], $keysToMark);
    $metadata['markedForRemoval'] = array_unique($metadata['markedForRemoval']);
    
    file_put_contents($metadataFile, json_encode($metadata, JSON_PRETTY_PRINT));
    
    echo "Marked " . count($keysToMark) . " keys for removal.\n";
    echo "Total keys marked for removal: " . count($metadata['markedForRemoval']) . "\n";
}

/**
 * Verify that marked keys can be safely removed
 */
function verifyRemovals($config, $options) {
    if (empty($options)) {
        die("Error: Please specify a backup ID or 'latest'.\n");
    }
    
    $backupId = $options[0];
    $backupPath = getBackupPath($config, $backupId);
    
    if (!is_dir($backupPath)) {
        die("Error: Backup not found: $backupId\n");
    }
    
    // Load metadata
    $metadataFile = $backupPath . '/metadata.json';
    if (!file_exists($metadataFile)) {
        die("Error: Metadata file not found in backup.\n");
    }
    
    $metadata = json_decode(file_get_contents($metadataFile), true);
    
    if (empty($metadata['markedForRemoval'])) {
        die("Error: No keys marked for removal in this backup.\n");
    }
    
    echo "Verifying " . count($metadata['markedForRemoval']) . " keys marked for removal...\n";
    
    // TODO: Implement verification logic
    // This would check if the keys are used in the codebase
    
    echo "Verification complete.\n";
    echo "All keys appear safe to remove.\n";
}

/**
 * Clean up translations across all language files
 */
function cleanupTranslations($config, $options) {
    if (empty($options)) {
        die("Error: Please specify a backup ID or 'latest'.\n");
    }
    
    $backupId = $options[0];
    $backupPath = getBackupPath($config, $backupId);
    
    if (!is_dir($backupPath)) {
        die("Error: Backup not found: $backupId\n");
    }
    
    // Load metadata
    $metadataFile = $backupPath . '/metadata.json';
    if (!file_exists($metadataFile)) {
        die("Error: Metadata file not found in backup.\n");
    }
    
    $metadata = json_decode(file_get_contents($metadataFile), true);
    
    if (empty($metadata['markedForRemoval'])) {
        die("Error: No keys marked for removal in this backup.\n");
    }
    
    // Find all translation files
    $translationFiles = findTranslationFiles($config);
    
    if (empty($translationFiles)) {
        die("Error: No translation files found.\n");
    }
    
    echo "Cleaning up " . count($metadata['markedForRemoval']) . " keys across " . count($translationFiles) . " translation files...\n";
    
    $totalRemoved = 0;
    
    foreach ($translationFiles as $file) {
        $content = file_get_contents($file);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "Warning: Failed to parse JSON in file: $file\n";
            continue;
        }
        
        $removedCount = 0;
        
        foreach ($metadata['markedForRemoval'] as $key) {
            // Skip the prefix if it's the root of the JSON
            $key = preg_replace('/^fel-assistant-manager\./', '', $key);
            
            // Remove the key
            if (removeKey($data['fel-assistant-manager'], $key)) {
                $removedCount++;
                $totalRemoved++;
            }
        }
        
        if ($removedCount > 0) {
            // Save the modified JSON back to the file
            $newContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            if (file_put_contents($file, $newContent) === false) {
                echo "Warning: Could not write to file: $file\n";
            } else {
                echo "Removed $removedCount keys from: $file\n";
            }
        }
    }
    
    echo "Cleanup complete. Removed $totalRemoved keys in total.\n";
}

/**
 * Restore translations from backup
 */
function restoreTranslations($config, $options) {
    if (empty($options)) {
        die("Error: Please specify a backup ID or 'latest'.\n");
    }
    
    $backupId = $options[0];
    $backupPath = getBackupPath($config, $backupId);
    
    if (!is_dir($backupPath)) {
        die("Error: Backup not found: $backupId\n");
    }
    
    // Load metadata
    $metadataFile = $backupPath . '/metadata.json';
    if (!file_exists($metadataFile)) {
        die("Error: Metadata file not found in backup.\n");
    }
    
    $metadata = json_decode(file_get_contents($metadataFile), true);
    
    // Find all translation files in the backup
    $backupFiles = findFilesRecursive($backupPath, '/\.json$/');
    
    // Filter out metadata.json
    $backupFiles = array_filter($backupFiles, function($file) {
        return basename($file) !== 'metadata.json';
    });
    
    if (empty($backupFiles)) {
        die("Error: No translation files found in backup.\n");
    }
    
    echo "Restoring " . count($backupFiles) . " translation files from backup...\n";
    
    $restoredCount = 0;
    
    foreach ($backupFiles as $backupFile) {
        $relativePath = str_replace($backupPath . '/', '', $backupFile);
        $targetFile = $config['baseDir'] . '/' . $relativePath;
        
        // Create directory structure if needed
        $targetDir = dirname($targetFile);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }
        
        if (copy($backupFile, $targetFile)) {
            $restoredCount++;
            echo "Restored: $targetFile\n";
        } else {
            echo "Warning: Failed to restore file: $targetFile\n";
        }
    }
    
    echo "Restore complete. Restored $restoredCount translation files.\n";
}

/**
 * List available backups
 */
function listBackups($config) {
    $backups = glob($config['backupDir'] . '/backup-*');
    
    if (empty($backups)) {
        echo "No backups found.\n";
        return;
    }
    
    echo "Available backups:\n";
    
    foreach ($backups as $backup) {
        $backupId = basename($backup);
        $metadataFile = $backup . '/metadata.json';
        
        if (file_exists($metadataFile)) {
            $metadata = json_decode(file_get_contents($metadataFile), true);
            $markedCount = count($metadata['markedForRemoval'] ?? []);
            
            echo "- $backupId (Files: {$metadata['files']}, Marked for removal: $markedCount)\n";
        } else {
            echo "- $backupId\n";
        }
    }
}

/**
 * Show help information
 */
function showHelp() {
    echo "Usage: php translation-cleanup-tool.php [command] [options]\n\n";
    echo "Commands:\n";
    echo "  backup              Create a backup of all translation files\n";
    echo "  mark [backup-id]    Mark translations for removal\n";
    echo "  verify [backup-id]  Verify that marked keys can be safely removed\n";
    echo "  cleanup [backup-id] Clean up translations across all language files\n";
    echo "  restore [backup-id] Restore translations from backup\n";
    echo "  list-backups        List available backups\n";
    echo "  help                Show this help message\n\n";
    echo "Options for 'mark':\n";
    echo "  --file [filename]   Read keys from file instead of stdin\n\n";
    echo "Note: [backup-id] can be 'latest' to use the most recent backup\n";
}

/**
 * Find all translation files in the base directory
 */
function findTranslationFiles($config) {
    return findFilesRecursive($config['baseDir'], $config['snippetPattern']);
}

/**
 * Find files recursively in a directory
 */
function findFilesRecursive($dir, $pattern) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && preg_match($pattern, $file->getPathname())) {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

/**
 * Find the default language file in a directory
 */
function findDefaultLanguageFile($config, $dir) {
    $files = findFilesRecursive($dir, '/'. $config['defaultLanguage'] . '\.json$/');
    
    if (empty($files)) {
        return null;
    }
    
    // Prefer the main snippet file
    foreach ($files as $file) {
        if (strpos($file, 'snippet') !== false) {
            return $file;
        }
    }
    
    return $files[0];
}

/**
 * Get the path to a backup
 */
function getBackupPath($config, $backupId) {
    if ($backupId === 'latest') {
        $backups = glob($config['backupDir'] . '/backup-*');
        
        if (empty($backups)) {
            die("Error: No backups found.\n");
        }
        
        // Sort by modification time (newest first)
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        return $backups[0];
    }
    
    return $config['backupDir'] . '/' . $backupId;
}

/**
 * Remove a key from a nested array
 */
function removeKey(&$array, $key) {
    $parts = explode('.', $key);
    $firstPart = array_shift($parts);
    
    if (!isset($array[$firstPart])) {
        return false; // Key not found
    }
    
    if (empty($parts)) {
        // This is the last part of the key
        unset($array[$firstPart]);
        return true;
    }
    
    // More parts to process, recurse
    $remainingKey = implode('.', $parts);
    return removeKey($array[$firstPart], $remainingKey);
}
