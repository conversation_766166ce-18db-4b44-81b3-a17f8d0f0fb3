<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\Uuid\Uuid;
use Psr\Log\LoggerInterface;
use Exception;
use function json_encode;

class OpenAiLogService
{
    public function __construct(
        private EntityRepository $aiLogRepository,
        private LoggerInterface $logger
    ) {
    }

    public function log(
        string $salesChannelId,
        string $assistantId,
        string $threadId,
        mixed $threadMessages,
        array $errors,
        Context $context,
        array $additionalData = []
    ): void
    {
        try {
            $this->aiLogRepository->upsert([[
                'id' => Uuid::fromStringToHex($assistantId . $threadId),
                'salesChannelId' => $salesChannelId,
                'assistantId' => $assistantId,
                'threadId' => $threadId,
                'threadMessages' => json_encode($threadMessages),
                'additionalData' => json_encode($additionalData),
                'errors' => json_encode($errors),
            ]], $context);
        } catch(Exception $e) {
            $this->logger->error('Failed to log thread messages', [
                'error' => $e->getMessage(),
                'threadId' => $threadId
            ]);
        }
    }

    public function logThreadDelete(string $assistantId, string $threadId, Context $context): void
    {
        try {
            $this->aiLogRepository->upsert([[
                'id' => Uuid::fromStringToHex($assistantId . $threadId),
                'threadDeleted' => true,
            ]], $context);
        } catch(Exception $e) {
            $this->logger->error('Failed to log thread deletion', [
                'error' => $e->getMessage(),
                'threadId' => $threadId
            ]);
        }
    }

}
