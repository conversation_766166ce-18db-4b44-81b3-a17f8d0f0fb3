import template from './api-test-button.html.twig';

const { Component, Mixin } = Shopware;

Component.register('fel-api-test-button', {
    template,

    props: ['label'],

    inject: ['felApiTest'],

    mixins: [
        Mixin.getByName('notification')
    ],

    data() {
        return {
            generalError: null,
            generalNotification: null,
            isLoading: false,
            isSaveSuccessful: false,
            tempAssistantList: {},
        };
    },

    computed: {
        pluginConfig() {
            let $parent = this.$parent;
            while ($parent.actualConfigData === undefined) {
                $parent = $parent.$parent;
            }
            return $parent.actualConfigData.null;
        }
    },

    methods: {
        saveFinish() {
            this.isSaveSuccessful = false;
        },

        check() {
            this.isLoading = true;
            var configKeyName = 'FelAIProductAdvisor.config.openAiApiKey';
            var assistantIdName = 'FelAIProductAdvisor.config.openAiAssistantId';

            var getTestKey = document.getElementById(configKeyName);
            var getAssistantId = document.getElementById(assistantIdName);

            if ('undefined' !== typeof getTestKey.value && getTestKey.value) {
                this.pluginConfig[configKeyName] = getTestKey.value;
            }

            if ('undefined' !== typeof getAssistantId.value) {
                this.pluginConfig[assistantIdName] = getAssistantId.value;
            }

            this.felApiTest.check(this.pluginConfig)
                .then((res) => {
                    this.isLoading = false;

                    if ('undefined' !== typeof res.assistantList) {
                        this.tempAssistantList = res.assistantList;
                    } else {
                        this.tempAssistantList = {};
                    }

                    if ('undefined' !== typeof res.assistantOk) {
                        this.generalNotification = res.assistantOk;
                    } else {
                        this.generalNotification = null;
                    }

                    if ('undefined' !== typeof res.error) {
                        var transMsg = this.$tc(`fel-gpt-assistant.api-test-button.${res.error}`);
                        this.createNotificationError({
                            title: this.$tc('fel-gpt-assistant.api-test-button.title'),
                            message: transMsg.replace('fel-gpt-assistant.api-test-button.', '')
                        });
                    }
                    else if ('undefined' !== typeof res.success && res.success) {
                        this.isSaveSuccessful = true;
                        this.createNotificationSuccess({
                            title: this.$tc('fel-gpt-assistant.api-test-button.title'),
                            message: this.$tc('fel-gpt-assistant.api-test-button.success')
                        });
                    } else {
                        this.createNotificationError({
                            title: this.$tc('fel-gpt-assistant.api-test-button.title'),
                            message: this.$tc('fel-gpt-assistant.api-test-button.error')
                        });
                    }

                    if ('undefined' !== typeof res.assistantFailed && true === res.assistantFailed) {
                        this.generalError = this.$tc('fel-gpt-assistant.api-test-button.assistantIdCheckFailed');
                    } else {
                        this.generalError = null;
                    }
                })
                .catch((error) => {
                    this.isLoading = false;
                    this.tempAssistantList = {};
                    this.generalNotification = null;
                    if ('undefined' !== typeof error.response && 'undefined' !== typeof error.response.data) {
                        if ('undefined' !== typeof error.response.data.errors && 'undefined' !== typeof error.response.data.errors[0]) {
                            this.generalError = error.response.data.errors[0].detail;
                        }
                    } else {
                        this.generalError = null;
                    }
                    if (this.generalError) {
                        this.createNotificationError({
                            title: this.$tc('fel-gpt-assistant.api-test-button.title'),
                            message: this.generalError
                        });
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        }
    }
})
