<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor;

use Shopware\Core\Framework\Plugin;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class FelAIProductAdvisor extends Plugin
{
    public static function getSubscribedEvents(): array
    {
        return [];
    }

    public function getBasePath(): string
    {
        return __DIR__ . '/../';
    }

    public function getAdministrationEntryPath(): string
    {
        return 'Resources/app/administration/src';
    }

    public function getStorefrontEntryPath(): string
    {
        return 'Resources/app/storefront/src';
    }

    /**
     * @return array<string>
     */
    public function getDependencies(): array
    {
        return [
            'FelOAIAssistantsManager'
        ];
    }
    public function build(ContainerBuilder $container): void
    {
        parent::build($container);

        // No packages to load
    }
}