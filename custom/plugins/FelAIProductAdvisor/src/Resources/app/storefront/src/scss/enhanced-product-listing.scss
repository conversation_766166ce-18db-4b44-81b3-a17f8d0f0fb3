.fel-enhanced-product-listing {
    margin: 1.5rem 0;
    font-family: var(--sw-font-family-base, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
    
    .fel-product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        
        @media (max-width: 576px) {
            grid-template-columns: 1fr;
        }
        
        &.fel-is-search-result-listing {
            margin-bottom: 1rem;
        }
    }
    
    .fel-product-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        background-color: #fff;
        height: 100%;
        
        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &.fel-highlight {
            border: 2px solid var(--sw-color-brand-primary, #0d6efd);
        }
        
        &.fel-is-single-product {
            grid-column: 1 / -1;
            display: flex;
            
            .fel-product-card-inner {
                display: flex;
                width: 100%;
                
                @media (max-width: 768px) {
                    flex-direction: column;
                }
                
                .fel-product-image {
                    flex: 0 0 300px;
                    max-width: 300px;
                    
                    @media (max-width: 768px) {
                        flex: 0 0 auto;
                        max-width: 100%;
                    }
                }
                
                .fel-product-content {
                    flex: 1;
                    padding: 1.5rem;
                }
            }
        }
    }
    
    .fel-product-card-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    
    .fel-product-image {
        position: relative;
        padding-top: 75%;
        background-color: #f8f9fa;
        overflow: hidden;
        
        .fel-product-image-link {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .fel-product-thumbnail {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        .fel-product-no-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            text-align: center;
            color: #6c757d;
            font-size: 0.875rem;
        }
    }
    
    .fel-product-content {
        padding: 1.25rem;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }
    
    .fel-product-title {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        line-height: 1.3;
        
        .fel-product-title-link {
            color: var(--sw-color-brand-primary, #0d6efd);
            text-decoration: none;
            
            &:hover {
                text-decoration: underline;
            }
        }
    }
    
    .fel-product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
        
        .fel-product-price {
            font-weight: 600;
            color: var(--sw-color-price, #e74c3c);
            font-size: 1.125rem;
        }
        
        .fel-product-manufacturer {
            font-size: 0.875rem;
            color: #6c757d;
        }
    }
    
    .fel-product-description {
        margin-bottom: 1rem;
        color: #495057;
        font-size: 0.9375rem;
        line-height: 1.5;
        flex-grow: 1;
        
        p {
            margin-bottom: 0;
        }
        
        .fel-ellipsis {
            font-weight: bold;
        }
    }
    
    .fel-product-properties {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
        
        .fel-product-property {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 0.75rem;
            color: #495057;
            white-space: nowrap;
        }
    }
    
    .fel-product-actions {
        margin-top: auto;
        
        .fel-product-details-btn {
            width: 100%;
            padding: 0.5rem 1rem;
            font-size: 0.9375rem;
        }
    }
    
    .fel-search-all-results {
        margin-bottom: 1.5rem;
    }
    
    .fel-category-list {
        margin-top: 1.5rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        
        .fel-category-list-header {
            padding: 0.75rem 1rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            
            .fel-category-list-title {
                font-weight: 600;
                color: #495057;
            }
        }
        
        .fel-category-list-items {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 0.5rem;
            padding: 1rem;
            
            @media (max-width: 576px) {
                grid-template-columns: 1fr;
            }
        }
        
        .fel-category-item {
            display: block;
            padding: 0.5rem 0.75rem;
            background-color: #f8f9fa;
            border-radius: 4px;
            color: var(--sw-color-brand-primary, #0d6efd);
            text-decoration: none;
            transition: background-color 0.2s ease;
            
            &:hover {
                background-color: #e9ecef;
            }
            
            .fel-category-breadcrumb {
                display: block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                
                em {
                    font-style: normal;
                    color: #6c757d;
                    font-size: 0.875rem;
                }
                
                strong {
                    font-weight: 600;
                }
            }
            
            &.fel-breadcrumb-truncate {
                .fel-category-breadcrumb {
                    em {
                        font-size: 0.75rem;
                    }
                }
            }
        }
    }
}


.fel-simple-product-listing {
    margin: 15px 0;
    font-family: var(--sw-font-family-base, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif);
}

.fel-product-listing-title {
    font-size: 18px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9e9e9;
}

.fel-product-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fel-product-item {
    display: flex;
    border: 1px solid #e9e9e9;
    border-radius: 5px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.fel-product-image-container {
    flex: 0 0 120px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.fel-product-image {
    max-width: 100%;
    max-height: 100px;
    object-fit: contain;
}

.fel-product-no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 12px;
    text-align: center;
}

.fel-product-info {
    flex: 1;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.fel-product-name {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.fel-product-name a {
    color: var(--sw-color-brand-primary, #0d6efd);
    text-decoration: none;
}

.fel-product-name a:hover {
    text-decoration: underline;
}

.fel-product-price-manufacturer {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.fel-product-price {
    font-weight: 600;
    color: var(--sw-color-price, #e74c3c);
}

.fel-product-manufacturer {
    font-size: 14px;
    color: #6c757d;
}

.fel-product-description {
    font-size: 14px;
    color: #495057;
    margin-bottom: 12px;
    flex-grow: 1;
}

.fel-product-actions {
    margin-top: auto;
}

.fel-product-details-btn {
    font-size: 14px;
    padding: 5px 10px;
}

.fel-search-all-results {
    margin-top: 15px;
    text-align: center;
}