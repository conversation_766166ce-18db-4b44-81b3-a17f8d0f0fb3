{% block fel_assistant_manager_notification_test %}
<div class="fel-assistant-manager-notification-test">
    <sw-card title="Notification Test" :isLoading="isLoading">
        <div class="fel-assistant-manager-notification-test__content">
            <h2>Notification Test</h2>
            <p>This page demonstrates how to use the notification methods:</p>

            <h3>catchSuccess</h3>
            <pre>catchSuccess(title, transReplace = {}, logConsole = false, message = null)</pre>
            <sw-button @click="catchSuccess('created')">Simple Success</sw-button>
            <sw-button @click="catchSuccess('importAssistantLogsFinished', { importedTotal: 5, total: 10 })">With Translation Params</sw-button>
            <sw-button @click="catchSuccess('created', {}, true)">With Console Logging</sw-button>
            <sw-button @click="catchSuccess('created', {}, false, 'This is a custom message')">With Custom Message</sw-button>
            <sw-button @click="catchSuccess('updatedAliasesForSuccessfully', { name: 'Test' }, false, 'savedTagAndAliasesSuccessfully')">With Params for Both</sw-button>

            <h3>catchInfo</h3>
            <pre>catchInfo(title, transReplace = {}, logConsole = false, message = null)</pre>
            <sw-button @click="catchInfo('filesAttachedSuccessfully')">Simple Info</sw-button>
            <sw-button @click="catchInfo('Storage State', {}, false, 'Storage size: 1024 bytes')">With Custom Message</sw-button>
            <sw-button @click="catchInfo('filesAttachedSuccessfully', { count: 3 }, false, 'noFilesSelected')">With Params for Both</sw-button>

            <h3>catchWarning</h3>
            <pre>catchWarning(title, transReplace = {}, logConsole = false, message = null)</pre>
            <sw-button @click="catchWarning('selectItemsToDelete')">Simple Warning</sw-button>
            <sw-button @click="catchWarning('attention', {}, true, 'This is a custom warning message')">With Custom Message</sw-button>
            <sw-button @click="catchWarning('warning', { type: 'Important' }, false, 'selectItemsToDelete')">With Params for Both</sw-button>

            <h3>catchErrors</h3>
            <pre>catchErrors(error, title = null, logConsole = true, variant = 'error', transReplace = {})</pre>
            <sw-button @click="catchErrors('failedToDeleteVectorStore')">Simple Error</sw-button>
            <sw-button @click="catchErrors('failedToUpdateAliasesFor', 'error', true, 'error', { name: 'Test Tag' })">With Translation Params</sw-button>
            <sw-button @click="catchErrors(new Error('Test error'), 'Custom Error Title')">With Error Object</sw-button>

            <h3>catchAction</h3>
            <pre>catchAction(actionType = 'confirm', message = 'confirmMessage', options = {}, transReplace = {})</pre>
            <sw-button @click="testCatchAction()">Test All Actions</sw-button>
        </div>
    </sw-card>
</div>
{% endblock %}
