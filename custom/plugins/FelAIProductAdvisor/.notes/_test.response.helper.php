<?php

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIService;
use Shopware\Core\Framework\Context;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Fel\AIProductAdvisor\Storefront\Controller\ChatbotController;


// get chatbot path
$reflectionMethod = new ReflectionMethod(ChatbotController::class, 'chatbot');
$attributes = $reflectionMethod->getAttributes(Route::class);
$chatbotPath = null;
foreach($attributes as $attribute) {
    if ($path = $attribute->newInstance()->getPath()) {
        $path = array_filter(explode('/', $path));
        array_pop($path);
        $chatbotPath = '/' . implode('/', $path);
        break;
    }
}


// get product properties and categories
$getProductProperties = json_decode('{
    "id": "chatblock-**********' . time() . '",
    "datetime": "' . date(DATE_RFC850) . '",
    "threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
    "threadMessages": [
        {
            "value": "Mein Name ist Fely. Wie kann ich Ihnen helfen?",
            "annotations": [],
            "datetime": "Wednesday, 13-Dec-23 14:29:12 CET",
            "role": "assistant",
            "id": "msg_nJOtTm04M8mGXd1dqD3T4pJj"
        }
    ],
    "actionRequired": {
        "id": "run_OE0ADdpYKyTRuqtcdT0p03ep",
        "assistant_id": "asst_Jdrq55QNvlSxQWoPM22OEx7k",
        "thread_id": "thread_VkAIBIEelmWgTjIPXW1HKpFz",
        "status": "requires_action",
        "required_action": {
            "type": "submit_tool_outputs",
            "submit_tool_outputs": {
                "tool_calls": [
                    {
                        "id": "call_QQY6ieyxlKF2khiBxWxO1xx",
                        "type": "function",
                        "function": {
                            "name": "get_product_properties",
                            "arguments": ""
                        }
                    }
                ]
            }
        },
        "last_error": null,
        "model": "gpt-4-1106-preview",
        "instructions": "You are a customer support chatbot",
        "tools": [],
        "file_ids": [],
        "metadata": []
    },
    "actionRequiredResponse": [],
    "trackError": [],
    "waitCycle": 2,
    "error": null,
    "_post": {"threadId": "thread_VkAIBIEelmWgTjIPXW1HKpFz","runId": "run_OE0ADdpYKyTRuqtcdT0p03ep","userInput": ""},
    "_messages": {}
}', true);

$getProductProperties = $this->openAiService->requiredAction($getProductProperties['actionRequired'], $request, $salesContext, $post);

if ($getProductProperties[0]['output'] ?? null) {
    $getProductProperties = json_decode($getProductProperties[0]['output'], true);
} else {
    $getProductProperties = [];
}



function flattenCategories(array $arr)
{
    $r = [];

    foreach($arr as $item) {
        if ($item['children'] ?? null) {
            $r = array_merge($r, $this->flattenCategories($item['children']));
        }
        unset($item['children']);
        $r[] = $item;
    }

    return $r;
}



function filterToolsubmitResponse(array $actionResponse)
{
    $r = [];
    $actions = [];
    foreach($actionResponse as $i => $data) {
        $output = $data['output'];
        if (is_string($output)) {
            $output = json_decode($output, true);
            if (is_array($output) AND ($output['exec'] ?? null)) {

                if ('product_search' === $output['exec'] AND !isset($actions['product_search'])) {
                    $data['output'] = json_encode(array_values($output['items']));
                    $actions['product_search'] = $output;
                }
                if ('redirect' === $output['exec'] AND !isset($actions['redirectTo'])) {
                    $actions['redirectTo'] = $output['url'];
                }
                if ('get_meta_information' === $output['exec']) {
                    $data['output'] = $output['response'];
                }
            }
        }
        $r[] = $data;
    }

    // dump($data);

    return [
        'output' => $r,
        'actions' => $actions,
    ];
}
