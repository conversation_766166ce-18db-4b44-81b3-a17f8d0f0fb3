<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>

        <service id="FelOaiChatbot\Service\CustomCookieProvider" decorates="Shopware\Storefront\Framework\Cookie\CookieProviderInterface">
            <argument type="service" id="FelOaiChatbot\Service\CustomCookieProvider.inner" />
        </service>

        <service id="FelOaiChatbot\Subscriber\AssistantEventListener" public="false">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <tag name="kernel.event_subscriber"/>
        </service>

        <service id="FelOaiChatbot\Storefront\Controller\ChatController" public="true">
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="translator"/>
            <argument type="service" id="FelOAIAssistantsManager\Service\OpenAI\OpenAIChatService"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

    </services>
</container>
