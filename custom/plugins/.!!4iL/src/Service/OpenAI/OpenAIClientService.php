<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Service\OpenAI;

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigService;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Exception;

class OpenAIClientService
{
    private ?string $threadId = null;

    public function __construct(
        private HttpClientInterface $httpClient,
        private OpenAIConfigService $aiConfig
    ) {
    }

    public function setThreadId(string $threadId): self
    {
        $this->threadId = $threadId;

        return $this;
    }

    public function setSalesChannelContext(SalesChannelContext $salesChannelContext): self
    {
        $this->aiConfig->setSalesChannelContext($salesChannelContext);

        return $this;
    }

    public function deleteThread(?string $threadId = null): array
    {
        $threadId ??= $this->threadId;

        return $threadId
            ? $this->clientRun('DELETE', "/threads/{$threadId}")
            : ['message' => 'No thread'] ;
    }

    public function checkRunStatus(string $runId): array
    {
        return $this->clientRun('GET', "/threads/{$this->threadId}/runs/{$runId}");
    }

    public function getThreadMessages(): array
    {
        return $this->clientRun('GET', "/threads/{$this->threadId}/messages");
    }

    public function submitToolOutputs(string $threadId, string $runId, array $output): array
    {
        return $this->clientRun('POST', "/threads/{$threadId}/runs/{$runId}/submit_tool_outputs", [
            'tool_outputs' => $output,
        ]);
    }

    public function addThreadMessage(string $role, mixed $content): array
    {
        return $this->clientRun('POST', "/threads/{$this->threadId}/messages", [
            'role' => $role,
            'content' => $content,
        ]);
    }

    public function runAddMessage(string $assistantId, string $role, string $userInput): array
    {
        $this->addThreadMessage($role, $userInput);

        return $this->clientRun('POST', "/threads/{$this->threadId}/runs", [
            'assistant_id' => $assistantId,
        ]);
    }

    public function runCreateThread(string $assistantId, string $role, string $prompt): array
    {

        return $this->clientRun('POST', "/threads/runs", [
            'assistant_id' => $assistantId,
            'thread' => [
                'messages' => [[
                    'role' => $role,
                    'content' => $prompt,
                ]],
            ],
        ]);
    }

    private function clientRun(string $method, string $url, ?array $json = [], ?array $headers = []): array
    {
        $set['headers'] = $this->aiConfig->getApiHeaders($headers);

        if ($json) {
            $set['json'] = $json;
        }

        try {
            return $this->httpClient
                ->request($method, $this->aiConfig->getUrl($url), $set)
                ->toArray();
        } catch(Exception $e) {
            return ['exception' => $e->getMessage()];
        }
    }

}
