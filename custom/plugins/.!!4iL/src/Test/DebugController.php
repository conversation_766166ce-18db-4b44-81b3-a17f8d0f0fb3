<?php declare(strict_types=1);

namespace Fel\AIProductAdvisor\Test;

use Fel\AIProductAdvisor\Service\OpenAI\OpenAIConfigAdapter;
use Fel\AIProductAdvisor\Service\OpenAI\OpenAIClientAdapter;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Contracts\HttpClient\HttpClientInterface;

/**
 * Debug controller to test OpenAI API directly
 * Only available in development environment
 */
#[Route(defaults: ['_routeScope' => ['api']])]
class DebugController
{
    public function __construct(
        private LoggerInterface $logger,
        private SystemConfigService $configService,
        private HttpClientInterface $httpClient,
        private OpenAIConfigAdapter $configAdapter,
        private OpenAIClientAdapter $clientAdapter,
        private string $environment
    ) {
        // Only log in development environment
        if ($this->environment === 'dev') {
            $this->logger->debug('DebugController: Constructor called');
        }
    }

    #[Route(path: '/api/fel-ai-product-advisor/debug/test-api', name: 'api.action.fel-ai-product-advisor.debug.test-api', methods: ['GET'])]
    public function testApi(Request $request): JsonResponse
    {
        // Only available in development environment
        if ($this->environment !== 'dev') {
            return new JsonResponse(['error' => 'Debug endpoints are only available in development environment'], 403);
        }

        $this->logger->debug('DebugController: Testing API connection');

        // Get API key and base URL
        $apiKey = $this->configService->get('FelAIProductAdvisor.config.openAiApiKey');
        $baseUrl = $this->configService->get('FelAIProductAdvisor.config.openAiBaseUrl') ?: 'https://api.openai.com/v1';

        if (empty($apiKey)) {
            $this->logger->error('DebugController: No API key configured');
            return new JsonResponse(['error' => 'No API key configured'], 400);
        }

        // Make a simple API call to test connectivity
        try {
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer {$apiKey}",
                'OpenAI-Beta' => 'assistants=v1'
            ];

            $response = $this->httpClient->request('GET', $baseUrl . '/models', [
                'headers' => $headers,
                'timeout' => 30
            ]);

            $result = $response->toArray();

            $this->logger->debug('DebugController: API call successful', [
                'status' => $response->getStatusCode(),
                'modelCount' => count($result['data'] ?? [])
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'API connection successful',
                'models' => array_slice($result['data'] ?? [], 0, 5) // Return first 5 models
            ]);
        } catch (\Exception $e) {
            $this->logger->error('DebugController: API call failed', [
                'error' => $e->getMessage()
            ]);

            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route(path: '/api/fel-ai-product-advisor/debug/test-thread', name: 'api.action.fel-ai-product-advisor.debug.test-thread', methods: ['GET'])]
    public function testThread(Request $request): JsonResponse
    {
        // Only available in development environment
        if ($this->environment !== 'dev') {
            return new JsonResponse(['error' => 'Debug endpoints are only available in development environment'], 403);
        }

        $this->logger->debug('DebugController: Testing thread creation');

        // Get API key and base URL
        $apiKey = $this->configService->get('FelAIProductAdvisor.config.openAiApiKey');
        $baseUrl = $this->configService->get('FelAIProductAdvisor.config.openAiBaseUrl') ?: 'https://api.openai.com/v1';
        $assistantId = $request->query->get('assistant_id') ?? $this->configAdapter->getAssistantId();

        if (empty($apiKey)) {
            $this->logger->error('DebugController: No API key configured');
            return new JsonResponse(['error' => 'No API key configured'], 400);
        }

        if (empty($assistantId)) {
            $this->logger->error('DebugController: No assistant ID provided');
            return new JsonResponse(['error' => 'No assistant ID provided'], 400);
        }

        // Create a thread and run
        try {
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer {$apiKey}",
                'OpenAI-Beta' => 'assistants=v1'
            ];

            // Step 1: Create a thread
            $this->logger->debug('DebugController: Creating thread');
            $threadResponse = $this->httpClient->request('POST', $baseUrl . '/threads', [
                'headers' => $headers,
                'json' => [],
                'timeout' => 30
            ]);

            $threadResult = $threadResponse->toArray();
            $threadId = $threadResult['id'] ?? null;

            if (!$threadId) {
                $this->logger->error('DebugController: Failed to create thread', [
                    'response' => $threadResult
                ]);
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Failed to create thread'
                ], 500);
            }

            // Step 2: Add a message to the thread
            $this->logger->debug('DebugController: Adding message to thread', [
                'threadId' => $threadId
            ]);
            $messageResponse = $this->httpClient->request('POST', $baseUrl . "/threads/{$threadId}/messages", [
                'headers' => $headers,
                'json' => [
                    'role' => 'user',
                    'content' => 'Hello, this is a test message from the debug controller.'
                ],
                'timeout' => 30
            ]);

            $messageResponse->toArray(); // Process response

            // Step 3: Create a run
            $this->logger->debug('DebugController: Creating run', [
                'threadId' => $threadId,
                'assistantId' => $assistantId
            ]);
            $runResponse = $this->httpClient->request('POST', $baseUrl . "/threads/{$threadId}/runs", [
                'headers' => $headers,
                'json' => [
                    'assistant_id' => $assistantId
                ],
                'timeout' => 30
            ]);

            $runResult = $runResponse->toArray();
            $runId = $runResult['id'] ?? null;

            if (!$runId) {
                $this->logger->error('DebugController: Failed to create run', [
                    'response' => $runResult
                ]);
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Failed to create run'
                ], 500);
            }

            $this->logger->debug('DebugController: Thread and run created successfully', [
                'threadId' => $threadId,
                'runId' => $runId
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Thread and run created successfully',
                'threadId' => $threadId,
                'runId' => $runId,
                'status' => $runResult['status'] ?? 'unknown'
            ]);
        } catch (\Exception $e) {
            $this->logger->error('DebugController: API call failed', [
                'error' => $e->getMessage()
            ]);

            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route(path: '/api/fel-ai-product-advisor/debug/test-required-action', name: 'api.action.fel-ai-product-advisor.debug.test-required-action', methods: ['GET'])]
    public function testRequiredAction(Request $request): JsonResponse
    {
        // Only available in development environment
        if ($this->environment !== 'dev') {
            return new JsonResponse(['error' => 'Debug endpoints are only available in development environment'], 403);
        }

        $this->logger->debug('DebugController: Testing required action handling');

        $threadId = $request->query->get('thread_id');
        $runId = $request->query->get('run_id');

        if (empty($threadId) || empty($runId)) {
            $this->logger->error('DebugController: No thread ID or run ID provided');
            return new JsonResponse(['error' => 'No thread ID or run ID provided'], 400);
        }

        try {
            // Set thread ID in client adapter
            $this->clientAdapter->setThreadId($threadId);

            // Check run status
            $this->logger->debug('DebugController: Checking run status');
            $statusResult = $this->clientAdapter->checkRunStatus($runId);

            if ($statusResult['status'] !== 'requires_action') {
                $this->logger->debug('DebugController: Run does not require action', [
                    'status' => $statusResult['status']
                ]);
                return new JsonResponse([
                    'success' => true,
                    'message' => 'Run does not require action',
                    'status' => $statusResult['status'],
                    'statusResult' => $statusResult
                ]);
            }

            // Get tool calls
            $toolCalls = $statusResult['required_action']['submit_tool_outputs']['tool_calls'] ?? [];

            if (empty($toolCalls)) {
                $this->logger->error('DebugController: No tool calls found');
                return new JsonResponse([
                    'success' => false,
                    'error' => 'No tool calls found'
                ], 500);
            }

            // Create empty tool outputs
            $toolOutputs = [];
            foreach ($toolCalls as $toolCall) {
                $toolOutputs[] = [
                    'tool_call_id' => $toolCall['id'],
                    'output' => json_encode(['status' => 'success', 'message' => 'Test output from debug controller'])
                ];
            }

            // Submit tool outputs
            $this->logger->debug('DebugController: Submitting tool outputs', [
                'toolOutputsCount' => count($toolOutputs)
            ]);
            $submitResult = $this->clientAdapter->submitToolOutputs($threadId, $runId, $toolOutputs);

            if (isset($submitResult['exception'])) {
                $this->logger->error('DebugController: Failed to submit tool outputs', [
                    'error' => $submitResult['exception']
                ]);
                return new JsonResponse([
                    'success' => false,
                    'error' => $submitResult['exception']
                ], 500);
            }

            return new JsonResponse([
                'success' => true,
                'message' => 'Tool outputs submitted successfully',
                'status' => $submitResult['status'] ?? 'unknown',
                'statusResult' => $statusResult,
                'submitResult' => $submitResult
            ]);
        } catch (\Exception $e) {
            $this->logger->error('DebugController: API call failed', [
                'error' => $e->getMessage()
            ]);

            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
