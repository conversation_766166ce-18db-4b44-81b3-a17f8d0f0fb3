#!/usr/bin/php
<?php

// Check if we're in restore mode
if (isset($argv[1]) && $argv[1] === '--restore') {
    // Find the most recent backup file
    $backupFiles = glob('custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json.backup-*.json');

    if (empty($backupFiles)) {
        die("Error: No backup files found.\n");
    }

    // Sort by modification time (newest first)
    usort($backupFiles, function($a, $b) {
        return filemtime($b) - filemtime($a);
    });

    $latestBackup = $backupFiles[0];
    $snippetFile = 'custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json';

    // Restore from backup
    if (copy($latestBackup, $snippetFile)) {
        echo "Successfully restored from backup: $latestBackup\n";
        exit(0);
    } else {
        die("Error: Failed to restore from backup: $latestBackup\n");
    }
}

// Display usage information
if (isset($argv[1]) && ($argv[1] === '--help' || $argv[1] === '-h')) {
    echo "Usage: php remove-unused-translations.php [options]\n";
    echo "Options:\n";
    echo "  --restore    Restore from the most recent backup\n";
    echo "  --help, -h   Display this help message\n";
    exit(0);
}

// List of keys to remove
$keysToRemove = [

    "fel-assistant-manager.catchErrors.completedRuns",
    "fel-assistant-manager.catchErrors.errorNotFound",
    "fel-assistant-manager.catchErrors.failedAssistantNotFound",
    "fel-assistant-manager.catchErrors.failedNoFilesFound",
    "fel-assistant-manager.catchErrors.failedOaiServerError",
    "fel-assistant-manager.catchErrors.failedToReadMessage",
    "fel-assistant-manager.catchErrors.managerCanNotRunNoplugins",
    "fel-assistant-manager.catchErrors.missingAccessData",
    "fel-assistant-manager.catchErrors.missingAssstantId",
    "fel-assistant-manager.catchErrors.missingPrefetchedProductProperties",
    "fel-assistant-manager.chat.allow",
    "fel-assistant-manager.chat.decline",
    "fel-assistant-manager.chat.description",
    "fel-assistant-manager.chat.failedToCreateThread",
    "fel-assistant-manager.chat.failedToSendMessage",
    "fel-assistant-manager.chat.failedWhileCheckingStatus",
    "fel-assistant-manager.chat.failedWhileFetchingMessages",
    "fel-assistant-manager.chat.failedWhileSendingYourData",
    "fel-assistant-manager.chat.missingRequired.felOpenAiApiKey",
    "fel-assistant-manager.chat.missingRequired.felOpenAiAssistantId",
    "fel-assistant-manager.chat.missingRequired.salesChannelId",
    "fel-assistant-manager.chat.missingRequired.storefrontUrl",
    "fel-assistant-manager.chat.noRunId",
    "fel-assistant-manager.chat.notifications",
    "fel-assistant-manager.chat.routeDescription",
    "fel-assistant-manager.chat.suggestions",
    "fel-assistant-manager.chat.withRunId",
    "fel-assistant-manager.chatlog.addNew",
    "fel-assistant-manager.chatlog.cancel",
    "fel-assistant-manager.chatlog.column.assistantId",
    "fel-assistant-manager.chatlog.column.runsCount",
    "fel-assistant-manager.chatlog.column.salesChannelId",
    "fel-assistant-manager.chatlog.details",
    "fel-assistant-manager.chatlog.save",
    "fel-assistant-manager.chatlog.selectItemsToDelete",
    "fel-assistant-manager.contentCheck.domain",
    "fel-assistant-manager.contentCheck.readableFormat",
    "fel-assistant-manager.contentCheck.salesChannelAccessKey",
    "fel-assistant-manager.contentEditor.inheritanceRestored",
    "fel-assistant-manager.delete.cancel",
    "fel-assistant-manager.delete.confirm",
    "fel-assistant-manager.delete.confirmDeleteLog",
    "fel-assistant-manager.delete.confirmDeleteTag",
    "fel-assistant-manager.delete.confirmDeleteVectorStore",
    "fel-assistant-manager.delete.confirmMessage",
    "fel-assistant-manager.delete.no",
    "fel-assistant-manager.delete.selectItemsToDelete",
    "fel-assistant-manager.delete.yes",
    "fel-assistant-manager.exception.failedToInitThread",
    "fel-assistant-manager.exception.maxLoopAttemptsReached",
    "fel-assistant-manager.exception.missingActionInRequest",
    "fel-assistant-manager.exception.missingAssistantIdForCreateThread",
    "fel-assistant-manager.exception.missingAssistantIdForSendMessage",
    "fel-assistant-manager.exception.missingMessageForSendMessage",
    "fel-assistant-manager.exception.missingMessageIdForDeleteMessage",
    "fel-assistant-manager.exception.missingOpenaiApiKey",
    "fel-assistant-manager.exception.missingOpenaiBaseUrl",
    "fel-assistant-manager.exception.missingRunIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForCheckStatus",
    "fel-assistant-manager.exception.missingThreadIdForDeleteMessage",
    "fel-assistant-manager.exception.missingThreadIdForDeleteThread",
    "fel-assistant-manager.exception.missingThreadIdForFetchMessages",
    "fel-assistant-manager.exception.missingThreadIdForGetThread",
    "fel-assistant-manager.exception.missingThreadIdForSendMessage",
    "fel-assistant-manager.fel-test-api-key.error.baseUrlNotValid",
    "fel-assistant-manager.fel-test-api-key.error.missingApiKey",
    "fel-assistant-manager.fel-test-api-key.error.missingBaseUrl",
    "fel-assistant-manager.fel-test-api-key.error.trimApiKey",
    "fel-assistant-manager.fel-test-api-key.success.notificationLabel",
    "fel-assistant-manager.files.vectorStore.fileIsEmpty",
    "fel-assistant-manager.files.vectorStore.noFilesAttached",
    "fel-assistant-manager.filter.filterCategories",
    "fel-assistant-manager.filter.filterManufacturer",
    "fel-assistant-manager.filter.filterProperties",
    "fel-assistant-manager.filter.generalName",
    "fel-assistant-manager.filter.ui.filteringFailedNoResultsFound",
    "fel-assistant-manager.filter.ui.hideFilter",
    "fel-assistant-manager.filter.ui.setVisibleToConfig",
    "fel-assistant-manager.filter.ui.showFilter",
    "fel-assistant-manager.filter.ui.showSelectedOnly",
    "fel-assistant-manager.general.assistantName",
    "fel-assistant-manager.general.configuration",
    "fel-assistant-manager.general.example",
    "fel-assistant-manager.general.openAIStatus",
    "fel-assistant-manager.general.openAITokenizer",
    "fel-assistant-manager.general.openAIUsage",
    "fel-assistant-manager.instructionIssues.logFunctionNote",
    "fel-assistant-manager.instructionIssues.productPropertyNote",
    "fel-assistant-manager.instructionIssues.searchLogsAggressiveUsage",
    "fel-assistant-manager.instructions.chatbot.gptThree",
    "fel-assistant-manager.log.addNewLog",
    "fel-assistant-manager.log.categoryId",
    "fel-assistant-manager.log.filter.all",
    "fel-assistant-manager.log.filter.assistant",
    "fel-assistant-manager.log.filter.label",
    "fel-assistant-manager.log.tag.backToTags",
    "fel-assistant-manager.notifications.errorWhileImportingLogs",
    "fel-assistant-manager.notifications.logsImported",
    "fel-assistant-manager.notifications.noFileSelected",
    "fel-assistant-manager.storageManager.cacheName.managerIsLoading",
    "fel-assistant-manager.storageManager.items",
    "fel-assistant-manager.storageManager.storages"

];

// Path to the English snippet file
$snippetFile = 'custom/plugins/FelOAIAssistantsManager/src/Resources/app/administration/src/module/fel-assistant-manager/snippet/en-GB.json';

// Create a backup file
$backupFile = $snippetFile . '.backup-' . date('Y-m-d-H-i-s') . '.json';
if (!copy($snippetFile, $backupFile)) {
    die("Error: Could not create backup file $backupFile\n");
}
echo "Created backup file: $backupFile\n";

// Read the file
$content = file_get_contents($snippetFile);
if ($content === false) {
    die("Error: Could not read file $snippetFile\n");
}

// Parse the JSON
$data = json_decode($content, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Error: Failed to parse JSON: " . json_last_error_msg() . "\n");
}

// Function to remove a key from a nested array
function removeKey(&$array, $key) {
    $parts = explode('.', $key);
    $firstPart = array_shift($parts);

    if (!isset($array[$firstPart])) {
        return false; // Key not found
    }

    if (empty($parts)) {
        // This is the last part of the key
        unset($array[$firstPart]);
        return true;
    }

    // More parts to process, recurse
    $remainingKey = implode('.', $parts);
    return removeKey($array[$firstPart], $remainingKey);
}

// Remove each key
$removedCount = 0;
foreach ($keysToRemove as $key) {
    // Skip the 'fel-assistant-manager.' prefix as it's the root of the JSON
    $key = preg_replace('/^fel-assistant-manager\./', '', $key);

    if (removeKey($data['fel-assistant-manager'], $key)) {
        $removedCount++;
        echo "Removed key: fel-assistant-manager.$key\n";
    } else {
        echo "Key not found: fel-assistant-manager.$key\n";
    }
}

// Save the modified JSON back to the file
$newContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
if (file_put_contents($snippetFile, $newContent) === false) {
    die("Error: Could not write to file $snippetFile\n");
}

echo "Done! Removed $removedCount keys from $snippetFile\n";
