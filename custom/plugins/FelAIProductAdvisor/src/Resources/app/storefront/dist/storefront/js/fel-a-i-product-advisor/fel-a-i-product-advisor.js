(()=>{"use strict";var t={857:t=>{var e=function(t){var e;return!!t&&"object"==typeof t&&"[object RegExp]"!==(e=Object.prototype.toString.call(t))&&"[object Date]"!==e&&t.$$typeof!==s},s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function a(t,e){return!1!==e.clone&&e.isMergeableObject(t)?n(Array.isArray(t)?[]:{},t,e):t}function o(t,e,s){return t.concat(e).map(function(t){return a(t,s)})}function i(t){return Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t).filter(function(e){return Object.propertyIsEnumerable.call(t,e)}):[])}function r(t,e){try{return e in t}catch(t){return!1}}function n(t,s,l){(l=l||{}).arrayMerge=l.arrayMerge||o,l.isMergeableObject=l.isMergeableObject||e,l.cloneUnlessOtherwiseSpecified=a;var c,d,h=Array.isArray(s);return h!==Array.isArray(t)?a(s,l):h?l.arrayMerge(t,s,l):(d={},(c=l).isMergeableObject(t)&&i(t).forEach(function(e){d[e]=a(t[e],c)}),i(s).forEach(function(e){(!r(t,e)||Object.hasOwnProperty.call(t,e)&&Object.propertyIsEnumerable.call(t,e))&&(r(t,e)&&c.isMergeableObject(s[e])?d[e]=(function(t,e){if(!e.customMerge)return n;var s=e.customMerge(t);return"function"==typeof s?s:n})(e,c)(t[e],s[e],c):d[e]=a(s[e],c))}),d)}n.all=function(t,e){if(!Array.isArray(t))throw Error("first argument should be an array");return t.reduce(function(t,s){return n(t,s,e)},{})},t.exports=n}},e={};function s(a){var o=e[a];if(void 0!==o)return o.exports;var i=e[a]={exports:{}};return t[a](i,i.exports,s),i.exports}(()=>{s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e}})(),(()=>{s.d=(t,e)=>{for(var a in e)s.o(e,a)&&!s.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:e[a]})}})(),(()=>{s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})(),(()=>{class t{static ucFirst(t){return t.charAt(0).toUpperCase()+t.slice(1)}static lcFirst(t){return t.charAt(0).toLowerCase()+t.slice(1)}static toDashCase(t){return t.replace(/([A-Z])/g,"-$1").replace(/^-/,"").toLowerCase()}static toLowerCamelCase(e,s){let a=t.toUpperCamelCase(e,s);return t.lcFirst(a)}static toUpperCamelCase(e,s){return s?e.split(s).map(e=>t.ucFirst(e.toLowerCase())).join(""):t.ucFirst(e.toLowerCase())}static parsePrimitive(t){try{return/^\d+(.|,)\d+$/.test(t)&&(t=t.replace(",",".")),JSON.parse(t)}catch(e){return t.toString()}}}class e{static isNode(t){return"object"==typeof t&&null!==t&&(t===document||t===window||t instanceof Node)}static hasAttribute(t,s){if(!e.isNode(t))throw Error("The element must be a valid HTML Node!");return"function"==typeof t.hasAttribute&&t.hasAttribute(s)}static getAttribute(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!1===e.hasAttribute(t,s))throw Error('The required property "'.concat(s,'" does not exist!'));if("function"!=typeof t.getAttribute){if(a)throw Error("This node doesn't support the getAttribute function!");return}return t.getAttribute(s)}static getDataAttribute(s,a){let o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=a.replace(/^data(|-)/,""),r=t.toLowerCamelCase(i,"-");if(!e.isNode(s)){if(o)throw Error("The passed node is not a valid HTML Node!");return}if(void 0===s.dataset){if(o)throw Error("This node doesn't support the dataset attribute!");return}let n=s.dataset[r];if(void 0===n){if(o)throw Error('The required data attribute "'.concat(a,'" does not exist on ').concat(s,"!"));return n}return t.parsePrimitive(n)}static querySelector(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!e.isNode(t))throw Error("The parent node is not a valid HTML Node!");let o=t.querySelector(s)||!1;if(a&&!1===o)throw Error('The required element "'.concat(s,'" does not exist in parent node!'));return o}static querySelectorAll(t,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(a&&!e.isNode(t))throw Error("The parent node is not a valid HTML Node!");let o=t.querySelectorAll(s);if(0===o.length&&(o=!1),a&&!1===o)throw Error('At least one item of "'.concat(s,'" must exist in parent node!'));return o}static getFocusableElements(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return t.querySelectorAll('\n            input:not([tabindex^="-"]):not([disabled]):not([type="hidden"]),\n            select:not([tabindex^="-"]):not([disabled]),\n            textarea:not([tabindex^="-"]):not([disabled]),\n            button:not([tabindex^="-"]):not([disabled]),\n            a[href]:not([tabindex^="-"]):not([disabled]),\n            [tabindex]:not([tabindex^="-"]):not([disabled])\n        ')}static getFirstFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return this.getFocusableElements(t)[0]}static getLastFocusableElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,e=this.getFocusableElements(t);return e[e.length-1]}}class a{static iterate(t,e){if(t instanceof Map||Array.isArray(t))return t.forEach(e);if(t instanceof FormData){for(var s of t.entries())e(s[1],s[0]);return}if(t instanceof NodeList)return t.forEach(e);if(t instanceof HTMLCollection)return Array.from(t).forEach(e);if(t instanceof Object)return Object.keys(t).forEach(s=>{e(t[s],s)});throw Error("The element type ".concat(typeof t," is not iterable!"))}}class o{static isSupported(){return"undefined"!==document.cookie}static setItem(t,e,s){if(null==t)throw Error("You must specify a key to set a cookie");let a=new Date;a.setTime(a.getTime()+864e5*s);let o="";"https:"===location.protocol&&(o="secure"),document.cookie="".concat(t,"=").concat(e,";expires=").concat(a.toUTCString(),";path=/;sameSite=lax;").concat(o)}static getItem(t){if(!t)return!1;let e=t+"=",s=document.cookie.split(";");for(let t=0;t<s.length;t++){let a=s[t];for(;" "===a.charAt(0);)a=a.substring(1);if(0===a.indexOf(e))return a.substring(e.length,a.length)}return!1}static removeItem(t){document.cookie="".concat(t,"= ; expires = Thu, 01 Jan 1970 00:00:00 GMT;path=/")}static key(){return""}static clear(){}}class i{setItem(t,e){return this._storage[t]=e}getItem(t){return Object.prototype.hasOwnProperty.call(this._storage,t)?this._storage[t]:null}removeItem(t){return delete this._storage[t]}key(t){return Object.values(this._storage)[t]||null}clear(){return this._storage={}}constructor(){this._storage={}}}class r{_chooseStorage(){return r._isSupported("localStorage")?this._storage=window.localStorage:r._isSupported("sessionStorage")?this._storage=window.sessionStorage:o.isSupported()?this._storage=o:this._storage=new i}static _isSupported(t){try{let e="__storage_test";return window[t].setItem(e,"1"),window[t].removeItem(e),!0}catch(t){return!1}}_validateStorage(){if("function"!=typeof this._storage.setItem)throw Error('The storage must have a "setItem" function');if("function"!=typeof this._storage.getItem)throw Error('The storage must have a "getItem" function');if("function"!=typeof this._storage.removeItem)throw Error('The storage must have a "removeItem" function');if("function"!=typeof this._storage.key)throw Error('The storage must have a "key" function');if("function"!=typeof this._storage.clear)throw Error('The storage must have a "clear" function')}getStorage(){return this._storage}constructor(){this._storage=null,this._chooseStorage(),this._validateStorage()}}let n=Object.freeze(new r).getStorage();var l=s(857);class c{publish(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=new CustomEvent(t,{detail:e,cancelable:s});return this.el.dispatchEvent(a),a}subscribe(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=this,o=t.split("."),i=s.scope?e.bind(s.scope):e;if(s.once&&!0===s.once){let e=i;i=function(s){a.unsubscribe(t),e(s)}}return this.el.addEventListener(o[0],i),this.listeners.push({splitEventName:o,opts:s,cb:i}),!0}unsubscribe(t){let e=t.split(".");return this.listeners=this.listeners.reduce((t,s)=>([...s.splitEventName].sort().toString()===e.sort().toString()?this.el.removeEventListener(s.splitEventName[0],s.cb):t.push(s),t),[]),!0}reset(){return this.listeners.forEach(t=>{this.el.removeEventListener(t.splitEventName[0],t.cb)}),this.listeners=[],!0}get el(){return this._el}set el(t){this._el=t}get listeners(){return this._listeners}set listeners(t){this._listeners=t}constructor(t=document){this._el=t,t.$emitter=this,this._listeners=[]}}let d="js-offcanvas-singleton";class h{open(t,e,s,a,o,i,r){this._removeExistingOffCanvas();let n=this._createOffCanvas(s,i,r,a);this.setContent(t,o),this._openOffcanvas(n,e)}setContent(t,e){let s=this.getOffCanvas();s[0]&&(s[0].innerHTML=t,this._registerEvents(e))}setAdditionalClassName(t){this.getOffCanvas()[0].classList.add(t)}getOffCanvas(){return document.querySelectorAll(".".concat(d))}close(t){let e=this.getOffCanvas();a.iterate(e,t=>{bootstrap.Offcanvas.getInstance(t).hide()}),setTimeout(()=>{this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:e})},t)}goBackInHistory(){window.history.back()}exists(){return this.getOffCanvas().length>0}_openOffcanvas(t,e){window.focusHandler.saveFocusState("offcanvas"),h.bsOffcanvas.show(),window.history.pushState("offcanvas-open",""),"function"==typeof e&&e()}_registerEvents(t){let e=this.getOffCanvas();a.iterate(e,s=>{let a=()=>{setTimeout(()=>{s.remove(),window.focusHandler.resumeFocusState("offcanvas"),this.$emitter.publish("onCloseOffcanvas",{offCanvasContent:e})},t),s.removeEventListener("hide.bs.offcanvas",a)};s.addEventListener("hide.bs.offcanvas",a)}),window.addEventListener("popstate",this.close.bind(this,t),{once:!0});let s=document.querySelectorAll(".".concat("js-offcanvas-close"));a.iterate(s,e=>e.addEventListener("click",this.close.bind(this,t)))}_removeExistingOffCanvas(){h.bsOffcanvas=null;let t=this.getOffCanvas();return a.iterate(t,t=>t.remove())}_getPositionClass(t){return"left"===t?"offcanvas-start":"right"===t?"offcanvas-end":"offcanvas-".concat(t)}_createOffCanvas(t,e,s,a){let o=document.createElement("div");if(o.classList.add("offcanvas",d),o.classList.add(this._getPositionClass(t)),o.setAttribute("tabindex","-1"),!0===e&&o.classList.add("is-fullwidth"),s){let t=typeof s;if("string"===t)o.classList.add(s);else if(Array.isArray(s))s.forEach(t=>{o.classList.add(t)});else throw Error('The type "'.concat(t,'" is not supported. Please pass an array or a string.'))}return document.body.appendChild(o),h.bsOffcanvas=new bootstrap.Offcanvas(o,{backdrop:!1!==a||"static"}),o}constructor(){this.$emitter=new c}}let u=Object.freeze(new h);class g{static open(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"left",a=!(arguments.length>3)||void 0===arguments[3]||arguments[3],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:350,i=arguments.length>5&&void 0!==arguments[5]&&arguments[5],r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"";u.open(t,e,s,a,o,i,r)}static setContent(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:350;u.setContent(t,e,s)}static setAdditionalClassName(t){u.setAdditionalClassName(t)}static close(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:350;u.close(t)}static exists(){return u.exists()}static getOffCanvas(){return u.getOffCanvas()}static REMOVE_OFF_CANVAS_DELAY(){return 350}}class f{get(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"application/json",a=this._createPreparedRequest("GET",t,s);return this._sendRequest(a,null,e)}post(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let o=this._createPreparedRequest("POST",t,a);return this._sendRequest(o,e,s)}delete(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let o=this._createPreparedRequest("DELETE",t,a);return this._sendRequest(o,e,s)}patch(t,e,s){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"application/json";a=this._getContentType(e,a);let o=this._createPreparedRequest("PATCH",t,a);return this._sendRequest(o,e,s)}abort(){if(this._request)return this._request.abort()}setErrorHandlingInternal(t){this._errorHandlingInternal=t}_registerOnLoaded(t,e){e&&(!0===this._errorHandlingInternal?(t.addEventListener("load",()=>{e(t.responseText,t)}),t.addEventListener("abort",()=>{console.warn("the request to ".concat(t.responseURL," was aborted"))}),t.addEventListener("error",()=>{console.warn("the request to ".concat(t.responseURL," failed with status ").concat(t.status))}),t.addEventListener("timeout",()=>{console.warn("the request to ".concat(t.responseURL," timed out"))})):t.addEventListener("loadend",()=>{e(t.responseText,t)}))}_sendRequest(t,e,s){return this._registerOnLoaded(t,s),t.send(e),t}_getContentType(t,e){return t instanceof FormData&&(e=!1),e}_createPreparedRequest(t,e,s){return this._request=new XMLHttpRequest,this._request.open(t,e),this._request.setRequestHeader("X-Requested-With","XMLHttpRequest"),s&&this._request.setRequestHeader("Content-type",s),this._request}constructor(){this._request=null,this._errorHandlingInternal=!1}}let p="loader",m={BEFORE:"before",INNER:"inner"};class v{create(){if(!this.exists()){if(this.position===m.INNER){this.parent.innerHTML=v.getTemplate();return}this.parent.insertAdjacentHTML(this._getPosition(),v.getTemplate())}}remove(){let t=this.parent.querySelectorAll(".".concat(p));a.iterate(t,t=>t.remove())}exists(){return this.parent.querySelectorAll(".".concat(p)).length>0}_getPosition(){return this.position===m.BEFORE?"afterbegin":"beforeend"}static getTemplate(){return'<div class="'.concat(p,'" role="status">\n                    <span class="').concat("visually-hidden",'">Loading...</span>\n                </div>')}static SELECTOR_CLASS(){return p}constructor(t,e=m.BEFORE){this.parent=t instanceof Element?t:document.body.querySelector(t),this.position=e}}let b=null;class C extends g{static open(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"left",o=!(arguments.length>4)||void 0===arguments[4]||arguments[4],i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:g.REMOVE_OFF_CANVAS_DELAY(),r=arguments.length>6&&void 0!==arguments[6]&&arguments[6],n=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"";if(!t)throw Error("A url must be given!");u._removeExistingOffCanvas();let l=u._createOffCanvas(a,r,n,o);this.setContent(t,e,s,o,i),u._openOffcanvas(l)}static setContent(t,e,s,a,o){let i=new f;super.setContent('<div class="offcanvas-body">'.concat(v.getTemplate(),"</div>"),a,o),b&&b.abort();let r=t=>{super.setContent(t,a,o),"function"==typeof s&&s(t)};b=e?i.post(t,e,C.executeCallback.bind(this,r)):i.get(t,C.executeCallback.bind(this,r))}static executeCallback(t,e){"function"==typeof t&&t(e),window.PluginManager.initializePlugins()}}let y="element-loader-backdrop";class E extends v{static create(t){t.classList.add("has-element-loader"),E.exists(t)||(E.appendLoader(t),setTimeout(()=>{let e=t.querySelector(".".concat(y));e&&e.classList.add("element-loader-backdrop-open")},1))}static remove(t){t.classList.remove("has-element-loader");let e=t.querySelector(".".concat(y));e&&e.remove()}static exists(t){return t.querySelectorAll(".".concat(y)).length>0}static getTemplate(){return'\n        <div class="'.concat(y,'">\n            <div class="loader" role="status">\n                <span class="').concat("visually-hidden",'">Loading...</span>\n            </div>\n        </div>\n        ')}static appendLoader(t){t.insertAdjacentHTML("beforeend",E.getTemplate())}}window.router["frontend.cookie.offcanvas"];class L extends window.PluginBaseClass{initChatbotTheme(){let t=document.getElementById("fel-chatbot-advisor");if(!t)return;let e=t.dataset.themeColor||"";if(e){document.documentElement.style.setProperty("--fel-chatbot-theme-color",e),document.documentElement.style.setProperty("--fel-chatbot-theme-color-hover",this._darkenColor(e,15));let t=this._hexToRgb(e);t&&document.documentElement.style.setProperty("--fel-chatbot-theme-color-rgb","".concat(t.r,", ").concat(t.g,", ").concat(t.b))}}init(){this.setPluginOptions()&&(this.setPluginVars(),this.loadTemplates(),this.registerEvents(),this.initChatbotTheme())}registerEvents(){this.handleEvent=t=>{if(this.isLoading&&!t.target.classList.contains("fel-ui-btn"))return t.preventDefault(),!1;switch(t.type){case"submit":return this.chatEventCallback(t,t.target);case"change":return this.changeEventCallback(t,t.target);case"click":return this.clickEventCallback(t,t.target)}},this.setEventHandler("add"),this.setLocalStorageIsAllowed(),this.swEventHelper()}swEventHelper(){document.$emitter.subscribe("CookieConfiguration_Update",this.cookieCallback.bind(this))}setLocalStorageIsAllowed(){this.checkIfLocalStorageIsAllowed()?(this.assistantEl.classList.add("fel-localstorage-is-allowed"),this.assistantEl.classList.remove("fel-localstorage-is-disabled"),this.setCheckedConsentLocalStorageTemplate(!0),this.assistantEl.classList.contains("active")&&this.storageSet("".concat(this.options.storage.chat,"_chatOpen"),"active"),this.assistantEl.classList.contains("fel-zoom-chat")&&this.storageSet("".concat(this.options.storage.chat,"_chatZoom"),"active")):(this.assistantEl.classList.add("fel-localstorage-is-disabled"),this.assistantEl.classList.remove("fel-localstorage-is-allowed"),this.setCheckedConsentLocalStorageTemplate(!1),this.clearLocalStorage())}cookieCallback(t){var e=this.options.selector.cookieDefaultName;void 0!==t.detail[e]?(this.localStorageAllowed=t.detail[e],this.localStorageAllowed&&this.enableLocalStorage()):(this.localStorageAllowed=!1,this.clearLocalStorage());var s=this.domGet(this.assistantEl,'[name="fel-allow-localstorage"]',!1);s&&(s.checked=!!this.localStorageAllowed||null,this.setCheckedConsentLocalStorageTemplate(this.localStorageAllowed))}checkIfLocalStorageIsAllowed(){return o.getItem(this.options.selector.cookieDefaultName)}setCheckedConsentLocalStorageTemplate(t){var e=this.assistantEl.querySelector('[name="fel-allow-localstorage"]');e&&(e.checked=!!t),this.template.greeting.querySelector('[name="fel-allow-localstorage"]').checked=!!t}toggleLocalStorage(t,e){e.checked?o.setItem(this.options.selector.cookieDefaultName,"allowed"):o.removeItem(this.options.selector.cookieDefaultName),this.setCheckedConsentLocalStorageTemplate(e.checked),this.setLocalStorageIsAllowed(),e.blur()}chatEventCallback(t,e){try{return this._chatEventCallback(t,e)}catch(t){return this.putToMessages("chatbot",e.message||"error")}}checkIfMessageThrown(t){return!!t.error&&void 0!==t.error.exception&&t.error.exception}_chatEventCallback(t,e){if("fel-chatbot-form"===e.id){if(t.preventDefault(),this.getChatbotUserInput()){var s=this.getChatbotUserInput().value,a=document.querySelector('[name="system-prefix"]').value,o=s;this.threadId&&this.getChatbotThreadId().value||(s=a+s),this.putToMessages("user",o);var i={threadId:this.getChatbotThreadId().value,runId:this.getChatbotRunId().value,userInput:s,assiId:document.querySelector('[name="fel-chatbot-assistant-id"]').value};""===i.runId?(this.isLoadingResponse=!1,this.clientPost("".concat(this.controllerUrl,"/create-thread"),i,t=>{if(t){if(this.$emitter.publish("felAssistantThreadCreated"),this.checkIfMessageThrown(t))return this.putToMessages("chatbot",this.checkIfMessageThrown(t),"",""," fel-system-exception");if(void 0!==t.id){this.threadId=t.id,this.getChatbotUserInput().value="",this.getChatbotRunId().value=t.runId,this.getChatbotThreadId().value=t.threadId,this.assistantEl.classList.add("contains-thread");var e=i.userInput,s={threadId:t.threadId,runId:t.runId,userInput:e,assiId:document.querySelector('[name="fel-chatbot-assistant-id"]').value};this.isLoadingResponse=!0,this.clientPost("".concat(this.controllerUrl,"/run"),s,t=>{this.handleRunResponse(t)});return}}})):this.threadId&&(this.isLoadingResponse=!0,this.getChatbotUserInput().value="",this.scrollThreadIntoView("smooth",!0),this.clientPost("".concat(this.controllerUrl,"/run"),i,t=>{this.handleRunResponse(t)}))}return!1}}replaceAiProductList(t,e){return e&&e.items&&e.items.products&&e.items.products.length>0&&(e.fetch.elements,this._client.get("/fel-product-listing-template",s=>{try{t.value=s;let e=this.getChatbotMessagesEl();if(e){let s=e.querySelector(".chatbot-message-item:last-child .chatbot-message .fel-inner");s&&(s.innerHTML=t.value)}}catch(s){var a=document.createElement("div"),o=document.createElement("div");if(a.insertAdjacentHTML("beforeend",t.originalValue),o.insertAdjacentHTML("beforeend",t.value),a.querySelector("ul")){var i=a.querySelectorAll("ul");if(i.length){if(1===i.length&&void 0!==e.items&&void 0!==e.items.products&&e.items.products.length){for(var r=0;r<e.items.products.length;r++)if(t.originalValue.includes(e.items.products[r].id)){a.querySelector("ul").replaceWith(o),t.value=a.innerHTML;break}}else t.value=t.originalValue}}}})),t}uiActionRequired(t){void 0!==t.redirectTo&&t.redirectTo&&t.redirectTo!==location.href&&(location.href=t.redirectTo)}deleteChatThread(){var t=this.getChatbotThreadId().value;t?(this.addIsLoading(),this._client.post("".concat(this.controllerUrl,"/delete-thread"),JSON.stringify({threadId:t}),t=>{this.removeIsLoading(),t&&(t=JSON.parse(t),this.assistantEl.classList.remove("contains-thread"),this.getChatbotRunId().value="",this.getChatbotThreadId().value="",this.chatClearStorage(),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML),this.getChatbotUserInput().value="",this.scrollThreadIntoView())})):(this.chatClearStorage(),this.assistantEl.classList.remove("contains-thread"),this.getChatbotMessagesEl()&&(this.getChatbotMessagesEl().innerHTML=this.template.greeting.outerHTML))}clientPost(t,e,s){this.addIsLoading(),this._client.post(t,JSON.stringify(e),t=>{if(this.removeIsLoading(),t)try{var e=JSON.parse(t);e&&(t=e)}catch(t){var a=document.createElement("div");return a.appendChild(document.createTextNode(t)),s({error:{exception:a.innerHTML.substring(0,200)}})}return s(t)})}scrollIntoView(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1],arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&void 0!==arguments[3]&&arguments[3]}scrollThreadIntoView(t,e){let s=this.getChatbotMessagesEl();if(s){let e=s.querySelectorAll(".chat-message-item"),a=null;for(let t=e.length-1;t>=0;t--)if(e[t].classList.contains("fel-user")){a=e[t];break}if(a){let e=a.offsetTop-10;s.scrollTo({top:e,behavior:t||"smooth"});return}let o=e[e.length-1];if(o){let e=o.offsetTop-20;s.scrollTo({top:e,behavior:t||"smooth"})}}}addIsLoading(){var t=this.template.loading;if(this.isLoading=!0,this.assistantEl.classList.add("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=!0,this.getChatbotUserInput().disabled=!0),this.removeGreetingContainer(),this.isLoadingResponse){var e=t.cloneNode(!0),s=this.domGet(e,".fel-chatbot-loader-info",!1);s&&s.dataset.secondRun&&(this.domGet(e,".fel-chatbot-loader-info").innerHTML=s.dataset.secondRun),t=e}this.putToMessages("loading",t.outerHTML),setTimeout(()=>{this.scrollThreadIntoView()},50)}removeIsLoading(){this.isLoading=!1,this.assistantEl.classList.remove("fel-chatbot-is-loading"),this.getChatSubmitButton()&&(this.getChatSubmitButton().disabled=null,this.getChatbotUserInput().disabled=null);var t=this.domGetAll(this.assistantEl,".fel-chatbot-load-container",!1);if(t.length)for(let e=0;e<t.length;e++)t[e].classList.add("position-absolute","w-100"),this.debounce(()=>{t[e].remove()},150)}removeGreetingContainer(){var t=this.domGet(this.assistantEl,this.options.selector.greetingContainer,!1);t&&(t.remove(),this.assistantEl.classList.remove("fel-chat-initial"))}getChatSubmitButton(){return this.domGet(this.assistantEl,this.options.selector.submitChatButton,!1)}getChatbotMessagesEl(){return this.domGet(this.assistantEl,this.options.selector.chatMessages)}processButtonsInMessage(t){return t?t.replace(/\[BUTTON:([^\]]+)\]/g,(t,e)=>'<button type="button" class="btn btn-outline-primary fel-btn" data-callback="chatSendMessage">'.concat(e,"</button>")):t}getChatbotUserInput(){return this.domGet(this.chatbotForm,this.options.selector.chatUserInput)}getChatbotThreadId(){return this.domGet(this.chatbotForm,this.options.selector.chatThreadId)}getChatbotRunId(){return this.domGet(this.chatbotForm,this.options.selector.chatRunId)}addToChatSubmit(t,e){var s=e.dataset.prompt;s&&(this.getChatbotUserInput().value=s,this.getChatSubmitButton().dispatchEvent(new MouseEvent("click",{bubbles:!0,cancelable:!0})))}chatSendMessage(t,e){let s=e.textContent.trim();s&&(this.getChatbotUserInput().value=s,this.getChatSubmitButton().dispatchEvent(new MouseEvent("click",{bubbles:!0,cancelable:!0})))}showAllProducts(t,e){let s=e.dataset.productIds;if(!s)return;let a=s.split(",");if(0===a.length)return;let o=e.closest(".fel-product-listing");if(!o)return;let i=o.querySelector(".fel-product-list");if(!i)return;e.remove();let r=Array.from(i.querySelectorAll(".fel-product-item")).map(t=>t.dataset.productId),n=a.filter(t=>!r.includes(t));n.forEach(t=>{let e='\n			<div class="fel-product-item" data-product-id="'.concat(t,'">\n				<div class="fel-product-image">\n					<!-- SVG-Platzhalterbild wird angezeigt, bis das echte Produktbild geladen ist -->\n					<svg id="meteor-icon-kit__regular-image" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 20.4948L9.25259 12.3356C9.63485 11.9056 10.3003 11.886 10.7071 12.2929L12.7071 14.2929C12.7219 14.3077 12.7361 14.3228 12.7498 14.3382L15.2318 11.3598C15.6082 10.9081 16.2913 10.8771 16.7071 11.2929L22 16.5858V3C22 2.44772 21.5523 2 21 2H3C2.44772 2 2 2.44772 2 3V20.4948ZM3.33795 22H21C21.5523 22 22 21.5523 22 21V19.4142L16.0672 13.4814L11.7682 18.6402C11.4147 19.0645 10.7841 19.1218 10.3598 18.7682C9.93554 18.4147 9.87821 17.7841 10.2318 17.3598L11.4842 15.857C11.416 15.8159 11.3517 15.7659 11.2929 15.7071L10.0428 14.457L3.33795 22ZM3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3C0 1.34315 1.34315 0 3 0ZM7.5 11C5.567 11 4 9.433 4 7.5C4 5.567 5.567 4 7.5 4C9.433 4 11 5.567 11 7.5C11 9.433 9.433 11 7.5 11ZM7.5 9C8.32843 9 9 8.32843 9 7.5C9 6.67157 8.32843 6 7.5 6C6.67157 6 6 6.67157 6 7.5C6 8.32843 6.67157 9 7.5 9Z" fill="#758CA3"/></svg>\n					<!-- Das echte Produktbild wird hier eingefügt, wenn es geladen ist -->\n				</div>\n				<div class="fel-product-info">\n					<div class="fel-product-name">Produkt wird geladen...</div>\n					<div class="fel-product-price">Preis wird geladen...</div>\n					<div class="fel-product-manufacturer">Hersteller wird geladen...</div>\n					<div class="fel-product-description">Beschreibung wird geladen...</div>\n					<a href="/detail/').concat(t,'" class="fel-product-button">Details ansehen</a>\n				</div>\n			</div>');i.insertAdjacentHTML("beforeend",e)}),this.initProductLoading(n),this.scrollThreadIntoView()}toggleChatbot(t,e){var s=e.dataset.toggleClass;if(s){this.assistantEl.classList.toggle(s);var a="".concat(this.options.storage.chat,"_chatOpen");this.assistantEl.classList.contains("active")?(this.storageSet(a,"active"),this.scrollThreadIntoView()):this.storageSet(a,"not-active")}}toggleChatZoom(t,e){if(e.classList.toggle("fel-active"),this.assistantEl){var s="".concat(this.options.storage.chat,"_chatZoom");e.classList.contains("fel-active")?(this.assistantEl.classList.add("fel-zoom-chat"),this.storageSet(s,"active")):(this.assistantEl.classList.remove("fel-zoom-chat"),this.storageSet(s,"not-active")),this.scrollThreadIntoView()}}removeChatMessage(t){if(void 0!==t.target.offsetParent&&void 0!==t.target.offsetParent.offsetParent){var e=t.target.offsetParent.offsetParent;e&&(e.previousElementSibling.classList.contains("user-message-item")&&e.previousElementSibling.remove(),e.remove(),this.chatToStorage(this.getChatbotThreadId().value),""==this.storageGet(this.options.storage.chat).trim()&&this.deleteChatThread(),this.scrollThreadIntoView())}}clickEventCallback(t,e){if(e.classList.contains("fel-show-all-products"))return t.preventDefault(),this.showAllProducts(t,e);if(e.classList.contains("fel-btn")){var s=e.dataset.callback;if(e.dataset.preventDefault&&t.preventDefault(),e.dataset.stopPropagation&&t.stopPropagation(),e.dataset.blur&&e.blur(),s){if("function"==typeof this[s])return this[s](...arguments);if("function"==typeof window[s])return window[s](...arguments)}}}changeEventCallback(t,e){if(e.classList.contains("fel-checkbox")){t.preventDefault();var s=e.dataset.callback;return"function"==typeof this[s]&&this[s](t,e),!1}}processButtonsInMessage(t){let e;if(!t||"string"!=typeof t)return t;let s=/\[BUTTON:(.*?)\]/g,a=t,o=[];for(;(e=s.exec(t))!==null;)o.push(e[1]);if(a=a.replace(s,""),o.length>0){if(a.includes('class="fel-text-plain')){let t=document.createElement("div");if(t.innerHTML=a,t.querySelector(".fel-text-plain")){let e='<div class="fel-chatbot-buttons-container mt-3">';e+='<div class="fel-chatbot-buttons">',o.forEach(t=>{e+='<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"\n							data-callback="addToChatSubmit" data-prompt="'.concat(t,'">').concat(t,"</button>")}),e+="</div></div>",a=t.innerHTML+e}}else{let t='<div class="fel-chatbot-buttons-container mt-3">';t+='<div class="fel-chatbot-buttons">',o.forEach(e=>{t+='<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"\n						data-callback="addToChatSubmit" data-prompt="'.concat(e,'">').concat(e,"</button>")}),t+="</div></div>",a+=t}}return a}putToMessages(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"";if(!e||""===e.trim())return;let i=e,r=null;try{e.trim().startsWith("{")&&e.trim().endsWith("}")&&(r=JSON.parse(e)).output&&(i=r.output)}catch(t){}if("chatbot"===t){if(r&&"product_search"===r.request_type){let e=this.extractProductIdsFromHtml(i);if(e.length>0){let r=this.generateProductTemplate(e,i);s&&(s=' id="'.concat(s,'"')),a&&(a=' data-datetime="'.concat(a,'"'));var n='<div class="fel-delete-message">\n						<span title="'.concat(this.options.translation.removeChatMessage,'"\n							class="btn fel-btn" data-callback="removeChatMessage">X</span>\n					</div>'),l='\n					<div class="'.concat(this.options.selector.chatMessageEl," ").concat(t,'-message-item fel-fade-in d-flex"').concat(s,'>\n						<div class="').concat(this.options.selector.chatMessage," ").concat(t,"-message").concat(o,'">\n							<div class="fel-inner"').concat(a,">\n								").concat(n,'\n								<div class="fel-product-container">').concat(r,"</div>\n							</div>\n						</div>\n					</div>");this.putHtml(this.getChatbotMessagesEl(),"beforeend",l),this.initProductLoading(e),setTimeout(()=>{this.updateChatButtonsTheme(),this.scrollThreadIntoView()},50);return}}i=this.processButtonsInMessage(i)}s&&(s=' id="'.concat(s,'"')),a&&(a=' data-datetime="'.concat(a,'"'));var n="chatbot"===t?'<div class="fel-delete-message">\n				<span title="'.concat(this.options.translation.removeChatMessage,'"\n					class="btn fel-btn" data-callback="removeChatMessage">X</span>\n			</div>'):"",l=["loading","greeting"].includes(t)?i:'\n			<div class="'.concat(this.options.selector.chatMessageEl," ").concat(t,"-message-item fel-fade-in d-flex ").concat("user"===t?"fel-user":"",'"').concat(s,'>\n				<div class="').concat(this.options.selector.chatMessage," ").concat(t,"-message").concat(o,'">\n					<div class="fel-inner"').concat(a,">\n						").concat(n,"\n						").concat(i,"\n					</div>\n				</div>\n			</div>");if(this.putHtml(this.getChatbotMessagesEl(),"beforeend",l),"user"===t){let t=document.getElementById("fel-chatbot-advisor");if(t){let e=t.dataset.themeColor||"";if(e){let t=this.getChatbotMessagesEl().querySelectorAll(".user-message .fel-inner"),s=t[t.length-1];s&&(s.style.backgroundColor=this._darkenColor(e,25),s.style.boxShadow="0 0 4px ".concat(e))}}}"chatbot"===t?setTimeout(()=>{this.updateChatButtonsTheme(),this.scrollThreadIntoView()},50):this.scrollThreadIntoView()}extractProductIdsFromHtml(t){let e;let s=[],a=/\/detail\/([a-f0-9]{32})/g;for(;(e=a.exec(t))!==null;)s.push(e[1]);let o=/href="[^"]*\/detail\/([a-f0-9]{32})"/g;for(;(e=o.exec(t))!==null;)s.includes(e[1])||s.push(e[1]);return s}processButtonsInMessage(t){let e=t;try{if(t.trim().startsWith("{")&&t.trim().endsWith("}")){let s=JSON.parse(t);s.output&&(s.output=this._processButtonsInHtml(s.output),e=JSON.stringify(s))}else e=this._processButtonsInHtml(t)}catch(s){e=this._processButtonsInHtml(t)}return e}_processButtonsInHtml(t){let e;let s=/\[BUTTON:(.*?)\]/g,a=[],o=t;for(;(e=s.exec(t))!==null;)a.push(e[1]);o=o.replace(s,"");let i=document.createElement("div");if(i.innerHTML=o,i.querySelectorAll("a[onclick]").forEach(t=>{let e=t.getAttribute("onclick");t.removeAttribute("onclick");let s=t.textContent.trim(),a=e.match(/alert\(['"](.*?)['"]\)/i);a&&a[1]&&(s=a[1]);let o=document.createElement("button");o.setAttribute("type","button"),o.setAttribute("data-prompt",s),o.setAttribute("data-callback","addToChatSubmit"),o.classList.add("btn","btn-outline-primary","fel-btn"),o.textContent=s,t.parentNode.replaceChild(o,t)}),i.querySelectorAll("*").forEach(t=>{if(t.hasAttribute("class")){let e=t.getAttribute("class");e.includes('"')&&t.setAttribute("class",e.replace(/\"/g,""))}if(t.hasAttribute("data-prompt")){let e=t.getAttribute("data-prompt");e.includes('"')&&t.setAttribute("data-prompt",e.replace(/\"/g,""))}if(t.hasAttribute("data-callback")){let e=t.getAttribute("data-callback");e.includes('"')&&t.setAttribute("data-callback",e.replace(/\"/g,""))}t.hasAttribute("style")&&t.removeAttribute("style")}),i.querySelectorAll("ul").forEach(t=>{t.classList.add("fel-chatbot-buttons")}),i.querySelectorAll("ul li").forEach(t=>{t.classList.add("fel-chatbot-button-item")}),i.querySelectorAll(".fel-btn").forEach(t=>{let e=document.getElementById("fel-chatbot-advisor");if(e&&e.dataset.themeColor){let s=e.dataset.themeColor;t.style.setProperty("--theme-color",s)}}),a.length>0){let t=document.getElementById("fel-chatbot-advisor"),e=t&&t.dataset.themeColor||"",s='<div class="fel-chatbot-buttons-container mt-3"><div class="fel-chatbot-buttons">';return a.forEach(t=>{let a=e?'style="border-color: '.concat(e,"; color: ").concat(e,';"'):"";s+='<button type="button" class="fel-btn btn btn-sm btn-outline-primary mr-2 mb-2"\n					'.concat(a,' data-callback="addToChatSubmit" data-prompt="').concat(t,'">').concat(t,"</button>")}),s+="</div></div>",i.innerHTML+s}return i.innerHTML}initProductLoading(t){setTimeout(()=>{t.forEach(t=>{fetch("/detail/"+t,{method:"GET",headers:{Accept:"text/html"}}).then(t=>t.text()).then(e=>{let s=new DOMParser().parseFromString(e,"text/html"),a=document.querySelector('.fel-product-item[data-product-id="'+t+'"]');if(a){let t=a.querySelector(".fel-product-name"),e=a.querySelector(".fel-product-price"),o=a.querySelector(".fel-product-manufacturer"),i=a.querySelector(".fel-product-description");if(t){let e=s.querySelector(".product-detail-name");e&&(t.textContent=e.textContent.trim())}if(e){let t=s.querySelector(".product-detail-price");t&&(e.textContent=t.textContent.trim())}if(o){let t=s.querySelector(".product-detail-manufacturer-link");t&&(o.textContent=t.textContent.trim())}if(i){let t=s.querySelector(".product-detail-description");t&&(i.textContent=t.textContent.trim())}let r=s.querySelector(".gallery-slider-image");if(r&&r.src){let e=a.querySelector(".fel-product-image");if(e){let s=e.querySelector("svg");s&&s.remove();let a=e.querySelector("img");a||(a=document.createElement("img"),e.appendChild(a)),a.src=r.src,a.alt=t?t.textContent:"Produktbild"}}}}).catch(t=>{})})},100)}generateProductTemplate(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s="Produktergebnisse";if(e){let t=e.match(/<h[1-3]>(.*?)<\/h[1-3]>/i);t&&t[1]&&(s=t[1])}let a=this.options.config&&this.options.config.maxProductsInChat?this.options.config.maxProductsInChat:4,o=t.length,i=o>a,r=i?t.slice(0,a):t,n='<div class="fel-product-listing">\n			<h3 class="fel-product-listing-title">'.concat(s,'</h3>\n			<div class="fel-product-list">');return r.forEach(t=>{n+='\n			<div class="fel-product-item" data-product-id="'.concat(t,'">\n				<div class="fel-product-image">\n					<!-- SVG-Platzhalterbild wird angezeigt, bis das echte Produktbild geladen ist -->\n					<svg id="meteor-icon-kit__regular-image" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 20.4948L9.25259 12.3356C9.63485 11.9056 10.3003 11.886 10.7071 12.2929L12.7071 14.2929C12.7219 14.3077 12.7361 14.3228 12.7498 14.3382L15.2318 11.3598C15.6082 10.9081 16.2913 10.8771 16.7071 11.2929L22 16.5858V3C22 2.44772 21.5523 2 21 2H3C2.44772 2 2 2.44772 2 3V20.4948ZM3.33795 22H21C21.5523 22 22 21.5523 22 21V19.4142L16.0672 13.4814L11.7682 18.6402C11.4147 19.0645 10.7841 19.1218 10.3598 18.7682C9.93554 18.4147 9.87821 17.7841 10.2318 17.3598L11.4842 15.857C11.416 15.8159 11.3517 15.7659 11.2929 15.7071L10.0428 14.457L3.33795 22ZM3 0H21C22.6569 0 24 1.34315 24 3V21C24 22.6569 22.6569 24 21 24H3C1.34315 24 0 22.6569 0 21V3C0 1.34315 1.34315 0 3 0ZM7.5 11C5.567 11 4 9.433 4 7.5C4 5.567 5.567 4 7.5 4C9.433 4 11 5.567 11 7.5C11 9.433 9.433 11 7.5 11ZM7.5 9C8.32843 9 9 8.32843 9 7.5C9 6.67157 8.32843 6 7.5 6C6.67157 6 6 6.67157 6 7.5C6 8.32843 6.67157 9 7.5 9Z" fill="#758CA3"/></svg>\n					<!-- Das echte Produktbild wird hier eingefügt, wenn es geladen ist -->\n				</div>\n				<div class="fel-product-info">\n					<div class="fel-product-name">Produkt wird geladen...</div>\n					<div class="fel-product-price">Preis wird geladen...</div>\n					<div class="fel-product-manufacturer">Hersteller wird geladen...</div>\n					<div class="fel-product-description">Beschreibung wird geladen...</div>\n					<a href="/detail/').concat(t,'" class="fel-product-button">Details ansehen</a>\n				</div>\n			</div>')}),i&&(n+='\n			<button class="fel-show-all-products" data-product-ids="'.concat(t.join(","),'">\n				Alle ').concat(o," Produkte anzeigen (").concat(o-a," weitere)\n			</button>")),n+="</div></div>"}domGet(){return e.querySelector(...arguments)}domGetAll(){return e.querySelectorAll(...arguments)}storageGet(){return n.getItem(...arguments)}storageSet(){this.checkIfLocalStorageIsAllowed()&&n.setItem(...arguments)}storageRemove(){n.removeItem(...arguments)}chatClearStorage(){this.storageRemove(this.options.storage.chat),this.storageRemove("".concat(this.options.storage.chat,"_threadId"))}chatToStorage(t){var e=this.getChatbotMessagesEl();if(e){var s=this.domGetAll(e,this.options.selector.loadingContainer,!1);s.length&&this.foreach(s,(t,e)=>{e.remove()}),this.removeActiveProducts(),this.storageSet(this.options.storage.chat,e.innerHTML),this.storageSet("".concat(this.options.storage.chat,"_threadId"),t),this.setActiveProducts()}}clearLocalStorage(){this.storageRemove("".concat(this.options.storage.chat,"_threadId")),this.storageRemove("".concat(this.options.storage.chat,"_chatOpen")),this.storageRemove("".concat(this.options.storage.chat,"_chatZoom"))}foreach(t,e){return a.iterate(t,function(t,s){e(s,t)})}objectMerge(){for(var t=arguments.length,e=Array(t),s=0;s<t;s++)e[s]=arguments[s];var a=(t,e)=>(Object.entries(e).forEach(e=>{let[s,o]=e;t[s]=o&&"object"==typeof o?a(t[s]||{},o):o}),t);return e.reduce(a,{})}putHtml(t,e,s){t.insertAdjacentHTML(e,s)}debounce(){return"function"==typeof arguments[0]?setTimeout(()=>{arguments[0].apply()},arguments[1]||0):void 0}felParseInt(t){var t=t.replace(/[^-\d\.]/g,"");return t?Math.round(parseFloat(t)):0}loadTemplates(){this.template={greeting:"",loading:"",messageItem:""};var t=document.getElementById("fel-chatbot-template");if(t){if(this.template.greeting=t.content.querySelector(this.options.selector.greetingContainer),this.template.greeting){var e=this.storageGet(this.options.storage.chat);this.putToMessages("greeting",e||this.template.greeting.outerHTML),e&&(this.setActiveProducts(),this.assistantEl.classList.add("contains-thread"),this.getChatbotThreadId().value=this.storageGet(this.options.storage.chat+"_threadId"))}this.template.loading=t.content.querySelector(this.options.selector.loadingContainer)}}getActiveProductsInChat(){return this.assistantEl.querySelectorAll(".fel-chat-product-list li[data-ref-id].active")}removeActiveProducts(){var t=this.getActiveProductsInChat();if(t.length)for(let e=0;e<t.length;e++)t[e].classList.remove("active")}setActiveProducts(){if(this.options.page.refId){var t=this.assistantEl.querySelectorAll('.fel-chat-product-list li[data-ref-id="'.concat(this.options.page.refId,'"]'));if(t.length)for(let e=0;e<t.length;e++)t[e].classList.add("active")}}setPluginVars(){this._client=new f,this.eventsRegistered=!1,this.isLoading=!1,this.isLoadingResponse=!1,this.threadId=null,this.options.eventHandlerMap=this.chatbotForm?[[this.chatbotForm,"submit.felBtn",!0],[this.assistantEl,"click.felBtn",!0],[this.assistantEl,"change.felBtn",!0]]:[]}setPluginOptions(){this.assistantEl=this.domGet(document.body,"[data-assistant-gpt-plugin]"),this.chatbotForm=this.domGet(this.assistantEl,"form#fel-chatbot-form"),this.controllerUrl=this.chatbotForm.action,this.chatMessages=this.domGet(this.assistantEl,this.options.selector.chatMessages);var t=this.assistantEl.dataset.options;return!!(t&&"string"==typeof t&&(t=JSON.parse(t)))&&(this.options=this.objectMerge(this.options,t),!0)}removeEvents(){this.setEventHandler("remove")}resetEvents(){this.removeEvents(),this.registerEvents()}setEventHandler(t){var e={add:"addEventListener",remove:"removeEventListener"};if("remove"===t&&!this.eventsRegistered)return!1;"add"===t&&this.eventsRegistered&&this.removeEvents(),this.foreach(this.options.eventHandlerMap,(s,a)=>{if(void 0!==a[1]){var o=a[1].split(".");a[0][e[t]](o[0],this,a[2]||!0)}}),this.eventsRegistered="add"===t}_darkenColor(t,e){let s=parseInt((t=t.replace("#","")).substring(0,2),16),a=parseInt(t.substring(2,4),16),o=parseInt(t.substring(4,6),16);return"#".concat(Math.floor(s*(100-e)/100).toString(16).padStart(2,"0")).concat(Math.floor(a*(100-e)/100).toString(16).padStart(2,"0")).concat(Math.floor(o*(100-e)/100).toString(16).padStart(2,"0"))}_hexToRgb(t){return(t=t.replace("#",""),/^[0-9A-Fa-f]{6}$/.test(t))?{r:parseInt(t.substring(0,2),16),g:parseInt(t.substring(2,4),16),b:parseInt(t.substring(4,6),16)}:null}handleRunResponse(t){let e=null;if(t){let o=null,i=!1;if(t.uiActionRequired&&void 0!==t.uiActionRequired.product_search&&t.uiActionRequired.product_search.items&&t.uiActionRequired.product_search.items.products&&t.uiActionRequired.product_search.items.products.length>0&&(i=!0,o=t.uiActionRequired.product_search),!i&&t._trackActions&&t._trackActions.length>0)for(let e of t._trackActions){if(e.response&&e.response.length>0)for(let t of e.response){if(t.output&&t.output.products&&t.output.products.length>0||t.tool_call_id&&"product_search"===t.exec){i=!0,o=t;break}if(t.output&&t.output.output&&t.output.output.products){i=!0,o=t.output;break}}if(i)break}if(this.checkIfMessageThrown(t))this.putToMessages("chatbot",this.checkIfMessageThrown(t),"",""," fel-system-exception");else if(void 0!==t.id){if(void 0!==t.threadMessages){var s=t.threadMessages;if(e||(e=s),void 0!==s[0]&&void 0!==s[0].role&&"assistant"===s[0].role){var a=!0;if(i){this.foreach(s,(e,s)=>{if("user"===s.role&&(a=!1),a){let e=this.processButtonsInMessage(s.value),a=e,o=document.createElement("div");o.innerHTML=e;let i=o.querySelectorAll("ul");if(i.length>0){for(let t of i)t.innerHTML.includes("/detail/")&&t.remove();a=o.innerHTML}this.putToMessages("chatbot",a,"",t.datetime)}});let e=[];if(o&&o.output&&o.output.products?e=o.output.products.map(t=>t.id):o&&o.items&&o.items.products?e=o.items.products.map(t=>t.id):o&&o.output&&o.output.output&&o.output.output.products?e=o.output.output.products.map(t=>t.id):o&&o.fetch&&o.fetch.elements&&(e=o.fetch.elements.map(t=>t.id)),0===e.length&&s&&s.length>0){for(let t of s)if("assistant"===t.role&&t.value){let s=this.extractProductIdsFromHtml(t.value);if(s.length>0){e=s;break}}}if(e.length>0){let t=this.generateProductTemplate(e);this.putToMessages("chatbot",t,"",""),this.initProductLoading(e)}}else this.foreach(s,(e,s)=>{if("user"===s.role&&(a=!1),a){if(t.uiActionRequired&&void 0!==t.uiActionRequired.product_search)void 0!==s.originalValue&&(s=this.replaceAiProductList(s,t.uiActionRequired.product_search));else if(!s.value.includes("<p")&&!s.value.includes("<ul")){var o=document.createElement("div");o.classList.add("fel-text-plain","text-break"),o.insertAdjacentHTML("beforeend",s.value.trim()),s.value=o.outerHTML}s.value=this.processButtonsInMessage(s.value),this.putToMessages("chatbot",s.value,"",t.datetime)}})}else this.putToMessages("chatbot",this.options.translation.errorClearChat,"",t.datetime)}this.chatToStorage(t.threadId),this.$emitter.publish("felAssistantMessageResponse")}}}}L.options={compactView:640>window.innerWidth,selector:{chatMessages:"#fel-chatbot-messages",greetingContainer:".fel-chatbot-greeting-container",loadingContainer:".fel-chatbot-load-container",submitChatButton:".fel-submit-chat-btn",chatThreadId:'[name="fel-chatbot-thread-id"]',chatRunId:'[name="fel-chatbot-run-id"]',chatUserInput:'[name="fel-chatbot-user-input"]',chatMessageEl:"chat-message-item",chatMessage:"chat-message",cookieDefaultName:"fel-chatbot-localstorage-accepted"},page:{refId:null},storage:{chat:"felChatStorage"}};class S extends window.PluginBaseClass{init(){let t=this.el.querySelector("#fel-chatbot-advisor");t&&t.classList.add("active"),this._registerEvents()}_registerEvents(){let t=this.el.querySelector(".fel-delete-thread-btn");t&&t.addEventListener("click",this._onDeleteThread.bind(this));let e=this.el.querySelector(".fel-submit-chat-btn");e&&e.addEventListener("click",this._onSubmit.bind(this))}_onDeleteThread(t){let e=window.PluginManager.getPluginInstanceFromElement(this.el,"FelAssistantPlugin");e&&"function"==typeof e.deleteChatThread&&e.deleteChatThread()}_onSubmit(t){let e=this.el.querySelector("#fel-chatbot-form");if(e){let t=document.createElement("input");t.type="hidden",t.name="cms-assistant-id",t.value=this.options.cmsAssistantId,e.appendChild(t)}}}window.PluginManager.register("FelAssistantPlugin",L,"[data-assistant-gpt-plugin]"),window.PluginManager.register("CmsChatbotPlugin",S,"[data-cms-assistant-gpt-plugin]")})()})();