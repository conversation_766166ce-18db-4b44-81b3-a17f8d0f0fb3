<div class="fel-oai-trainer-container">
    <div class="fel-oai-trainer-container-inner">

        <div class="fel-oai-trainer-header-container fel-content-box">

            <div class="fel-oai-trainer-main-description">
                <div class="fel-oai-trainer-main-description-title fel-flex-space-between">
                    <h2>{{ $t('fel-gpt-assistant.trainer.name') }}</h2>
                    <div class="fel-oai-trainer-head-optons">
                        <button
                            class="sw-button sw-button--x-small"
                            data-action="toggle"
                            data-target=".fel-oai-trainer-examples-default-examples"
                            data-target-class="fel-is-hidden"
                            data-self-class="active"
                            data-active-if="not"
                            v-html="$t('fel-gpt-assistant.trainer.ui.defaultExamples')"
                            @click="felMultiBtn($event)"></button>
                        <button
                            class="sw-button sw-button--x-small"
                            data-action="toggle"
                            data-target=".fel-oai-trainer-examples-main-hide"
                            data-target-class="fel-is-hidden"
                            data-self-class="active"
                            data-query-all="true"
                            data-active-if="not"
                            v-html="$t('fel-gpt-assistant.trainer.ui.description')"
                            @click="felMultiBtn($event)"></button>
                        <button
                            class="sw-button sw-button--x-small"
                            data-action="toggle"
                            data-target=".fel-oai-trainer-container-wrapper"
                            data-target-class="fel-is-hidden"
                            @click="felMultiBtn($event)">X</button>
                    </div>
                </div>
                {# default examples #}
                <div class="fel-oai-trainer-examples-default-examples fel-is-hidden">
                    <p class="fel-trainer-box" v-html="$t('fel-gpt-assistant.trainer.ui.defaultExamplesAlreadyCapable')"></p>
                    <pre class="fel-size-xxs" v-html="getGenericExamplesForSearchQueries"></pre>
                </div>
                {# about #}
                <div class="fel-oai-trainer-examples-main-hide fel-trainer-box fel-is-hidden">
                    <p v-for="description,idx in $t('fel-gpt-assistant.trainer.descriptions')" v-html="description"></p>
                    <table class="fel-compare-gpts">
                        <caption>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparison') }}</caption>
                        <thead>
                            <tr>
                                <th>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparisonHeader.model') }}</th>
                                <th>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparisonHeader.requests') }}</th>
                                <th>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparisonHeader.tokens') }}</th>
                                <th>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparisonHeader.price') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td>gpt-4-turbo</td><td><b>20</b></td><td><b>35,519</b></td><td><b>$0.94</b></td></tr>
                            <tr><td>gpt-3.5-turbo</td><td><b>1667</b></td><td><b>4,824,057</b></td><td><b>$3.00</b></td></tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>{{ $t('fel-gpt-assistant.config.fileAlternate') }}</td>
                                <td>{{ $t('fel-gpt-assistant.trainer.ui.gptModelsComparisonHeader.tokens') }}: <b>2,833</b></td>
                                <td colspan="2">Character: <b>10260</b></td>
                            </tr>
                        </tfoot>
                    </table>
                    <p><b>{{ $t('fel-gpt-assistant.trainer.fewShotExamplesNote.title') }}</b></p>
                    <p v-for="description in $t('fel-gpt-assistant.trainer.fewShotExamplesNote.descriptions')" v-html="description"></p>
                </div>
            </div>
            {# starter #}
            <div class="fel-oai-starter-container">
                <div class="fel-oai-trainer-starter-button fel-flex-space-between">
                    <div class="fel-oai-trainer-start-app-container">
                        <button class="sw-button sw-button--small" @click="initTool" v-html="$t('fel-gpt-assistant.trainer.ui.start')"></button>
                        <button
                            class="sw-button sw-button--small"
                            data-action="toggle"
                            data-target=".fel-oai-trainer-examples-tool-instructions"
                            data-target-class="fel-is-hidden"
                            data-self-class="active"
                            data-active-if="not"
                            v-html="$t('fel-gpt-assistant.trainer.ui.introduction')"
                            @click="felMultiBtn($event)"></button>
                    </div>
                    <div class="fel-oai-trainer-reset-selection-container fel-is-hiddden">
                        <button
                            class="sw-button sw-button--small"
                            :disabled="!enablePropertySelection"
                            v-html="$t('fel-gpt-assistant.general.reset')"
                            @click="resetActualSelection($event)"></button>
                    </div>
                </div>
                {# Introduction #}
                <div class="fel-oai-trainer-examples-tool-instructions fel-trainer-box fel-is-hidden">
                    <p v-for="description in $t('fel-gpt-assistant.trainer.step.1.Instructions')" v-html="description"></p>
                </div>
            </div>

        </div>

        <div class="fel-oai-training-input-wrapper">
            {# User inputs #}
            <div class="fel-oai-trainer-user-query-container">
                <div class="sw-field sw-block-field sw-contextual-field">
                    <div class="sw-block-field__block">
                        <input
                            v-model="tempUserQueryProductName"
                            type="text"
                            name="training-query"
                            value=""
                            :placeholder="$t('fel-gpt-assistant.trainer.ui.produktName')"
                            :disabled="!tempUserQueryProductName && !enablePropertySelection"
                            @input="handleProductName">
                        <input
                            v-model="tempUserQueryMinPrice"
                            type="number"
                            name="training-query-min-price"
                            value=""
                            data-price="min"
                            autocomplete="off"
                            :placeholder="$t('fel-gpt-assistant.trainer.ui.minPrice')"
                            :disabled="!enablePriceSelection"
                            @input="handlePriceSelection">
                        <input
                            v-model="tempUserQueryMaxPrice"
                            type="number"
                            name="training-query-max-price"
                            value=""
                            data-price="max"
                            autocomplete="off"
                            :placeholder="$t('fel-gpt-assistant.trainer.ui.maxPrice')"
                            :disabled="!enablePriceSelection"
                            @input="handlePriceSelection">
                        <select
                            v-model:value="tempUserQuerySorting"
                            :disabled="!enablePriceSelection"
                            :title="$t('fel-gpt-assistant.trainer.ui.sorting')"
                            @change="handleOrderSelection">
                            <option data-reset="true" v-html="$t('fel-gpt-assistant.trainer.ui.sorting')"></option>
                            <option v-for="sort in this.availableFunctionsConfig.sorting"
                                :value="sort"
                                v-html="$t(`fel-gpt-assistant.trainer.ui.availableSortings.${sort}`)"></option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="fel-is-hidden">
                <div class="sw-field sw-block-field sw-contextual-field">
                    <div class="sw-block-field__block">
                        <input
                            v-model="tempUserPrompt"
                            type="text"
                            name="training-user"
                            value=""
                            readonly="true"
                            :placeholder="$t('fel-gpt-assistant.trainer.ui.userQuery')"
                            :disabled="!tempUserPrompt">
                    </div>
                </div>
                <div class="sw-field sw-block-field sw-contextual-field">
                    <div class="sw-block-field__block">
                        <input
                            v-model="tempAssistantPrompt"
                            type="text"
                            name="training-assistant"
                            value=""
                            readonly="true"
                            :placeholder="$t('fel-gpt-assistant.trainer.ui.assistantResponse')"
                            :disabled="!tempUserPrompt">
                    </div>
                </div>
            </div>

        </div>

        <div class="fel-oai-trainer-available-properties-container" :hidden="!tempUserPrompt">
            <ul class="fel-oai-trainer-option-list fel-oai-trainer-option-list-properties" :disabled="!enablePropertySelection">
                <li><b>{{ $t('fel-gpt-assistant.trainer.ui.properties') }}</b></li>
                <li v-for="data in propertyGroupOptionsList">
                    <span
                        class="sw-button sw-button--small"
                        data-in-group="properties"
                        :data-id="data.id"
                        :data-name="data.attributes.name"
                        :title="data.id"
                        v-html="data.attributes.name"
                        @click="handlePropertySelection"></span>
                </li>
            </ul>
            <ul class="fel-oai-trainer-option-list fel-oai-trainer-option-list-manufacturer" :disabled="!enableManufacturerSelection">
                <li><b>{{ $t('fel-gpt-assistant.trainer.ui.manufacturer') }}</b></li>
                <li v-for="data in manufacturerList">
                    <span
                        class="sw-button sw-button--small"
                        data-in-group="manufacturer"
                        :data-id="data.id"
                        :data-name="data.attributes.name"
                        :title="data.id"
                        v-html="data.attributes.name"
                        @click="handlePropertySelection"></span>
                </li>
            </ul>
        </div>

        <div class="fel-oai-trainer-final-examples-container">
            <div class="sw-field sw-block-field sw-contextual-field">
                <div class="sw-block-field__blck">

                    <div class="fel-oai-final-content-container fel-trainer-box" :hidden="!tempUserPrompt">
                        <textarea
                            v-model="finalRequiredProperties"
                            name="fel-final-example-properties"
                            class="fel-textarea"
                            :rows="finalRequiredProperties.split(`\n`).length"></textarea>
                        <textarea
                            v-model="finalExampleContent"
                            name="fel-final-example-chat-examples"
                            class="fel-textarea"
                            :rows="finalExampleContent.split(`\n`).length + parseInt(finalExampleContent.length / 140)"
                            v-html="finalExampleContent.trim()"></textarea>
                    </div>

                    <div class="fel-oai-storage-capture-container">
                        <div class="fel-oai-storage-btn-container fel-flex-space-between">
                            <div>
                                <div :hidden="!tempUserPrompt">
                                    <button
                                        class="sw-button sw-button--small"
                                        :title="$t('fel-gpt-assistant.trainer.ui.storage.lastAdjustmentsNote')"
                                        v-html="$t('fel-gpt-assistant.trainer.ui.storage.capture')"
                                        @click="captureCurrentExample($event)"></button>
                                </div>
                            </div>
                            <div>
                                {# get storage #}
                                <template v-if="storage.merged">
                                    <button
                                        class="sw-button sw-button--small active"
                                        data-action="toggle"
                                        data-target=".fel-storage-display-container"
                                        data-target-class="fel-is-hidden"
                                        data-self-class="active"
                                        data-active-if="not"
                                        v-html="$t('fel-gpt-assistant.trainer.ui.storage.loadExamples')"
                                        @click="felMultiBtn($event)"></button>
                                </template>
                                <template v-else>
                                    <button
                                        v-if="examplesInStorage && !storage.removed"
                                        class="sw-button sw-button--small fel-load-examples-from-storage"
                                        :title="$t('fel-gpt-assistant.trainer.ui.storage.localStorageNote')"
                                        v-html="$t('fel-gpt-assistant.trainer.ui.storage.loadExamples')"
                                        @click="parseFromStorage($event)"></button>
                                </template>
                                {# delete examples from storage #}
                                <button
                                    v-if="(examplesInStorage && !storage.removed) || storage.added"
                                    class="sw-button sw-button--small sw-button--danger fel-delete-examples-from-storag"
                                    :title="$t('fel-gpt-assistant.trainer.ui.storage.localStorageNote')"
                                    v-html="$t('fel-gpt-assistant.trainer.ui.storage.deleteExamples')"
                                    @click="_deleteFromStorage($event)"></button>
                            </div>
                        </div>
                    </div>

                    {# storage #}
                    <template v-if="(examplesInStorage && !storage.removed) || storage.added">
                        <div class="fel-oai-storage-container">
                            <div class="fel-content-box">
                                <div class="fel-storage-display-container">

                                    <div v-if="null !== storage.merged" class="fel-oai-copy-and-pastable-examples fel-trainer-box">
                                        <textarea name="fel-oai-trainer-combined-response" :rows="finalExamplesStringLength">{{
                                            exampleCollectionToString({
                                                title: "Generic random examples for search queries:\n\nget_product_properties()",
                                                requiredProperties: storage.merged.requiredProperties,
                                                defaultContactExample: `user: "Contact details."\nassistant: get_meta_information()`,
                                                conversation: storage.merged.conversation,
                                            }).trim()
                                        }}</textarea>
                                    </div>

                                    <div v-if="null !== storage.merged" class="fel-trainer-box">
                                        <div class="fel-flex-space-between">
                                            <div>
                                                <button
                                                    class="sw-button sw-button--small sw-button--ghost fel-replace-default-with-custom-examples"
                                                    :title="$t('fel-gpt-assistant.trainer.ui.storage.adjustBeforeReplace')"
                                                    v-html="$t('fel-gpt-assistant.trainer.ui.replaceDefaultWithCustom')"
                                                    @click="replaceDefaultExampleWithCustom"></button>
                                                <small>
                                                    {{ $t('fel-gpt-assistant.trainer.ui.total') }}:
                                                    {{ storage.merged.conversation.length + 1 }}
                                                </small>
                                            </div>
                                            <div>
                                                <button
                                                    class="sw-button sw-button--small"
                                                    data-action="toggle"
                                                    data-target=".fel-oai-final-notes"
                                                    data-target-class="fel-is-hidden"
                                                    data-self-class="active"
                                                    data-active-if="not"
                                                    v-html="$t('fel-gpt-assistant.trainer.ui.info')"
                                                    @click="felMultiBtn($event)"></button>
                                                <button
                                                    class="sw-button sw-button--small fel-download-as-txt"
                                                    :title="$t('fel-gpt-assistant.files.saveFile')"
                                                    @click="exportToFile('gpt-chatbot-training.txt', finalExamplesStringPlain)">
                                                    TXT
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="fel-oai-final-notes fel-trainer-box fel-is-hidden">
                                        <p>{{ $t('fel-gpt-assistant.trainer.ui.storage.usageNotification') }}</p>
                                        <p>{{ $t('fel-gpt-assistant.trainer.ui.storage.howToUseNotification') }}</p>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </template>

                </div>
            </div>
        </div>
    </div>

    <div class="fel-trainer-footer fel-trainer-box text-center">
        <p>{{ $t('fel-gpt-assistant.trainer.name') }}</p>
    </div>

</div>
